import { _decorator,isValid,Material,size,sp,v2,} from 'cc';
import TrackComponent from '../../base/TrackComponent';
import { GameEnum } from '../../../const/GameEnum';
import { Tools } from '../../../utils/Tools';
import { GameIns } from '../../../GameIns';
import { TrackGroup } from '../../../data/EnemyWave';
import FBoxCollider from '../../../collider-system/FBoxCollider';
import { ColliderGroupType } from '../../../collider-system/FCollider';
import EnemyPlaneBase from '../enemy/EnemyPlaneBase';
import { EnemyData } from '../../../data/EnemyData';
import { MyApp } from '../../../../app/MyApp';
const { ccclass, property } = _decorator;

const RAND_POINT = [
    v2(-100,-320),
    v2(-100,-250),
    v2(100,-320),
    v2(100,-250),
]

@ccclass("BossPlane")
export default class BossPlane extends EnemyPlaneBase {

    @property(sp.Skeleton)//boss显示组件
    spinePlane: sp.Skeleton | null = null;

    _idleName: string = "idle1";
    _formIndex: number = -1;//形态索引

    //随机移动参数
    _posX: number = 0;
    _posY: number = 0;
    _moveToX: number = 0;
    _moveToY: number = 0;
    _moveSpeed: number = 0;
    _bArriveDes: boolean = false;//是否达到目标点
    // //下一个航点
    _nextWayPointTime: number = 0;
    _nextWayPointX: number = 0;
    _nextWayPointY: number = 0;
    _nextWayPointInterval: number = 0;
    _nextWaySpeed: number = 0;

    _action: number = -1;
    _bFirstWayPoint: boolean = false;
    tip: string = "";//boss警告提示文本
    _hurtActDuration = 0.2; // 受伤动画持续时间

    protected onLoad(): void {
        this._trackCom = Tools.addScript(this.node, TrackComponent);
    }

    protected update(dt: number): void {
        dt = dt * GameIns.battleManager.animSpeed;
        if (this._hurtActDuration > 0) {
            this._hurtActDuration -= dt;
            if (this._hurtActDuration <= 0) {
                this.resetRoleEffect();
            }
        }
    }

    initPlane(data: EnemyData) {
        super.initPlane(data);
        this._bFirstWayPoint = true;
        this.bDamageable = false;
        this._initCollide();
        this._initTrack();
        this.setFormIndex(0);
    }


    _initTrack() {
        this._trackCom!.setTrackGroupOverCall(() => {
            if (this._action === GameEnum.BossAction.Appear) {
                this._trackCom!.setTrackAble(false);
                this.setAction(GameEnum.BossAction.Transform);
            }
        });
    }

    _initCollide(): void {
        this.collideComp = this.getComponent(FBoxCollider) || this.addComponent(FBoxCollider);
        this.collideComp!.init(this, size(250, 250)); // 初始化碰撞组件
        this.collideComp!.groupType = ColliderGroupType.ENEMY_NORMAL;
        this.colliderEnabled = false;
    }

        /**
     * 设置形态索引（一般boss会有好几段变形，要等策划设计）
     * @param index 形态索引
     */
    setFormIndex(index: number) {
        this._formIndex = index;
        this._idleName = `idle1`;
        this.setAction(GameEnum.BossAction.Appear);
    }

    setAction(action: number) {
        if (this._action !== action) {
            this._action = action;

            let BossAction = GameEnum.BossAction;
            switch (this._action) {
                case BossAction.Normal:
                    this._playSkel(this._idleName, true, () => { });
                    this.bDamageable = true;
                    break;

                case BossAction.Appear:
                    this._playSkel(`enter${this._formIndex + 1}`, true, () => { });
                    this._startAppearTrack();
                    break;

                case BossAction.Transform:
                    this._playSkel(`ready${this._formIndex + 1}`, false, () => {
                        this.transformEnd();
                    });
                    break;

                case BossAction.AttackPrepare:
                    this.scheduleOnce(() => {
                        this.setAction(BossAction.AttackIng);
                    });
                    break;

                case BossAction.AttackIng:
                case BossAction.AttackOver:
                    break;
                case BossAction.Blast:
                    break;
                default:
            }
        }
    }

    _playSkel(animName: string, loop: boolean, callback: Function) {
        this.spinePlane!.setCompleteListener(() => {
            callback?.()
        })
        this.spinePlane!.setAnimation(0, animName, loop);
    }

    /**
     * 设置提示信息
     * @param tip 提示信息
     */
    setTip(tip: string) {
        this.tip = tip;
    }
    /**
    * 变形结束
    */
    transformEnd() {
        if (this.tip !== "") {
            GameIns.battleManager.bossChangeFinish(this.tip);
        } else {
            GameIns.battleManager.bossFightStart();
        }
    }

    /**
     * 开始战斗
     */
    startBattle() {
        this._startNormalTrack();
        this.setAction(GameEnum.BossAction.Normal);
        this.colliderEnabled = true;
    }

    /**
     * 更新游戏逻辑
     * @param deltaTime 每帧时间
     */
    updateGameLogic(deltaTime: number) {
        if (!this.isDead) {
            this.updateAction(deltaTime);
        }
    }

    updateAction(deltaTime: number) {
        let BossAction = GameEnum.BossAction;
        switch (this._action) {
            case BossAction.Normal:
                this._processNextWayPoint(deltaTime);
                this._updateMove(deltaTime);
                this._processNextAttack(deltaTime);
                break;

            case BossAction.Appear:
                this._updateMove(deltaTime);
                if (this._bArriveDes) {
                    this.setAction(BossAction.Transform);
                }
                break;

            case BossAction.Transform:
                break;

            case BossAction.AttackPrepare:
                this._processNextWayPoint(deltaTime);
                break;

            case BossAction.AttackIng:
                this._processNextWayPoint(deltaTime);
                this._udpateShoot(deltaTime);
                break;

            case BossAction.AttackOver:
                this._processNextWayPoint(deltaTime);
                this.setAction(BossAction.Normal);
                break;

            case BossAction.Blast:
                break;
        }
    }
    /**
     * 开始出现轨迹
     */
    _startAppearTrack() {
        // 0,150,16,150

        //出现的轨迹，暂时写死，等波次管理器做完，再接入新的轨迹系统
        const trackGroup = new TrackGroup();
        trackGroup.loopNum = 1;
        trackGroup.trackIDs = [16];
        trackGroup.speeds = [150];
        trackGroup.trackIntervals = [0];

        this._trackCom!.init(this, [trackGroup], [], v2(0, 150));
        this._trackCom!.setTrackAble(true);
        this._trackCom!.startTrack();
    }

    /**
     * 开始正常轨迹,boss随机移动位置
     */
    _startNormalTrack() {
        this._trackCom!.setTrackAble(false);
        this.setAction(GameEnum.BossAction.Normal);
    }

    /**
     * 移动到指定位置
     * @param x X 坐标
     * @param y Y 坐标
     * @param speed 移动速度
     * @param transformMove 是否为变形移动
     */
    moveToPos(x: number, y: number, speed: number) {
        this._moveToX = x;
        this._moveToY = y;
        this._moveSpeed = speed;
        this._bArriveDes = false;
    }


    setPos(x: number, y: number, update: boolean = true) {
        this.node.setPosition(x, y);
        this._posX = x;
        this._posY = y;
    }

    /**
     * 处理下一个路径点
     * @param deltaTime 每帧时间
     */
    _processNextWayPoint(deltaTime: number) {
        if (this._bArriveDes) {
            this._nextWayPointTime += deltaTime;
            if (this._nextWayPointTime > this._nextWayPointInterval) {
                this._nextWayPointInterval = 0.15;
                this._nextWayPointTime = 0;

                if (this._bFirstWayPoint) {
                    this._bFirstWayPoint = false;
                } else {
                    const index = Tools.random_int(0, RAND_POINT.length - 1);
                    this._nextWayPointX = RAND_POINT[index].x;
                    this._nextWayPointY = RAND_POINT[index].y;
                    this._nextWaySpeed = Tools.getRandomInArray([2,2.2])!;
                    this.moveToPos(this._nextWayPointX, this._nextWayPointY, this._nextWaySpeed);
                }
            }
        }
    }


    _updateMove(deltaTime: number) {
        if (this._action === GameEnum.BossAction.Appear) {
            // 如果 Boss 在出现阶段或有轨迹组，则更新轨迹逻辑
            this._trackCom!.updateGameLogic(deltaTime);
        } else if (!this._bArriveDes) {
            const deltaX = this._moveToX - this._posX;
            const deltaY = this._moveToY - this._posY;
            const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

            let moveX = 0;
            let moveY = 0;

            // 如果距离小于等于移动速度，则直接到达目标点
            if (distance <= this._moveSpeed) {
                moveX = deltaX;
                moveY = deltaY;
            }
            // 否则按比例移动
            else {
                moveX = this._moveSpeed * deltaX / distance;
                moveY = this._moveSpeed * deltaY / distance;
            }

            // 更新位置
            this._posX += moveX;
            this._posY += moveY;
            this.setPos(this._posX, this._posY);

            // 检查是否到达目的地（当移动量很小时认为已到达）
            this._bArriveDes = (Math.abs(moveX) < 0.5 && Math.abs(moveY) < 0.5);
        }
    }

    /**
     * 处理下一次攻击
     * @param deltaTime 每帧时间
     */
    _processNextAttack(deltaTime: number) {
        // if (this._shootAble && this._action === GameEnum.BossAction.Normal) {
        //     this._nextAttackTime += deltaTime;
        //     if (this._nextAttackTime > this._nextAttackInterval) {
        //         this._nextAttackInterval = Tools.getRandomInArray(this._data!.attackIntervals)!;
        //         this._nextAttackTime = 0;

        //         let attackAction = null;
        //         if (this._bOrderAttack) {
        //             const randomIndex = Tools.getRandomInArray(this._orderAtkArr)!;
        //             Tools.arrRemove(this._orderAtkArr, randomIndex);
        //             attackAction = this._atkActions[randomIndex];
        //             this._orderIndex++;
        //             if (this._orderIndex > this._atkActions.length - 1) {
        //                 this._bOrderAttack = false;
        //             }
        //         } else {
        //             attackAction = Tools.getRandomInArray(this._atkActions);
        //         }

        //         if (attackAction) {
        //             this._bAttackMove = attackAction.bAtkMove;
        //             this._attackID = attackAction.atkActId;
        //             this._attackPoints.splice(0);

        //             for (const pointId of attackAction.atkPointId) {
        //                 const pointData = this._atkPointDatas[pointId];
        //                 if (pointData[0]) {
        //                     let attackPoint = this._atkPointsPool[pointId]
        //                     if (!attackPoint) {
        //                         const pointNode = new Node();
        //                         this.node.addChild(pointNode);
        //                         attackPoint = pointNode.addComponent(AttackPoint);
        //                         this._atkPointsPool.push(attackPoint);
        //                     }
        //                     attackPoint.initForBoss(pointData[1], this);
        //                     this._attackPoints.push(attackPoint);
        //                 }
        //             }

        //             if (this._attackPoints.length > 0) {
        //                 this.setAction(GameEnum.BossAction.AttackPrepare);
        //             }
        //         }
        //     }
        // }
    }

    /**
     * 更新射击逻辑
     * @param deltaTime 每帧时间
     */
    async _udpateShoot(deltaTime: number) {
        // if (this._shootAble) {
        //     let allAttacksOver = true;

        //     for (const attackPoint of this._attackPoints) {
        //         await attackPoint.updateGameLogic(deltaTime);
        //         if (!attackPoint.isAttackOver()) {
        //             allAttacksOver = false;
        //         }
        //     }

        //     if (allAttacksOver) {
        //         this.setAction(GameEnum.BossAction.AttackOver);
        //     }
        // }
    }


    //由于boss资源不是新的，暂时把闪白效果，写在这里
    
    playHurtAnim(){
        this.playFlashAnim();
    }
    // 播放闪白动画
    async playFlashAnim() {
        let material = await MyApp.resMgr.loadAsync("effect/flash/flash", Material);
        if (material && isValid(this.spinePlane)){
            this.spinePlane!.customMaterial = material;
        }
        this._hurtActDuration = 0.2;
    }

    resetRoleEffect(){
        this._hurtActDuration = 0;
        this.spinePlane!.customMaterial = null;
    }

    setAnimSpeed(speed: number) {
        if (this.spinePlane && isValid(this.spinePlane)){
            this.spinePlane.timeScale = speed;
        }
    }
}