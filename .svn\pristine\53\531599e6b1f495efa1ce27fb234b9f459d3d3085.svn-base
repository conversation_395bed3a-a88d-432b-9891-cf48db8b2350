import { _decorator, assetManager, CCInteger, Component, instantiate, Prefab, v2, Vec2 } from 'cc';
import { EDITOR } from 'cc/env';
const { ccclass, property, executeInEditMode } = _decorator;

@ccclass('EmittierTerrain')
@executeInEditMode()
export class EmittierTerrain extends Component {

    protected onLoad(): void {
        if (EDITOR) {
            this.node.removeAllChildren();
            this._loadElems();
        }
    }

    protected update(): void {
        
    }

    public play(bPlay: boolean): void {
        if (EDITOR) {
            if (bPlay) {
            }
        }
    }

    protected onDestroy(): void {
        this.node.removeAllChildren();
    }

    private _loadElems(): void {
        
    }
}

