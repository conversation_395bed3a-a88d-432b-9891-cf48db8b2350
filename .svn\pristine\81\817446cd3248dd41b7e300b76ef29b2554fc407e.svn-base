import { _decorator, assetManager, JsonAsset } from "cc";
import { EDITOR } from 'cc/env';
import { IMgr } from '../../../../scripts/core/base/IMgr';
import * as cfg from '../autogen/luban/schema';
import { BundleName } from "../const/BundleConst";
const { ccclass, property } = _decorator;
@ccclass("LubanMgr")
export class LubanMgr extends IMgr {
    private _table: cfg.Tables | null = null;
    private static readonly LUBAN_PATH = 'luban/';

    init(): void {
        super.init();
    }

    load(): Promise<void> {
        return new Promise((resolve, reject) => {
            const bundle = assetManager.bundles.get(BundleName.Luban);
            bundle!.loadDir("./", JsonAsset, (err, assets: JsonAsset[]) => {
                if (err) {
                    reject(err);
                    return;
                }
                var dataMap = new Map<string, JsonAsset>();
                for (let asset of assets) {
                    dataMap.set(asset.name, asset);
                }
                this._table = new cfg.Tables((file: string) => {
                    if (dataMap.has(file)) {
                        return dataMap.get(file)!.json;
                    }
                    console.warn(`LubanMgr: File ${file} not found in loaded assets.`);
                    return null;
                });
                resolve();
            });
        });
    }

    // 仅在编辑器环境下使用
    async initInEditor(): Promise<void> {
        if (this._table != null || !EDITOR) {
            return;
        }

        // 遍历assets/bundles/luban/ 目录下的json文件
        const root_dir = 'db://assets/bundles/luban/';
        const pattern = root_dir + '**/*.json';

        try {
            // @ts-ignore
            const res = await Editor.Message.request('asset-db', 'query-assets', { pattern });
            const arr = Array.isArray(res) ? res : (Array.isArray(res[0]) ? res[0] : []);
            const files = arr.map((a: any) => a.path).filter((a: any) => a.startsWith(root_dir));
            
            // console.log('LubanMgr: Found files:', files);
            let LoadedCount = 0;

            var dataMap = new Map<string, any>();
            // Load each JSON file's content
            for (let filePath of files) {
                try {
                    // @ts-ignore
                    const uuid = await Editor.Message.request('asset-db', 'query-uuid', filePath + '.json');
                    assetManager.loadAny<JsonAsset>(uuid, (err, jsonAsset) => {
                        if (err) {
                            console.warn(`LubanMgr: Failed to load asset ${filePath}:`, err);
                            return;
                        }
                        dataMap.set(jsonAsset.name, jsonAsset);
                        LoadedCount++;
                    });
                } catch (fileError) {
                    console.warn(`LubanMgr: Failed to load file ${filePath}:`, fileError);
                }
            }

            // Wait until all files are loaded
            while (LoadedCount < files.length) {
                await new Promise(res => setTimeout(res, 100)); // wait 100ms
            }

            this._table = new cfg.Tables((file: string) => {
                if (dataMap.has(file)) {
                    return dataMap.get(file)!.json;
                }
                
                return null;
            });

        } catch (error) {
            console.error("LubanMgr: Failed to initialize in editor:", error);
            throw error;
        }
    }

    get table(): cfg.Tables {
        return this._table!;
    }
}