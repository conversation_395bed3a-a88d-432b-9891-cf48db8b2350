import { _decorator, Component, Vec2, CCFloat, Enum, js } from 'cc';
const { ccclass, property, executeInEditMode } = _decorator;

import { LevelDataElem } from 'db://assets/scripts/leveldata/leveldata';

import { LevelEditorBaseUI } from './LevelEditorBaseUI';
import { LevelEditorLayerUI } from './LevelEditorLayerUI';

enum LevelLayerElemLayer {
    Node,
    Background,
    Floor_0,
    Floor_1,
    Floor_2,
    Floor_3,
    Floor_4,
    Floor_5,
    Floor_6,
    Sky_0,
    Sky_1,
    Sky_2,
    Sky_3,
    Sky_4,
    Sky_5,
}
Enum(LevelLayerElemLayer);

@ccclass('LevelEditorElemUI')
@executeInEditMode()
export class LevelEditorElemUI extends Component {
    @property(CCFloat)
    public get time(): number {
        const layerNode = this.node.parent!.parent
        const rootNode = layerNode!.parent!.parent
        const baseUI = rootNode!.getComponent(LevelEditorBaseUI)
        if (!baseUI) {
            return 0;
        }
        const layer = baseUI.floorLayers.find((layer) => layer.node == layerNode) || baseUI.skyLayers.find((layer) => layer.node == layerNode)
        if (!layer) {
            return 0;
        }
        return this.node.position.y / layer.speed;
    }
    @property({type:LevelLayerElemLayer})
    public set layer(value: LevelLayerElemLayer) {
        var layerCom : LevelEditorLayerUI|null = null;
        var scene = this.node.scene;
        var baseCom = this.node.scene.getComponentInChildren(LevelEditorBaseUI);
        if (value == LevelLayerElemLayer.Background) {
            layerCom = baseCom!.backgroundLayer!.node!.getComponent(LevelEditorLayerUI)!;
        } else if (value >= LevelLayerElemLayer.Floor_0 && value <= LevelLayerElemLayer.Floor_6) {
            let index = value - LevelLayerElemLayer.Floor_0;
            if (index <= baseCom!.floorLayers.length) {
                layerCom = baseCom!.floorLayers[index].node!.getComponent(LevelEditorLayerUI)!;
            }
        } else if (value >= LevelLayerElemLayer.Sky_0 && value <= LevelLayerElemLayer.Sky_5) {
            let index = value - LevelLayerElemLayer.Sky_0;
            if (index <= baseCom!.skyLayers.length) {
                layerCom = baseCom!.skyLayers[index].node!.getComponent(LevelEditorLayerUI)!;
            }
        }
        if (layerCom == null) {
            return; 
        }
        const worldPos = this.node.getWorldPosition()
        console.log("LevelEditorElemUI set layer " + js.getClassName(this));
        switch(js.getClassName(this))
        {
            case "LevelEditorWaveUI":
                layerCom.wavesNode!.addChild(this.node);
                break
            case "LevelEditorEventUI":
                layerCom.eventsNode!.addChild(this.node);
                break
        }
        this.node.setWorldPosition(worldPos)
    }
    public get layer(): LevelLayerElemLayer {
        const layerCom = this.node.parent?.parent?.getComponent(LevelEditorLayerUI)
        if (!layerCom) {
            return LevelLayerElemLayer.Node;
        }
        const baseCom = layerCom.node.parent?.parent?.getComponent(LevelEditorBaseUI)
        if (!baseCom) {
            return LevelLayerElemLayer.Node;
        }
        if (baseCom.backgroundLayer?.node == layerCom.node) {
            return LevelLayerElemLayer.Background;
        }
        var index = baseCom.floorLayers.findIndex((layer) => layer.node == layerCom.node);
        if (index >= 0) {
            return LevelLayerElemLayer.Floor_0 + index;
        }
        index = baseCom.skyLayers.findIndex((layer) => layer.node == layerCom.node);
        if (index >= 0) {
            return LevelLayerElemLayer.Sky_0 + index;
        }
        return LevelLayerElemLayer.Node;
    }

    public elemID = "";
    protected onLoad(): void {
        if (this.elemID == "") {
            this.elemID = this.uuid;
        }
    }
    public initByLevelData(data: LevelDataElem) {
        this.node.setPosition(data.position.x, data.position.y);
        this.elemID = data.elemID;
        this.node.name = data.name;
    }
    public fillLevelData(data: LevelDataElem) {
        data.position = new Vec2(this.node.position.x, this.node.position.y);
        data.elemID = this.elemID;
        data.name = this.node.name;
    }
}