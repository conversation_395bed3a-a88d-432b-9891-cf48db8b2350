
import { director, Rect } from "cc";
import { PlaneData } from "db://assets/bundles/common/script/data/plane/PlaneData";
import { SingletonBase } from "../../../../../scripts/core/base/SingletonBase";
import { UIMgr } from "../../../../../scripts/core/base/UIMgr";
import { LoadingUI } from "../../ui/gameui/LoadingUI";
import { BulletSystem } from "../bullet/BulletSystem";
import { GameEnum } from "../const/GameEnum";
import { GameIns } from "../GameIns";
import GameMapRun from "../ui/map/GameMapRun";
import EventManager from "../../event/EventManager";
import { GameEvent } from "../event/GameEvent";

export class BattleManager extends SingletonBase<BattleManager> {

    gameType = GameEnum.GameType.Common;
    initBattleEnd = false;
    gameStart = false;
    animSpeed = 1;
    _gameTime = 0;

    mainStage = 0;
    subStage = 0;

    _loadTotal = 0;
    _loadCount = 0;

    setBattleInfo(mainStage: number, subStage: number, planeData: PlaneData) {
        this.mainStage = mainStage;
        this.subStage = subStage;
        GameIns.mainPlaneManager.setPlaneData(planeData);
    }

    mainReset() {
        GameIns.enemyManager.mainReset();
        GameIns.bossManager.mainReset();
        GameIns.waveManager.reset();
        GameIns.mainPlaneManager.mainReset();
        GameMapRun.instance!.reset();
        GameMapRun.instance!.clear();
        GameIns.hurtEffectManager.clear();
        GameIns.gameRuleManager.reset();
    }

    subReset() {
        GameIns.gameRuleManager.reset();
        GameIns.waveManager.reset();
        GameIns.enemyManager.subReset();
        GameIns.bossManager.subReset();
    }

    /**
     * 检查所有资源是否加载完成
     */
    checkLoadFinish() {
        this._loadCount++;
        let loadingUI = UIMgr.get(LoadingUI)
        loadingUI.updateProgress(this._loadCount / this._loadTotal)
        if (this._loadCount >= this._loadTotal) {
            UIMgr.closeUI(LoadingUI)
            this.initBattle();
        }

    }

    addLoadCount(count: number) {
        this._loadTotal += count;
    }

    startLoading() {
        GameIns.gameRuleManager.gameSortie();
        GameIns.mainPlaneManager.preload();
        GameIns.hurtEffectManager.preLoad();//伤害特效资源
        GameMapRun.instance!.initData(this.mainStage);//地图背景初始化
        GameIns.enemyManager.preLoad();//敌人资源
        GameIns.bossManager.preLoad();//boss资源
    }



    initBattle() {
        GameIns.stageManager.initBattle(this.mainStage, this.subStage);
        GameIns.mainPlaneManager.mainPlane!.initBattle();
        GameIns.mainPlaneManager.mainPlane!.planeIn();
        BulletSystem.init(new Rect(0, 0, 750, 1334));
    }

    onPlaneIn() {
        this.initBattleEnd = true;
    }

    beginBattle() {
        if (this.initBattleEnd && !this.gameStart) {
            this.gameStart = true;
            EventManager.Instance.emit(GameEvent.GameStart)
            GameIns.stageManager.gameStart();
            GameIns.waveManager.gameStart();
            GameIns.gameRuleManager.gameStart();

            GameIns.mainPlaneManager.mainPlane!.begine(true);
        }
    }

    /**
     * 更新游戏逻辑
     * @param {number} dt 每帧的时间间隔
     */
    update(dt: number) {
        dt = dt * this.animSpeed;
        if (GameIns.gameRuleManager.isGameOver()) {
            if (GameIns.gamePlaneManager) {
                GameIns.gamePlaneManager.enemyTarget = null;
            }
            return;
        }

        if (GameIns.gameRuleManager.isInBattle() || GameIns.gameRuleManager.isGameWillOver()) {
            GameIns.gamePlaneManager.update(dt);
            GameIns.waveManager.updateGameLogic(dt);
            GameIns.enemyManager.updateGameLogic(dt);
            GameIns.bossManager.updateGameLogic(dt);

            GameIns.gameRuleManager.updateGameLogic(dt);

            //子弹发射器系统
            BulletSystem.tick(dt);

            this._gameTime += dt;
        } else if (GameIns.gamePlaneManager) {
            GameIns.gamePlaneManager.enemyTarget = null;
        }
    }

    setTouchState(isTouch: boolean) {
        if (isTouch){
            this.beginBattle();
            // director.getScheduler().setTimeScale(1);
            this.animSpeed = 1;
        }else{
            // director.getScheduler().setTimeScale(0.3);
            this.animSpeed = 0.2;
        }
        GameIns.enemyManager.setAnimSpeed(this.animSpeed);
        GameIns.bossManager.setAnimSpeed(this.animSpeed);
        GameIns.mainPlaneManager.mainPlane!.setAnimSpeed(this.animSpeed);
    }

    /**
     * 战斗失败逻辑
     */
    async battleDie() {
        GameIns.gameRuleManager.gamePause();
    }

    //     /**
    //      * 战斗复活逻辑
    //      */
    //     relifeBattle() {
    //         GameIns.eventManager.emit(GameEvent.MainRelife);
    //         GameIns.gameRuleManager.gameResume();
    //     }

    /**
     * 战斗失败结算
     */
    battleFail() {
        GameIns.gameMainUI!.showGameResult(false);
        GameIns.mainPlaneManager.mainPlane!.hpNode!.active = false;
        this.endBattle();
    }

    /**
     * 战斗胜利逻辑
     */
    battleSucc() {
        GameIns.mainPlaneManager.mainPlane!.hpNode!.active = false;
        this.endBattle();

        if (GameIns.stageManager.checkStage(this.mainStage, this.subStage + 1)) {
            this.startNextBattle();
        } else {
            GameIns.gameMainUI!.showGameResult(true);
        }
    }
    /**
 * 继续下一场战斗
 */
    startNextBattle() {
        this.subReset();
        this.subStage += 1;
        this.initBattle();
    }

    /**
     * 结束战斗
     */
    endBattle() {
        BulletSystem.destroy();
        GameIns.gameRuleManager.gameOver();

        this.gameStart = false;
        this.initBattleEnd = false;
    }


    /**
     * Boss切换完成
     * @param {string} bossName Boss名称
     */
    bossChangeFinish(bossName: string) {
        // const bossEnterDialog = GameIns.uiManager.getDialog(BossEnterDialog.default);
        // if (bossEnterDialog) {
        //     bossEnterDialog.node.active = true;
        //     GameIns.mainPlaneManager.moveAble = false;
        //     bossEnterDialog.showTips(bossName);
        // }
    }

    bossWillEnter() {
        GameIns.mainPlaneManager.mainPlane!.setFireEnable(false);
        GameIns.mainPlaneManager.moveAble = false;
    }
    /**
     * 开始Boss战斗
     */
    bossFightStart() {
        GameIns.mainPlaneManager.mainPlane!.setFireEnable(true);
        GameIns.mainPlaneManager.moveAble = true;

        GameIns.bossManager.bossFightStart();
    }

    /**
     * 获取屏幕比例
     * @returns {number} 屏幕比例
     */
    getRatio() {
        return 0.666667; // 固定比例值
    }

    isGameType(gameType: number) {
        return this.gameType == gameType;
    }
}