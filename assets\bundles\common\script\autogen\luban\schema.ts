
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------


 
/**
 * 特效绑定点
 */
export enum BindSocket {
    /**
     * 自己
     */
    Self = 0,
    /**
     * 屏幕
     */
    Scene = 1,
}

 
 
/**
 * buff类型
 */
export enum BuffType {
    /**
     * 正面
     */
    Positive = 0,
    /**
     * 中性
     */
    Neutral = 1,
    /**
     * 负面
     */
    Negative = 2,
}

 
 
/**
 * 子弹来源
 */
export enum BulletSourceType {
    /**
     * 自机
     */
    MAINPLANE = 0,
    /**
     * 僚机
     */
    WINGPLANE = 1,
    /**
     * 敌机
     */
    ENEMYPLANE = 2,
}

 
 
/**
 * 子弹类型
 */
export enum BulletType {
    /**
     * 常规
     */
    NORMAL = 0,
    /**
     * 核弹
     */
    NUCLEAR = 1,
}

 
 
/**
 * 伤害类型
 */
export enum DamageType {
    /**
     * 所有
     */
    ALL = 0,
    /**
     * 爆炸
     */
    EXPLOSIVE = 1,
    /**
     * 普通
     */
    NORMAL = 2,
    /**
     * 能量
     */
    ENERGETIC = 3,
    /**
     * 物理
     */
    PHYSICAL = 4,
}

 
 
/**
 * 效果类型
 */
export enum EffectType {
    /**
     * 最大生命值%
     */
    AttrMaxHPPer = 1,
    /**
     * 最大生命值+
     */
    AttrMaxHPAdd = 2,
    /**
     * 生命恢复速度%
     */
    AttrHPRecoveryPer = 3,
    /**
     * 生命恢复速度+
     */
    AttrHPRecoveryAdd = 4,
    /**
     * 基于最大生命值的恢复速度%
     */
    AttrHPRecoveryMaxHPPerAdd = 5,
    /**
     * 回复最大生命值%
     */
    HealMaxHPPer = 6,
    /**
     * 回复已损失生命%
     */
    HealLoseHPPer = 7,
    /**
     * 回复指定生命值+
     */
    HealHP = 8,
    /**
     * 攻击力%
     */
    AttrAttackPer = 9,
    /**
     * 攻击力+
     */
    AttrAttackAdd = 10,
    /**
     * 对Boss伤害%
     */
    AttrAttackBossPer = 11,
    /**
     * 对普通怪物伤害%
     */
    AttrAttackNormalPer = 12,
    /**
     * 幸运值%
     */
    AttrFortunatePer = 13,
    /**
     * 幸运值+
     */
    AttrFortunateAdd = 14,
    /**
     * 闪避率+
     */
    AttrMissAdd = 15,
    /**
     * 子弹伤害抗性%
     */
    AttrBulletHurtResistancePer = 16,
    /**
     * 子弹伤害抗性+
     */
    AttrBulletHurtResistanceAdd = 17,
    /**
     * 撞击伤害减免%
     */
    AttrCollisionHurtDerateAdd = 18,
    /**
     * 撞击伤害抗性%
     */
    AttrCollisionHurtResistancePer = 19,
    /**
     * 撞击伤害抗性+
     */
    AttrCollisionHurtResistanceAdd = 20,
    /**
     * 子弹伤害减免%
     */
    AttrBulletHurtDerateAdd = 21,
    /**
     * 结算得分%
     */
    AttrFinalScoreAdd = 22,
    /**
     * 击杀得分%
     */
    AttrKillScoreAdd = 23,
    /**
     * 能量回复%
     */
    AttrEnergyRecoveryPerAdd = 24,
    /**
     * 能量回复+
     */
    AttrEnergyRecoveryAdd = 25,
    /**
     * 拾取范围%
     */
    AttrPickRadiusPer = 26,
    /**
     * 拾取范围+
     */
    AttrPickRadiusAdd = 27,
    /**
     * 添加Buff
     */
    ApplyBuff = 28,
    /**
     * 免疫子弹伤害
     */
    ImmuneBulletHurt = 29,
    /**
     * 免疫撞击伤害
     */
    ImmuneCollisionHurt = 30,
    /**
     * 无视子弹
     */
    IgnoreBullet = 31,
    /**
     * 无视撞击
     */
    IgnoreCollision = 32,
    /**
     * 免疫核弹伤害
     */
    ImmuneBombHurt = 33,
    /**
     * 免疫主动技能
     */
    ImmuneActiveSkillHurt = 34,
    /**
     * 无敌
     */
    Invincible = 35,
    /**
     * 开局核弹携带数+
     */
    AttrBombMax = 36,
    /**
     * 子弹攻击+
     */
    BulletAttackAdd = 37,
    /**
     * 子弹攻击%
     */
    BulletAttackPer = 38,
    /**
     * 基于最大生命值系数的伤害%
     */
    HurtMaxHPPer = 39,
    /**
     * 基于当前生命值系数的伤害%
     */
    HurtCurHPPer = 40,
    /**
     * 核弹伤害+
     */
    AttrBombHurtAdd = 41,
    /**
     * 核弹伤害%
     */
    AttrBombHurtPer = 42,
    /**
     * 击杀
     */
    Kill = 43,
    /**
     * 伤害
     */
    Hurt = 44,
}

 
 
/**
 * 装备部位
 */
export enum EquipClass {
    /**
     * 无效值
     */
    NONE = 0,
    /**
     * 火力核心
     */
    WEAPON = 1,
    /**
     * 副武器
     */
    SUB_WEAPON = 2,
    /**
     * 装甲核心
     */
    ARMOR = 3,
    /**
     * 科技核心
     */
    TECHNIC = 4,
}

 
 
/**
 * GM命令页签
 */
export enum GMTabID {
    /**
     * 通用
     */
    COMMON = 0,
    /**
     * 战斗
     */
    BATTLE = 1,
}

 
 
/**
 * 道具的使用效果
 */
export enum ItemEffectType {
    /**
     * 无效果
     */
    NONE = 0,
    /**
     * 使用后消耗道具，并掉落相应的ID的随机道具，参数1为掉落ID
     */
    DROP = 1,
    /**
     * 使用后消耗道具，并掉落相应数量的金币，参数1为每个1道具对应多少个金币
     */
    GEN_GOLD = 2,
    /**
     * 使用后消耗道具，并掉落相应数量的钻石，参数1为每个1道具对应多少个钻石
     */
    GEN_DIAMOND = 3,
    /**
     * 使用后消耗道具，并掉落相应数量的经验，参数1为每个1道具对应多少经验值
     */
    GEN_XP = 4,
    /**
     * 使用后消耗道具，并掉落相应数量的体力，参数1为每个1道具对应多少体力值
     */
    GEN_ENERGY = 5,
    /**
     * 使用后消耗道具，并掉落相应数量的另一个道具，参数1为新道具ID，参数2为每个1道具对应多少个新道具
     */
    GEN_ITEM = 6,
}

 
 
/**
 * 道具的使用类型
 */
export enum ItemUseType {
    /**
     * 不可直接从背包来使用的道具
     */
    NONE = 0,
    /**
     * 先放入背包内，然后由玩家从背包手动选择后使用
     */
    MANUAL = 1,
    /**
     * 当进入背包时立刻触发使用，并产生效果，一般不直接占用背包格子
     */
    AUTO = 2,
}

 
 
/**
 * 模式类型
 */
export enum ModeType {
    /**
     * 无尽
     */
    ENDLESS = 0,
    /**
     * 剧情
     */
    STORY = 1,
    /**
     * 远征
     */
    EXPEDITION = 2,
    /**
     * 无尽PK
     */
    ENDLESSPK = 3,
    /**
     * 好友PK
     */
    FRIENDPK = 4,
}

 
 
/**
 * 货币类型
 */
export enum MoneyType {
    /**
     * 无效值
     */
    NONE = 0,
    /**
     * 金币
     */
    GOLD = 1,
    /**
     * 钻石
     */
    DIAMOND = 2,
    /**
     * 体力
     */
    POWER = 3,
    /**
     * 道具
     */
    ITEM = 4,
}

 
 
/**
 * 模式类型
 */
export enum PlayCycle {
    /**
     * 每日
     */
    DAY = 0,
    /**
     * 每周
     */
    WEEK = 1,
}

 
 
/**
 * 装备属性名称
 */
export enum PropName {
    /**
     * 无效值
     */
    NONE = 0,
    /**
     * 攻击力
     */
    HURT = 1,
    /**
     * 生命值
     */
    HP = 2,
}

 
 
/**
 * 普通(Common)、精良(Uncommon)、稀有(Rare)、史诗(Epic)、传说(Legendary)、神话(Mythic)
 */
export enum QualityType {
    /**
     * 无效值
     */
    NONE = 0,
    /**
     * 普通
     */
    COMMON = 1,
    /**
     * 精良
     */
    UNCOMMON = 2,
    /**
     * 稀有
     */
    RACE = 3,
    /**
     * 史诗
     */
    EPIC = 4,
    /**
     * 传说
     */
    LEGENDARY = 5,
    /**
     * 神话
     */
    MYTHIC = 6,
}

 
 
export enum ResCondType {
    /**
     * 可以不填，默认无条件限制
     */
    NONE = 0,
    /**
     * 等级达到指定值
     */
    LEVEL = 1,
    /**
     * 关卡
     */
    STAGE = 2,
    /**
     * 星级
     */
    STAR = 3,
    /**
     * 战力
     */
    FORCE = 4,
    /**
     * 指定 ID 的活动在开放时间段内
     */
    ACTIVITY = 5,
}

 
 
export enum ResGoalType {
    /**
     * 通过某模型大类达到指定次数/模式ID/次数
     */
    MODE_PASS_TIMES = 1,
    /**
     * 通过某指定关卡达到指定次数/关卡ID/次数
     */
    STAGE_PASS_TIMES = 2,
    /**
     * 消灭指定类型的怪物数量达到目标/怪物类型/数量
     */
    KILL_MONSTER_CLASS_COUNT = 3,
    /**
     * 商城指定抽奖操作达到指定次数/抽奖ID/次数
     */
    LOTTERY_TIMES = 4,
    /**
     * 领取挂机奖励达到指定次数/次数
     */
    AFK_TIMES = 5,
    /**
     * 充值达到指定金额/金额
     */
    CHARGE_AMOUNT = 6,
    /**
     * 购买指定商品（包括体力等）达到指定数量/商品ID/数量
     */
    BUY_ITEM_COUNT = 7,
    /**
     * 看广告达到指定次数/次数
     */
    WATCH_AD_TIMES = 8,
    /**
     * 累计登录达到指定天数/天数
     */
    LOGIN_DAYS = 9,
    /**
     * 玩家达到指定等级
     */
    ROLE_LEVEL = 10,
    /**
     * 消耗金币达到指定数额
     */
    CONSUME_GOLD = 11,
    /**
     * 消耗钻石达到指定数额
     */
    CONSUME_DIAMOND = 12,
    /**
     * 消耗体力达到指定数额
     */
    CONSUME_ENERGY = 13,
    /**
     * 消耗道具达到指定数额
     */
    CONSUME_ITEM = 14,
    /**
     * 拥有指定品质的装备达到指定数量
     */
    EQUIP_QUALITY = 15,
    /**
     * 拥有指定等级的装备达到指定数量
     */
    EQUIP_LEVEL = 16,
    /**
     * 执行装备合成达到指定次数
     */
    EQUIP_COMB_TIMES = 17,
    /**
     * 执行装备升级达到指定次数
     */
    EQUIP_UPGRADE_TIMES = 18,
    /**
     * 公会捐献达到指定次数
     */
    GUILD_DONATE_TIMES = 19,
    /**
     * 拥有战机（已解锁）达到指定数量
     */
    FIGHTER_UNLOCK_COUNT = 20,
    /**
     * 所有已解锁战机星级累计到指定数量
     */
    FIGHTER_STAR_TOTAL = 21,
    /**
     * 角色战力达到指定数值
     */
    ROLE_FORCE = 22,
    /**
     * 消耗指定金额的货币获得奖励
     */
    SPECIFY_BUG_CONSUME = 23,
    /**
     * 使用特定道具达到指定次数
     */
    USE_ITEM_TIMES = 24,
    /**
     * 执行签到操作，配置时参数1配置为签到的第几天(1-7，以接受任务的时间算起，当天第1天)
     */
    DAILY_SIGN_IN = 25,
    /**
     * 参与某模型大类达到指定次数/模式ID/次数
     */
    MODE_ENTER_TIMES = 26,
    /**
     * 参与某指定关卡达到指定次数/关卡ID/次数
     */
    STAGE_ENTER_TIMES = 27,
    /**
     * 任务达成后的累计轨道值达到指定数值/轨道ID/数值
     */
    ORBIT_VALUE = 28,
}

 
 
export enum ResLootType {
    /**
     * 单个
     */
    SINGLE = 1,
    /**
     * 多个
     */
    MULTI = 2,
}

 
 
export enum ResPeriodType {
    /**
     * 单次完成，不重置， 默认，可不填
     */
    SINGLE = 0,
    /**
     * 每日 0时重置
     */
    DAILY = 1,
    /**
     * 每周 周一0时重置
     */
    WEEKLY = 2,
    /**
     * 每月 1日0时重置
     */
    MONTHLY = 3,
}

 
 
export enum ResTaskClass {
    /**
     * 默认，通过 ID 来识别
     */
    NONE = 0,
    /**
     * 日常任务
     */
    DAILY_TASK = 1,
    /**
     * 周常任务
     */
    WEEKLY_TASK = 2,
    /**
     * 月常任务
     */
    MONTHLY_TASK = 3,
    /**
     * 挑战任务
     */
    CHALLENGE = 4,
    /**
     * 活动
     */
    ACTIVITY = 5,
    /**
     * 任务轨道
     */
    TASK_ORBIT = 8,
    /**
     * 成就
     */
    ACHIEVEMENT = 9,
}

 
 
/**
 * 技能/buff条件
 */
export enum SkillConditionType {
    /**
     * 无条件
     */
    NONE = 0,
}

 
 
/**
 * 目标类型
 */
export enum TargetType {
    /**
     * 自己
     */
    Self = 0,
    /**
     * 自机
     */
    Main = 1,
    /**
     * 玩家方
     */
    MainFriendly = 2,
    /**
     * 全部敌方
     */
    Enemy = 3,
    /**
     * Boss敌方
     */
    BossEnemy = 4,
    /**
     * 普通敌方
     */
    NormalEnemy = 5,
}

 





export class ApplyBuff {

    constructor(_json_: any) {
        if (_json_.target === undefined) { throw new Error() }
        this.target = _json_.target
        if (_json_.buffID === undefined) { throw new Error() }
        this.buffID = _json_.buffID
    }

    readonly target: TargetType
    readonly buffID: number

    resolve(tables:Tables) {
        
        
    }
}




export namespace builtin {
export class vector2 {

    constructor(_json_: any) {
        if (_json_.x === undefined) { throw new Error() }
        this.x = _json_.x
        if (_json_.y === undefined) { throw new Error() }
        this.y = _json_.y
    }

    readonly x: number
    readonly y: number

    resolve(tables:Tables) {
        
        
    }
}

}


export namespace builtin {
export class vector3 {

    constructor(_json_: any) {
        if (_json_.x === undefined) { throw new Error() }
        this.x = _json_.x
        if (_json_.y === undefined) { throw new Error() }
        this.y = _json_.y
        if (_json_.z === undefined) { throw new Error() }
        this.z = _json_.z
    }

    readonly x: number
    readonly y: number
    readonly z: number

    resolve(tables:Tables) {
        
        
        
    }
}

}


export namespace builtin {
export class vector4 {

    constructor(_json_: any) {
        if (_json_.x === undefined) { throw new Error() }
        this.x = _json_.x
        if (_json_.y === undefined) { throw new Error() }
        this.y = _json_.y
        if (_json_.z === undefined) { throw new Error() }
        this.z = _json_.z
        if (_json_.w === undefined) { throw new Error() }
        this.w = _json_.w
    }

    readonly x: number
    readonly y: number
    readonly z: number
    readonly w: number

    resolve(tables:Tables) {
        
        
        
        
    }
}

}



export class ConParam {

    constructor(_json_: any) {
        if (_json_.con === undefined) { throw new Error() }
        this.con = _json_.con
        if (_json_.param === undefined) { throw new Error() }
        this.param = _json_.param
    }

    readonly con: number
    readonly param: number

    resolve(tables:Tables) {
        
        
    }
}





/**
 * 消耗的材料
 */
export class ConsumeItem {

    constructor(_json_: any) {
        if (_json_.id === undefined) { throw new Error() }
        this.id = _json_.id
        if (_json_.num === undefined) { throw new Error() }
        this.num = _json_.num
    }

    readonly id: number
    readonly num: number

    resolve(tables:Tables) {
        
        
    }
}





export class ConsumeMoney {

    constructor(_json_: any) {
        if (_json_.type === undefined) { throw new Error() }
        this.type = _json_.type
        if (_json_.num === undefined) { throw new Error() }
        this.num = _json_.num
    }

    /**
     * 货币类型
     */
    readonly type: MoneyType
    /**
     * 货币数量
     */
    readonly num: number

    resolve(tables:Tables) {
        
        
    }
}





export class EffectParam {

    constructor(_json_: any) {
        if (_json_.type === undefined) { throw new Error() }
        this.type = _json_.type
        if (_json_.target === undefined) { throw new Error() }
        this.target = _json_.target
        if (_json_.param === undefined) { throw new Error() }
        { this.param = []; for(let _ele0 of _json_.param) { let _e0; _e0 = _ele0; this.param.push(_e0);}}
    }

    readonly type: EffectType
    readonly target: TargetType
    readonly param: number[]

    resolve(tables:Tables) {
        
        
        
    }
}





/**
 * 装备属性
 */
export class EquipProp {

    constructor(_json_: any) {
        if (_json_.id === undefined) { throw new Error() }
        this.id = _json_.id
        if (_json_.value === undefined) { throw new Error() }
        this.value = _json_.value
    }

    readonly id: PropName
    readonly value: number

    resolve(tables:Tables) {
        
        
    }
}





export class GM {

    constructor(_json_: any) {
        if (_json_.tabID === undefined) { throw new Error() }
        this.tabID = _json_.tabID
        if (_json_.tabName === undefined) { throw new Error() }
        this.tabName = _json_.tabName
        if (_json_.name === undefined) { throw new Error() }
        this.name = _json_.name
        if (_json_.cmd === undefined) { throw new Error() }
        this.cmd = _json_.cmd
        if (_json_.desc === undefined) { throw new Error() }
        this.desc = _json_.desc
    }

    /**
     * 页签ID
     */
    readonly tabID: GMTabID
    /**
     * 页签名称
     */
    readonly tabName: string
    /**
     * 按钮名称
     */
    readonly name: string
    /**
     * 命令
     */
    readonly cmd: string
    /**
     * 描述
     */
    readonly desc: string

    resolve(tables:Tables) {
        
        
        
        
        
    }
}





/**
 * 战机效果
 */
export class PlaneEffect {

    constructor(_json_: any) {
        if (_json_.effect_id === undefined) { throw new Error() }
        this.effectId = _json_.effect_id
    }

    readonly effectId: number

    resolve(tables:Tables) {
        
    }
}





/**
 * 升星材料
 */
export class PlaneMaterial {

    constructor(_json_: any) {
        if (_json_.material_id === undefined) { throw new Error() }
        this.materialId = _json_.material_id
        if (_json_.material_count === undefined) { throw new Error() }
        this.materialCount = _json_.material_count
    }

    readonly materialId: number
    readonly materialCount: number

    resolve(tables:Tables) {
        
        
    }
}





/**
 * 战机属性
 */
export class PlaneProperty {

    constructor(_json_: any) {
        if (_json_.MaxHP === undefined) { throw new Error() }
        this.MaxHP = _json_.MaxHP
        if (_json_.HPRecovery === undefined) { throw new Error() }
        this.HPRecovery = _json_.HPRecovery
        if (_json_.Attack === undefined) { throw new Error() }
        this.Attack = _json_.Attack
        if (_json_.Fortunate === undefined) { throw new Error() }
        this.Fortunate = _json_.Fortunate
        if (_json_.Miss === undefined) { throw new Error() }
        this.Miss = _json_.Miss
        if (_json_.BulletHurtResistance === undefined) { throw new Error() }
        this.BulletHurtResistance = _json_.BulletHurtResistance
        if (_json_.CollisionHurtResistance === undefined) { throw new Error() }
        this.CollisionHurtResistance = _json_.CollisionHurtResistance
        if (_json_.PickRadius === undefined) { throw new Error() }
        this.PickRadius = _json_.PickRadius
        if (_json_.FinalScore === undefined) { throw new Error() }
        this.FinalScore = _json_.FinalScore
        if (_json_.BombMax === undefined) { throw new Error() }
        this.BombMax = _json_.BombMax
        if (_json_.MaxEnergy === undefined) { throw new Error() }
        this.MaxEnergy = _json_.MaxEnergy
        if (_json_.EnergyRecovery === undefined) { throw new Error() }
        this.EnergyRecovery = _json_.EnergyRecovery
    }

    readonly MaxHP: number
    readonly HPRecovery: number
    readonly Attack: number
    readonly Fortunate: number
    readonly Miss: number
    readonly BulletHurtResistance: number
    readonly CollisionHurtResistance: number
    readonly PickRadius: number
    readonly FinalScore: number
    readonly BombMax: number
    readonly MaxEnergy: number
    readonly EnergyRecovery: number

    resolve(tables:Tables) {
        
        
        
        
        
        
        
        
        
        
        
        
    }
}





/**
 * 战机属性
 */
export class PlanePropertyElem {

    constructor(_json_: any) {
        if (_json_.prop_type === undefined) { throw new Error() }
        this.propType = _json_.prop_type
        if (_json_.prop_param === undefined) { throw new Error() }
        { this.propParam = []; for(let _ele0 of _json_.prop_param) { let _e0; _e0 = _ele0; this.propParam.push(_e0);}}
    }

    readonly propType: EffectType
    readonly propParam: number[]

    resolve(tables:Tables) {
        
        
    }
}





/**
 * 属性增幅
 */
export class PropInc {

    constructor(_json_: any) {
        if (_json_.id === undefined) { throw new Error() }
        this.id = _json_.id
        if (_json_.inc === undefined) { throw new Error() }
        this.inc = _json_.inc
    }

    readonly id: PropName
    /**
     * 万分比
     */
    readonly inc: number

    resolve(tables:Tables) {
        
        
    }
}





/**
 * 随机策略
 */
export class randStrategy {

    constructor(_json_: any) {
        if (_json_.ID === undefined) { throw new Error() }
        this.ID = _json_.ID
        if (_json_.Weight === undefined) { throw new Error() }
        this.Weight = _json_.Weight
    }

    /**
     * 随机策略ID
     */
    readonly ID: number
    /**
     * ID的权重
     */
    readonly Weight: number

    resolve(tables:Tables) {
        
        
    }
}





export class RatingParam {

    constructor(_json_: any) {
        if (_json_.rating === undefined) { throw new Error() }
        this.rating = _json_.rating
        if (_json_.param === undefined) { throw new Error() }
        this.param = _json_.param
    }

    readonly rating: number
    readonly param: number

    resolve(tables:Tables) {
        
        
    }
}





export class ResAchievement {

    constructor(_json_: any) {
        if (_json_.task_id === undefined) { throw new Error() }
        this.taskId = _json_.task_id
        if (_json_.group_id === undefined) { throw new Error() }
        this.groupId = _json_.group_id
        if (_json_.prev_id === undefined) { throw new Error() }
        this.prevId = _json_.prev_id
        if (_json_.period_type === undefined) { throw new Error() }
        this.periodType = _json_.period_type
        if (_json_.task_cond === undefined) { throw new Error() }
        { this.taskCond = []; for(let _ele0 of _json_.task_cond) { let _e0; _e0 = new ResCondition(_ele0); this.taskCond.push(_e0);}}
        if (_json_.task_goal === undefined) { throw new Error() }
        this.taskGoal = new ResTaskGoal(_json_.task_goal)
        if (_json_.accumulate === undefined) { throw new Error() }
        this.accumulate = _json_.accumulate
        if (_json_.reward_id === undefined) { throw new Error() }
        this.rewardId = _json_.reward_id
        if (_json_.link_to === undefined) { throw new Error() }
        this.linkTo = _json_.link_to
    }

    /**
     * 成就 ID
     */
    readonly taskId: number
    /**
     * 成就集 ID
     */
    readonly groupId: number
    /**
     * 前置ID
     */
    readonly prevId: number
    /**
     * 重置周期
     */
    readonly periodType: ResPeriodType
    readonly taskCond: ResCondition[]
    readonly taskGoal: ResTaskGoal
    /**
     * 是否累积
     */
    readonly accumulate: boolean
    /**
     * 奖励ID
     */
    readonly rewardId: number
    /**
     * 追踪
     */
    readonly linkTo: string

    resolve(tables:Tables) {
        
        
        
        
        for (let _e of this.taskCond) { _e?.resolve(tables); }
        this.taskGoal?.resolve(tables);
        
        
        
    }
}





export class ResActivity {

    constructor(_json_: any) {
        if (_json_.task_id === undefined) { throw new Error() }
        this.taskId = _json_.task_id
        if (_json_.group_id === undefined) { throw new Error() }
        this.groupId = _json_.group_id
        if (_json_.desc === undefined) { throw new Error() }
        this.desc = _json_.desc
        if (_json_.ui_res === undefined) { throw new Error() }
        this.uiRes = _json_.ui_res
        if (_json_.precv_id === undefined) { throw new Error() }
        this.precvId = _json_.precv_id
        if (_json_.period_type === undefined) { throw new Error() }
        this.periodType = _json_.period_type
        if (_json_.task_cond === undefined) { throw new Error() }
        { this.taskCond = []; for(let _ele0 of _json_.task_cond) { let _e0; _e0 = new ResCondition(_ele0); this.taskCond.push(_e0);}}
        if (_json_.task_goal === undefined) { throw new Error() }
        this.taskGoal = new ResTaskGoal(_json_.task_goal)
        if (_json_.accumlate === undefined) { throw new Error() }
        this.accumlate = _json_.accumlate
        if (_json_.reward_id === undefined) { throw new Error() }
        this.rewardId = _json_.reward_id
        if (_json_.open_date === undefined) { throw new Error() }
        this.openDate = _json_.open_date
        if (_json_.open_time === undefined) { throw new Error() }
        this.openTime = _json_.open_time
        if (_json_.close_date === undefined) { throw new Error() }
        this.closeDate = _json_.close_date
        if (_json_.close_time === undefined) { throw new Error() }
        this.closeTime = _json_.close_time
        if (_json_.link_to === undefined) { throw new Error() }
        this.linkTo = _json_.link_to
    }

    /**
     * 活动ID
     */
    readonly taskId: number
    /**
     * 活动集ID
     */
    readonly groupId: number
    /**
     * 说明
     */
    readonly desc: string
    /**
     * UI资源配置
     */
    readonly uiRes: string
    /**
     * 前置ID
     */
    readonly precvId: number
    /**
     * 重置周期
     */
    readonly periodType: ResPeriodType
    /**
     * 条件类型
     */
    readonly taskCond: ResCondition[]
    readonly taskGoal: ResTaskGoal
    /**
     * 目标是否累积
     */
    readonly accumlate: boolean
    /**
     * 奖励ID
     */
    readonly rewardId: number
    /**
     * 开放日期
     */
    readonly openDate: string
    /**
     * 开放时间
     */
    readonly openTime: string
    /**
     * 结束日期
     */
    readonly closeDate: string
    /**
     * 结束时间
     */
    readonly closeTime: string
    /**
     * 活动追踪
     */
    readonly linkTo: string

    resolve(tables:Tables) {
        
        
        
        
        
        
        for (let _e of this.taskCond) { _e?.resolve(tables); }
        this.taskGoal?.resolve(tables);
        
        
        
        
        
        
        
    }
}





export class ResBuffer {

    constructor(_json_: any) {
        if (_json_.id === undefined) { throw new Error() }
        this.id = _json_.id
        if (_json_.buffType === undefined) { throw new Error() }
        this.buffType = _json_.buffType
        if (_json_.conditionID === undefined) { throw new Error() }
        this.conditionID = _json_.conditionID
        if (_json_.duration === undefined) { throw new Error() }
        this.duration = _json_.duration
        if (_json_.durationBonus === undefined) { throw new Error() }
        this.durationBonus = _json_.durationBonus
        if (_json_.maxStack === undefined) { throw new Error() }
        this.maxStack = _json_.maxStack
        if (_json_.refreshType === undefined) { throw new Error() }
        this.refreshType = _json_.refreshType
        if (_json_.cycle === undefined) { throw new Error() }
        this.cycle = _json_.cycle
        if (_json_.cycleTimes === undefined) { throw new Error() }
        this.cycleTimes = _json_.cycleTimes
        if (_json_.EffectPath === undefined) { throw new Error() }
        this.EffectPath = _json_.EffectPath
        if (_json_.EffectSocket === undefined) { throw new Error() }
        this.EffectSocket = _json_.EffectSocket
        if (_json_.EffectPos === undefined) { throw new Error() }
        this.EffectPos = new builtin.vector2(_json_.EffectPos)
        if (_json_.effects === undefined) { throw new Error() }
        { this.effects = []; for(let _ele0 of _json_.effects) { let _e0; _e0 = new EffectParam(_ele0); this.effects.push(_e0);}}
    }

    /**
     * ID
     */
    readonly id: number
    /**
     * 类别
     */
    readonly buffType: BuffType
    /**
     * Buff触发条件
     */
    readonly conditionID: number
    /**
     * 持续时间
     */
    readonly duration: number
    /**
     * 持续时间加成
     */
    readonly durationBonus: number
    /**
     * 最大叠加次数
     */
    readonly maxStack: number
    /**
     * 叠加刷新策略
     */
    readonly refreshType: boolean
    /**
     * 周期
     */
    readonly cycle: number
    /**
     * 周期计数
     */
    readonly cycleTimes: number
    /**
     * 特效Prefab
     */
    readonly EffectPath: string
    /**
     * 特效挂点
     */
    readonly EffectSocket: BindSocket
    /**
     * 特效偏移
     */
    readonly EffectPos: builtin.vector2
    readonly effects: EffectParam[]

    resolve(tables:Tables) {
        
        
        
        
        
        
        
        
        
        
        
        
        
    }
}





export class ResBullet {

    constructor(_json_: any) {
        if (_json_.id === undefined) { throw new Error() }
        this.id = _json_.id
        if (_json_.name === undefined) { throw new Error() }
        this.name = _json_.name
        if (_json_.source === undefined) { throw new Error() }
        this.source = _json_.source
        if (_json_.type === undefined) { throw new Error() }
        this.type = _json_.type
        if (_json_.damage_type === undefined) { throw new Error() }
        this.damageType = _json_.damage_type
        if (_json_.prefab === undefined) { throw new Error() }
        this.prefab = _json_.prefab
        if (_json_.attack_coefficient === undefined) { throw new Error() }
        this.attackCoefficient = _json_.attack_coefficient
        if (_json_.penetration_count === undefined) { throw new Error() }
        this.penetrationCount = _json_.penetration_count
        if (_json_.penetration_cooldown === undefined) { throw new Error() }
        this.penetrationCooldown = _json_.penetration_cooldown
        if (_json_.hit_bounce_count === undefined) { throw new Error() }
        this.hitBounceCount = _json_.hit_bounce_count
        if (_json_.boundary_bounce_count === undefined) { throw new Error() }
        this.boundaryBounceCount = _json_.boundary_bounce_count
    }

    /**
     * ID
     */
    readonly id: number
    /**
     * 名称
     */
    readonly name: string
    /**
     * 子弹来源
     */
    readonly source: BulletSourceType
    /**
     * 子弹类型
     */
    readonly type: BulletType
    /**
     * 子弹伤害类型
     */
    readonly damageType: DamageType
    /**
     * 子弹Prefab
     */
    readonly prefab: string
    /**
     * 攻击转换系数%
     */
    readonly attackCoefficient: number
    /**
     * 穿透次数
     */
    readonly penetrationCount: number
    /**
     * 穿透伤害冷却
     */
    readonly penetrationCooldown: number
    /**
     * 命中反弹次数
     */
    readonly hitBounceCount: number
    /**
     * 屏幕反弹次数
     */
    readonly boundaryBounceCount: number

    resolve(tables:Tables) {
        
        
        
        
        
        
        
        
        
        
        
    }
}





export class ResChapter {

    constructor(_json_: any) {
        if (_json_.id === undefined) { throw new Error() }
        this.id = _json_.id
        if (_json_.levelCount === undefined) { throw new Error() }
        this.levelCount = _json_.levelCount
        if (_json_.levelGroupCount === undefined) { throw new Error() }
        this.levelGroupCount = _json_.levelGroupCount
        if (_json_.strategy === undefined) { throw new Error() }
        this.strategy = _json_.strategy
        if (_json_.damageBonus === undefined) { throw new Error() }
        this.damageBonus = _json_.damageBonus
        if (_json_.lifeBounus === undefined) { throw new Error() }
        this.lifeBounus = _json_.lifeBounus
        if (_json_.strategyList === undefined) { throw new Error() }
        { this.strategyList = []; for(let _ele0 of _json_.strategyList) { let _e0; _e0 = new randStrategy(_ele0); this.strategyList.push(_e0);}}
    }

    /**
     * 章节ID
     */
    readonly id: number
    /**
     * 章节关卡数量
     */
    readonly levelCount: number
    /**
     * 章节关卡组数量
     */
    readonly levelGroupCount: number
    /**
     * 1=随机<br/>2=随机不重复<br/>3=顺序重复
     */
    readonly strategy: number
    /**
     * 章节伤害加成
     */
    readonly damageBonus: number
    /**
     * 章节生命加成
     */
    readonly lifeBounus: number
    readonly strategyList: randStrategy[]

    resolve(tables:Tables) {
        
        
        
        
        
        
        for (let _e of this.strategyList) { _e?.resolve(tables); }
    }
}





export class ResCondition {

    constructor(_json_: any) {
        if (_json_.cond_type === undefined) { throw new Error() }
        this.condType = _json_.cond_type
        if (_json_.params === undefined) { throw new Error() }
        { this.params = []; for(let _ele0 of _json_.params) { let _e0; _e0 = _ele0; this.params.push(_e0);}}
    }

    readonly condType: ResCondType
    readonly params: number[]

    resolve(tables:Tables) {
        
        
    }
}





export class ResEffect {

    constructor(_json_: any) {
        if (_json_.id === undefined) { throw new Error() }
        this.id = _json_.id
        if (_json_.name === undefined) { throw new Error() }
        this.name = _json_.name
        if (_json_.description === undefined) { throw new Error() }
        this.description = _json_.description
        if (_json_.icon === undefined) { throw new Error() }
        this.icon = _json_.icon
        if (_json_.effect_type === undefined) { throw new Error() }
        this.effectType = _json_.effect_type
        if (_json_.effect_value === undefined) { throw new Error() }
        this.effectValue = _json_.effect_value
        if (_json_.effect_params === undefined) { throw new Error() }
        this.effectParams = _json_.effect_params
    }

    /**
     * 效果ID
     */
    readonly id: number
    /**
     * 效果名称
     */
    readonly name: string
    /**
     * 效果描述
     */
    readonly description: string
    /**
     * 效果图标
     */
    readonly icon: string
    /**
     * 效果类型
     */
    readonly effectType: number
    /**
     * 效果数值
     */
    readonly effectValue: number
    /**
     * 效果参数
     */
    readonly effectParams: string

    resolve(tables:Tables) {
        
        
        
        
        
        
        
    }
}





export class ResEnemy {

    constructor(_json_: any) {
        if (_json_.id === undefined) { throw new Error() }
        this.id = _json_.id
        if (_json_.name === undefined) { throw new Error() }
        this.name = _json_.name
        if (_json_.comment === undefined) { throw new Error() }
        this.comment = _json_.comment
        if (_json_.prefab === undefined) { throw new Error() }
        this.prefab = _json_.prefab
        if (_json_.rank === undefined) { throw new Error() }
        this.rank = _json_.rank
        if (_json_.purpose === undefined) { throw new Error() }
        this.purpose = _json_.purpose
        if (_json_.base_hp === undefined) { throw new Error() }
        this.baseHp = _json_.base_hp
        if (_json_.base_atk === undefined) { throw new Error() }
        this.baseAtk = _json_.base_atk
        if (_json_.move_speed === undefined) { throw new Error() }
        this.moveSpeed = _json_.move_speed
        if (_json_.kill_score === undefined) { throw new Error() }
        this.killScore = _json_.kill_score
        if (_json_.show_hp_bar === undefined) { throw new Error() }
        this.showHpBar = _json_.show_hp_bar
        if (_json_.use_hit_count === undefined) { throw new Error() }
        this.useHitCount = _json_.use_hit_count
        if (_json_.hit_count_to_kill === undefined) { throw new Error() }
        this.hitCountToKill = _json_.hit_count_to_kill
        if (_json_.hit_count_interval === undefined) { throw new Error() }
        this.hitCountInterval = _json_.hit_count_interval
        if (_json_.target_priority === undefined) { throw new Error() }
        this.targetPriority = _json_.target_priority
        if (_json_.immune_bullet_damage === undefined) { throw new Error() }
        this.immuneBulletDamage = _json_.immune_bullet_damage
        if (_json_.immune_collide_damage === undefined) { throw new Error() }
        this.immuneCollideDamage = _json_.immune_collide_damage
        if (_json_.ignore_bullet === undefined) { throw new Error() }
        this.ignoreBullet = _json_.ignore_bullet
        if (_json_.ignore_collide === undefined) { throw new Error() }
        this.ignoreCollide = _json_.ignore_collide
        if (_json_.immune_nuke === undefined) { throw new Error() }
        this.immuneNuke = _json_.immune_nuke
        if (_json_.immune_active_skill === undefined) { throw new Error() }
        this.immuneActiveSkill = _json_.immune_active_skill
        if (_json_.invincible === undefined) { throw new Error() }
        this.invincible = _json_.invincible
        if (_json_.collide_level === undefined) { throw new Error() }
        this.collideLevel = _json_.collide_level
        if (_json_.collide_damage === undefined) { throw new Error() }
        this.collideDamage = _json_.collide_damage
        if (_json_.drop_radius === undefined) { throw new Error() }
        this.dropRadius = _json_.drop_radius
    }

    /**
     * 单位ID
     */
    readonly id: number
    /**
     * 单位名称
     */
    readonly name: string
    /**
     * 备注
     */
    readonly comment: string
    /**
     * 单位Prefab
     */
    readonly prefab: string
    /**
     * 单位级别
     */
    readonly rank: number
    /**
     * 单位用途
     */
    readonly purpose: string
    /**
     * 基础生命值
     */
    readonly baseHp: number
    /**
     * 基础攻击力
     */
    readonly baseAtk: number
    /**
     * 移动速度
     */
    readonly moveSpeed: number
    /**
     * 击杀得分
     */
    readonly killScore: number
    /**
     * 是否显示血条
     */
    readonly showHpBar: boolean
    /**
     * 是否开启受击次数
     */
    readonly useHitCount: boolean
    /**
     * 受击次数
     */
    readonly hitCountToKill: number
    /**
     * 受击间隔
     */
    readonly hitCountInterval: number
    /**
     * 选中级别
     */
    readonly targetPriority: number
    /**
     * 免疫子弹伤害
     */
    readonly immuneBulletDamage: boolean
    /**
     * 免疫撞击伤害
     */
    readonly immuneCollideDamage: boolean
    /**
     * 无视子弹
     */
    readonly ignoreBullet: boolean
    /**
     * 无视撞击
     */
    readonly ignoreCollide: boolean
    /**
     * 是否免疫核弹
     */
    readonly immuneNuke: boolean
    /**
     * 是否免疫主动技能
     */
    readonly immuneActiveSkill: boolean
    /**
     * 是否无敌
     */
    readonly invincible: boolean
    /**
     * 相撞等级
     */
    readonly collideLevel: number
    /**
     * 相撞伤害
     */
    readonly collideDamage: number
    /**
     * 掉落半径
     */
    readonly dropRadius: number

    resolve(tables:Tables) {
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
    }
}





export class ResEquip {

    constructor(_json_: any) {
        if (_json_.id === undefined) { throw new Error() }
        this.id = _json_.id
        if (_json_.name === undefined) { throw new Error() }
        this.name = _json_.name
        if (_json_.icon === undefined) { throw new Error() }
        this.icon = _json_.icon
        if (_json_.quality === undefined) { throw new Error() }
        this.quality = _json_.quality
        if (_json_.quality_sub === undefined) { throw new Error() }
        this.qualitySub = _json_.quality_sub
        if (_json_.equip_class === undefined) { throw new Error() }
        this.equipClass = _json_.equip_class
        if (_json_.props === undefined) { throw new Error() }
        { this.props = []; for(let _ele0 of _json_.props) { let _e0; _e0 = new EquipProp(_ele0); this.props.push(_e0);}}
        if (_json_.consume_items === undefined) { throw new Error() }
        { this.consumeItems = []; for(let _ele0 of _json_.consume_items) { let _e0; _e0 = new ConsumeItem(_ele0); this.consumeItems.push(_e0);}}
    }

    /**
     * ID
     */
    readonly id: number
    /**
     * 名称
     */
    readonly name: string
    /**
     * 图标
     */
    readonly icon: string
    /**
     * 品质
     */
    readonly quality: QualityType
    /**
     * 品质子等级
     */
    readonly qualitySub: number
    /**
     * 装备部位
     */
    readonly equipClass: EquipClass
    /**
     * 属性
     */
    readonly props: EquipProp[]
    /**
     * 消耗材料
     */
    readonly consumeItems: ConsumeItem[]

    resolve(tables:Tables) {
        
        
        
        
        
        
        for (let _e of this.props) { _e?.resolve(tables); }
        for (let _e of this.consumeItems) { _e?.resolve(tables); }
    }
}





export class ResEquipUpgrade {

    constructor(_json_: any) {
        if (_json_.equip_class === undefined) { throw new Error() }
        this.equipClass = _json_.equip_class
        if (_json_.level_from === undefined) { throw new Error() }
        this.levelFrom = _json_.level_from
        if (_json_.level_to === undefined) { throw new Error() }
        this.levelTo = _json_.level_to
        if (_json_.prop_inc === undefined) { throw new Error() }
        { this.propInc = []; for(let _ele0 of _json_.prop_inc) { let _e0; _e0 = new PropInc(_ele0); this.propInc.push(_e0);}}
        if (_json_.consume_money === undefined) { throw new Error() }
        this.consumeMoney = new ConsumeMoney(_json_.consume_money)
        if (_json_.consume_items === undefined) { throw new Error() }
        { this.consumeItems = []; for(let _ele0 of _json_.consume_items) { let _e0; _e0 = new ConsumeItem(_ele0); this.consumeItems.push(_e0);}}
    }

    /**
     * 部位
     */
    readonly equipClass: EquipClass
    /**
     * 等级下限
     */
    readonly levelFrom: number
    /**
     * 等级上限
     */
    readonly levelTo: number
    /**
     * 属性增幅
     */
    readonly propInc: PropInc[]
    /**
     * 消耗货币
     */
    readonly consumeMoney: ConsumeMoney
    /**
     * 消耗材料
     */
    readonly consumeItems: ConsumeItem[]

    resolve(tables:Tables) {
        
        
        
        for (let _e of this.propInc) { _e?.resolve(tables); }
        this.consumeMoney?.resolve(tables);
        for (let _e of this.consumeItems) { _e?.resolve(tables); }
    }
}





export class ResGameMode {

    constructor(_json_: any) {
        if (_json_.ID === undefined) { throw new Error() }
        this.ID = _json_.ID
        if (_json_.modeType === undefined) { throw new Error() }
        this.modeType = _json_.modeType
        if (_json_.chapterID === undefined) { throw new Error() }
        this.chapterID = _json_.chapterID
        if (_json_.order === undefined) { throw new Error() }
        this.order = _json_.order
        if (_json_.resourceID === undefined) { throw new Error() }
        this.resourceID = _json_.resourceID
        if (_json_.description === undefined) { throw new Error() }
        this.description = _json_.description
        if (_json_.conList === undefined) { throw new Error() }
        { this.conList = []; for(let _ele0 of _json_.conList) { let _e0; _e0 = new ConParam(_ele0); this.conList.push(_e0);}}
        if (_json_.cycle === undefined) { throw new Error() }
        this.cycle = _json_.cycle
        if (_json_.times === undefined) { throw new Error() }
        this.times = _json_.times
        if (_json_.monType === undefined) { throw new Error() }
        this.monType = _json_.monType
        if (_json_.costParam1 === undefined) { throw new Error() }
        this.costParam1 = _json_.costParam1
        if (_json_.costParam2 === undefined) { throw new Error() }
        this.costParam2 = _json_.costParam2
        if (_json_.rebirthTimes === undefined) { throw new Error() }
        this.rebirthTimes = _json_.rebirthTimes
        if (_json_.rebirthCost === undefined) { throw new Error() }
        this.rebirthCost = _json_.rebirthCost
        if (_json_.power === undefined) { throw new Error() }
        this.power = _json_.power
        if (_json_.rogueID === undefined) { throw new Error() }
        this.rogueID = _json_.rogueID
        if (_json_.LevelLimit === undefined) { throw new Error() }
        this.LevelLimit = _json_.LevelLimit
        if (_json_.rogueFirst === undefined) { throw new Error() }
        this.rogueFirst = _json_.rogueFirst
        if (_json_.sweepLimit === undefined) { throw new Error() }
        this.sweepLimit = _json_.sweepLimit
        if (_json_.rewardID1 === undefined) { throw new Error() }
        this.rewardID1 = _json_.rewardID1
        if (_json_.rewardID2 === undefined) { throw new Error() }
        this.rewardID2 = _json_.rewardID2
        if (_json_.ratingList === undefined) { throw new Error() }
        { this.ratingList = []; for(let _ele0 of _json_.ratingList) { let _e0; _e0 = new RatingParam(_ele0); this.ratingList.push(_e0);}}
    }

    /**
     * ID
     */
    readonly ID: number
    /**
     * 模式类型
     */
    readonly modeType: ModeType
    /**
     * 章节ID
     */
    readonly chapterID: number
    /**
     * 排序
     */
    readonly order: number
    /**
     * 入口资源
     */
    readonly resourceID: number
    /**
     * 文本介绍
     */
    readonly description: string
    readonly conList: ConParam[]
    /**
     * 进入周期
     */
    readonly cycle: PlayCycle
    /**
     * 进入次数
     */
    readonly times: number
    /**
     * 消耗类型
     */
    readonly monType: MoneyType
    /**
     * 消耗参数1
     */
    readonly costParam1: number
    /**
     * 消耗参数2
     */
    readonly costParam2: number
    /**
     * 复活次数
     */
    readonly rebirthTimes: number
    /**
     * 复活消耗
     */
    readonly rebirthCost: number
    /**
     * 战力评估
     */
    readonly power: number
    /**
     * 肉鸽组
     */
    readonly rogueID: number
    /**
     * 局内等级上限
     */
    readonly LevelLimit: number
    /**
     * 初始肉鸽选择
     */
    readonly rogueFirst: number
    /**
     * 扫荡次数
     */
    readonly sweepLimit: number
    /**
     * 奖励ID1
     */
    readonly rewardID1: number
    /**
     * 奖励ID2
     */
    readonly rewardID2: number
    readonly ratingList: RatingParam[]

    resolve(tables:Tables) {
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
    }
}





export class ResGlobalAttr {

    constructor(_json_: any) {
        if (_json_.GoldProducion === undefined) { throw new Error() }
        this.GoldProducion = _json_.GoldProducion
        if (_json_.MaxEnergy === undefined) { throw new Error() }
        this.MaxEnergy = _json_.MaxEnergy
        if (_json_.EnergyRecoverInterval === undefined) { throw new Error() }
        this.EnergyRecoverInterval = _json_.EnergyRecoverInterval
        if (_json_.EnergyRecoverValue === undefined) { throw new Error() }
        this.EnergyRecoverValue = _json_.EnergyRecoverValue
        if (_json_.ItemPickUpRadius === undefined) { throw new Error() }
        this.ItemPickUpRadius = _json_.ItemPickUpRadius
        if (_json_.PostHitProtection === undefined) { throw new Error() }
        this.PostHitProtection = _json_.PostHitProtection
        if (_json_.CameraTranslationMaxMoveSpeed === undefined) { throw new Error() }
        this.CameraTranslationMaxMoveSpeed = _json_.CameraTranslationMaxMoveSpeed
        if (_json_.CameraTranslationMoveDelay === undefined) { throw new Error() }
        this.CameraTranslationMoveDelay = _json_.CameraTranslationMoveDelay
    }

    /**
     * 每回合发放的金币
     */
    readonly GoldProducion: number
    /**
     * 体力上限值
     */
    readonly MaxEnergy: number
    /**
     * 体力恢复的间隔时间
     */
    readonly EnergyRecoverInterval: number
    /**
     * 体力恢复的值
     */
    readonly EnergyRecoverValue: number
    /**
     * 局内道具拾取距离
     */
    readonly ItemPickUpRadius: number
    /**
     * 受击保护
     */
    readonly PostHitProtection: number
    /**
     * 镜头平移最大跟随速度
     */
    readonly CameraTranslationMaxMoveSpeed: number
    /**
     * 镜头平移启动延迟
     */
    readonly CameraTranslationMoveDelay: number

    resolve(tables:Tables) {
        
        
        
        
        
        
        
        
    }
}





export class ResItem {

    constructor(_json_: any) {
        if (_json_.id === undefined) { throw new Error() }
        this.id = _json_.id
        if (_json_.name === undefined) { throw new Error() }
        this.name = _json_.name
        if (_json_.icon === undefined) { throw new Error() }
        this.icon = _json_.icon
        if (_json_.quality === undefined) { throw new Error() }
        this.quality = _json_.quality
        if (_json_.quality_sub === undefined) { throw new Error() }
        this.qualitySub = _json_.quality_sub
        if (_json_.use_type === undefined) { throw new Error() }
        this.useType = _json_.use_type
        if (_json_.effect_id === undefined) { throw new Error() }
        this.effectId = _json_.effect_id
        if (_json_.effect_param1 === undefined) { throw new Error() }
        this.effectParam1 = _json_.effect_param1
        if (_json_.effect_param2 === undefined) { throw new Error() }
        this.effectParam2 = _json_.effect_param2
        if (_json_.max_stack_num === undefined) { throw new Error() }
        this.maxStackNum = _json_.max_stack_num
    }

    /**
     * ID
     */
    readonly id: number
    /**
     * 名称
     */
    readonly name: string
    /**
     * 图标
     */
    readonly icon: string
    /**
     * 品质
     */
    readonly quality: QualityType
    /**
     * 品质子等级
     */
    readonly qualitySub: number
    /**
     * 使用类型
     */
    readonly useType: ItemUseType
    /**
     * 效果类型
     */
    readonly effectId: ItemEffectType
    /**
     * 效果参数1
     */
    readonly effectParam1: number
    /**
     * 效果参数2
     */
    readonly effectParam2: number
    /**
     * 最大叠放数量
     */
    readonly maxStackNum: number

    resolve(tables:Tables) {
        
        
        
        
        
        
        
        
        
        
    }
}





export class ResLevel {

    constructor(_json_: any) {
        if (_json_.id === undefined) { throw new Error() }
        this.id = _json_.id
        if (_json_.prefab === undefined) { throw new Error() }
        this.prefab = _json_.prefab
        if (_json_.forbidFire === undefined) { throw new Error() }
        this.forbidFire = _json_.forbidFire
        if (_json_.forbidNBomb === undefined) { throw new Error() }
        this.forbidNBomb = _json_.forbidNBomb
        if (_json_.forbidActSkill === undefined) { throw new Error() }
        this.forbidActSkill = _json_.forbidActSkill
        if (_json_.planeCollisionScaling === undefined) { throw new Error() }
        this.planeCollisionScaling = _json_.planeCollisionScaling
        if (_json_.levelType === undefined) { throw new Error() }
        this.levelType = _json_.levelType
    }

    /**
     * 关卡id
     */
    readonly id: number
    /**
     * 关卡prefab
     */
    readonly prefab: string
    /**
     * 是、否（默认值）
     */
    readonly forbidFire: boolean
    /**
     * 是、否（默认值）
     */
    readonly forbidNBomb: boolean
    /**
     * 是、否（默认值）
     */
    readonly forbidActSkill: boolean
    /**
     * 0到1（1表示正常碰撞）
     */
    readonly planeCollisionScaling: number
    /**
     * 1=常规关卡<br/>2=机关关卡<br/>3=金币关卡<br/>4=火箭关卡<br/>99=Boss关
     */
    readonly levelType: number

    resolve(tables:Tables) {
        
        
        
        
        
        
        
    }
}





export class ResLevelGroup {

    constructor(_json_: any) {
        if (_json_.id === undefined) { throw new Error() }
        this.id = _json_.id
        if (_json_.normLevelCount === undefined) { throw new Error() }
        this.normLevelCount = _json_.normLevelCount
        if (_json_.normLevelST === undefined) { throw new Error() }
        this.normLevelST = _json_.normLevelST
        if (_json_.normSTList === undefined) { throw new Error() }
        { this.normSTList = []; for(let _ele0 of _json_.normSTList) { let _e0; _e0 = new randStrategy(_ele0); this.normSTList.push(_e0);}}
        if (_json_.bossLevelCount === undefined) { throw new Error() }
        this.bossLevelCount = _json_.bossLevelCount
        if (_json_.bossLevelST === undefined) { throw new Error() }
        this.bossLevelST = _json_.bossLevelST
        if (_json_.bossSTList === undefined) { throw new Error() }
        { this.bossSTList = []; for(let _ele0 of _json_.bossSTList) { let _e0; _e0 = new randStrategy(_ele0); this.bossSTList.push(_e0);}}
    }

    /**
     * 关卡组ID
     */
    readonly id: number
    /**
     * 常规关卡数量
     */
    readonly normLevelCount: number
    /**
     * 1=随机<br/>2=随机不重复<br/>3=顺序重复
     */
    readonly normLevelST: number
    readonly normSTList: randStrategy[]
    /**
     * BOSS关卡数量
     */
    readonly bossLevelCount: number
    /**
     * 1=随机<br/>2=随机不重复<br/>3=顺序重复
     */
    readonly bossLevelST: number
    readonly bossSTList: randStrategy[]

    resolve(tables:Tables) {
        
        
        
        for (let _e of this.normSTList) { _e?.resolve(tables); }
        
        
        for (let _e of this.bossSTList) { _e?.resolve(tables); }
    }
}





export class ResLoot {

    constructor(_json_: any) {
        if (_json_.loot_id === undefined) { throw new Error() }
        this.lootId = _json_.loot_id
        if (_json_.loot_group === undefined) { throw new Error() }
        this.lootGroup = _json_.loot_group
        if (_json_.loot_type === undefined) { throw new Error() }
        this.lootType = _json_.loot_type
        if (_json_.item_list === undefined) { throw new Error() }
        { this.itemList = []; for(let _ele0 of _json_.item_list) { let _e0; _e0 = new ResLootItem(_ele0); this.itemList.push(_e0);}}
    }

    /**
     * 掉落ID
     */
    readonly lootId: number
    /**
     * 掉落组
     */
    readonly lootGroup: number
    /**
     * 掉落方式
     */
    readonly lootType: ResLootType
    readonly itemList: ResLootItem[]

    resolve(tables:Tables) {
        
        
        
        for (let _e of this.itemList) { _e?.resolve(tables); }
    }
}





export class ResLootItem {

    constructor(_json_: any) {
        if (_json_.item_id === undefined) { throw new Error() }
        this.itemId = _json_.item_id
        if (_json_.count === undefined) { throw new Error() }
        this.count = _json_.count
        if (_json_.rate === undefined) { throw new Error() }
        this.rate = _json_.rate
        if (_json_.protect_times === undefined) { throw new Error() }
        this.protectTimes = _json_.protect_times
    }

    /**
     * item_id 可以是道具，装备，飞机，或者另一个掉落ID
     */
    readonly itemId: number
    readonly count: number
    readonly rate: number
    readonly protectTimes: number

    resolve(tables:Tables) {
        
        
        
        
    }
}





export class ResPlane {

    constructor(_json_: any) {
        if (_json_.id === undefined) { throw new Error() }
        this.id = _json_.id
        if (_json_.star_level === undefined) { throw new Error() }
        this.starLevel = _json_.star_level
        if (_json_.name === undefined) { throw new Error() }
        this.name = _json_.name
        if (_json_.icon === undefined) { throw new Error() }
        this.icon = _json_.icon
        if (_json_.portrait === undefined) { throw new Error() }
        this.portrait = _json_.portrait
        if (_json_.description === undefined) { throw new Error() }
        this.description = _json_.description
        if (_json_.quality === undefined) { throw new Error() }
        this.quality = _json_.quality
        if (_json_.property === undefined) { throw new Error() }
        this.property = new PlaneProperty(_json_.property)
        if (_json_.effects === undefined) { throw new Error() }
        { this.effects = []; for(let _ele0 of _json_.effects) { let _e0; _e0 = new PlaneEffect(_ele0); this.effects.push(_e0);}}
        if (_json_.materials === undefined) { throw new Error() }
        { this.materials = []; for(let _ele0 of _json_.materials) { let _e0; _e0 = new PlaneMaterial(_ele0); this.materials.push(_e0);}}
    }

    /**
     * ID
     */
    readonly id: number
    /**
     * 战机星级
     */
    readonly starLevel: number
    /**
     * 名称
     */
    readonly name: string
    /**
     * 图标
     */
    readonly icon: string
    /**
     * 立绘
     */
    readonly portrait: string
    /**
     * 描述
     */
    readonly description: string
    /**
     * 品质
     */
    readonly quality: QualityType
    readonly property: PlaneProperty
    /**
     * 效果列表
     */
    readonly effects: PlaneEffect[]
    /**
     * 升星材料
     */
    readonly materials: PlaneMaterial[]

    resolve(tables:Tables) {
        
        
        
        
        
        
        
        this.property?.resolve(tables);
        for (let _e of this.effects) { _e?.resolve(tables); }
        for (let _e of this.materials) { _e?.resolve(tables); }
    }
}





export class ResSkill {

    constructor(_json_: any) {
        if (_json_.id === undefined) { throw new Error() }
        this.id = _json_.id
        if (_json_.name === undefined) { throw new Error() }
        this.name = _json_.name
        if (_json_.desc === undefined) { throw new Error() }
        this.desc = _json_.desc
        if (_json_.icon === undefined) { throw new Error() }
        this.icon = _json_.icon
        if (_json_.cd === undefined) { throw new Error() }
        this.cd = _json_.cd
        if (_json_.CostID === undefined) { throw new Error() }
        this.CostID = _json_.CostID
        if (_json_.CostNum === undefined) { throw new Error() }
        this.CostNum = _json_.CostNum
        if (_json_.conditionID === undefined) { throw new Error() }
        this.conditionID = _json_.conditionID
        if (_json_.ApplyBuffs === undefined) { throw new Error() }
        { this.ApplyBuffs = []; for(let _ele0 of _json_.ApplyBuffs) { let _e0; _e0 = new ApplyBuff(_ele0); this.ApplyBuffs.push(_e0);}}
    }

    /**
     * ID
     */
    readonly id: number
    /**
     * 技能名称
     */
    readonly name: string
    /**
     * 描述
     */
    readonly desc: string
    /**
     * 技能图标prefab
     */
    readonly icon: string
    /**
     * 冷却时间
     */
    readonly cd: number
    /**
     * 费用ID
     */
    readonly CostID: number
    /**
     * 费用消耗值
     */
    readonly CostNum: number
    /**
     * 条件
     */
    readonly conditionID: number
    readonly ApplyBuffs: ApplyBuff[]

    resolve(tables:Tables) {
        
        
        
        
        
        
        
        
        
    }
}





export class ResStage {

    constructor(_json_: any) {
        if (_json_.id === undefined) { throw new Error() }
        this.id = _json_.id
        if (_json_.mainStage === undefined) { throw new Error() }
        this.mainStage = _json_.mainStage
        if (_json_.subStage === undefined) { throw new Error() }
        this.subStage = _json_.subStage
        if (_json_.type === undefined) { throw new Error() }
        this.type = _json_.type
        if (_json_.enemyGroupID === undefined) { throw new Error() }
        this.enemyGroupID = _json_.enemyGroupID
        if (_json_.delay === undefined) { throw new Error() }
        this.delay = _json_.delay
        if (_json_.enemyNorRate === undefined) { throw new Error() }
        this.enemyNorRate = _json_.enemyNorRate
    }

    /**
     * id
     */
    readonly id: number
    /**
     * 关卡
     */
    readonly mainStage: number
    /**
     * 阶段
     */
    readonly subStage: number
    /**
     * 类型0:普通敌机 100：boss
     */
    readonly type: number
    /**
     * 波次id
     */
    readonly enemyGroupID: string
    /**
     * 延迟时间
     */
    readonly delay: number
    /**
     * 属性倍率（血量，攻击，碰撞攻击）
     */
    readonly enemyNorRate: string

    resolve(tables:Tables) {
        
        
        
        
        
        
        
    }
}





export class ResTask {

    constructor(_json_: any) {
        if (_json_.task_id === undefined) { throw new Error() }
        this.taskId = _json_.task_id
        if (_json_.group_id === undefined) { throw new Error() }
        this.groupId = _json_.group_id
        if (_json_.task_class === undefined) { throw new Error() }
        this.taskClass = _json_.task_class
        if (_json_.prev_id === undefined) { throw new Error() }
        this.prevId = _json_.prev_id
        if (_json_.period_type === undefined) { throw new Error() }
        this.periodType = _json_.period_type
        if (_json_.task_cond === undefined) { throw new Error() }
        { this.taskCond = []; for(let _ele0 of _json_.task_cond) { let _e0; _e0 = new ResCondition(_ele0); this.taskCond.push(_e0);}}
        if (_json_.task_goal === undefined) { throw new Error() }
        this.taskGoal = new ResTaskGoal(_json_.task_goal)
        if (_json_.accumulate === undefined) { throw new Error() }
        this.accumulate = _json_.accumulate
        if (_json_.reward_id === undefined) { throw new Error() }
        this.rewardId = _json_.reward_id
        if (_json_.orbit_group_id === undefined) { throw new Error() }
        this.orbitGroupId = _json_.orbit_group_id
        if (_json_.orbit_value === undefined) { throw new Error() }
        this.orbitValue = _json_.orbit_value
        if (_json_.open_date === undefined) { throw new Error() }
        this.openDate = _json_.open_date
        if (_json_.open_time === undefined) { throw new Error() }
        this.openTime = _json_.open_time
        if (_json_.close_date === undefined) { throw new Error() }
        this.closeDate = _json_.close_date
        if (_json_.close_time === undefined) { throw new Error() }
        this.closeTime = _json_.close_time
        if (_json_.link_to === undefined) { throw new Error() }
        this.linkTo = _json_.link_to
    }

    /**
     * 任务 ID
     */
    readonly taskId: number
    /**
     * 任务集 ID
     */
    readonly groupId: number
    /**
     * 任务类型
     */
    readonly taskClass: ResTaskClass
    /**
     * 前置任务 ID
     */
    readonly prevId: number
    /**
     * 重置周期
     */
    readonly periodType: ResPeriodType
    /**
     * 任务接取条件
     */
    readonly taskCond: ResCondition[]
    readonly taskGoal: ResTaskGoal
    /**
     * 目标值累积
     */
    readonly accumulate: boolean
    /**
     * 奖励ID
     */
    readonly rewardId: number
    /**
     * 奖励轨道集ID
     */
    readonly orbitGroupId: number
    /**
     * 完成奖励值
     */
    readonly orbitValue: number
    /**
     * 开放日期(yyyymmdd)
     */
    readonly openDate: string
    /**
     * 开放时间(HHMMSS)
     */
    readonly openTime: string
    /**
     * 结束日期(yyyymmdd)
     */
    readonly closeDate: string
    /**
     * 结束时间(HHMMSS)
     */
    readonly closeTime: string
    /**
     * 任务追踪
     */
    readonly linkTo: string

    resolve(tables:Tables) {
        
        
        
        
        
        for (let _e of this.taskCond) { _e?.resolve(tables); }
        this.taskGoal?.resolve(tables);
        
        
        
        
        
        
        
        
        
    }
}





/**
 * 任务配置表项定义
 */
export class ResTask2 {

    constructor(_json_: any) {
        if (_json_.task_id === undefined) { throw new Error() }
        this.taskId = _json_.task_id
        if (_json_.group_id === undefined) { throw new Error() }
        this.groupId = _json_.group_id
        if (_json_.desc === undefined) { throw new Error() }
        this.desc = _json_.desc
        if (_json_.task_class === undefined) { throw new Error() }
        this.taskClass = _json_.task_class
        if (_json_.ui_res === undefined) { throw new Error() }
        this.uiRes = _json_.ui_res
        if (_json_.prev_id === undefined) { throw new Error() }
        this.prevId = _json_.prev_id
        if (_json_.period_type === undefined) { throw new Error() }
        this.periodType = _json_.period_type
        if (_json_.task_cond === undefined) { throw new Error() }
        { this.taskCond = []; for(let _ele0 of _json_.task_cond) { let _e0; _e0 = new ResCondition(_ele0); this.taskCond.push(_e0);}}
        if (_json_.task_goal === undefined) { throw new Error() }
        this.taskGoal = new ResTaskGoal(_json_.task_goal)
        if (_json_.accumlate === undefined) { throw new Error() }
        this.accumlate = _json_.accumlate
        if (_json_.reward_id === undefined) { throw new Error() }
        this.rewardId = _json_.reward_id
        if (_json_.task_orbit_id === undefined) { throw new Error() }
        this.taskOrbitId = _json_.task_orbit_id
        if (_json_.task_orbit_value === undefined) { throw new Error() }
        this.taskOrbitValue = _json_.task_orbit_value
        if (_json_.open_date === undefined) { throw new Error() }
        this.openDate = _json_.open_date
        if (_json_.open_time === undefined) { throw new Error() }
        this.openTime = _json_.open_time
        if (_json_.close_date === undefined) { throw new Error() }
        this.closeDate = _json_.close_date
        if (_json_.close_time === undefined) { throw new Error() }
        this.closeTime = _json_.close_time
        if (_json_.link_to === undefined) { throw new Error() }
        this.linkTo = _json_.link_to
    }

    readonly taskId: number
    readonly groupId: number
    readonly desc: string
    /**
     * 可不配置
     */
    readonly taskClass: ResTaskClass
    readonly uiRes: string
    readonly prevId: number
    readonly periodType: ResPeriodType
    readonly taskCond: ResCondition[]
    readonly taskGoal: ResTaskGoal
    readonly accumlate: boolean
    /**
     * 需要客户端领取
     */
    readonly rewardId: number
    readonly taskOrbitId: number
    /**
     * 自动累积
     */
    readonly taskOrbitValue: number
    /**
     * yyyymmdd
     */
    readonly openDate: string
    /**
     * HHMMSS
     */
    readonly openTime: string
    /**
     * yyyymmdd
     */
    readonly closeDate: string
    /**
     * HHMMSS
     */
    readonly closeTime: string
    /**
     * 连接到完成任务相关的 UI
     */
    readonly linkTo: string

    resolve(tables:Tables) {
        
        
        
        
        
        
        
        for (let _e of this.taskCond) { _e?.resolve(tables); }
        this.taskGoal?.resolve(tables);
        
        
        
        
        
        
        
        
        
    }
}





export class ResTaskGoal {

    constructor(_json_: any) {
        if (_json_.goal_type === undefined) { throw new Error() }
        this.goalType = _json_.goal_type
        if (_json_.params === undefined) { throw new Error() }
        { this.params = []; for(let _ele0 of _json_.params) { let _e0; _e0 = _ele0; this.params.push(_e0);}}
        if (_json_.desc === undefined) { throw new Error() }
        this.desc = _json_.desc
    }

    readonly goalType: ResGoalType
    readonly params: number[]
    readonly desc: string

    resolve(tables:Tables) {
        
        
        
    }
}





export class ResTaskOrbit {

    constructor(_json_: any) {
        if (_json_.task_id === undefined) { throw new Error() }
        this.taskId = _json_.task_id
        if (_json_.group_id === undefined) { throw new Error() }
        this.groupId = _json_.group_id
        if (_json_.index === undefined) { throw new Error() }
        this.index = _json_.index
        if (_json_.period_type === undefined) { throw new Error() }
        this.periodType = _json_.period_type
        if (_json_.task_cond === undefined) { throw new Error() }
        { this.taskCond = []; for(let _ele0 of _json_.task_cond) { let _e0; _e0 = new ResCondition(_ele0); this.taskCond.push(_e0);}}
        if (_json_.task_goal === undefined) { throw new Error() }
        this.taskGoal = new ResTaskGoal(_json_.task_goal)
        if (_json_.accumulate === undefined) { throw new Error() }
        this.accumulate = _json_.accumulate
        if (_json_.reward_id === undefined) { throw new Error() }
        this.rewardId = _json_.reward_id
    }

    /**
     * 轨道ID
     */
    readonly taskId: number
    /**
     * 轨道集
     */
    readonly groupId: number
    /**
     * 序号
     */
    readonly index: number
    /**
     * 循环类型
     */
    readonly periodType: ResPeriodType
    readonly taskCond: ResCondition[]
    readonly taskGoal: ResTaskGoal
    /**
     * 是否累积
     */
    readonly accumulate: boolean
    /**
     * 奖励ID
     */
    readonly rewardId: number

    resolve(tables:Tables) {
        
        
        
        
        for (let _e of this.taskCond) { _e?.resolve(tables); }
        this.taskGoal?.resolve(tables);
        
        
    }
}





export class ResTrack {

    constructor(_json_: any) {
        if (_json_.id === undefined) { throw new Error() }
        this.id = _json_.id
        if (_json_.tpe === undefined) { throw new Error() }
        this.tpe = _json_.tpe
        if (_json_.value === undefined) { throw new Error() }
        this.value = _json_.value
    }

    /**
     * id
     */
    readonly id: number
    /**
     * 类型
     */
    readonly tpe: number
    /**
     * 值(不同的轨迹类型，数据代表的信息不一样)
     */
    readonly value: string

    resolve(tables:Tables) {
        
        
        
    }
}





export class ResUpgrade {

    constructor(_json_: any) {
        if (_json_.role_level === undefined) { throw new Error() }
        this.roleLevel = _json_.role_level
        if (_json_.xp === undefined) { throw new Error() }
        this.xp = _json_.xp
        if (_json_.reward_id === undefined) { throw new Error() }
        this.rewardId = _json_.reward_id
    }

    /**
     * 玩家等级
     */
    readonly roleLevel: number
    /**
     * 升级所需的经验
     */
    readonly xp: number
    /**
     * 升级奖励
     */
    readonly rewardId: number

    resolve(tables:Tables) {
        
        
        
    }
}





export class ResWave {

    constructor(_json_: any) {
        if (_json_.id === undefined) { throw new Error() }
        this.id = _json_.id
        if (_json_.enemyGroupID === undefined) { throw new Error() }
        this.enemyGroupID = _json_.enemyGroupID
        if (_json_.delay === undefined) { throw new Error() }
        this.delay = _json_.delay
        if (_json_.planeType === undefined) { throw new Error() }
        this.planeType = _json_.planeType
        if (_json_.planeId === undefined) { throw new Error() }
        this.planeId = _json_.planeId
        if (_json_.interval === undefined) { throw new Error() }
        this.interval = _json_.interval
        if (_json_.offsetPos === undefined) { throw new Error() }
        this.offsetPos = _json_.offsetPos
        if (_json_.num === undefined) { throw new Error() }
        this.num = _json_.num
        if (_json_.pos === undefined) { throw new Error() }
        this.pos = _json_.pos
        if (_json_.track === undefined) { throw new Error() }
        this.track = _json_.track
        if (_json_.trackParams === undefined) { throw new Error() }
        this.trackParams = _json_.trackParams
        if (_json_.rotatioSpeed === undefined) { throw new Error() }
        this.rotatioSpeed = _json_.rotatioSpeed
        if (_json_.FirstShootDelay === undefined) { throw new Error() }
        this.FirstShootDelay = _json_.FirstShootDelay
    }

    /**
     * id
     */
    readonly id: number
    /**
     * 波次 ID
     */
    readonly enemyGroupID: number
    /**
     * 延迟时间
     */
    readonly delay: number
    /**
     * 0 表示普通敌机
     */
    readonly planeType: number
    /**
     * 敌机id
     */
    readonly planeId: number
    /**
     * 生成间隔时间
     */
    readonly interval: number
    /**
     * 根据敌机数量设置偏移位置
     */
    readonly offsetPos: string
    /**
     * 生成的敌机数量
     */
    readonly num: number
    /**
     * 初始位置
     */
    readonly pos: string
    /**
     * 轨迹路径(次数,轨迹索引,;id,速度,间隔；)
     */
    readonly track: string
    /**
     * 轨迹参数
     */
    readonly trackParams: string
    /**
     * 旋转速度
     */
    readonly rotatioSpeed: number
    /**
     * 首次射击延迟
     */
    readonly FirstShootDelay: string

    resolve(tables:Tables) {
        
        
        
        
        
        
        
        
        
        
        
        
        
    }
}





export class SkillCondition {

    constructor(_json_: any) {
        if (_json_.type === undefined) { throw new Error() }
        this.type = _json_.type
        if (_json_.param === undefined) { throw new Error() }
        { this.param = []; for(let _ele0 of _json_.param) { let _e0; _e0 = _ele0; this.param.push(_e0);}}
    }

    readonly type: SkillConditionType
    readonly param: number[]

    resolve(tables:Tables) {
        
        
    }
}






export class TbGlobalAttr {

    private _data: ResGlobalAttr
    constructor(_json_: any) {
        if (_json_.length != 1) throw new Error('table mode=one, but size != 1')
        this._data = new ResGlobalAttr(_json_[0])
    }

    getData(): ResGlobalAttr { return this._data; }

    /**
     * 每回合发放的金币
     */
    get  GoldProducion(): number { return this._data.GoldProducion; }
    /**
     * 体力上限值
     */
    get  MaxEnergy(): number { return this._data.MaxEnergy; }
    /**
     * 体力恢复的间隔时间
     */
    get  EnergyRecoverInterval(): number { return this._data.EnergyRecoverInterval; }
    /**
     * 体力恢复的值
     */
    get  EnergyRecoverValue(): number { return this._data.EnergyRecoverValue; }
    /**
     * 局内道具拾取距离
     */
    get  ItemPickUpRadius(): number { return this._data.ItemPickUpRadius; }
    /**
     * 受击保护
     */
    get  PostHitProtection(): number { return this._data.PostHitProtection; }
    /**
     * 镜头平移最大跟随速度
     */
    get  CameraTranslationMaxMoveSpeed(): number { return this._data.CameraTranslationMaxMoveSpeed; }
    /**
     * 镜头平移启动延迟
     */
    get  CameraTranslationMoveDelay(): number { return this._data.CameraTranslationMoveDelay; }

    resolve(tables:Tables)
    {
        this._data.resolve(tables)
    }
    
}




export class TbEquipUpgrade {
    private _dataList: ResEquipUpgrade[]
    
    constructor(_json_: any) {
        this._dataList = []
        for(var _json2_ of _json_) {
            let _v: ResEquipUpgrade
            _v = new ResEquipUpgrade(_json2_)
            this._dataList.push(_v)
        }
    }

    getDataList(): ResEquipUpgrade[] { return this._dataList }

    get(index: number): ResEquipUpgrade | undefined { return this._dataList[index] }
    
    resolve(tables:Tables) {
        for(let  data of this._dataList)
        {
            data.resolve(tables)
        }
    }

}




export class TbGM {
    private _dataList: GM[]
    
    constructor(_json_: any) {
        this._dataList = []
        for(var _json2_ of _json_) {
            let _v: GM
            _v = new GM(_json2_)
            this._dataList.push(_v)
        }
    }

    getDataList(): GM[] { return this._dataList }

    get(index: number): GM | undefined { return this._dataList[index] }
    
    resolve(tables:Tables) {
        for(let  data of this._dataList)
        {
            data.resolve(tables)
        }
    }

}




export class TbPlane {
    private _dataMap: Map<number, ResPlane>
    private _dataList: ResPlane[]
    constructor(_json_: any) {
        this._dataMap = new Map<number, ResPlane>()
        this._dataList = []
        for(var _json2_ of _json_) {
            let _v: ResPlane
            _v = new ResPlane(_json2_)
            this._dataList.push(_v)
            this._dataMap.set(_v.id, _v)
        }
    }

    getDataMap(): Map<number, ResPlane> { return this._dataMap; }
    getDataList(): ResPlane[] { return this._dataList; }

    get(key: number): ResPlane | undefined { return this._dataMap.get(key); }

    resolve(tables:Tables) {
        for(let  data of this._dataList)
        {
            data.resolve(tables)
        }
    }

}




export class TbResAchievement {
    private _dataMap: Map<number, ResAchievement>
    private _dataList: ResAchievement[]
    constructor(_json_: any) {
        this._dataMap = new Map<number, ResAchievement>()
        this._dataList = []
        for(var _json2_ of _json_) {
            let _v: ResAchievement
            _v = new ResAchievement(_json2_)
            this._dataList.push(_v)
            this._dataMap.set(_v.taskId, _v)
        }
    }

    getDataMap(): Map<number, ResAchievement> { return this._dataMap; }
    getDataList(): ResAchievement[] { return this._dataList; }

    get(key: number): ResAchievement | undefined { return this._dataMap.get(key); }

    resolve(tables:Tables) {
        for(let  data of this._dataList)
        {
            data.resolve(tables)
        }
    }

}




export class TbResActivity {
    private _dataMap: Map<number, ResActivity>
    private _dataList: ResActivity[]
    constructor(_json_: any) {
        this._dataMap = new Map<number, ResActivity>()
        this._dataList = []
        for(var _json2_ of _json_) {
            let _v: ResActivity
            _v = new ResActivity(_json2_)
            this._dataList.push(_v)
            this._dataMap.set(_v.taskId, _v)
        }
    }

    getDataMap(): Map<number, ResActivity> { return this._dataMap; }
    getDataList(): ResActivity[] { return this._dataList; }

    get(key: number): ResActivity | undefined { return this._dataMap.get(key); }

    resolve(tables:Tables) {
        for(let  data of this._dataList)
        {
            data.resolve(tables)
        }
    }

}




export class TbResBuffer {
    private _dataMap: Map<number, ResBuffer>
    private _dataList: ResBuffer[]
    constructor(_json_: any) {
        this._dataMap = new Map<number, ResBuffer>()
        this._dataList = []
        for(var _json2_ of _json_) {
            let _v: ResBuffer
            _v = new ResBuffer(_json2_)
            this._dataList.push(_v)
            this._dataMap.set(_v.id, _v)
        }
    }

    getDataMap(): Map<number, ResBuffer> { return this._dataMap; }
    getDataList(): ResBuffer[] { return this._dataList; }

    get(key: number): ResBuffer | undefined { return this._dataMap.get(key); }

    resolve(tables:Tables) {
        for(let  data of this._dataList)
        {
            data.resolve(tables)
        }
    }

}




export class TbResBullet {
    private _dataMap: Map<number, ResBullet>
    private _dataList: ResBullet[]
    constructor(_json_: any) {
        this._dataMap = new Map<number, ResBullet>()
        this._dataList = []
        for(var _json2_ of _json_) {
            let _v: ResBullet
            _v = new ResBullet(_json2_)
            this._dataList.push(_v)
            this._dataMap.set(_v.id, _v)
        }
    }

    getDataMap(): Map<number, ResBullet> { return this._dataMap; }
    getDataList(): ResBullet[] { return this._dataList; }

    get(key: number): ResBullet | undefined { return this._dataMap.get(key); }

    resolve(tables:Tables) {
        for(let  data of this._dataList)
        {
            data.resolve(tables)
        }
    }

}




export class TbResChapter {
    private _dataMap: Map<number, ResChapter>
    private _dataList: ResChapter[]
    constructor(_json_: any) {
        this._dataMap = new Map<number, ResChapter>()
        this._dataList = []
        for(var _json2_ of _json_) {
            let _v: ResChapter
            _v = new ResChapter(_json2_)
            this._dataList.push(_v)
            this._dataMap.set(_v.id, _v)
        }
    }

    getDataMap(): Map<number, ResChapter> { return this._dataMap; }
    getDataList(): ResChapter[] { return this._dataList; }

    get(key: number): ResChapter | undefined { return this._dataMap.get(key); }

    resolve(tables:Tables) {
        for(let  data of this._dataList)
        {
            data.resolve(tables)
        }
    }

}




export class TbResEffect {
    private _dataMap: Map<number, ResEffect>
    private _dataList: ResEffect[]
    constructor(_json_: any) {
        this._dataMap = new Map<number, ResEffect>()
        this._dataList = []
        for(var _json2_ of _json_) {
            let _v: ResEffect
            _v = new ResEffect(_json2_)
            this._dataList.push(_v)
            this._dataMap.set(_v.id, _v)
        }
    }

    getDataMap(): Map<number, ResEffect> { return this._dataMap; }
    getDataList(): ResEffect[] { return this._dataList; }

    get(key: number): ResEffect | undefined { return this._dataMap.get(key); }

    resolve(tables:Tables) {
        for(let  data of this._dataList)
        {
            data.resolve(tables)
        }
    }

}




export class TbResEnemy {
    private _dataMap: Map<number, ResEnemy>
    private _dataList: ResEnemy[]
    constructor(_json_: any) {
        this._dataMap = new Map<number, ResEnemy>()
        this._dataList = []
        for(var _json2_ of _json_) {
            let _v: ResEnemy
            _v = new ResEnemy(_json2_)
            this._dataList.push(_v)
            this._dataMap.set(_v.id, _v)
        }
    }

    getDataMap(): Map<number, ResEnemy> { return this._dataMap; }
    getDataList(): ResEnemy[] { return this._dataList; }

    get(key: number): ResEnemy | undefined { return this._dataMap.get(key); }

    resolve(tables:Tables) {
        for(let  data of this._dataList)
        {
            data.resolve(tables)
        }
    }

}




export class TbResEquip {
    private _dataMap: Map<number, ResEquip>
    private _dataList: ResEquip[]
    constructor(_json_: any) {
        this._dataMap = new Map<number, ResEquip>()
        this._dataList = []
        for(var _json2_ of _json_) {
            let _v: ResEquip
            _v = new ResEquip(_json2_)
            this._dataList.push(_v)
            this._dataMap.set(_v.id, _v)
        }
    }

    getDataMap(): Map<number, ResEquip> { return this._dataMap; }
    getDataList(): ResEquip[] { return this._dataList; }

    get(key: number): ResEquip | undefined { return this._dataMap.get(key); }

    resolve(tables:Tables) {
        for(let  data of this._dataList)
        {
            data.resolve(tables)
        }
    }

}




export class TbResGameMode {
    private _dataMap: Map<number, ResGameMode>
    private _dataList: ResGameMode[]
    constructor(_json_: any) {
        this._dataMap = new Map<number, ResGameMode>()
        this._dataList = []
        for(var _json2_ of _json_) {
            let _v: ResGameMode
            _v = new ResGameMode(_json2_)
            this._dataList.push(_v)
            this._dataMap.set(_v.ID, _v)
        }
    }

    getDataMap(): Map<number, ResGameMode> { return this._dataMap; }
    getDataList(): ResGameMode[] { return this._dataList; }

    get(key: number): ResGameMode | undefined { return this._dataMap.get(key); }

    resolve(tables:Tables) {
        for(let  data of this._dataList)
        {
            data.resolve(tables)
        }
    }

}




export class TbResItem {
    private _dataMap: Map<number, ResItem>
    private _dataList: ResItem[]
    constructor(_json_: any) {
        this._dataMap = new Map<number, ResItem>()
        this._dataList = []
        for(var _json2_ of _json_) {
            let _v: ResItem
            _v = new ResItem(_json2_)
            this._dataList.push(_v)
            this._dataMap.set(_v.id, _v)
        }
    }

    getDataMap(): Map<number, ResItem> { return this._dataMap; }
    getDataList(): ResItem[] { return this._dataList; }

    get(key: number): ResItem | undefined { return this._dataMap.get(key); }

    resolve(tables:Tables) {
        for(let  data of this._dataList)
        {
            data.resolve(tables)
        }
    }

}




export class TbResLevel {
    private _dataMap: Map<number, ResLevel>
    private _dataList: ResLevel[]
    constructor(_json_: any) {
        this._dataMap = new Map<number, ResLevel>()
        this._dataList = []
        for(var _json2_ of _json_) {
            let _v: ResLevel
            _v = new ResLevel(_json2_)
            this._dataList.push(_v)
            this._dataMap.set(_v.id, _v)
        }
    }

    getDataMap(): Map<number, ResLevel> { return this._dataMap; }
    getDataList(): ResLevel[] { return this._dataList; }

    get(key: number): ResLevel | undefined { return this._dataMap.get(key); }

    resolve(tables:Tables) {
        for(let  data of this._dataList)
        {
            data.resolve(tables)
        }
    }

}




export class TbResLevelGroup {
    private _dataMap: Map<number, ResLevelGroup>
    private _dataList: ResLevelGroup[]
    constructor(_json_: any) {
        this._dataMap = new Map<number, ResLevelGroup>()
        this._dataList = []
        for(var _json2_ of _json_) {
            let _v: ResLevelGroup
            _v = new ResLevelGroup(_json2_)
            this._dataList.push(_v)
            this._dataMap.set(_v.id, _v)
        }
    }

    getDataMap(): Map<number, ResLevelGroup> { return this._dataMap; }
    getDataList(): ResLevelGroup[] { return this._dataList; }

    get(key: number): ResLevelGroup | undefined { return this._dataMap.get(key); }

    resolve(tables:Tables) {
        for(let  data of this._dataList)
        {
            data.resolve(tables)
        }
    }

}




export class TbResLoot {
    private _dataMap: Map<number, ResLoot>
    private _dataList: ResLoot[]
    constructor(_json_: any) {
        this._dataMap = new Map<number, ResLoot>()
        this._dataList = []
        for(var _json2_ of _json_) {
            let _v: ResLoot
            _v = new ResLoot(_json2_)
            this._dataList.push(_v)
            this._dataMap.set(_v.lootId, _v)
        }
    }

    getDataMap(): Map<number, ResLoot> { return this._dataMap; }
    getDataList(): ResLoot[] { return this._dataList; }

    get(key: number): ResLoot | undefined { return this._dataMap.get(key); }

    resolve(tables:Tables) {
        for(let  data of this._dataList)
        {
            data.resolve(tables)
        }
    }

}




export class TbResSkill {
    private _dataMap: Map<number, ResSkill>
    private _dataList: ResSkill[]
    constructor(_json_: any) {
        this._dataMap = new Map<number, ResSkill>()
        this._dataList = []
        for(var _json2_ of _json_) {
            let _v: ResSkill
            _v = new ResSkill(_json2_)
            this._dataList.push(_v)
            this._dataMap.set(_v.id, _v)
        }
    }

    getDataMap(): Map<number, ResSkill> { return this._dataMap; }
    getDataList(): ResSkill[] { return this._dataList; }

    get(key: number): ResSkill | undefined { return this._dataMap.get(key); }

    resolve(tables:Tables) {
        for(let  data of this._dataList)
        {
            data.resolve(tables)
        }
    }

}




export class TbResStage {
    private _dataMap: Map<number, ResStage>
    private _dataList: ResStage[]
    constructor(_json_: any) {
        this._dataMap = new Map<number, ResStage>()
        this._dataList = []
        for(var _json2_ of _json_) {
            let _v: ResStage
            _v = new ResStage(_json2_)
            this._dataList.push(_v)
            this._dataMap.set(_v.id, _v)
        }
    }

    getDataMap(): Map<number, ResStage> { return this._dataMap; }
    getDataList(): ResStage[] { return this._dataList; }

    get(key: number): ResStage | undefined { return this._dataMap.get(key); }

    resolve(tables:Tables) {
        for(let  data of this._dataList)
        {
            data.resolve(tables)
        }
    }

}




export class TbResTask {
    private _dataMap: Map<number, ResTask>
    private _dataList: ResTask[]
    constructor(_json_: any) {
        this._dataMap = new Map<number, ResTask>()
        this._dataList = []
        for(var _json2_ of _json_) {
            let _v: ResTask
            _v = new ResTask(_json2_)
            this._dataList.push(_v)
            this._dataMap.set(_v.taskId, _v)
        }
    }

    getDataMap(): Map<number, ResTask> { return this._dataMap; }
    getDataList(): ResTask[] { return this._dataList; }

    get(key: number): ResTask | undefined { return this._dataMap.get(key); }

    resolve(tables:Tables) {
        for(let  data of this._dataList)
        {
            data.resolve(tables)
        }
    }

}




export class TbResTaskOrbit {
    private _dataMap: Map<number, ResTaskOrbit>
    private _dataList: ResTaskOrbit[]
    constructor(_json_: any) {
        this._dataMap = new Map<number, ResTaskOrbit>()
        this._dataList = []
        for(var _json2_ of _json_) {
            let _v: ResTaskOrbit
            _v = new ResTaskOrbit(_json2_)
            this._dataList.push(_v)
            this._dataMap.set(_v.taskId, _v)
        }
    }

    getDataMap(): Map<number, ResTaskOrbit> { return this._dataMap; }
    getDataList(): ResTaskOrbit[] { return this._dataList; }

    get(key: number): ResTaskOrbit | undefined { return this._dataMap.get(key); }

    resolve(tables:Tables) {
        for(let  data of this._dataList)
        {
            data.resolve(tables)
        }
    }

}




export class TbResTrack {
    private _dataMap: Map<number, ResTrack>
    private _dataList: ResTrack[]
    constructor(_json_: any) {
        this._dataMap = new Map<number, ResTrack>()
        this._dataList = []
        for(var _json2_ of _json_) {
            let _v: ResTrack
            _v = new ResTrack(_json2_)
            this._dataList.push(_v)
            this._dataMap.set(_v.id, _v)
        }
    }

    getDataMap(): Map<number, ResTrack> { return this._dataMap; }
    getDataList(): ResTrack[] { return this._dataList; }

    get(key: number): ResTrack | undefined { return this._dataMap.get(key); }

    resolve(tables:Tables) {
        for(let  data of this._dataList)
        {
            data.resolve(tables)
        }
    }

}




export class TbResUpgrade {
    private _dataMap: Map<number, ResUpgrade>
    private _dataList: ResUpgrade[]
    constructor(_json_: any) {
        this._dataMap = new Map<number, ResUpgrade>()
        this._dataList = []
        for(var _json2_ of _json_) {
            let _v: ResUpgrade
            _v = new ResUpgrade(_json2_)
            this._dataList.push(_v)
            this._dataMap.set(_v.roleLevel, _v)
        }
    }

    getDataMap(): Map<number, ResUpgrade> { return this._dataMap; }
    getDataList(): ResUpgrade[] { return this._dataList; }

    get(key: number): ResUpgrade | undefined { return this._dataMap.get(key); }

    resolve(tables:Tables) {
        for(let  data of this._dataList)
        {
            data.resolve(tables)
        }
    }

}




export class TbResWave {
    private _dataMap: Map<number, ResWave>
    private _dataList: ResWave[]
    constructor(_json_: any) {
        this._dataMap = new Map<number, ResWave>()
        this._dataList = []
        for(var _json2_ of _json_) {
            let _v: ResWave
            _v = new ResWave(_json2_)
            this._dataList.push(_v)
            this._dataMap.set(_v.id, _v)
        }
    }

    getDataMap(): Map<number, ResWave> { return this._dataMap; }
    getDataList(): ResWave[] { return this._dataList; }

    get(key: number): ResWave | undefined { return this._dataMap.get(key); }

    resolve(tables:Tables) {
        for(let  data of this._dataList)
        {
            data.resolve(tables)
        }
    }

}




type JsonLoader = (file: string) => any

export class Tables {
    private _TbGlobalAttr: TbGlobalAttr
    get TbGlobalAttr(): TbGlobalAttr  { return this._TbGlobalAttr;}
    private _TbEquipUpgrade: TbEquipUpgrade
    get TbEquipUpgrade(): TbEquipUpgrade  { return this._TbEquipUpgrade;}
    private _TbGM: TbGM
    get TbGM(): TbGM  { return this._TbGM;}
    private _TbPlane: TbPlane
    get TbPlane(): TbPlane  { return this._TbPlane;}
    private _TbResAchievement: TbResAchievement
    get TbResAchievement(): TbResAchievement  { return this._TbResAchievement;}
    private _TbResActivity: TbResActivity
    get TbResActivity(): TbResActivity  { return this._TbResActivity;}
    private _TbResBuffer: TbResBuffer
    get TbResBuffer(): TbResBuffer  { return this._TbResBuffer;}
    private _TbResBullet: TbResBullet
    get TbResBullet(): TbResBullet  { return this._TbResBullet;}
    private _TbResChapter: TbResChapter
    get TbResChapter(): TbResChapter  { return this._TbResChapter;}
    private _TbResEffect: TbResEffect
    get TbResEffect(): TbResEffect  { return this._TbResEffect;}
    private _TbResEnemy: TbResEnemy
    get TbResEnemy(): TbResEnemy  { return this._TbResEnemy;}
    private _TbResEquip: TbResEquip
    get TbResEquip(): TbResEquip  { return this._TbResEquip;}
    private _TbResGameMode: TbResGameMode
    get TbResGameMode(): TbResGameMode  { return this._TbResGameMode;}
    private _TbResItem: TbResItem
    get TbResItem(): TbResItem  { return this._TbResItem;}
    private _TbResLevel: TbResLevel
    get TbResLevel(): TbResLevel  { return this._TbResLevel;}
    private _TbResLevelGroup: TbResLevelGroup
    get TbResLevelGroup(): TbResLevelGroup  { return this._TbResLevelGroup;}
    private _TbResLoot: TbResLoot
    get TbResLoot(): TbResLoot  { return this._TbResLoot;}
    private _TbResSkill: TbResSkill
    get TbResSkill(): TbResSkill  { return this._TbResSkill;}
    private _TbResStage: TbResStage
    get TbResStage(): TbResStage  { return this._TbResStage;}
    private _TbResTask: TbResTask
    get TbResTask(): TbResTask  { return this._TbResTask;}
    private _TbResTaskOrbit: TbResTaskOrbit
    get TbResTaskOrbit(): TbResTaskOrbit  { return this._TbResTaskOrbit;}
    private _TbResTrack: TbResTrack
    get TbResTrack(): TbResTrack  { return this._TbResTrack;}
    private _TbResUpgrade: TbResUpgrade
    get TbResUpgrade(): TbResUpgrade  { return this._TbResUpgrade;}
    private _TbResWave: TbResWave
    get TbResWave(): TbResWave  { return this._TbResWave;}

    constructor(loader: JsonLoader) {
        this._TbGlobalAttr = new TbGlobalAttr(loader('tbglobalattr'))
        this._TbEquipUpgrade = new TbEquipUpgrade(loader('tbequipupgrade'))
        this._TbGM = new TbGM(loader('tbgm'))
        this._TbPlane = new TbPlane(loader('tbplane'))
        this._TbResAchievement = new TbResAchievement(loader('tbresachievement'))
        this._TbResActivity = new TbResActivity(loader('tbresactivity'))
        this._TbResBuffer = new TbResBuffer(loader('tbresbuffer'))
        this._TbResBullet = new TbResBullet(loader('tbresbullet'))
        this._TbResChapter = new TbResChapter(loader('tbreschapter'))
        this._TbResEffect = new TbResEffect(loader('tbreseffect'))
        this._TbResEnemy = new TbResEnemy(loader('tbresenemy'))
        this._TbResEquip = new TbResEquip(loader('tbresequip'))
        this._TbResGameMode = new TbResGameMode(loader('tbresgamemode'))
        this._TbResItem = new TbResItem(loader('tbresitem'))
        this._TbResLevel = new TbResLevel(loader('tbreslevel'))
        this._TbResLevelGroup = new TbResLevelGroup(loader('tbreslevelgroup'))
        this._TbResLoot = new TbResLoot(loader('tbresloot'))
        this._TbResSkill = new TbResSkill(loader('tbresskill'))
        this._TbResStage = new TbResStage(loader('tbresstage'))
        this._TbResTask = new TbResTask(loader('tbrestask'))
        this._TbResTaskOrbit = new TbResTaskOrbit(loader('tbrestaskorbit'))
        this._TbResTrack = new TbResTrack(loader('tbrestrack'))
        this._TbResUpgrade = new TbResUpgrade(loader('tbresupgrade'))
        this._TbResWave = new TbResWave(loader('tbreswave'))

        this._TbGlobalAttr.resolve(this)
        this._TbEquipUpgrade.resolve(this)
        this._TbGM.resolve(this)
        this._TbPlane.resolve(this)
        this._TbResAchievement.resolve(this)
        this._TbResActivity.resolve(this)
        this._TbResBuffer.resolve(this)
        this._TbResBullet.resolve(this)
        this._TbResChapter.resolve(this)
        this._TbResEffect.resolve(this)
        this._TbResEnemy.resolve(this)
        this._TbResEquip.resolve(this)
        this._TbResGameMode.resolve(this)
        this._TbResItem.resolve(this)
        this._TbResLevel.resolve(this)
        this._TbResLevelGroup.resolve(this)
        this._TbResLoot.resolve(this)
        this._TbResSkill.resolve(this)
        this._TbResStage.resolve(this)
        this._TbResTask.resolve(this)
        this._TbResTaskOrbit.resolve(this)
        this._TbResTrack.resolve(this)
        this._TbResUpgrade.resolve(this)
        this._TbResWave.resolve(this)
    }
}

