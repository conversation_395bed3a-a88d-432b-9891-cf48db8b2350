import { _decorator, assetManager, CCInteger, Component, instantiate, Prefab, v2, Vec2 } from 'cc';
import { EDITOR } from 'cc/env';
const { ccclass, property, executeInEditMode } = _decorator;

@ccclass('TerrainElem')
export class TerrainElem {
    @property(CCInteger)
    public weight: number = 0;
    @property(Prefab)
    public elem: Prefab | null = null;
    @property({displayName: "坐标偏移"})
    public offSet: Vec2 = new Vec2(0, 0);
}

@ccclass('RandTerrain')
@executeInEditMode()
export class RandTerrain extends Component {
    @property({type: [TerrainElem]})
    public terrain: TerrainElem[] = [];

    protected onLoad(): void {
        if (EDITOR) {
            this.node.removeAllChildren();
            this._loadElems();
        }
    }

    protected update(): void {
        if (EDITOR) {
            if (this.terrain === null || this.terrain.length == 0) {
                return;
            }

            const isCountMatch = this.node.children.length === this.terrain.length;
            let isUUIDMatch = true;

            for (let i = 0; i < Math.min(this.node.children.length, this.terrain.length); i++) {
                const terrainElem = this.terrain[i];
                
                // 增加空值检查
                if (!terrainElem || !terrainElem.elem) {
                    console.warn(`TerrainElem at index ${i} is invalid`);
                    continue; // 跳过无效元素
                }

                const node = this.node.children[i];
                // @ts-ignore
                const nodeUUID = node._prefab?.asset?._uuid; // 使用可选链
                const elemUUID = terrainElem.elem.uuid;

                if (nodeUUID !== elemUUID) {
                    isUUIDMatch = false;
                    break;
                }
            }

            if (!isCountMatch || !isUUIDMatch) {
                this._loadElems();
            } else {
                this.node.children.forEach((child, index) => {
                    this.terrain[index].offSet = v2(child.position.x, child.position.y);
                });
            }
        }
    }

    public play(bPlay: boolean): void {
        if (EDITOR) {
            if (bPlay) {
                let totalWeight = 0;
                for (let i = 0; i < this.terrain.length; i++) {
                    totalWeight += this.terrain[i].weight;
                }
                
                const randomValue = Math.random() * totalWeight;
                let accumulatedWeight = 0;
                let selectedIndex = 0;
                
                for (let i = 0; i < this.terrain.length; i++) {
                    accumulatedWeight += this.terrain[i].weight;
                    if (randomValue < accumulatedWeight) {
                        selectedIndex = i;
                        break;
                    }
                }

                // 设置所有节点的active状态
                for (let i = 0; i < this.node.children.length; i++) {
                    this.node.children[i].active = (i === selectedIndex);
                }
            } else {
                for (let i = 0; i < this.node.children.length; i++) {
                    this.node.children[i].active = true;
                }
            }
        }
    }

    protected onDestroy(): void {
        this.node.removeAllChildren();
    }

    private _loadElems(): void {
        this.terrain.forEach((elem) => {
            if (!elem || elem.elem == null) {
                return;
            }

            this.node.removeAllChildren();
            assetManager.loadAny({uuid:elem.elem?.uuid}, (err, prefab:Prefab) => { 
                if (err) {
                    console.error("RandTerrain load TerrainElem prefab err", err);
                    return;
                }

                var node = instantiate(prefab);
                node.setPosition(elem.offSet.x, elem.offSet.y, 0);
                this.node!.addChild(node);
            });
        })
    }
}

