import { instantiate, Node, Prefab } from "cc";
import { MyApp } from 'db://assets/bundles/common/script/app/MyApp';
import { SingletonBase } from "../../../../../scripts/core/base/SingletonBase";
import GameResourceList from "../const/GameResourceList";
import { EnemyData } from "../data/EnemyData";
import { GameIns } from "../GameIns";
import BattleLayer from "../ui/layer/BattleLayer";
import BossPlane from "../ui/plane/boss/BossPlane";
import { Tools } from "../utils/Tools";


export class BossManager extends SingletonBase<BossManager> {
    _bossArr: BossPlane[] = [];
    _pfBoss: Prefab | null = null;

    constructor() {
        super();
    }

    async preLoad() {
        GameIns.battleManager.addLoadCount(1);
        MyApp.resMgr.load(GameResourceList.PrefabBoss, Prefab, (error: any, prefab: Prefab) => {
            this._pfBoss = prefab;
            GameIns.battleManager.checkLoadFinish();
        });
    }


    mainReset() {
        this.subReset();
    }

    /**
     * 重置子关卡
     */
    subReset() {
        for (const boss of this._bossArr) {
            boss.node.parent = null;
            setTimeout(() => {
                boss.node.destroy();
            }, 1000);
        }
        this._bossArr = [];
    }


    /**
 * 添加 Boss
 * @param bossType Boss 类型
 * @param bossId Boss ID
 */
    addBoss(bossId: number): BossPlane | null {
        const planeData = new EnemyData();
        planeData.planeId = bossId;

        if (!this._pfBoss) {
            throw new Error("Boss prefab is not initialized. Call preLoad() first.");
        }
        const node: Node = instantiate(this._pfBoss);
        node.name = "boss";
        BattleLayer.me.addEnemy(node);

        const boss = node.getComponent(BossPlane)!;
        boss.initPlane(planeData);
        this._bossArr.push(boss);

        return boss;
    }


    /**
     * 移除 Boss
     * @param boss 要移除的 Boss
     */
    removeBoss(boss: BossPlane) {
        Tools.arrRemove(this._bossArr, boss);
        boss.node.parent = null;
        boss.node.destroy();
    }

    /**
     * 更新游戏逻辑
     * @param deltaTime 每帧时间
     */
    updateGameLogic(deltaTime: number) {
        for (let i = 0; i < this._bossArr.length; i++) {
            const boss = this._bossArr[i];
            if (boss.removeAble) {
                this.removeBoss(boss);
                i--;
            } else {
                boss.updateGameLogic(deltaTime);
            }
        }
    }

    /**
     * 开始 Boss 战斗
     */
    bossFightStart() {
        for (const boss of this._bossArr) {
            if (!boss.isDead) {
                boss.startBattle();
                break;
            }
        }
    }

    /**
     * 获取所有 Boss
     */
    get bosses(): BossPlane[] {
        return this._bossArr;
    }

    /**
     * 检查是否所有 Boss 已结束
     */
    isBossOver(): boolean {
        return this._bossArr.length === 0;
    }

    /**
     * 检查是否所有 Boss 已死亡
     */
    isBossDead(): boolean {
        for (const boss of this._bossArr) {
            if (!boss.isDead) {
                return false;
            }
        }
        return true;
    }

    setAnimSpeed(speed: number) {
        for (const boss of this._bossArr) {
            boss.setAnimSpeed(speed);
        }
    }
}