import { _decorator, Graphics, Color, Node, Vec3, Sprite } from 'cc';
import { GizmoDrawer, RegisterGizmoDrawer } from './GizmoDrawer';
import { GizmoUtils } from './GizmoUtils';
import { GizmoManager } from './GizmoManager';
import { LevelEditorEventUI } from 'db://assets/editor/level/LevelEditorEventUI';

/**
 * Gizmo drawer for LevelEditorEventUI components
 * Draws visual debugging information for event triggers
 */
@RegisterGizmoDrawer
export class LevelEditorEventUIGizmo extends GizmoDrawer<LevelEditorEventUI> {

    public readonly componentType = LevelEditorEventUI;
    public readonly drawerName = "LevelEditorEventUIGizmo";

    // Display settings
    public iconSize: number = 24;

    // Track last cleanup time to avoid frequent cleanup calls
    private static lastCleanupTime: number = 0;
    private static cleanupInterval: number = 1000; // 1 second

    public drawGizmos(levelEvent: LevelEditorEventUI, graphics: Graphics, node: Node): void {
        const eventId = levelEvent.elemID || levelEvent.node.uuid;

        // Check if we need to create/update the icon
        const existingIcon = this.findIconNode(eventId);
        const hasTriggers = levelEvent.triggers && levelEvent.triggers.length > 0;

        if (hasTriggers && !existingIcon) {
            // Create icon if we have triggers but no icon yet
            this.createEventIcon(levelEvent, node.worldPosition);
        } else if (!hasTriggers && existingIcon) {
            // Remove icon if we no longer have triggers
            this.clearIconNodes(levelEvent);
        } else if (existingIcon) {
            // Just update position of existing icon
            existingIcon.setWorldPosition(node.worldPosition.x, node.worldPosition.y, 0);
        }

        // Only run cleanup periodically to avoid performance issues
        const now = Date.now();
        if (now - LevelEditorEventUIGizmo.lastCleanupTime > LevelEditorEventUIGizmo.cleanupInterval) {
            LevelEditorEventUIGizmo.removeUnusedIconNodes();
            LevelEditorEventUIGizmo.lastCleanupTime = now;
        }
    }

    /**
     * Find existing icon node for a specific EventUI by searching children
     */
    private findIconNode(eventId: string): Node | null {
        const gizmoInstance = GizmoManager.Instance;
        if (!gizmoInstance) return null;
        const gizmoParent = gizmoInstance.node;
        for (let i = 0; i < gizmoParent.children.length; i++) {
            const child = gizmoParent.children[i];
            // @ts-ignore
            if (child.pEventId === eventId) {
                return child;
            }
        }
        return null;
    }

    /**
     * Create a single icon for the EventUI
     */
    private createEventIcon(levelEvent: LevelEditorEventUI, worldPosition: Vec3): void {
        const eventId = levelEvent.elemID || levelEvent.node.uuid;

        // Double-check that we don't already have an icon (safety check)
        const existingIcon = this.findIconNode(eventId);
        if (existingIcon) {
            console.warn(`Icon already exists for EventUI ${eventId}, skipping creation`);
            return;
        }

        // Create the icon node
        const gizmoInstance = GizmoManager.Instance;
        if (!gizmoInstance) return;
        const iconNode = this.createIconNode("trigger.png", worldPosition.x, worldPosition.y, gizmoInstance.node);
        // Mark the node with eventId for later identification
        // @ts-ignore
        iconNode.pEventId = eventId;
    }

    /**
     * Clear icon nodes for a specific EventUI
     */
    private clearIconNodes(levelEvent: LevelEditorEventUI): void {
        const eventId = levelEvent.elemID || levelEvent.node.uuid;
        const iconNode = this.findIconNode(eventId);

        if (iconNode && iconNode.isValid) {
            iconNode.destroy();
        }
    }

    /**
     * Create an icon node with sprite
     */
    private createIconNode(iconName: string, worldX: number, worldY: number, parent: Node): Node {
        const iconNode = new Node(`Icon_${iconName}`);
        iconNode.setWorldPosition(worldX, worldY, 0);
        iconNode.setScale(1, 1, 1);
        parent.addChild(iconNode);

        const sprite = iconNode.addComponent(Sprite);

        // Load and apply sprite frame using GizmoManager
        GizmoManager.loadAndApplySpriteFrame(iconName, sprite);

        return iconNode;
    }

    public getPriority(): number {
        return 10; // Draw event gizmos with medium priority
    }

    /**
     * Configure display options
     */
    public configure(options: {
        iconSize?: number;
    }): void {
        if (options.iconSize !== undefined) this.iconSize = options.iconSize;
    }

    /**
     * Clear all icon nodes (useful for cleanup)
     * This method searches for all nodes with pEventId property and destroys them
     */
    public static clearAllIconNodes(): void {
        // find GizmoManager node
        const gizmoManager = GizmoManager.Instance;
        if (gizmoManager) {
            const gizmoParent = gizmoManager.node;
            const nodesToRemove: Node[] = [];

            for (let i = 0; i < gizmoParent.children.length; i++) {
                const child = gizmoParent.children[i];
                // @ts-ignore
                if (child.pEventId) {
                    nodesToRemove.push(child);
                }
            }

            nodesToRemove.forEach(node => {
                if (node.isValid) {
                    node.destroy();
                }
            });
        }
    }

    public static removeUnusedIconNodes(): void {
        const gizmoManager = GizmoManager.Instance;
        if (!gizmoManager) return;

        // Find all LevelEditorEventUI components in the scene
        const scene = gizmoManager.node.scene;
        if (!scene) return;

        const allLevelEventUI = scene.getComponentsInChildren(LevelEditorEventUI);
        const usedEventIds = new Set<string>();

        // Collect all valid event IDs
        allLevelEventUI.forEach(eventUI => {
            const eventId = eventUI.elemID || eventUI.node.uuid;
            if (eventId) {
                usedEventIds.add(eventId);
            }
        });

        // Find and remove unused icon nodes (iterate backwards to avoid index issues)
        const gizmoParent = gizmoManager.node;
        const nodesToRemove: Node[] = [];

        for (let i = 0; i < gizmoParent.children.length; i++) {
            const child = gizmoParent.children[i];
            // @ts-ignore
            const pEventId = child.pEventId;
            if (!pEventId || !usedEventIds.has(pEventId)) {
                nodesToRemove.push(child);
            }
        }

        // Remove the nodes
        nodesToRemove.forEach(node => {
            if (node.isValid) {
                node.destroy();
            }
        });
    }

    /**
     * Called when the drawer is unregistered - cleanup resources
     */
    public onUnregister(): void {
        LevelEditorEventUIGizmo.clearAllIconNodes();
    }
}
