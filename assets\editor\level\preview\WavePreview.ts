import { _decorator, Node, Prefab, CCBoolean, CCFloat, CCInteger, Component, Vec2, instantiate } from 'cc';
const { ccclass, property, executeInEditMode, menu } = _decorator;

import { WaveData, eSpawnOrder, eWaveCompletion } from 'db://assets/bundles/common/script/game/data/WaveData';
import { Wave } from 'db://assets/bundles/common/script/game/wave/Wave';
import { LubanMgr } from 'db://assets/bundles/common/script/luban/LubanMgr';
import { LevelEditorUtils } from '../utils';

/// 用来创建和管理波次的所有飞机对象
@ccclass('WavePreview')
@menu("怪物/编辑器/波次预览")
@executeInEditMode()
export class WavePreview extends Component {

    private static _instance: WavePreview|null = null;
    public static get instance(): WavePreview|null {
        return this._instance;
    }

    private _luban: LubanMgr|null = null;
    public get luban(): LubanMgr|null {
        if (this._luban == null) {
            this._luban = new LubanMgr();
        }
        return this._luban;
    }

    private _bindingMap: Map<Wave, Node[]> = new Map();

    onLoad() {
        if (WavePreview._instance == null) {
            WavePreview._instance = this;
        } else {
            console.warn("WavePreview multiple instance");
        }
    }

    reset() {
        this.node.removeAllChildren();
        this._bindingMap.clear();
    }

    update(dt: number) {
        this._bindingMap.forEach((nodes, wave) => {
            this.updateWave(wave, nodes);
        });
    }

    setupWave(wave: Wave) {
        const waveData = wave.waveData;
        if (waveData.spawnGroup && waveData.spawnGroup.length > 0) {
            let planeId = 0;
            for (let i = 0; i < waveData.spawnGroup.length; i++) {
                if (waveData.spawnGroup[i].planeID > 0) {
                    planeId = waveData.spawnGroup[i].planeID;
                    break;
                }
            }

            if (planeId == 0) {
                console.warn("WavePreview createPlane no valid planeId in spawnGroup");
                return;
            }

            if (this.luban?.table == null) {
                this.luban?.initInEditor().then(() => {
                    this.createPlane(wave, planeId);
                });
            }
        }
    }

    private createPlane(wave: Wave, planeId: number) {
        const planeData = this.luban?.table.TbResEnemy.get(planeId);
        if (planeData == null) {
            console.warn("WavePreview createPlane no planeData for id:", planeId);
            return;
        }

        const fullPath = "db://assets/resources/" + planeData.prefab + ".prefab";
        LevelEditorUtils.loadByPath<Prefab>(fullPath).then((prefab) => {
            if (prefab) {
                const node = instantiate(prefab);
                if (node) {
                    this.node.addChild(node);
                    let nodes = this._bindingMap.get(wave);
                    if (nodes == null) {
                        nodes = [];
                        this._bindingMap.set(wave, nodes);
                    }
                    nodes.push(node);
                }
            }
        });
    }

    updateWave(wave: Wave, nodes: Node[]) {
        const wavePos = wave.node.worldPosition;
        const waveData = wave.waveData;
        const nodePos = new Vec2(wavePos.x + waveData.spawnPosX.eval(), wavePos.y + waveData.spawnPosY.eval());
        const nodeAngle = waveData.spawnAngle.eval();

        for (let i = 0; i < nodes.length; i++) {
            const node = nodes[i];
            node.setWorldPosition(nodePos.x, nodePos.y, 0);
            node.setRotationFromEuler(0, 0, nodeAngle);
        }
    }
}