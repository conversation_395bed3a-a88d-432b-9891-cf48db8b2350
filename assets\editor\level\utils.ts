import { __private, _decorator, assetManager, Asset, CCFloat, CCInteger, Component, Enum, Node, Prefab } from "cc";
import { LayerSplicingMode, LayerType } from "db://assets/bundles/common/script/leveldata/leveldata";

const { ccclass, property } = _decorator;

export enum LayerTypeZh {
    背景 = LayerType.Background,
    随机 = LayerType.Random,
    滚动 = LayerType.Scroll,
    发射组 = LayerType.Emittier
}

enum LayerSplicingModeZh {
    节点高度拼接 = LayerSplicingMode.node_height,
    固定高度拼接 = LayerSplicingMode.fix_height,
    随机高度拼接 = LayerSplicingMode.random_height
}

@ccclass('LayerRandomRange')
export class LayerRandomRange {
    @property({ displayName: "最小值" })
    public min: number = 0;
    
    @property({ displayName: "最大值" })
    max: number = 0;

    constructor(min: number = 0, max: number = 0) {
        this.min = min;
        this.max = max;
    }
}

@ccclass('LevelEditorScrollLayerUI')
export class LevelScrollLayerUI { 
    @property({type: [Prefab], displayName: '滚动体'})
    public scrollPrefabs: Prefab[] = [];
    @property({type: CCInteger, displayName: "权重"})
    public weight: number = 100;

    @property({visible: false})
    public splicingMode: LayerSplicingMode = LayerSplicingMode.node_height;
    @property({type: Enum(LayerSplicingModeZh), displayName:"拼接模式",})
    public get splicingModeZh(): LayerSplicingModeZh { return this.splicingMode as unknown as LayerSplicingModeZh;}
    public set splicingModeZh(value: LayerSplicingModeZh) { this.splicingMode = value as unknown as LayerSplicingMode; }
    @property({type: LayerRandomRange, displayName:"X偏移范围",
        visible: function(this: LevelScrollLayerUI) {
            return this.splicingMode === LayerSplicingMode.random_height;
        }
    })
    
    public splicingOffsetX: LayerRandomRange = new LayerRandomRange(); 
    @property({type: LayerRandomRange, displayName:"Y偏移范围",
        visible: function(this: LevelScrollLayerUI) {
            return this.splicingMode === LayerSplicingMode.random_height;
        }
    })
    public splicingOffsetY: LayerRandomRange = new LayerRandomRange(); 
}

@ccclass('LevelEditorRandTerrainUI')
export class LevelRandTerrainUI {
    @property({type: CCInteger, displayName: "权重"})
    public weight: number = 100;

    @property({type: Prefab, displayName: "地形组预制体"})
    public terrainElement: Prefab | null = null; // 可以是TerrainElem、DynamicTerrains的预制体
}

@ccclass('LevelEditorRandTerrainsLayerUI')
export class LevelRandTerrainsLayerUI {
    @property({type: CCInteger, displayName: "权重"})
    public weight: number = 100;

    @property({type: [LevelRandTerrainUI], displayName: "地形策略"})
    public dynamicTerrain: LevelRandTerrainUI[] = []; 
}

@ccclass('LevelEditorRandTerrainsLayersUI')
export class LevelRandTerrainsLayersUI {
    @property({type: [LevelRandTerrainsLayerUI], displayName: "地形策略组"})
    public dynamicTerrains: LevelRandTerrainsLayerUI[] = []; 
}

@ccclass('LevelEditorEmittierLayerUI')
export class LevelEmittierLayerUI {
    @property({type: [Prefab], displayName: "发射体"})
    public emittierPrefabs: Prefab[] = [];
}

@ccclass('LevelEditorLayer')
export class LevelLayer {
    @property({displayName: "备注"})
    public remark: string = "";
    @property({type: CCInteger, displayName: "层级顺序",})
    public zIndex: number = 0;
    @property({type: Node, group: "层级信息"})
    public node: Node | null = null;
    @property({type:CCFloat, displayName:"速度", group: "层级信息"})
    public speed: number = 10;
    @property({type: Enum(LayerTypeZh), displayName:"地形类型", group: "地形信息"})
    public get typeZh(): LayerTypeZh { return this.type as unknown as LayerTypeZh;}
    public set typeZh(value: LayerTypeZh) { this.type = value as unknown as LayerType;}
    public type: LayerType = LayerType.Background;
    @property({ type: [LevelScrollLayerUI], displayName: "滚动组", group: "地形信息",
        visible: function(this: LevelLayer) {
            return this.type === LayerType.Scroll;
        }
    })
    public scrollLayers: LevelScrollLayerUI[] = [];

    @property({type: [LevelRandTerrainsLayersUI], displayName:"随机组", group: "地形信息",
        visible: function(this: LevelLayer) {
            return this.type === LayerType.Random;
        },
    })
    public randomLayers: LevelRandTerrainsLayersUI[] = [];
}

@ccclass('LevelEditorBackgroundLayer')
export class LevelBackgroundLayer extends LevelLayer {
    @property({type: [Prefab], displayName: '背景组',group: "地形信息"})
    public backgrounds: Prefab[] = [];

    public backgroundsNode: Node|null = null;
}

export class LevelEditorUtils {
    public static getOrAddNode(node_parent: Node, name: string): Node {
        var node = node_parent!.getChildByName(name);
        if (node == null) {
            node = new Node(name);
            node_parent.addChild(node);
        }
        return node;
    }

    public static getOrAddComp<T extends Component>(node: Node, classConstructor: __private.__types_globals__Constructor<T>): T {
        var comp = node.getComponent(classConstructor);
        if (comp == null) {
            comp = node.addComponent(classConstructor);
        }
        return comp;
    }
    
    // 仅在编辑器中使用
    public static async loadByPath<T extends Asset>(path: string): Promise<T|null> {
        if (path == null || path == "") {
            return Promise.resolve(null);
        }
        
        // @ts-ignore
        const uuid = await Editor.Message.request('asset-db', 'query-uuid', path);
        return new Promise<T | null>((resolve) => {
            assetManager.loadAny<T>(uuid, (err, asset: T) => {
                if (err) {
                    console.warn(`Failed to load by path: ${path}`, err);
                    resolve(null);
                    return;
                }
                resolve(asset);
            });
        });
    }
}

