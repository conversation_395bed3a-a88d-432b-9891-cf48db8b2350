[{"__type__": "cc.Prefab", "_name": "wave_1", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "wave_1", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [], "_active": true, "_components": [{"__id__": 2}], "_prefab": {"__id__": 12}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "194feuuQpZAtYZElOT4+NCI", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 3}, "waveData": {"__id__": 4}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "79jIYdhXpCTKOCBcNLg1bY"}, {"__type__": "WaveData", "spawnGroup": [{"__id__": 5}, {"__id__": 6}], "spawnOrder": 1, "waveCompletion": 1, "waveCompletionParam": {"__id__": 7}, "spawnInterval": {"__id__": 8}, "spawnPosX": {"__id__": 9}, "spawnPosY": {"__id__": 10}, "spawnAngle": {"__id__": 11}, "eventGroupData": []}, {"__type__": "SpawnGroup", "planeID": 1, "weight": 50}, {"__type__": "SpawnGroup", "planeID": 1, "weight": 50}, {"__type__": "ExpressionValue", "value": 5, "isExpression": false, "expression": "5", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 1000, "isExpression": false, "expression": "1000", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 0, "isExpression": false, "expression": "0", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 500, "isExpression": false, "expression": "500", "serializedProgram": null}, {"__type__": "ExpressionValue", "value": 270, "isExpression": false, "expression": "270", "serializedProgram": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": null, "targetOverrides": null}]