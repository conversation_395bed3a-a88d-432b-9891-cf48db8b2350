2025-9-16 19:08:55-log: Cannot access game frame or container.
2025-9-16 19:08:55-debug: asset-db:require-engine-code (433ms)
2025-9-16 19:08:55-log: meshopt wasm decoder initialized
2025-9-16 19:08:55-log: [bullet]:bullet wasm lib loaded.
2025-9-16 19:08:55-log: [box2d]:box2d wasm lib loaded.
2025-9-16 19:08:55-log: Cocos Creator v3.8.6
2025-9-16 19:08:55-log: Using legacy pipeline
2025-9-16 19:08:55-debug: [Assets Memory track]: asset-db:worker-init: initEngine start:30.92MB, end 80.03MB, increase: 49.11MB
2025-9-16 19:08:55-debug: [Assets Memory track]: asset-db-plugin-register: programming start:80.94MB, end 84.33MB, increase: 3.39MB
2025-9-16 19:08:56-debug: [Assets Memory track]: asset-db-plugin-register: builder start:84.37MB, end 290.60MB, increase: 206.23MB
2025-9-16 19:08:55-log: Forward render pipeline initialized.
2025-9-16 19:08:56-debug: [Assets Memory track]: asset-db-plugin-register: project start:80.80MB, end 289.22MB, increase: 208.43MB
2025-9-16 19:08:56-debug: [Assets Memory track]: asset-db:worker-init: initPlugin start:80.06MB, end 289.59MB, increase: 209.53MB
2025-9-16 19:08:56-debug: run package(google-play) handler(enable) start
2025-9-16 19:08:56-debug: run package(google-play) handler(enable) success!
2025-9-16 19:08:56-debug: run package(harmonyos-next) handler(enable) start
2025-9-16 19:08:56-debug: run package(harmonyos-next) handler(enable) success!
2025-9-16 19:08:56-debug: run package(huawei-agc) handler(enable) start
2025-9-16 19:08:56-debug: run package(honor-mini-game) handler(enable) start
2025-9-16 19:08:56-debug: run package(honor-mini-game) handler(enable) success!
2025-9-16 19:08:56-debug: run package(huawei-agc) handler(enable) success!
2025-9-16 19:08:56-debug: run package(huawei-quick-game) handler(enable) success!
2025-9-16 19:08:56-debug: run package(ios) handler(enable) success!
2025-9-16 19:08:56-debug: run package(ios) handler(enable) start
2025-9-16 19:08:56-debug: run package(huawei-quick-game) handler(enable) start
2025-9-16 19:08:56-debug: run package(linux) handler(enable) success!
2025-9-16 19:08:56-debug: run package(linux) handler(enable) start
2025-9-16 19:08:56-debug: run package(mac) handler(enable) start
2025-9-16 19:08:56-debug: run package(migu-mini-game) handler(enable) start
2025-9-16 19:08:56-debug: run package(migu-mini-game) handler(enable) success!
2025-9-16 19:08:56-debug: run package(native) handler(enable) start
2025-9-16 19:08:56-debug: run package(mac) handler(enable) success!
2025-9-16 19:08:56-debug: run package(native) handler(enable) success!
2025-9-16 19:08:56-debug: run package(ohos) handler(enable) start
2025-9-16 19:08:56-debug: run package(ohos) handler(enable) success!
2025-9-16 19:08:56-debug: run package(oppo-mini-game) handler(enable) success!
2025-9-16 19:08:56-debug: run package(oppo-mini-game) handler(enable) start
2025-9-16 19:08:56-debug: run package(runtime-dev-tools) handler(enable) success!
2025-9-16 19:08:56-debug: run package(taobao-mini-game) handler(enable) start
2025-9-16 19:08:56-debug: run package(runtime-dev-tools) handler(enable) start
2025-9-16 19:08:56-debug: run package(vivo-mini-game) handler(enable) success!
2025-9-16 19:08:56-debug: run package(web-desktop) handler(enable) success!
2025-9-16 19:08:56-debug: run package(vivo-mini-game) handler(enable) start
2025-9-16 19:08:56-debug: run package(taobao-mini-game) handler(enable) success!
2025-9-16 19:08:56-debug: run package(web-mobile) handler(enable) start
2025-9-16 19:08:56-debug: run package(web-desktop) handler(enable) start
2025-9-16 19:08:56-debug: run package(web-mobile) handler(enable) success!
2025-9-16 19:08:56-debug: run package(wechatgame) handler(enable) success!
2025-9-16 19:08:56-debug: run package(wechatgame) handler(enable) start
2025-9-16 19:08:56-debug: run package(wechatprogram) handler(enable) start
2025-9-16 19:08:56-debug: run package(windows) handler(enable) start
2025-9-16 19:08:56-debug: run package(windows) handler(enable) success!
2025-9-16 19:08:56-debug: run package(xiaomi-quick-game) handler(enable) start
2025-9-16 19:08:56-debug: run package(xiaomi-quick-game) handler(enable) success!
2025-9-16 19:08:56-debug: run package(wechatprogram) handler(enable) success!
2025-9-16 19:08:56-debug: run package(im-plugin) handler(enable) start
2025-9-16 19:08:56-debug: run package(cocos-service) handler(enable) start
2025-9-16 19:08:56-debug: run package(cocos-service) handler(enable) success!
2025-9-16 19:08:56-debug: run package(fix-asset-default-userdata) handler(enable) start
2025-9-16 19:08:56-debug: asset-db:worker-init: initPlugin (1045ms)
2025-9-16 19:08:56-debug: run package(fix-asset-default-userdata) handler(enable) success!
2025-9-16 19:08:56-debug: run package(im-plugin) handler(enable) success!
2025-9-16 19:08:56-debug: run package(emitter-editor) handler(enable) success!
2025-9-16 19:08:56-debug: run package(emitter-editor) handler(enable) start
2025-9-16 19:08:56-debug: [Assets Memory track]: asset-db:worker-init start:30.91MB, end 291.47MB, increase: 260.55MB
2025-9-16 19:08:56-debug: Run asset db hook programming:beforePreStart ...
2025-9-16 19:08:56-debug: Run asset db hook programming:beforePreStart success!
2025-9-16 19:08:56-debug: Run asset db hook engine-extends:beforePreStart success!
2025-9-16 19:08:56-debug: Run asset db hook engine-extends:beforePreStart ...
2025-9-16 19:08:56-debug: Preimport db internal success
2025-9-16 19:08:56-debug: run package(localization-editor) handler(enable) success!
2025-9-16 19:08:56-debug: asset-db:worker-init (1689ms)
2025-9-16 19:08:56-debug: run package(localization-editor) handler(enable) start
2025-9-16 19:08:56-debug: asset-db-hook-engine-extends-beforePreStart (141ms)
2025-9-16 19:08:56-debug: asset-db-hook-programming-beforePreStart (141ms)
2025-9-16 19:08:56-debug: run package(wave-editor) handler(enable) start
2025-9-16 19:08:56-debug: run package(wave-editor) handler(enable) success!
2025-9-16 19:08:56-debug: Preimport db assets success
2025-9-16 19:08:56-debug: Run asset db hook programming:afterPreStart ...
2025-9-16 19:08:56-debug: starting packer-driver...
2025-9-16 19:08:56-debug: run package(placeholder) handler(enable) start
2025-9-16 19:08:56-debug: run package(placeholder) handler(enable) success!
2025-9-16 19:09:02-debug: initialize scripting environment...
2025-9-16 19:09:02-debug: [[Executor]] prepare before lock
2025-9-16 19:09:02-debug: Set detail map pack:///resolution-detail-map.json: {}
2025-9-16 19:09:02-debug: [[Executor]] prepare after unlock
2025-9-16 19:09:02-debug: Run asset db hook programming:afterPreStart success!
2025-9-16 19:09:02-debug: Run asset db hook engine-extends:afterPreStart success!
2025-9-16 19:09:02-debug: Run asset db hook engine-extends:afterPreStart ...
2025-9-16 19:09:02-debug: Start up the 'internal' database...
2025-9-16 19:09:02-debug: asset-db-hook-programming-afterPreStart (5379ms)
2025-9-16 19:09:02-debug: asset-db:worker-effect-data-processing (231ms)
2025-9-16 19:09:02-debug: asset-db-hook-engine-extends-afterPreStart (231ms)
2025-9-16 19:09:02-debug: Start up the 'assets' database...
2025-9-16 19:09:02-debug: asset-db:worker-startup-database[internal] (5710ms)
2025-9-16 19:09:02-debug: lazy register asset handler *
2025-9-16 19:09:02-debug: [Assets Memory track]: asset-db:worker-init: startup start:175.53MB, end 192.37MB, increase: 16.84MB
2025-9-16 19:09:02-debug: lazy register asset handler directory
2025-9-16 19:09:02-debug: lazy register asset handler json
2025-9-16 19:09:02-debug: lazy register asset handler spine-data
2025-9-16 19:09:02-debug: lazy register asset handler dragonbones
2025-9-16 19:09:02-debug: lazy register asset handler text
2025-9-16 19:09:02-debug: lazy register asset handler dragonbones-atlas
2025-9-16 19:09:02-debug: lazy register asset handler typescript
2025-9-16 19:09:02-debug: lazy register asset handler javascript
2025-9-16 19:09:02-debug: lazy register asset handler terrain
2025-9-16 19:09:02-debug: lazy register asset handler tiled-map
2025-9-16 19:09:02-debug: lazy register asset handler buffer
2025-9-16 19:09:02-debug: lazy register asset handler image
2025-9-16 19:09:02-debug: lazy register asset handler prefab
2025-9-16 19:09:02-debug: lazy register asset handler scene
2025-9-16 19:09:02-debug: lazy register asset handler sign-image
2025-9-16 19:09:02-debug: lazy register asset handler sprite-frame
2025-9-16 19:09:02-debug: lazy register asset handler alpha-image
2025-9-16 19:09:02-debug: lazy register asset handler texture
2025-9-16 19:09:02-debug: lazy register asset handler render-texture
2025-9-16 19:09:02-debug: lazy register asset handler texture-cube-face
2025-9-16 19:09:02-debug: lazy register asset handler rt-sprite-frame
2025-9-16 19:09:02-debug: lazy register asset handler gltf
2025-9-16 19:09:02-debug: lazy register asset handler gltf-mesh
2025-9-16 19:09:02-debug: lazy register asset handler texture-cube
2025-9-16 19:09:02-debug: lazy register asset handler gltf-animation
2025-9-16 19:09:02-debug: lazy register asset handler gltf-skeleton
2025-9-16 19:09:02-debug: lazy register asset handler gltf-material
2025-9-16 19:09:02-debug: lazy register asset handler erp-texture-cube
2025-9-16 19:09:02-debug: lazy register asset handler fbx
2025-9-16 19:09:02-debug: lazy register asset handler gltf-embeded-image
2025-9-16 19:09:02-debug: lazy register asset handler gltf-scene
2025-9-16 19:09:02-debug: lazy register asset handler physics-material
2025-9-16 19:09:02-debug: lazy register asset handler effect
2025-9-16 19:09:02-debug: lazy register asset handler effect-header
2025-9-16 19:09:02-debug: lazy register asset handler audio-clip
2025-9-16 19:09:02-debug: lazy register asset handler animation-clip
2025-9-16 19:09:02-debug: lazy register asset handler animation-graph-variant
2025-9-16 19:09:02-debug: lazy register asset handler material
2025-9-16 19:09:02-debug: lazy register asset handler animation-graph
2025-9-16 19:09:02-debug: lazy register asset handler animation-mask
2025-9-16 19:09:02-debug: lazy register asset handler bitmap-font
2025-9-16 19:09:02-debug: lazy register asset handler sprite-atlas
2025-9-16 19:09:02-debug: lazy register asset handler ttf-font
2025-9-16 19:09:02-debug: lazy register asset handler particle
2025-9-16 19:09:02-debug: lazy register asset handler label-atlas
2025-9-16 19:09:02-debug: lazy register asset handler render-stage
2025-9-16 19:09:02-debug: lazy register asset handler auto-atlas
2025-9-16 19:09:02-debug: lazy register asset handler render-pipeline
2025-9-16 19:09:02-debug: lazy register asset handler render-flow
2025-9-16 19:09:02-debug: lazy register asset handler instantiation-material
2025-9-16 19:09:02-debug: lazy register asset handler instantiation-skeleton
2025-9-16 19:09:02-debug: lazy register asset handler instantiation-mesh
2025-9-16 19:09:02-debug: lazy register asset handler video-clip
2025-9-16 19:09:02-debug: lazy register asset handler instantiation-animation
2025-9-16 19:09:02-debug: asset-db:worker-startup-database[assets] (5672ms)
2025-9-16 19:09:02-debug: asset-db:start-database (5791ms)
2025-9-16 19:09:02-debug: fix the bug of updateDefaultUserData
2025-9-16 19:09:02-debug: asset-db:ready (9260ms)
2025-9-16 19:09:02-debug: init worker message success
2025-9-16 19:09:02-debug: programming:execute-script (4ms)
2025-9-16 19:09:02-debug: [Build Memory track]: builder:worker-init start:196.16MB, end 208.75MB, increase: 12.60MB
2025-9-16 19:09:02-debug: builder:worker-init (308ms)
2025-9-16 19:11:13-debug: refresh db internal success
2025-9-16 19:11:13-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\utils.ts
background: #aaff85; color: #000;
color: #000;
2025-9-16 19:11:13-debug: refresh db assets success
2025-9-16 19:11:13-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-16 19:11:13-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-16 19:11:13-debug: asset-db:refresh-all-database (191ms)
2025-9-16 19:11:13-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-16 19:11:13-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 09:24:17-debug: refresh db internal success
2025-9-17 09:24:17-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorEventUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:24:17-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\luban\LubanMgr.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:24:17-debug: refresh db assets success
2025-9-17 09:24:17-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 09:24:17-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 09:24:17-debug: asset-db:refresh-all-database (247ms)
2025-9-17 09:24:17-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-17 09:24:17-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-9-17 09:24:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:24:18-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (12ms)
2025-9-17 09:25:40-debug: refresh db internal success
2025-9-17 09:25:40-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\luban\LubanMgr.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:25:41-debug: refresh db assets success
2025-9-17 09:25:41-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 09:25:41-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 09:25:41-debug: asset-db:refresh-all-database (172ms)
2025-9-17 09:25:41-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 09:25:41-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-17 09:25:41-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:25:41-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (9ms)
2025-9-17 09:25:53-debug: refresh db internal success
2025-9-17 09:25:53-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\luban\LubanMgr.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:25:53-debug: refresh db assets success
2025-9-17 09:25:53-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 09:25:53-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 09:25:53-debug: asset-db:refresh-all-database (164ms)
2025-9-17 09:25:53-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 09:25:53-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-17 09:25:53-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:25:53-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (14ms)
2025-9-17 09:33:04-debug: refresh db internal success
2025-9-17 09:33:04-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorEventUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:33:04-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\luban\LubanMgr.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:33:04-debug: refresh db assets success
2025-9-17 09:33:04-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 09:33:04-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 09:33:04-debug: asset-db:refresh-all-database (164ms)
2025-9-17 09:33:05-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:33:05-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (4ms)
2025-9-17 09:33:17-debug: refresh db internal success
2025-9-17 09:33:17-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\luban\LubanMgr.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:33:17-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 09:33:17-debug: refresh db assets success
2025-9-17 09:33:17-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 09:33:17-debug: asset-db:refresh-all-database (131ms)
2025-9-17 09:33:17-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:33:17-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (4ms)
2025-9-17 09:33:29-debug: refresh db internal success
2025-9-17 09:33:29-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\luban\LubanMgr.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:33:29-debug: refresh db assets success
2025-9-17 09:33:29-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 09:33:29-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 09:33:29-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 09:33:29-debug: asset-db:refresh-all-database (134ms)
2025-9-17 09:33:29-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 09:33:30-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:33:30-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (3ms)
2025-9-17 09:33:39-debug: refresh db internal success
2025-9-17 09:33:39-debug: refresh db assets success
2025-9-17 09:33:39-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 09:33:39-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 09:33:39-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 09:33:39-debug: asset-db:refresh-all-database (114ms)
2025-9-17 09:33:39-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 09:36:38-debug: refresh db internal success
2025-9-17 09:36:38-debug: refresh db assets success
2025-9-17 09:36:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 09:36:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 09:36:38-debug: asset-db:refresh-all-database (194ms)
2025-9-17 09:36:38-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 09:36:38-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 09:37:18-debug: refresh db internal success
2025-9-17 09:37:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbresloot.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:37:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbresupgrade.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:37:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:37:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbequipupgrade.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:37:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbgm.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:37:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbresachievement.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:37:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbglobalattr.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:37:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbresactivity.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:37:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbplane.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:37:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbresbullet.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:37:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbresbuffer.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:37:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbreschapter.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:37:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbreseffect.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:37:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbreslevel.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:37:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbresgamemode.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:37:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbresequip.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:37:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbresenemy.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:37:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbresitem.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:37:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbreslevelgroup.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:37:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbresskill.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:37:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbresstage.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:37:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbrestaskorbit.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:37:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbrestask.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:37:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbreswave.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:37:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbrestrack.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:37:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\autogen\luban
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:37:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\autogen\luban\schema.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:37:19-debug: refresh db assets success
2025-9-17 09:37:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 09:37:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 09:37:19-debug: asset-db:refresh-all-database (162ms)
2025-9-17 09:37:19-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-17 09:37:19-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-17 09:37:19-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:37:19-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (5ms)
2025-9-17 09:37:24-debug: refresh db internal success
2025-9-17 09:37:24-debug: refresh db assets success
2025-9-17 09:37:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 09:37:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 09:37:24-debug: asset-db:refresh-all-database (115ms)
2025-9-17 09:37:24-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 09:37:24-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 09:37:25-debug: refresh db internal success
2025-9-17 09:37:25-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 09:37:25-debug: refresh db assets success
2025-9-17 09:37:25-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 09:37:25-debug: asset-db:refresh-all-database (124ms)
2025-9-17 09:37:25-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-17 09:37:25-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-17 09:38:12-debug: refresh db internal success
2025-9-17 09:38:12-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\utils.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:38:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 09:38:12-debug: refresh db assets success
2025-9-17 09:38:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 09:38:12-debug: asset-db:refresh-all-database (164ms)
2025-9-17 09:38:12-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 09:38:12-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 09:38:12-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:38:12-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (6ms)
2025-9-17 09:38:45-debug: refresh db internal success
2025-9-17 09:38:45-debug: refresh db assets success
2025-9-17 09:38:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 09:38:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 09:38:45-debug: asset-db:refresh-all-database (148ms)
2025-9-17 09:38:45-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 09:38:45-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 09:39:02-debug: refresh db internal success
2025-9-17 09:39:02-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorEventUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:39:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 09:39:02-debug: refresh db assets success
2025-9-17 09:39:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 09:39:02-debug: asset-db:refresh-all-database (159ms)
2025-9-17 09:39:02-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 09:39:02-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 09:39:02-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:39:02-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (6ms)
2025-9-17 09:39:30-debug: refresh db internal success
2025-9-17 09:39:30-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorEventUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:39:30-debug: refresh db assets success
2025-9-17 09:39:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 09:39:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 09:39:30-debug: asset-db:refresh-all-database (165ms)
2025-9-17 09:39:30-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 09:39:30-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 09:39:30-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:39:30-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (5ms)
2025-9-17 09:39:54-debug: refresh db internal success
2025-9-17 09:39:54-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorEventUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:39:54-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 09:39:54-debug: refresh db assets success
2025-9-17 09:39:54-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 09:39:54-debug: asset-db:refresh-all-database (160ms)
2025-9-17 09:39:54-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-17 09:39:54-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:39:54-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (5ms)
2025-9-17 09:40:17-debug: refresh db internal success
2025-9-17 09:40:17-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorEventUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:40:17-debug: refresh db assets success
2025-9-17 09:40:17-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 09:40:17-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 09:40:17-debug: asset-db:refresh-all-database (166ms)
2025-9-17 09:40:17-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 09:40:17-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 09:40:17-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:40:17-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (25ms)
2025-9-17 09:41:23-debug: refresh db internal success
2025-9-17 09:41:23-debug: refresh db assets success
2025-9-17 09:41:23-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 09:41:23-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 09:41:23-debug: asset-db:refresh-all-database (145ms)
2025-9-17 09:41:23-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 09:50:01-debug: refresh db internal success
2025-9-17 09:50:01-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorEventUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:50:01-debug: refresh db assets success
2025-9-17 09:50:01-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 09:50:01-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 09:50:01-debug: asset-db:refresh-all-database (166ms)
2025-9-17 09:50:01-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 09:50:01-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 09:50:01-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:50:01-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (4ms)
2025-9-17 09:50:31-debug: refresh db internal success
2025-9-17 09:50:31-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\utils.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:50:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 09:50:32-debug: refresh db assets success
2025-9-17 09:50:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 09:50:32-debug: asset-db:refresh-all-database (165ms)
2025-9-17 09:50:32-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:50:32-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (8ms)
2025-9-17 09:50:38-debug: refresh db internal success
2025-9-17 09:50:38-debug: refresh db assets success
2025-9-17 09:50:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 09:50:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 09:50:38-debug: asset-db:refresh-all-database (116ms)
2025-9-17 09:50:38-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 09:51:39-debug: refresh db internal success
2025-9-17 09:51:39-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\utils.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:51:39-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\luban\LubanMgr.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:51:39-debug: refresh db assets success
2025-9-17 09:51:39-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 09:51:39-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 09:51:39-debug: asset-db:refresh-all-database (121ms)
2025-9-17 09:51:39-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:51:39-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (7ms)
2025-9-17 09:52:07-debug: refresh db internal success
2025-9-17 09:52:07-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorEventUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:52:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 09:52:07-debug: refresh db assets success
2025-9-17 09:52:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 09:52:07-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 09:52:07-debug: asset-db:refresh-all-database (164ms)
2025-9-17 09:52:07-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 09:52:07-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:52:07-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (5ms)
2025-9-17 09:52:12-debug: refresh db internal success
2025-9-17 09:52:12-debug: refresh db assets success
2025-9-17 09:52:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 09:52:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 09:52:12-debug: asset-db:refresh-all-database (115ms)
2025-9-17 09:52:12-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 09:52:12-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 09:52:38-debug: refresh db internal success
2025-9-17 09:52:38-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorEventUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:52:38-debug: refresh db assets success
2025-9-17 09:52:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 09:52:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 09:52:38-debug: asset-db:refresh-all-database (169ms)
2025-9-17 09:52:38-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 09:52:38-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 09:52:39-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 09:52:39-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (4ms)
2025-9-17 09:52:49-debug: refresh db internal success
2025-9-17 09:52:49-debug: refresh db assets success
2025-9-17 09:52:49-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 09:52:49-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 09:52:49-debug: asset-db:refresh-all-database (116ms)
2025-9-17 09:52:49-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 10:12:18-debug: refresh db internal success
2025-9-17 10:12:18-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorEventUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:12:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\luban\LubanMgr.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:12:18-debug: refresh db assets success
2025-9-17 10:12:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:12:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:12:18-debug: asset-db:refresh-all-database (192ms)
2025-9-17 10:12:18-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 10:12:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:12:18-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (6ms)
2025-9-17 10:14:55-debug: refresh db internal success
2025-9-17 10:14:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\luban\LubanMgr.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:14:55-debug: refresh db assets success
2025-9-17 10:14:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:14:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:14:55-debug: asset-db:refresh-all-database (187ms)
2025-9-17 10:14:55-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 10:14:55-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 10:14:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:14:55-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (6ms)
2025-9-17 10:19:37-debug: refresh db internal success
2025-9-17 10:19:37-debug: refresh db assets success
2025-9-17 10:19:37-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:19:37-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:19:37-debug: asset-db:refresh-all-database (121ms)
2025-9-17 10:19:37-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 10:19:37-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 10:19:42-debug: refresh db internal success
2025-9-17 10:19:42-debug: refresh db assets success
2025-9-17 10:19:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:19:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:19:42-debug: asset-db:refresh-all-database (114ms)
2025-9-17 10:22:03-debug: refresh db internal success
2025-9-17 10:22:03-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\luban\LubanMgr.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:22:04-debug: refresh db assets success
2025-9-17 10:22:04-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:22:04-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:22:04-debug: asset-db:refresh-all-database (162ms)
2025-9-17 10:22:04-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:22:04-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (4ms)
2025-9-17 10:22:42-debug: refresh db internal success
2025-9-17 10:22:42-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\luban\LubanMgr.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:22:42-debug: refresh db assets success
2025-9-17 10:22:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:22:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:22:42-debug: asset-db:refresh-all-database (160ms)
2025-9-17 10:22:42-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:22:42-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (6ms)
2025-9-17 10:22:52-debug: refresh db internal success
2025-9-17 10:22:52-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\luban\LubanMgr.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:22:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:22:52-debug: refresh db assets success
2025-9-17 10:22:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:22:52-debug: asset-db:refresh-all-database (175ms)
2025-9-17 10:22:52-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:22:52-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (3ms)
2025-9-17 10:23:53-debug: refresh db internal success
2025-9-17 10:23:53-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\luban\LubanMgr.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:23:53-debug: refresh db assets success
2025-9-17 10:23:53-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:23:53-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:23:53-debug: asset-db:refresh-all-database (129ms)
2025-9-17 10:23:53-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 10:23:53-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:23:53-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (4ms)
2025-9-17 10:25:18-debug: refresh db internal success
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\feiji-qietu
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\shadow
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\mask
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\below
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\firePoint
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\mainblast
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\relief
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\skill
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\wine\crazy
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\wine\daodan
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\wine\dun
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\wine
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\wine\fire
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\wine\hp
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\Land\Land_Clouds
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\Ocean\Ocean_Cloud_Element
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Ocean\Ocean_Cloud_Element
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\common\script\game\GameFunc.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\common\script\game\data\GameMapData.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\common\script\game\manager\GameResManager.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\common\script\game\data\MapItemData.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\common\script\game\manager\SceneManager.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\base\ImageSequence.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\base\NodeMove.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\base\PfFrameAnim.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\prefabs\FrameAnim.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\prefabs\HurtEffect.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\prefabs\HurtNum.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\skel_boss_smoke.atlas
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\skel_boss_smoke.json
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\skel_boss_smoke.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\skel_snakelight.atlas
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\skel_snakelight.json
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\skel_snakelight.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\skel_boss_smoke.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\feiji-qietu\feiji-qietu.atlas
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\skel_snakelight.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\feiji-qietu\feiji-qietu.json
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\feiji-qietu\feiji-qietu.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\enemy\enemyBullet.plist@11826
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\feiji-qietu\feiji-qietu.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\enemy\enemyBullet.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\effectParticle.plist@20823
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\enemy\enemyBullet.plist@32557
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\enemy\enemyBullet.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\effectParticle.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@40080
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\effectParticle.plist@fe5fd
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\enemy\enemyBullet.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\effectParticle.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\enemy\enemyBullet.plist@c7180
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\effectParticle.plist@609e4
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\effectParticle.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@53267
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\enemy\enemyBullet.plist@c95fb
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\effectParticle.plist@5de41
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@56961
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\enemy\enemyBullet.plist@37a6b
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\effectParticle.plist@29bf4
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@57752
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\enemy\enemyBullet.plist@9b596
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\mainPlane\package_mainPlane_trans_701.plist@92347
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\effectParticle.plist@f1467
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\enemy\enemyBullet.plist@df470
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@67228
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\mainPlane\package_mainPlane_trans_701.plist@b9a53
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\effectParticle.plist@27c89
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@72634
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\enemy\enemyBullet.plist@14f48
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\mainPlane\package_mainPlane_trans_701.plist@4782e
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\effectParticle.plist@271df
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\enemy\enemyBullet.plist@99d69
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@74855
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\mainPlane\package_mainPlane_trans_701.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\mainPlane\package_mainPlane_trans_701.plist@750c2
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\effectParticle.plist@ab596
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\enemy\enemyBullet.plist@a734a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@75260
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\mainPlane\package_mainPlane_trans_701.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\mainPlane\package_mainPlane_trans_701.plist@cfbfe
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\effectParticle.plist@ae6bd
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\enemy\enemyBullet.plist@d2ce3
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@75691
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\mainPlane\package_mainPlane_trans_701.plist@d38b1
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\mainPlane\package_mainPlane_trans_701.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\effectParticle.plist@fbbc0
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@90798
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\enemy\enemyBullet.plist@871f1
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\mainPlane\package_mainPlane_trans_701.plist@08587
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\effectParticle.plist@206b6
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\enemy\enemyBullet.plist@54ce2
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@94029
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\mainPlane\package_mainPlane_trans_701.plist@2aada
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\mask\mask1.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\effectParticle.plist@314ee
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\enemy\enemyBullet.plist
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@95417
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\mainPlane\package_mainPlane_trans_701.plist@e8d72
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\mask\mask1.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\effectParticle.plist@5ac64
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@95628
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\shadow\package_shadow.plist@12550
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\mainPlane\package_mainPlane_trans_701.plist@ccc9c
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\mask\mask1.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\effectParticle.plist@cac25
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@98735
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\shadow\package_shadow.plist@69297
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\mainPlane\package_mainPlane_trans_701.plist@b8471
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\effectParticle.plist@03f22
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@ea5f9
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\shadow\package_shadow.plist@94634
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\mainPlane\package_mainPlane_trans_701.plist@91ed8
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\effectParticle.plist
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\shadow\package_shadow.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@e02f2
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\mainPlane\package_mainPlane_trans_701.plist@ac7c8
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\shadow\package_shadow.plist@65faa
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\below\skel_mainblow.atlas
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\shadow\package_shadow.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@105bd
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\mainPlane\package_mainPlane_trans_701.plist@ecd7b
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\shadow\package_shadow.plist@d805f
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\mainPlane\package_mainPlane_trans_701.plist@a4a6e
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@133ab
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\shadow\package_shadow.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\shadow\package_shadow.plist@817b8
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\below\skel_mainblow.json
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@395cf
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\mainPlane\package_mainPlane_trans_701.plist@3a0da
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\shadow\package_shadow.plist@8763d
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\below\skel_mainblow.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\mainPlane\package_mainPlane_trans_701.plist@5fd56
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@00a7d
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\shadow\package_shadow.plist@9997e
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_1.atlas
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\below\skel_mainblow.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\mainPlane\package_mainPlane_trans_701.plist@0396b
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@8dddc
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\shadow\package_shadow.plist@dd7c3
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\below\skel_mainblow.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\mainPlane\package_mainPlane_trans_701.plist@1362a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@3344d
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\shadow\package_shadow.plist@8151c
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_1.json
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\shadow\package_shadow.plist@8e521
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\mainPlane\package_mainPlane_trans_701.plist@33eee
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@134c6
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_1.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_10.atlas
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\mainPlane\package_mainPlane_trans_701.plist@e1918
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\shadow\package_shadow.plist@e0291
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@0da9d
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_1.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\shadow\package_shadow.plist@0d9f8
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\mainPlane\package_mainPlane_trans_701.plist@02984
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@3e2f2
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_1.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_10.json
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\mainPlane\package_mainPlane_trans_701.plist@b1394
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\shadow\package_shadow.plist@bef4a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@874e1
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_10.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\mainPlane\package_mainPlane_trans_701.plist@9265f
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\shadow\package_shadow.plist@5f2ab
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@e4cfa
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_11.atlas
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_10.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\shadow\package_shadow.plist@99ff4
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@e1c02
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\mainPlane\package_mainPlane_trans_701.plist@4e58d
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_10.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\shadow\package_shadow.plist@1b3f8
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\mainPlane\package_mainPlane_trans_701.plist@25c1b
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@b6b80
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_11.json
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\shadow\package_shadow.plist@2d0fe
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\mainPlane\package_mainPlane_trans_701.plist
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@d76e8
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_11.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_112.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_12.atlas
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\shadow\package_shadow.plist@5de2b
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@7c5d6
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_11.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_112.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\shadow\package_shadow.plist@797fc
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@aeb71
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_11.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_112.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\shadow\package_shadow.plist@25c1b
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@6ec8d
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_12.json
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\shadow\package_shadow.plist
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@a57ed
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_12.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_12_circle.atlas
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@e9fe7
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_12_circle.json
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_12_circle.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_12.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@db1ad
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_12_shield.atlas
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_12_circle.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_12.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@f40b5
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_12_shield.json
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_12_circle.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@29b92
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_12_shield.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_13.atlas
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_13.json
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@edcbf
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_13.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_12_shield.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@f5d29
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_14.atlas
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_13.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@c90ca
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_12_shield.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_14.json
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_13.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@9c7b7
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_14.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_15.atlas
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@4fbe2
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_15.json
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_14.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_15.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@207ab
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_14.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_2.atlas
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_2.json
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_15.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@ed458
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_2.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_15.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@c1954
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_3.atlas
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_3.json
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_2.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@b7b3a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_3.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_4.atlas
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@3ff98
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_2.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_4.json
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_3.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@801da
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_4.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@e4833
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_3.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_5.atlas
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_5.json
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_4.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@ba68c
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_5.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_4.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@d1d78
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_6.atlas
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_6.json
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_5.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@6888f
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_5.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_6.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_7.atlas
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@c2271
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_7.json
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_6.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@fc1d3
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_7.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_6.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@f0417
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_8.atlas
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_8.json
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_7.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@4fcc4
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_8.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_7.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_9.atlas
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@e4330
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_9.json
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_8.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@ea5df
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_8.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_9.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_92.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\firePoint\skel_mainfire.atlas
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@b6f64
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_9.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_92.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@d536d
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\firePoint\skel_mainfire.json
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_9.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\crazy\skel_mainplane_92.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@b54fa
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\firePoint\skel_mainfire.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\mainblast\skel_mainblast.atlas
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@2e2cf
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\firePoint\skel_mainfire.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\mainblast\skel_mainblast.json
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@442e7
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\firePoint\skel_mainfire.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\mainblast\skel_mainblast.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\relief\skel_relief.atlas
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@e11dc
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\mainblast\skel_mainblast.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\relief\skel_relief.json
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\relief\skel_relief.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\mainblast\skel_mainblast.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@d9b3f
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\skill\skel_cloth_skill.atlas
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\skill\skel_cloth_skill.json
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\relief\skel_relief.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@5d61c
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\skill\skel_cloth_skill.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\relief\skel_relief.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@c0e78
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\skill\skel_deer_skill_bg.atlas
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\skill\skel_cloth_skill.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\skill\skel_deer_skill_bg.json
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@e419b
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\skill\skel_cloth_skill.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\skill\skel_deer_skill_bg.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@6f542
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\skill\skel_deer_skill_front.atlas
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\skill\skel_deer_skill_front.json
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\skill\skel_deer_skill_bg.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@8bf1d
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\skill\skel_deer_skill_front.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\skill\skel_deer_skill_mid.atlas
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\skill\skel_deer_skill_bg.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@8d91b
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\skill\skel_deer_skill_mid.json
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\skill\skel_deer_skill_front.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@aaa01
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\skill\skel_deer_skill_mid.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\skill\skel_deer_skill_front.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@cb7de
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\skill\skel_legcloth_skill.atlas
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\skill\skel_legcloth_skill.json
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\skill\skel_deer_skill_mid.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@dfe3f
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\skill\skel_legcloth_skill.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\skill\skel_deer_skill_mid.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@e7738
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\skill\skel_shield_skill_back.atlas
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\skill\skel_legcloth_skill.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\skill\skel_shield_skill_back.json
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@5440c
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\skill\skel_legcloth_skill.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\skill\skel_shield_skill_back.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@807e3
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\skill\skel_shield_skill_front.atlas
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\skill\skel_shield_skill_front.json
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\skill\skel_shield_skill_back.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@de519
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\skill\skel_shield_skill_front.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\skill\skel_shield_skill_back.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@b90f2
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\skill\skel_sword_skill.atlas
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\skill\skel_sword_skill.json
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\skill\skel_shield_skill_front.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@64dcb
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\skill\skel_sword_skill.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\skill\skel_shield_skill_front.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@2650e
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\wine\crazy\skel_crazylight.atlas
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\wine\crazy\skel_crazylight.json
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\skill\skel_sword_skill.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@27e7f
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\wine\crazy\skel_crazylight.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@04920
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\skill\skel_sword_skill.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\wine\daodan\skel_bulletlight.atlas
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\wine\daodan\skel_bulletlight.json
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\wine\crazy\skel_crazylight.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@e3b7a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\wine\daodan\skel_bulletlight.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\wine\crazy\skel_crazylight.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@8c21b
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\wine\dun\skel_dunpoint.atlas
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\wine\dun\skel_dunpoint.json
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\wine\daodan\skel_bulletlight.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@317bd
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\wine\daodan\skel_bulletlight.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\wine\dun\skel_dunpoint.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\wine\dun\skel_hudun.atlas
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@40d1b
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\wine\dun\skel_hudun.json
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\wine\dun\skel_dunpoint.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@6a7f2
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\wine\dun\skel_hudun.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\wine\dun\skel_dunpoint.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@322fe
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\wine\fire\skel_winefire.atlas
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\wine\fire\skel_winefire.json
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\wine\dun\skel_hudun.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@e3c44
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\wine\fire\skel_winefire.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\wine\dun\skel_hudun.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@d63bc
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\wine\hp\skel_hpback.atlas
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\wine\hp\skel_hpback.json
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\wine\fire\skel_winefire.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@dc159
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\wine\fire\skel_winefire.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\wine\hp\skel_hpback.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\wine\hp\skel_hpfront.atlas
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@be1f5
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\wine\hp\skel_hpfront.json
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\wine\hp\skel_hpback.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@53fb9
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\wine\hp\skel_hpfront.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\wine\hp\skel_hpback.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@357f7
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\Land\Land_Clouds\CloudsFog.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\Land\Land_Clouds\Cloudshadow.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\wine\hp\skel_hpfront.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@00323
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\Land\Land_Clouds\Clouds_start01.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\Land\Land_Clouds\Clouds_start02.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\spine\mainPlane\wine\hp\skel_hpfront.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@e1f4e
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\Land\Land_Clouds\HeavyClouds01.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\Land\Land_Clouds\HeavyClouds02.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\Land\Land_Clouds\NormalCloud1.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@e4bca
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\Land\Land_Clouds\NormalCloud2.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\Land\Land_Clouds\NormalCloud3.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\Land\Land_Clouds\NormalCloud4.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@a2e43
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\Land\Land_Clouds\test_Cloud.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\Ocean\Ocean_Cloud_Element\Ocean_Clouds1.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\Ocean\Ocean_Cloud_Element\Ocean_Clouds2.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\Ocean\Ocean_Cloud_Element\Ocean_Clouds3.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@f6355
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\Ocean\Ocean_Cloud_Element\Ocean_Clouds4.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\Ocean\Ocean_Cloud_Element\Ocean_Clouds5.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\Ocean\Ocean_Cloud_Element\Ocean_Clouds_Start1.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@f09fe
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\Ocean\Ocean_Cloud_Element\Ocean_Clouds_Start2.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\Ocean\Ocean_Cloud_Element\Ocean_Fog.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Ocean\Ocean_Cloud_Element\Ocean_Clouds1.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Ocean\Ocean_Cloud_Element\Ocean_Clouds2.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@27c5a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Ocean\Ocean_Cloud_Element\Ocean_Clouds3.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Ocean\Ocean_Cloud_Element\Ocean_Clouds4.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Ocean\Ocean_Cloud_Element\Ocean_Clouds2.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Ocean\Ocean_Cloud_Element\Ocean_Clouds1.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@298aa
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Ocean\Ocean_Cloud_Element\Ocean_Clouds3.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Ocean\Ocean_Cloud_Element\Ocean_Clouds4.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Ocean\Ocean_Cloud_Element\Ocean_Clouds1.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Ocean\Ocean_Cloud_Element\Ocean_Clouds2.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@1b5d2
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Ocean\Ocean_Cloud_Element\Ocean_Clouds3.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Ocean\Ocean_Cloud_Element\Ocean_Clouds4.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@99f9c
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Ocean\Ocean_Cloud_Element\Ocean_Clouds5.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Ocean\Ocean_Cloud_Element\Ocean_Clouds_Start1.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@a1ae5
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Ocean\Ocean_Cloud_Element\Ocean_Clouds_Start2.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Ocean\Ocean_Cloud_Element\Ocean_Fog.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Ocean\Ocean_Cloud_Element\Ocean_Clouds5.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Ocean\Ocean_Cloud_Element\Ocean_Clouds_Start1.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@88d91
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Ocean\Ocean_Cloud_Element\Ocean_Clouds_Start2.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Ocean\Ocean_Cloud_Element\Ocean_Fog.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Ocean\Ocean_Cloud_Element\Ocean_Clouds5.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@4d047
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Ocean\Ocean_Cloud_Element\Ocean_Clouds_Start1.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Ocean\Ocean_Cloud_Element\Ocean_Fog.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Ocean\Ocean_Cloud_Element\Ocean_Clouds_Start2.png
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@984cf
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\spine\guide
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\scripts\core\base\MessageBox.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@2860b
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\event
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\event\GameEvent.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\common\AnnouncementUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\common\RewardUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@b296c
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\layer\GameInUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\task\components\TaskItem.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\Clouds
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\common\TextUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@b7a7b
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@68b67
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@e62fa
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@e9c3d
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@8baf2
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@430e6
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@9e68c
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@df742
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@805f3
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@6ef8e
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@8f583
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@cc8f3
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@1cfdd
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@04723
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@6296f
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@e5deb
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@3e76d
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@4c462
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@213bc
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@69e89
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@d854c
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@a8e86
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@e50a2
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@c46d9
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@483e8
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@1a2bc
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@e8fb5
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@8a534
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@dc24f
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@1a27a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@db10d
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@5376e
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@50e93
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@acf20
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@8b7b7
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@fdc55
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@401dd
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@a4002
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@5c569
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@b5520
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@be228
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@d6b2b
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@aa2e3
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@1959f
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@e280c
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@138de
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@d72fe
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@1ab9d
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@fac82
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@3e0cc
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@77fe4
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@fda07
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@79dd2
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@c9e1a
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@ad4c3
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@7d623
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@6eb3e
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@ddfdb
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@61e88
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@d8c5c
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@a4465
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@8ea62
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@527e1
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@6f9d0
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@0b226
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@01c91
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@e230f
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@d2c06
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@04e06
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@a3cfd
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@1f042
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@b9f69
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@3d7b3
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@3797e
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist@67cde
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cDestroy%c: E:\M2Game\Client\assets\resources\game\texture\hurtEffect\hurtEffects.plist
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\130000001.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\AnnouncementUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_task\prefab\ui\TaskItem.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\TextUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\RewardUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\randTerrain\randomTer_3.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\prefabs\effect\HurtNum.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\randTerrain\randtest1.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\spine\guide\shouzhi.atlas
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\spine\guide\shouzhi.png
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\spine\guide\shouzhi.skel
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Mask_DeepBlue.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\TempClouds\Clouds_duststorm.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\TempClouds\Clouds_duststorm2.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\TempClouds\Clouds_duststorm3.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\TempClouds\Clouds_duststorm4.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\spine\guide\shouzhi.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-log: UUID is initialized for E:\M2Game\Client\assets\resources\game\spine\guide\shouzhi.png.
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\TempClouds\Thunder_Clouds.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\TempClouds\Upiter_Cloud_01.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\TempClouds\Upiter_Cloud_02.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\TempClouds\Clouds_duststorm.png
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\TempClouds\Clouds_duststorm2.png
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\TempClouds\Clouds_duststorm3.png
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\TempClouds\Clouds_duststorm4.png
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\TempClouds\Thunder_Clouds.png
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\TempClouds\Clouds_duststorm.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\TempClouds\Clouds_duststorm2.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\TempClouds\Clouds_duststorm.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\TempClouds\Clouds_duststorm2.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\TempClouds\Clouds_duststorm3.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\TempClouds\Clouds_duststorm4.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\TempClouds\Thunder_Clouds.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\TempClouds\Clouds_duststorm3.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\TempClouds\Clouds_duststorm4.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\TempClouds\Thunder_Clouds.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\TempClouds\Upiter_Cloud_01.png
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\TempClouds\Upiter_Cloud_02.png
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\Clouds\CloudsFog.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\Clouds\Cloudshadow.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\Clouds\Clouds_start01.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\Clouds\Clouds_start02.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\TempClouds\Upiter_Cloud_01.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\Clouds\HeavyClouds01.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\TempClouds\Upiter_Cloud_02.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\TempClouds\Upiter_Cloud_01.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\TempClouds\Upiter_Cloud_02.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\Clouds\HeavyClouds02.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\Clouds\NormalCloud1.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\Clouds\NormalCloud2.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\Clouds\NormalCloud3.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\Clouds\NormalCloud4.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\Clouds\test_Cloud.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\Ocean\Node_Node_Element_ToLand.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\Ocean\RandomElement01.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\Ocean\RandomElement02.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Ocean\Node_Element_ToLand.png
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\texture\boss
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\texture\mainPlane
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\texture\map
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\scenes
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\editor\level
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Ocean\Node_Element_ToLand.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\scenes\Game.scene
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Ocean\Node_Element_ToLand.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbreslevel.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorBaseUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorLayerUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\utils.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\animation
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\prefabs
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\texture
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\spine
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\scripts\core\base
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\app
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\CommonEntry.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\leveldata
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\luban
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_task\prefab\ui
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\animation\ToastUIClose.anim
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\2.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\20001.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\4.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\randTerrain
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\prefabs\effect
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\texture\enemy
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\app\MyApp.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\autogen\luban
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\task
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\collider-system
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\GameIns.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\const
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\manager
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\scenes
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\leveldata\leveldata.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\luban\LubanMgr.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\common
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\friend
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\pk
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\task
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\PopupUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\ToastUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\TopUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_task\prefab\ui\TaskUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\autogen\luban\schema.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-log: 资源数据库已锁定，资源操作(bound _reimportAsset)将会延迟响应，请稍侯
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\task\Task.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\collider-system\FCollider.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\const\GameResourceList.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\manager\BattleManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\manager\BossManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\manager\EnemyManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\manager\HurtEffectManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\scenes\GameMain.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\base
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\layer
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\map
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\common\PopupUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\friend\FriendUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\pk\PKUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\task\TaskUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\task\components
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\TempClouds
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\TempClouds
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\base\Controller.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\base\Entity.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\map\GameMapRun.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\boss
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\mainPlane
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\skill
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\task\components\ProgressPanel.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\Land
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\Ocean
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Texture\HighSky\Ocean
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\boss\BossPlane.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\mainPlane\MainPlane.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\skill\BuffComp.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: refresh db assets success
2025-9-17 10:25:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:25:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: asset-db:refresh-all-database (528ms)
2025-9-17 10:25:18-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\spine\guide\shouzhi.skel
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-17 10:25:18-log: UUID is initialized for E:\M2Game\Client\assets\resources\game\spine\guide\shouzhi.png.
2025-9-17 10:25:18-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (7ms)
2025-9-17 10:25:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:18-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (5ms)
2025-9-17 10:25:19-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:19-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (5ms)
2025-9-17 10:25:21-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:25:21-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (4ms)
2025-9-17 10:26:15-debug: refresh db internal success
2025-9-17 10:26:15-debug: refresh db assets success
2025-9-17 10:26:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:26:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:26:15-debug: asset-db:refresh-all-database (132ms)
2025-9-17 10:26:15-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 10:26:15-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 10:26:22-debug: refresh db internal success
2025-9-17 10:26:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\luban\LubanMgr.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:26:22-debug: refresh db assets success
2025-9-17 10:26:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:26:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:26:22-debug: asset-db:refresh-all-database (111ms)
2025-9-17 10:26:22-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 10:26:22-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 10:26:22-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:26:22-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (5ms)
2025-9-17 10:33:24-debug: refresh db internal success
2025-9-17 10:33:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:33:24-debug: refresh db assets success
2025-9-17 10:33:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:33:24-debug: asset-db:refresh-all-database (130ms)
2025-9-17 10:34:05-debug: refresh db internal success
2025-9-17 10:34:05-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorEventUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:34:05-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:34:05-debug: refresh db assets success
2025-9-17 10:34:05-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:34:05-debug: asset-db:refresh-all-database (144ms)
2025-9-17 10:34:05-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:34:05-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (4ms)
2025-9-17 10:38:46-debug: refresh db internal success
2025-9-17 10:38:46-debug: refresh db assets success
2025-9-17 10:38:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:38:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:38:46-debug: asset-db:refresh-all-database (121ms)
2025-9-17 10:38:46-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 10:38:46-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
