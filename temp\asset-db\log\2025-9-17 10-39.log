2025-9-17 10:39:05-debug: start **** info
2025-9-17 10:39:05-log: Cannot access game frame or container.
2025-9-17 10:39:06-debug: asset-db:require-engine-code (417ms)
2025-9-17 10:39:06-log: meshopt wasm decoder initialized
2025-9-17 10:39:06-log: [bullet]:bullet wasm lib loaded.
2025-9-17 10:39:06-log: [box2d]:box2d wasm lib loaded.
2025-9-17 10:39:06-log: Forward render pipeline initialized.
2025-9-17 10:39:06-debug: [Assets Memory track]: asset-db:worker-init: initEngine start:30.87MB, end 80.06MB, increase: 49.19MB
2025-9-17 10:39:06-debug: [Assets Memory track]: asset-db-plugin-register: programming start:80.96MB, end 84.06MB, increase: 3.10MB
2025-9-17 10:39:07-debug: [Assets Memory track]: asset-db-plugin-register: builder start:84.09MB, end 290.53MB, increase: 206.44MB
2025-9-17 10:39:06-log: Cocos Creator v3.8.6
2025-9-17 10:39:07-debug: [Assets Memory track]: asset-db:worker-init: initPlugin start:80.08MB, end 288.92MB, increase: 208.84MB
2025-9-17 10:39:06-log: Using legacy pipeline
2025-9-17 10:39:07-debug: [Assets Memory track]: asset-db-plugin-register: project start:80.82MB, end 288.89MB, increase: 208.08MB
2025-9-17 10:39:07-debug: run package(google-play) handler(enable) start
2025-9-17 10:39:07-debug: run package(google-play) handler(enable) success!
2025-9-17 10:39:07-debug: run package(harmonyos-next) handler(enable) start
2025-9-17 10:39:07-debug: run package(harmonyos-next) handler(enable) success!
2025-9-17 10:39:07-debug: run package(honor-mini-game) handler(enable) success!
2025-9-17 10:39:07-debug: run package(huawei-agc) handler(enable) start
2025-9-17 10:39:07-debug: run package(honor-mini-game) handler(enable) start
2025-9-17 10:39:07-debug: run package(huawei-agc) handler(enable) success!
2025-9-17 10:39:07-debug: run package(huawei-quick-game) handler(enable) success!
2025-9-17 10:39:07-debug: run package(huawei-quick-game) handler(enable) start
2025-9-17 10:39:07-debug: run package(linux) handler(enable) start
2025-9-17 10:39:07-debug: run package(ios) handler(enable) success!
2025-9-17 10:39:07-debug: run package(linux) handler(enable) success!
2025-9-17 10:39:07-debug: run package(mac) handler(enable) success!
2025-9-17 10:39:07-debug: run package(migu-mini-game) handler(enable) start
2025-9-17 10:39:07-debug: run package(mac) handler(enable) start
2025-9-17 10:39:07-debug: run package(migu-mini-game) handler(enable) success!
2025-9-17 10:39:07-debug: run package(native) handler(enable) success!
2025-9-17 10:39:07-debug: run package(native) handler(enable) start
2025-9-17 10:39:07-debug: run package(ios) handler(enable) start
2025-9-17 10:39:07-debug: run package(oppo-mini-game) handler(enable) start
2025-9-17 10:39:07-debug: run package(ohos) handler(enable) success!
2025-9-17 10:39:07-debug: run package(ohos) handler(enable) start
2025-9-17 10:39:07-debug: run package(oppo-mini-game) handler(enable) success!
2025-9-17 10:39:07-debug: run package(runtime-dev-tools) handler(enable) success!
2025-9-17 10:39:07-debug: run package(runtime-dev-tools) handler(enable) start
2025-9-17 10:39:07-debug: run package(taobao-mini-game) handler(enable) start
2025-9-17 10:39:07-debug: run package(taobao-mini-game) handler(enable) success!
2025-9-17 10:39:07-debug: run package(web-desktop) handler(enable) start
2025-9-17 10:39:07-debug: run package(vivo-mini-game) handler(enable) success!
2025-9-17 10:39:07-debug: run package(web-desktop) handler(enable) success!
2025-9-17 10:39:07-debug: run package(vivo-mini-game) handler(enable) start
2025-9-17 10:39:07-debug: run package(web-mobile) handler(enable) start
2025-9-17 10:39:07-debug: run package(web-mobile) handler(enable) success!
2025-9-17 10:39:07-debug: run package(wechatgame) handler(enable) start
2025-9-17 10:39:07-debug: run package(wechatgame) handler(enable) success!
2025-9-17 10:39:07-debug: run package(wechatprogram) handler(enable) start
2025-9-17 10:39:07-debug: run package(windows) handler(enable) start
2025-9-17 10:39:07-debug: run package(xiaomi-quick-game) handler(enable) start
2025-9-17 10:39:07-debug: run package(xiaomi-quick-game) handler(enable) success!
2025-9-17 10:39:07-debug: run package(wechatprogram) handler(enable) success!
2025-9-17 10:39:07-debug: run package(cocos-service) handler(enable) start
2025-9-17 10:39:07-debug: run package(cocos-service) handler(enable) success!
2025-9-17 10:39:07-debug: run package(im-plugin) handler(enable) success!
2025-9-17 10:39:07-debug: run package(windows) handler(enable) success!
2025-9-17 10:39:07-debug: run package(im-plugin) handler(enable) start
2025-9-17 10:39:07-debug: run package(emitter-editor) handler(enable) start
2025-9-17 10:39:07-debug: run package(fix-asset-default-userdata) handler(enable) start
2025-9-17 10:39:07-debug: run package(emitter-editor) handler(enable) success!
2025-9-17 10:39:07-debug: asset-db:worker-init: initPlugin (1042ms)
2025-9-17 10:39:07-debug: run package(fix-asset-default-userdata) handler(enable) success!
2025-9-17 10:39:07-debug: [Assets Memory track]: asset-db:worker-init start:30.86MB, end 291.14MB, increase: 260.28MB
2025-9-17 10:39:07-debug: Run asset db hook programming:beforePreStart ...
2025-9-17 10:39:07-debug: Run asset db hook programming:beforePreStart success!
2025-9-17 10:39:07-debug: Run asset db hook engine-extends:beforePreStart success!
2025-9-17 10:39:07-debug: Run asset db hook engine-extends:beforePreStart ...
2025-9-17 10:39:07-debug: run package(localization-editor) handler(enable) start
2025-9-17 10:39:07-debug: asset-db-hook-programming-beforePreStart (108ms)
2025-9-17 10:39:07-debug: asset-db:worker-init (1633ms)
2025-9-17 10:39:07-debug: run package(localization-editor) handler(enable) success!
2025-9-17 10:39:07-debug: asset-db-hook-engine-extends-beforePreStart (108ms)
2025-9-17 10:39:07-debug: Preimport db internal success
2025-9-17 10:39:07-debug: run package(wave-editor) handler(enable) success!
2025-9-17 10:39:07-debug: run package(wave-editor) handler(enable) start
2025-9-17 10:39:07-debug: run package(placeholder) handler(enable) start
2025-9-17 10:39:07-debug: run package(placeholder) handler(enable) success!
2025-9-17 10:39:07-debug: Run asset db hook programming:afterPreStart ...
2025-9-17 10:39:07-debug: Preimport db assets success
2025-9-17 10:39:07-debug: starting packer-driver...
2025-9-17 10:39:12-debug: initialize scripting environment...
2025-9-17 10:39:12-debug: [[Executor]] prepare before lock
2025-9-17 10:39:12-debug: Set detail map pack:///resolution-detail-map.json: {}
2025-9-17 10:39:12-debug: [[Executor]] prepare after unlock
2025-9-17 10:39:12-debug: Run asset db hook engine-extends:afterPreStart success!
2025-9-17 10:39:12-debug: Run asset db hook programming:afterPreStart success!
2025-9-17 10:39:12-debug: [Assets Memory track]: asset-db:worker-init: preStart start:291.15MB, end 305.06MB, increase: 13.91MB
2025-9-17 10:39:12-debug: Run asset db hook engine-extends:afterPreStart ...
2025-9-17 10:39:12-debug: Start up the 'internal' database...
2025-9-17 10:39:12-debug: asset-db-hook-programming-afterPreStart (5431ms)
2025-9-17 10:39:12-debug: asset-db:worker-effect-data-processing (224ms)
2025-9-17 10:39:12-debug: asset-db-hook-engine-extends-afterPreStart (224ms)
2025-9-17 10:39:12-debug: Start up the 'assets' database...
2025-9-17 10:39:12-debug: asset-db:worker-startup-database[internal] (5731ms)
2025-9-17 10:39:13-debug: lazy register asset handler *
2025-9-17 10:39:13-debug: lazy register asset handler directory
2025-9-17 10:39:13-debug: lazy register asset handler json
2025-9-17 10:39:13-debug: lazy register asset handler spine-data
2025-9-17 10:39:13-debug: lazy register asset handler dragonbones
2025-9-17 10:39:13-debug: lazy register asset handler text
2025-9-17 10:39:13-debug: lazy register asset handler dragonbones-atlas
2025-9-17 10:39:13-debug: lazy register asset handler terrain
2025-9-17 10:39:13-debug: lazy register asset handler javascript
2025-9-17 10:39:13-debug: lazy register asset handler prefab
2025-9-17 10:39:13-debug: lazy register asset handler typescript
2025-9-17 10:39:13-debug: lazy register asset handler sprite-frame
2025-9-17 10:39:13-debug: lazy register asset handler tiled-map
2025-9-17 10:39:13-debug: lazy register asset handler buffer
2025-9-17 10:39:13-debug: lazy register asset handler scene
2025-9-17 10:39:13-debug: lazy register asset handler sign-image
2025-9-17 10:39:13-debug: lazy register asset handler image
2025-9-17 10:39:13-debug: lazy register asset handler texture
2025-9-17 10:39:13-debug: lazy register asset handler alpha-image
2025-9-17 10:39:13-debug: lazy register asset handler erp-texture-cube
2025-9-17 10:39:13-debug: lazy register asset handler texture-cube-face
2025-9-17 10:39:13-debug: lazy register asset handler texture-cube
2025-9-17 10:39:13-debug: lazy register asset handler render-texture
2025-9-17 10:39:13-debug: lazy register asset handler gltf
2025-9-17 10:39:13-debug: lazy register asset handler rt-sprite-frame
2025-9-17 10:39:13-debug: lazy register asset handler gltf-material
2025-9-17 10:39:13-debug: lazy register asset handler gltf-animation
2025-9-17 10:39:13-debug: lazy register asset handler gltf-scene
2025-9-17 10:39:13-debug: lazy register asset handler gltf-skeleton
2025-9-17 10:39:13-debug: lazy register asset handler gltf-embeded-image
2025-9-17 10:39:13-debug: lazy register asset handler fbx
2025-9-17 10:39:13-debug: lazy register asset handler gltf-mesh
2025-9-17 10:39:13-debug: lazy register asset handler physics-material
2025-9-17 10:39:13-debug: lazy register asset handler material
2025-9-17 10:39:13-debug: lazy register asset handler audio-clip
2025-9-17 10:39:13-debug: lazy register asset handler effect
2025-9-17 10:39:13-debug: lazy register asset handler animation-graph
2025-9-17 10:39:13-debug: lazy register asset handler effect-header
2025-9-17 10:39:13-debug: lazy register asset handler animation-clip
2025-9-17 10:39:13-debug: lazy register asset handler animation-graph-variant
2025-9-17 10:39:13-debug: lazy register asset handler animation-mask
2025-9-17 10:39:13-debug: lazy register asset handler bitmap-font
2025-9-17 10:39:13-debug: lazy register asset handler ttf-font
2025-9-17 10:39:13-debug: lazy register asset handler particle
2025-9-17 10:39:13-debug: lazy register asset handler render-pipeline
2025-9-17 10:39:13-debug: lazy register asset handler auto-atlas
2025-9-17 10:39:13-debug: lazy register asset handler label-atlas
2025-9-17 10:39:13-debug: lazy register asset handler sprite-atlas
2025-9-17 10:39:13-debug: lazy register asset handler instantiation-material
2025-9-17 10:39:13-debug: lazy register asset handler render-flow
2025-9-17 10:39:13-debug: lazy register asset handler instantiation-mesh
2025-9-17 10:39:13-debug: lazy register asset handler instantiation-skeleton
2025-9-17 10:39:13-debug: lazy register asset handler video-clip
2025-9-17 10:39:13-debug: lazy register asset handler render-stage
2025-9-17 10:39:13-debug: lazy register asset handler instantiation-animation
2025-9-17 10:39:13-debug: asset-db:worker-startup-database[assets] (5678ms)
2025-9-17 10:39:13-debug: asset-db:ready (8925ms)
2025-9-17 10:39:13-debug: init worker message success
2025-9-17 10:39:13-debug: fix the bug of updateDefaultUserData
2025-9-17 10:39:13-debug: asset-db:start-database (5799ms)
2025-9-17 10:39:13-debug: programming:execute-script (3ms)
2025-9-17 10:39:13-debug: [Build Memory track]: builder:worker-init start:197.75MB, end 210.15MB, increase: 12.40MB
2025-9-17 10:39:13-debug: builder:worker-init (306ms)
2025-9-17 10:39:53-debug: refresh db internal success
2025-9-17 10:39:53-debug: refresh db assets success
2025-9-17 10:39:53-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:39:53-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:39:53-debug: asset-db:refresh-all-database (149ms)
2025-9-17 10:39:53-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 10:39:53-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 10:40:33-debug: refresh db internal success
2025-9-17 10:40:33-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\luban\LubanMgr.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:40:33-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:40:33-debug: refresh db assets success
2025-9-17 10:40:33-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:40:33-debug: asset-db:refresh-all-database (149ms)
2025-9-17 10:40:33-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 10:40:33-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:40:33-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (13ms)
2025-9-17 10:41:18-debug: refresh db internal success
2025-9-17 10:41:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\luban\LubanMgr.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:41:18-debug: refresh db assets success
2025-9-17 10:41:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:41:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:41:18-debug: asset-db:refresh-all-database (146ms)
2025-9-17 10:41:18-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 10:41:19-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:41:19-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (10ms)
2025-9-17 10:42:26-debug: refresh db internal success
2025-9-17 10:42:26-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\luban\LubanMgr.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:42:26-debug: refresh db assets success
2025-9-17 10:42:26-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:42:26-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:42:26-debug: asset-db:refresh-all-database (148ms)
2025-9-17 10:42:26-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 10:42:26-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 10:42:26-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:42:26-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (4ms)
2025-9-17 10:43:59-debug: refresh db internal success
2025-9-17 10:44:00-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\luban\LubanMgr.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:44:00-debug: refresh db assets success
2025-9-17 10:44:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:44:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:44:00-debug: asset-db:refresh-all-database (144ms)
2025-9-17 10:44:00-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 10:44:00-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 10:44:00-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:44:00-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (7ms)
2025-9-17 10:48:26-debug: refresh db internal success
2025-9-17 10:48:26-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\luban\LubanMgr.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:48:26-debug: refresh db assets success
2025-9-17 10:48:26-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:48:26-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:48:26-debug: asset-db:refresh-all-database (142ms)
2025-9-17 10:48:26-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:48:26-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (6ms)
2025-9-17 10:49:06-debug: refresh db internal success
2025-9-17 10:49:06-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorEventUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:49:06-debug: refresh db assets success
2025-9-17 10:49:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:49:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:49:06-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 10:49:06-debug: asset-db:refresh-all-database (139ms)
2025-9-17 10:49:06-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 10:49:07-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:49:07-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (8ms)
2025-9-17 10:49:59-debug: refresh db internal success
2025-9-17 10:49:59-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorEventUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:49:59-debug: refresh db assets success
2025-9-17 10:49:59-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:49:59-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:49:59-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 10:49:59-debug: asset-db:refresh-all-database (139ms)
2025-9-17 10:49:59-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 10:49:59-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:49:59-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (8ms)
2025-9-17 10:50:08-debug: refresh db internal success
2025-9-17 10:50:08-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorEventUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:50:08-debug: refresh db assets success
2025-9-17 10:50:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:50:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:50:08-debug: asset-db:refresh-all-database (136ms)
2025-9-17 10:50:08-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 10:50:08-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 10:50:08-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:50:08-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (6ms)
2025-9-17 10:50:21-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:50:21-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (4ms)
2025-9-17 10:50:37-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\wave_1.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:50:37-debug: asset-db:reimport-assetd5f5e836-9495-4c08-890f-d92cc6696838 (2ms)
2025-9-17 10:52:19-debug: refresh db internal success
2025-9-17 10:52:19-debug: refresh db assets success
2025-9-17 10:52:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:52:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:52:19-debug: asset-db:refresh-all-database (127ms)
2025-9-17 10:53:24-debug: refresh db internal success
2025-9-17 10:53:24-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorEventUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:53:24-debug: refresh db assets success
2025-9-17 10:53:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:53:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:53:24-debug: asset-db:refresh-all-database (140ms)
2025-9-17 10:53:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:53:24-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (8ms)
2025-9-17 10:54:01-debug: refresh db internal success
2025-9-17 10:54:01-debug: refresh db assets success
2025-9-17 10:54:01-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:54:01-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:54:01-debug: asset-db:refresh-all-database (102ms)
2025-9-17 10:54:35-debug: refresh db internal success
2025-9-17 10:54:35-debug: refresh db assets success
2025-9-17 10:54:35-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:54:35-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:54:35-debug: asset-db:refresh-all-database (138ms)
2025-9-17 10:54:35-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 10:54:35-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-17 10:54:58-debug: refresh db internal success
2025-9-17 10:54:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:54:58-debug: refresh db assets success
2025-9-17 10:54:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:54:58-debug: asset-db:refresh-all-database (125ms)
2025-9-17 10:55:37-debug: refresh db internal success
2025-9-17 10:55:37-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorEventUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:55:37-debug: refresh db assets success
2025-9-17 10:55:37-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:55:37-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:55:37-debug: asset-db:refresh-all-database (140ms)
2025-9-17 10:55:37-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:55:37-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (7ms)
2025-9-17 10:56:09-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:56:09-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (3ms)
2025-9-17 10:57:47-debug: refresh db internal success
2025-9-17 10:57:47-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:57:47-debug: refresh db assets success
2025-9-17 10:57:47-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:57:47-debug: asset-db:refresh-all-database (106ms)
2025-9-17 10:58:00-debug: refresh db internal success
2025-9-17 10:58:00-debug: %cImport%c: E:\M2Game\Client\assets\editor\icons\trigger.png
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:58:00-debug: %cReImport%c: E:\M2Game\Client\assets\editor\icons\trigger.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-9-17 10:58:00-debug: refresh db assets success
2025-9-17 10:58:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:58:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:58:00-debug: asset-db:refresh-all-database (143ms)
2025-9-17 10:58:00-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 10:58:00-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-17 10:58:09-debug: refresh db internal success
2025-9-17 10:58:09-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:58:09-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:58:09-debug: refresh db assets success
2025-9-17 10:58:09-debug: asset-db:refresh-all-database (106ms)
2025-9-17 10:58:27-debug: refresh db internal success
2025-9-17 10:58:27-debug: refresh db assets success
2025-9-17 10:58:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:58:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:58:27-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 10:58:27-debug: asset-db:refresh-all-database (110ms)
2025-9-17 10:58:27-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 10:58:43-debug: refresh db internal success
2025-9-17 10:58:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:58:43-debug: refresh db assets success
2025-9-17 10:58:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:58:43-debug: asset-db:refresh-all-database (126ms)
2025-9-17 10:59:45-debug: refresh db internal success
2025-9-17 10:59:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 10:59:45-debug: refresh db assets success
2025-9-17 10:59:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 10:59:45-debug: asset-db:refresh-all-database (111ms)
2025-9-17 11:01:22-debug: refresh db internal success
2025-9-17 11:01:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:01:22-debug: refresh db assets success
2025-9-17 11:01:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:01:22-debug: asset-db:refresh-all-database (102ms)
2025-9-17 11:01:22-debug: asset-db:worker-effect-data-processing (-1ms)
2025-9-17 11:01:22-debug: asset-db-hook-engine-extends-afterRefresh (-1ms)
2025-9-17 11:02:38-debug: refresh db internal success
2025-9-17 11:02:38-debug: refresh db assets success
2025-9-17 11:02:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:02:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:02:38-debug: asset-db:refresh-all-database (127ms)
2025-9-17 11:03:18-debug: refresh db internal success
2025-9-17 11:03:18-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorEventUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:03:18-debug: refresh db assets success
2025-9-17 11:03:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:03:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:03:18-debug: asset-db:refresh-all-database (136ms)
2025-9-17 11:03:18-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 11:03:18-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 11:03:18-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:03:18-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (6ms)
2025-9-17 11:03:59-debug: refresh db internal success
2025-9-17 11:03:59-debug: refresh db assets success
2025-9-17 11:03:59-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:03:59-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:03:59-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 11:03:59-debug: asset-db:refresh-all-database (109ms)
2025-9-17 11:03:59-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 11:05:28-debug: refresh db internal success
2025-9-17 11:05:28-debug: refresh db assets success
2025-9-17 11:05:28-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:05:28-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:05:28-debug: asset-db:refresh-all-database (101ms)
2025-9-17 11:05:28-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 11:05:28-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 11:06:11-debug: refresh db internal success
2025-9-17 11:06:11-debug: refresh db assets success
2025-9-17 11:06:11-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:06:11-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:06:11-debug: asset-db:refresh-all-database (108ms)
2025-9-17 11:07:07-debug: refresh db internal success
2025-9-17 11:07:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:07:07-debug: refresh db assets success
2025-9-17 11:07:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:07:07-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 11:07:07-debug: asset-db:refresh-all-database (104ms)
2025-9-17 11:07:07-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 11:07:08-debug: refresh db internal success
2025-9-17 11:07:08-debug: refresh db assets success
2025-9-17 11:07:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:07:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:07:08-debug: asset-db:refresh-all-database (109ms)
2025-9-17 11:07:08-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 11:07:08-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 11:07:10-debug: refresh db internal success
2025-9-17 11:07:10-debug: refresh db assets success
2025-9-17 11:07:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:07:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:07:10-debug: asset-db:refresh-all-database (98ms)
2025-9-17 11:07:10-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 11:07:24-debug: refresh db internal success
2025-9-17 11:07:24-debug: refresh db assets success
2025-9-17 11:07:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:07:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:07:24-debug: asset-db:refresh-all-database (107ms)
2025-9-17 11:07:24-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 11:07:24-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 11:08:06-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:08:06-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (5ms)
2025-9-17 11:08:11-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\wave_1.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:08:11-debug: asset-db:reimport-assetd5f5e836-9495-4c08-890f-d92cc6696838 (2ms)
2025-9-17 11:10:48-debug: refresh db internal success
2025-9-17 11:10:48-debug: refresh db assets success
2025-9-17 11:10:48-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:10:48-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:10:48-debug: asset-db:refresh-all-database (127ms)
2025-9-17 11:13:14-debug: refresh db internal success
2025-9-17 11:13:14-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorEventUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:13:14-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\wave\Wave.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:13:14-debug: refresh db assets success
2025-9-17 11:13:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:13:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:13:14-debug: asset-db:refresh-all-database (141ms)
2025-9-17 11:13:14-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-17 11:13:14-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-17 11:13:15-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:13:15-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (7ms)
2025-9-17 11:13:15-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:13:15-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (7ms)
2025-9-17 11:13:29-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:13:29-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (4ms)
2025-9-17 11:13:42-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\wave_1.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:13:42-debug: asset-db:reimport-assetd5f5e836-9495-4c08-890f-d92cc6696838 (2ms)
2025-9-17 11:15:38-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:15:38-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (3ms)
2025-9-17 11:15:56-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\wave_1.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:15:56-debug: asset-db:reimport-assetd5f5e836-9495-4c08-890f-d92cc6696838 (1ms)
2025-9-17 11:16:08-debug: start refresh asset from E:\M2Game\Client\assets\resources\game\level\wave\wave_1.prefab...
2025-9-17 11:16:08-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\wave_1.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:16:08-debug: refresh asset E:\M2Game\Client\assets\resources\game\level\wave success
2025-9-17 11:16:08-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:16:08-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (4ms)
2025-9-17 11:17:38-debug: refresh db internal success
2025-9-17 11:17:38-debug: refresh db assets success
2025-9-17 11:17:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:17:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:17:38-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 11:17:38-debug: asset-db:refresh-all-database (114ms)
2025-9-17 11:17:38-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 11:17:41-debug: refresh db internal success
2025-9-17 11:17:41-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:17:41-debug: refresh db assets success
2025-9-17 11:17:41-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:17:41-debug: asset-db:refresh-all-database (104ms)
2025-9-17 11:17:41-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 11:17:41-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-17 11:23:07-debug: refresh db internal success
2025-9-17 11:23:08-debug: refresh db assets success
2025-9-17 11:23:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:23:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:23:08-debug: asset-db:refresh-all-database (128ms)
2025-9-17 11:23:08-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 11:27:18-debug: refresh db internal success
2025-9-17 11:27:19-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorEventUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:27:19-debug: refresh db assets success
2025-9-17 11:27:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:27:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:27:19-debug: asset-db:refresh-all-database (147ms)
2025-9-17 11:27:19-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 11:27:19-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 11:27:19-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:27:19-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (8ms)
2025-9-17 11:27:52-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:27:52-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (4ms)
2025-9-17 11:27:55-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\wave_1.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:27:55-debug: asset-db:reimport-assetd5f5e836-9495-4c08-890f-d92cc6696838 (3ms)
2025-9-17 11:28:24-debug: refresh db internal success
2025-9-17 11:28:24-debug: refresh db assets success
2025-9-17 11:28:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:28:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:28:24-debug: asset-db:refresh-all-database (101ms)
2025-9-17 11:28:30-debug: refresh db internal success
2025-9-17 11:28:30-debug: refresh db assets success
2025-9-17 11:28:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:28:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:28:30-debug: asset-db:refresh-all-database (100ms)
2025-9-17 11:40:07-debug: refresh db internal success
2025-9-17 11:40:07-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\preview\WavePreview.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:40:07-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\preview
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:40:07-debug: %cImport%c: E:\M2Game\Client\assets\editor\level
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:40:07-debug: refresh db assets success
2025-9-17 11:40:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:40:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:40:07-debug: asset-db:refresh-all-database (168ms)
2025-9-17 11:40:07-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 11:40:07-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 11:40:07-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:40:07-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (5ms)
2025-9-17 11:40:34-debug: refresh db internal success
2025-9-17 11:40:34-debug: refresh db assets success
2025-9-17 11:40:34-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:40:34-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:40:34-debug: asset-db:refresh-all-database (101ms)
2025-9-17 11:40:34-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 11:40:34-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 11:40:39-debug: refresh db internal success
2025-9-17 11:40:39-debug: refresh db assets success
2025-9-17 11:40:39-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:40:39-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:40:39-debug: asset-db:refresh-all-database (115ms)
2025-9-17 11:40:39-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 11:40:39-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 11:54:58-debug: refresh db internal success
2025-9-17 11:54:58-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorBaseUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:54:58-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorEventUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:54:58-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\preview\WavePreview.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:54:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:54:58-debug: refresh db assets success
2025-9-17 11:54:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:54:58-debug: asset-db:refresh-all-database (138ms)
2025-9-17 11:54:58-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:54:58-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (5ms)
2025-9-17 11:55:14-debug: refresh db internal success
2025-9-17 11:55:14-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\preview\WavePreview.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:55:14-debug: refresh db assets success
2025-9-17 11:55:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:55:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:55:14-debug: asset-db:refresh-all-database (106ms)
2025-9-17 11:55:14-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 11:55:14-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 11:55:14-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:55:14-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (35ms)
2025-9-17 11:57:17-debug: refresh db internal success
2025-9-17 11:57:17-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:57:17-debug: refresh db assets success
2025-9-17 11:57:17-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:57:17-debug: asset-db:refresh-all-database (133ms)
2025-9-17 11:57:17-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 11:57:17-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 11:57:32-debug: refresh db internal success
2025-9-17 11:57:33-debug: refresh db assets success
2025-9-17 11:57:33-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:57:33-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:57:33-debug: asset-db:refresh-all-database (102ms)
2025-9-17 11:57:34-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:57:34-debug: asset-db:reimport-asset401efd7e-bd20-4537-a13a-f25e6238c2a9 (5ms)
2025-9-17 11:57:40-debug: refresh db internal success
2025-9-17 11:57:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:57:40-debug: refresh db assets success
2025-9-17 11:57:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:57:40-debug: asset-db:refresh-all-database (106ms)
2025-9-17 11:57:40-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 11:57:40-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 11:58:34-debug: refresh db internal success
2025-9-17 11:58:34-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\common\script\game\randTerrain
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\dyncTerrain
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\dyncTerrain\EmittierTerrain.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\common\script\game\randTerrain\RandTerrain.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\dyncTerrain\RandTerrain.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\editor\level
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbresloot.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbreslevel.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorBaseUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\utils.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\1.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\autogen\luban
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\manager
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\Plane.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\autogen\luban\schema.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\manager\BattleManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\manager\BossManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\manager\EnemyManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\manager\WaveManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\boss
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\mainPlane
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\PlaneBase.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\boss\BossPlane.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\mainPlane\MainPlane.ts
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:34-debug: refresh db assets success
2025-9-17 11:58:34-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 11:58:34-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 11:58:34-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-17 11:58:34-debug: asset-db:refresh-all-database (175ms)
2025-9-17 11:58:34-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-17 11:58:36-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:36-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (4ms)
2025-9-17 11:58:37-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\HighAltitude_LandToOcean.json
background: #aaff85; color: #000;
color: #000;
2025-9-17 11:58:37-debug: asset-db:reimport-assetb4742619-9d3c-4cea-99ac-6d74562a83cf (4ms)
2025-9-17 12:02:25-debug: refresh db internal success
2025-9-17 12:02:25-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-17 12:02:25-debug: refresh db assets success
2025-9-17 12:02:25-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-17 12:02:25-debug: asset-db:refresh-all-database (136ms)
