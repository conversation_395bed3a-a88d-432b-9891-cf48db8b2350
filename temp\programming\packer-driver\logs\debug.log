10:39:07.453 debug: 2025/9/17 10:39:07
10:39:07.453 debug: Project: E:\M2Game\Client
10:39:07.454 debug: Targets: editor,preview
10:39:07.462 debug: Incremental file seems great.
10:39:07.462 debug: Engine path: C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine
10:39:07.478 debug: Initializing target [Editor]
10:39:07.478 debug: Loading cache
10:39:07.484 debug: Loading cache costs 6.413300000000163ms.
10:39:07.485 debug: Engine features shipped in editor: base,gfx-webgl,gfx-webgl2,gfx-empty,gfx-webgpu,3d,animation,skeletal-animation,2d,rich-text,mask,graphics,ui-skew,ui,affine-transform,particle,particle-2d,physics-framework,physics-cannon,physics-physx,physics-ammo,physics-builtin,physics-2d-framework,physics-2d-box2d-jsb,physics-2d-box2d,physics-2d-builtin,physics-2d-box2d-wasm,intersection-2d,primitive,profiler,occlusion-query,geometry-renderer,debug-renderer,audio,video,xr,light-probe,terrain,webview,tween,tiled-map,vendor-google,spine-3.8,spine-4.2,dragon-bones,marionette,procedural-animation,custom-pipeline,custom-pipeline-builtin-scripts,custom-pipeline-post-process,legacy-pipeline,websocket,websocket-server,meshopt
10:39:07.485 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc"
  }
}
10:39:07.485 debug: Initializing target [Preview]
10:39:07.485 debug: Loading cache
10:39:07.493 debug: Loading cache costs 7.3449999999998ms.
10:39:07.493 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc"
  }
}
10:39:07.524 debug: Sync engine features: 2d,3d,affine-transform,animation,audio,base,custom-pipeline,debug-renderer,gfx-webgl,gfx-webgl2,graphics,intersection-2d,marionette,mask,meshopt,particle-2d,physics-2d-builtin,profiler,rich-text,skeletal-animation,spine-3.8,tween,ui,video,websocket,webview,custom-pipeline
10:39:07.529 debug: Reset databases. Enumerated domains: [
  {
    "root": "db://internal/",
    "physical": "C:\\ProgramData\\cocos\\editors\\Creator\\3.8.6\\resources\\resources\\3d\\engine\\editor\\assets"
  },
  {
    "root": "db://assets/",
    "physical": "E:\\M2Game\\Client\\assets"
  }
]
10:39:07.530 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc",
    "db://internal/": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/",
    "db://assets/": "file:///E:/M2Game/Client/assets/"
  }
}
10:39:07.530 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc",
    "db://internal/": "file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/",
    "db://assets/": "file:///E:/M2Game/Client/assets/"
  }
}
10:39:07.531 debug: Pulling asset-db.
10:39:07.579 debug: Fetch asset-db cost: 48.29790000000003ms.
10:39:07.580 debug: Build iteration starts.
Number of accumulated asset changes: 227
Feature changed: false
10:39:07.581 debug: Target(editor) build started.
10:39:07.583 debug: Detected change: cce:/internal/x/cc. Last mtime: Thu Jan 01 1970 08:00:04 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:03 GMT+0800 (中国标准时间)
10:39:07.583 debug: Inspect cce:/internal/x/cc
10:39:07.620 debug: transform url: 'cce:/internal/x/cc' costs: 36.70 ms
10:39:07.621 debug: Resolve cce:/internal/x/cc-fu/base from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/base.
10:39:07.621 debug: Resolve cce:/internal/x/cc-fu/gfx-webgl from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl.
10:39:07.621 debug: Resolve cce:/internal/x/cc-fu/gfx-webgl2 from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl2.
10:39:07.621 debug: Resolve cce:/internal/x/cc-fu/gfx-empty from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-empty.
10:39:07.621 debug: Resolve cce:/internal/x/cc-fu/gfx-webgpu from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgpu.
10:39:07.621 debug: Resolve cce:/internal/x/cc-fu/3d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/3d.
10:39:07.621 debug: Resolve cce:/internal/x/cc-fu/animation from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/animation.
10:39:07.621 debug: Resolve cce:/internal/x/cc-fu/skeletal-animation from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/skeletal-animation.
10:39:07.621 debug: Resolve cce:/internal/x/cc-fu/2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/2d.
10:39:07.621 debug: Resolve cce:/internal/x/cc-fu/sorting from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/sorting.
10:39:07.621 debug: Resolve cce:/internal/x/cc-fu/rich-text from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/rich-text.
10:39:07.621 debug: Resolve cce:/internal/x/cc-fu/mask from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/mask.
10:39:07.621 debug: Resolve cce:/internal/x/cc-fu/graphics from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/graphics.
10:39:07.621 debug: Resolve cce:/internal/x/cc-fu/ui-skew from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/ui-skew.
10:39:07.621 debug: Resolve cce:/internal/x/cc-fu/ui from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/ui.
10:39:07.621 debug: Resolve cce:/internal/x/cc-fu/affine-transform from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/affine-transform.
10:39:07.621 debug: Resolve cce:/internal/x/cc-fu/particle from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/particle.
10:39:07.621 debug: Resolve cce:/internal/x/cc-fu/particle-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/particle-2d.
10:39:07.621 debug: Resolve cce:/internal/x/cc-fu/physics-framework from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-framework.
10:39:07.621 debug: Resolve cce:/internal/x/cc-fu/physics-cannon from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-cannon.
10:39:07.621 debug: Resolve cce:/internal/x/cc-fu/physics-physx from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-physx.
10:39:07.621 debug: Resolve cce:/internal/x/cc-fu/physics-ammo from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-ammo.
10:39:07.621 debug: Resolve cce:/internal/x/cc-fu/physics-builtin from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-builtin.
10:39:07.621 debug: Resolve cce:/internal/x/cc-fu/physics-2d-framework from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-framework.
10:39:07.621 debug: Resolve cce:/internal/x/cc-fu/physics-2d-box2d-jsb from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-box2d-jsb.
10:39:07.621 debug: Resolve cce:/internal/x/cc-fu/physics-2d-box2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-box2d.
10:39:07.621 debug: Resolve cce:/internal/x/cc-fu/physics-2d-builtin from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-builtin.
10:39:07.622 debug: Resolve cce:/internal/x/cc-fu/physics-2d-box2d-wasm from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-box2d-wasm.
10:39:07.622 debug: Resolve cce:/internal/x/cc-fu/intersection-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/intersection-2d.
10:39:07.622 debug: Resolve cce:/internal/x/cc-fu/primitive from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/primitive.
10:39:07.622 debug: Resolve cce:/internal/x/cc-fu/profiler from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/profiler.
10:39:07.622 debug: Resolve cce:/internal/x/cc-fu/geometry-renderer from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/geometry-renderer.
10:39:07.622 debug: Resolve cce:/internal/x/cc-fu/audio from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/audio.
10:39:07.622 debug: Resolve cce:/internal/x/cc-fu/video from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/video.
10:39:07.622 debug: Resolve cce:/internal/x/cc-fu/xr from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/xr.
10:39:07.622 debug: Resolve cce:/internal/x/cc-fu/light-probe from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/light-probe.
10:39:07.622 debug: Resolve cce:/internal/x/cc-fu/terrain from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/terrain.
10:39:07.622 debug: Resolve cce:/internal/x/cc-fu/webview from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/webview.
10:39:07.622 debug: Resolve cce:/internal/x/cc-fu/tween from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tween.
10:39:07.622 debug: Resolve cce:/internal/x/cc-fu/tiled-map from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tiled-map.
10:39:07.622 debug: Resolve cce:/internal/x/cc-fu/vendor-google from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/vendor-google.
10:39:07.622 debug: Resolve cce:/internal/x/cc-fu/spine from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/spine.
10:39:07.622 debug: Resolve cce:/internal/x/cc-fu/dragon-bones from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/dragon-bones.
10:39:07.622 debug: Resolve cce:/internal/x/cc-fu/custom-pipeline from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/custom-pipeline.
10:39:07.622 debug: Resolve cce:/internal/x/cc-fu/custom-pipeline-post-process from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/custom-pipeline-post-process.
10:39:07.622 debug: Resolve cce:/internal/x/cc-fu/legacy-pipeline from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/legacy-pipeline.
10:39:07.622 debug: Detected change: cce:/internal/x/prerequisite-imports. Last mtime: Thu Jan 01 1970 23:25:12 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:03 GMT+0800 (中国标准时间)
10:39:07.622 debug: Inspect cce:/internal/x/prerequisite-imports
10:39:07.665 debug: transform url: 'cce:/internal/x/prerequisite-imports' costs: 42.60 ms
10:39:07.666 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts.
10:39:07.667 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
10:39:07.667 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
10:39:07.668 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
10:39:07.668 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
10:39:07.669 debug: Resolve file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts from cce:/internal/x/prerequisite-imports as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts.
10:39:07.669 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts.
10:39:07.669 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts.
10:39:07.670 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/audio/audioManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/audio/audioManager.ts.
10:39:07.670 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/autogen/luban/schema.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/autogen/luban/schema.ts.
10:39:07.671 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/const/AttributeConst.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/const/AttributeConst.ts.
10:39:07.671 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/const/BundleConst.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/const/BundleConst.ts.
10:39:07.671 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/DataManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/DataManager.ts.
10:39:07.671 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/bag/Bag.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/bag/Bag.ts.
10:39:07.672 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/base/AttributeData.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/base/AttributeData.ts.
10:39:07.672 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/base/BaseInfo.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/base/BaseInfo.ts.
10:39:07.672 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/equip/Equip.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/equip/Equip.ts.
10:39:07.673 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/equip/EquipCombine.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/equip/EquipCombine.ts.
10:39:07.673 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/equip/EquipSlots.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/equip/EquipSlots.ts.
10:39:07.673 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/fight/Rogue.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/fight/Rogue.ts.
10:39:07.674 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/game_level/GameLevel.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/game_level/GameLevel.ts.
10:39:07.674 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/gm/GM.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/gm/GM.ts.
10:39:07.674 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/mail/Mail.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/mail/Mail.ts.
10:39:07.675 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/pk/PK.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/pk/PK.ts.
10:39:07.675 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/plane/PlaneCacheInfo.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/plane/PlaneCacheInfo.ts.
10:39:07.676 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/plane/PlaneData.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/plane/PlaneData.ts.
10:39:07.676 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/data/task/Task.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/data/task/Task.ts.
10:39:07.676 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/event/DataEvent.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/event/DataEvent.ts.
10:39:07.676 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/event/EventManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/event/EventManager.ts.
10:39:07.677 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/event/HomeUIEvent.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/event/HomeUIEvent.ts.
10:39:07.677 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/event/PlaneUIEvent.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/event/PlaneUIEvent.ts.
10:39:07.677 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/GameIns.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/GameIns.ts.
10:39:07.678 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/Bullet.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/Bullet.ts.
10:39:07.678 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/BulletPerformanceMonitor.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/BulletPerformanceMonitor.ts.
10:39:07.678 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/BulletSystem.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/BulletSystem.ts.
10:39:07.679 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/Easing.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/Easing.ts.
10:39:07.679 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/Emitter.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/Emitter.ts.
10:39:07.679 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/EventGroup.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/EventGroup.ts.
10:39:07.680 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/EventRunner.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/EventRunner.ts.
10:39:07.680 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/ObjectPool.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/ObjectPool.ts.
10:39:07.680 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/PropertyContainer.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/PropertyContainer.ts.
10:39:07.680 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/actions/BulletEventActions.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/actions/BulletEventActions.ts.
10:39:07.681 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/actions/EmitterEventActions.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/actions/EmitterEventActions.ts.
10:39:07.681 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/actions/IEventAction.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/actions/IEventAction.ts.
10:39:07.681 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/conditions/BulletEventConditions.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/conditions/BulletEventConditions.ts.
10:39:07.682 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/conditions/EmitterEventConditions.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/conditions/EmitterEventConditions.ts.
10:39:07.682 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/conditions/IEventCondition.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/conditions/IEventCondition.ts.
10:39:07.682 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/collider-system/FBoxCollider.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/collider-system/FBoxCollider.ts.
10:39:07.683 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/collider-system/FCircleCollider.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/collider-system/FCircleCollider.ts.
10:39:07.683 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/collider-system/FCollider.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/collider-system/FCollider.ts.
10:39:07.683 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/collider-system/FColliderManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/collider-system/FColliderManager.ts.
10:39:07.684 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/collider-system/FPolygonCollider.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/collider-system/FPolygonCollider.ts.
10:39:07.684 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/collider-system/Intersection.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/collider-system/Intersection.ts.
10:39:07.684 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/collider-system/QuadTree.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/collider-system/QuadTree.ts.
10:39:07.685 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/const/GameConst.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/const/GameConst.ts.
10:39:07.685 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/const/GameEnum.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/const/GameEnum.ts.
10:39:07.685 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/const/GameResourceList.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/const/GameResourceList.ts.
10:39:07.685 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/data/BossData.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/data/BossData.ts.
10:39:07.686 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/data/BulletEventData.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/data/BulletEventData.ts.
10:39:07.686 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/data/EnemyData.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/data/EnemyData.ts.
10:39:07.686 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/data/EnemyWave.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/data/EnemyWave.ts.
10:39:07.687 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/data/MainPlaneFightData.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/data/MainPlaneFightData.ts.
10:39:07.687 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/data/StageData.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/data/StageData.ts.
10:39:07.687 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/data/TrackData.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/data/TrackData.ts.
10:39:07.688 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/data/WaveData.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/data/WaveData.ts.
10:39:07.688 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/data/bullet/BulletData.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/data/bullet/BulletData.ts.
10:39:07.688 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/data/bullet/EmitterData.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/data/bullet/EmitterData.ts.
10:39:07.689 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/data/bullet/EventActionType.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/data/bullet/EventActionType.ts.
10:39:07.689 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/data/bullet/EventConditionType.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/data/bullet/EventConditionType.ts.
10:39:07.689 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/data/bullet/EventGroupData.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/data/bullet/EventGroupData.ts.
10:39:07.690 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/data/bullet/ExpressionValue.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/data/bullet/ExpressionValue.ts.
10:39:07.690 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/event/GameEvent.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/event/GameEvent.ts.
10:39:07.690 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/level/LevelItem.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/level/LevelItem.ts.
10:39:07.690 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/level/LevelItemEvent.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/level/LevelItemEvent.ts.
10:39:07.691 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/BattleManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/BattleManager.ts.
10:39:07.691 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/BossManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/BossManager.ts.
10:39:07.691 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/EnemyManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/EnemyManager.ts.
10:39:07.692 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/GameDataManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/GameDataManager.ts.
10:39:07.692 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/GamePlaneManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/GamePlaneManager.ts.
10:39:07.692 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/GameRuleManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/GameRuleManager.ts.
10:39:07.693 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/GlobalDataManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/GlobalDataManager.ts.
10:39:07.693 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/HurtEffectManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/HurtEffectManager.ts.
10:39:07.693 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/MainPlaneManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/MainPlaneManager.ts.
10:39:07.702 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/StageManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/StageManager.ts.
10:39:07.703 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/WaveManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/WaveManager.ts.
10:39:07.703 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/move/IMovable.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/move/IMovable.ts.
10:39:07.703 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/move/Movable.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/move/Movable.ts.
10:39:07.704 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/randTerrain/RandTerrain.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/randTerrain/RandTerrain.ts.
10:39:07.704 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/scenes/GameMain.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/scenes/GameMain.ts.
10:39:07.704 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/test/ColliderTest.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/test/ColliderTest.ts.
10:39:07.705 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/base/BaseComp.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/base/BaseComp.ts.
10:39:07.705 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/base/Controller.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/base/Controller.ts.
10:39:07.705 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/base/Entity.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/base/Entity.ts.
10:39:07.706 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/base/TrackComponent.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/base/TrackComponent.ts.
10:39:07.706 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/base/UIAnimMethods.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/base/UIAnimMethods.ts.
10:39:07.706 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/layer/BattleLayer.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/layer/BattleLayer.ts.
10:39:07.706 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/layer/EffectLayer.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/layer/EffectLayer.ts.
10:39:07.707 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/layer/EnemyEffectLayer.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/layer/EnemyEffectLayer.ts.
10:39:07.707 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/layer/GameInUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/layer/GameInUI.ts.
10:39:07.707 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/GameMapRun.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/GameMapRun.ts.
10:39:07.707 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelBaseUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelBaseUI.ts.
10:39:07.708 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelCondition.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelCondition.ts.
10:39:07.708 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelElemUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelElemUI.ts.
10:39:07.708 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelEventUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelEventUI.ts.
10:39:07.709 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelLayerUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelLayerUI.ts.
10:39:07.709 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelWaveUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelWaveUI.ts.
10:39:07.710 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/PlaneBase.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/PlaneBase.ts.
10:39:07.710 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/boss/BossPlane.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/boss/BossPlane.ts.
10:39:07.710 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/enemy/EnemyPlane.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/enemy/EnemyPlane.ts.
10:39:07.711 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/enemy/EnemyPlaneBase.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/enemy/EnemyPlaneBase.ts.
10:39:07.711 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/mainPlane/MainPlane.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/mainPlane/MainPlane.ts.
10:39:07.711 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/skill/BuffComp.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/skill/BuffComp.ts.
10:39:07.712 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/skill/SkillComp.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/skill/SkillComp.ts.
10:39:07.712 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/weapon/Weapon.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/weapon/Weapon.ts.
10:39:07.712 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/utils/Helper.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/utils/Helper.ts.
10:39:07.712 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/utils/RPN.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/utils/RPN.ts.
10:39:07.713 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/utils/Tools.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/utils/Tools.ts.
10:39:07.713 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/game/wave/Wave.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/game/wave/Wave.ts.
10:39:07.713 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/condition/LevelDataEventCondtion.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/condition/LevelDataEventCondtion.ts.
10:39:07.713 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/condition/LevelDataEventCondtionDelayDistance.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/condition/LevelDataEventCondtionDelayDistance.ts.
10:39:07.714 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/condition/LevelDataEventCondtionDelayTime.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/condition/LevelDataEventCondtionDelayTime.ts.
10:39:07.714 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/condition/LevelDataEventCondtionWave.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/condition/LevelDataEventCondtionWave.ts.
10:39:07.714 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/condition/newCondition.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/condition/newCondition.ts.
10:39:07.714 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/leveldata.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/leveldata.ts.
10:39:07.715 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/trigger/LevelDataEventTrigger.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/trigger/LevelDataEventTrigger.ts.
10:39:07.715 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/trigger/LevelDataEventTriggerAudio.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/trigger/LevelDataEventTriggerAudio.ts.
10:39:07.715 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/trigger/LevelDataEventTriggerLog.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/trigger/LevelDataEventTriggerLog.ts.
10:39:07.715 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/trigger/LevelDataEventTriggerWave.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/trigger/LevelDataEventTriggerWave.ts.
10:39:07.716 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/trigger/newTrigger.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/trigger/newTrigger.ts.
10:39:07.716 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/luban/LubanMgr.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/luban/LubanMgr.ts.
10:39:07.716 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/network/NetMgr.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/network/NetMgr.ts.
10:39:07.717 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/plane/PlaneManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/plane/PlaneManager.ts.
10:39:07.717 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/plane/StateDefine.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/plane/StateDefine.ts.
10:39:07.717 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/plane/StateMachine.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/plane/StateMachine.ts.
10:39:07.718 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/platformsdk/DevLogin.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/platformsdk/DevLogin.ts.
10:39:07.718 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/platformsdk/DevLoginData.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/platformsdk/DevLoginData.ts.
10:39:07.718 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/platformsdk/IPlatformSDK.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/platformsdk/IPlatformSDK.ts.
10:39:07.718 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/platformsdk/WXLogin.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/platformsdk/WXLogin.ts.
10:39:07.719 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/Plane.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/Plane.ts.
10:39:07.719 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/AnnouncementUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/AnnouncementUI.ts.
10:39:07.719 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/PopupUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/PopupUI.ts.
10:39:07.719 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/RewardUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/RewardUI.ts.
10:39:07.720 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/TextUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/TextUI.ts.
10:39:07.720 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/ToastUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/ToastUI.ts.
10:39:07.720 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/TopBlockInputUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/TopBlockInputUI.ts.
10:39:07.721 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/SelectList/uiSelect.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/SelectList/uiSelect.ts.
10:39:07.721 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/SelectList/uiSelectItem.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/SelectList/uiSelectItem.ts.
10:39:07.721 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/button/ButtonPlus.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/button/ButtonPlus.ts.
10:39:07.722 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/button/DragButton.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/button/DragButton.ts.
10:39:07.722 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/dropdown/DropDown.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/dropdown/DropDown.ts.
10:39:07.722 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/list/List.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/list/List.ts.
10:39:07.723 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/list/ListItem.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/list/ListItem.ts.
10:39:07.723 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/friend/FriendAddUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/friend/FriendAddUI.ts.
10:39:07.723 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/friend/FriendCellUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/friend/FriendCellUI.ts.
10:39:07.723 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/friend/FriendListUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/friend/FriendListUI.ts.
10:39:07.724 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/friend/FriendStrangerUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/friend/FriendStrangerUI.ts.
10:39:07.724 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/friend/FriendUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/friend/FriendUI.ts.
10:39:07.724 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/gameui/DevLoginUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/gameui/DevLoginUI.ts.
10:39:07.724 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/gameui/LoadingUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/gameui/LoadingUI.ts.
10:39:07.725 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/gameui/RatioScaler.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/gameui/RatioScaler.ts.
10:39:07.725 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/gameui/game/MBoomUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/gameui/game/MBoomUI.ts.
10:39:07.725 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/gameui/res/PlaneRes.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/gameui/res/PlaneRes.ts.
10:39:07.725 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>
10:39:07.726 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>
10:39:07.726 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>
10:39:07.726 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>
10:39:07.727 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>
10:39:07.727 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>
10:39:07.727 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/DialogueUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/DialogueUI.ts.
10:39:07.728 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/RogueSelectIcon.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/RogueSelectIcon.ts.
10:39:07.728 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/RogueUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/RogueUI.ts.
10:39:07.728 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/mail/MailCellUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/mail/MailCellUI.ts.
10:39:07.728 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/mail/MailUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/mail/MailUI.ts.
10:39:10.097 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/main/MainUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/main/MainUI.ts.
10:39:10.097 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/pk/PKHistoryCellUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/pk/PKHistoryCellUI.ts.
10:39:10.097 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/pk/PKHistoryUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/pk/PKHistoryUI.ts.
10:39:10.097 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/pk/PKRewardIcon.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/pk/PKRewardIcon.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/pk/PKUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/pk/PKUI.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/PlaneCombineResultUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/PlaneCombineResultUI.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/PlaneEquipInfoUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/PlaneEquipInfoUI.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/PlaneTypes.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/PlaneTypes.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/PlaneUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/PlaneUI.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/back_pack/BagGrid.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/back_pack/BagGrid.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/back_pack/BagItem.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/back_pack/BagItem.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/back_pack/SortTypeDropdown.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/back_pack/SortTypeDropdown.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/back_pack/Tabs.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/back_pack/Tabs.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/display/CombineDisplay.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/display/CombineDisplay.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/display/EquipDisplay.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/display/EquipDisplay.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/shop/ShopUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/shop/ShopUI.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/skyisland/SkyIslandUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/skyisland/SkyIslandUI.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/story/BuidingUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/story/BuidingUI.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/story/BuildingInfoUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/story/BuildingInfoUI.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/story/StoryUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/story/StoryUI.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/talent/TalentUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/talent/TalentUI.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/task/TaskUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/task/TaskUI.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/task/components/ProgressPanel.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/task/components/ProgressPanel.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/bundles/common/script/ui/task/components/TaskItem.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/common/script/ui/task/components/TaskItem.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/bundles/gm/script/GmEntry.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/gm/script/GmEntry.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/bundles/gm/script/ui/GmButtonUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/gm/script/ui/GmButtonUI.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/bundles/gm/script/ui/GmUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/bundles/gm/script/ui/GmUI.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/editor/emitter/EmitterEditor.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/emitter/EmitterEditor.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/editor/gizmos/EmitterGizmo.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/gizmos/EmitterGizmo.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/editor/gizmos/GizmoDrawer.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/gizmos/GizmoDrawer.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/editor/gizmos/GizmoManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/gizmos/GizmoManager.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/editor/gizmos/GizmoUtils.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/gizmos/GizmoUtils.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/editor/gizmos/LevelEventGizmo.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/gizmos/LevelEventGizmo.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/editor/gizmos/WaveGizmo.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/gizmos/WaveGizmo.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/editor/level/LevelEditorBaseUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/level/LevelEditorBaseUI.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/editor/level/LevelEditorCondition.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/level/LevelEditorCondition.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/editor/level/LevelEditorElemUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/level/LevelEditorElemUI.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/editor/level/LevelEditorEventUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/level/LevelEditorEventUI.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/editor/level/LevelEditorLayerUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/level/LevelEditorLayerUI.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/editor/level/LevelEditorUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/level/LevelEditorUI.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/editor/level/LevelEditorWaveParam.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/level/LevelEditorWaveParam.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/editor/level/LevelEditorWaveUI.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/level/LevelEditorWaveUI.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/editor/level/utils.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/level/utils.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/editor/planeview/PlaneView.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/editor/planeview/PlaneView.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/scripts/core/base/Bundle.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/core/base/Bundle.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/scripts/core/base/IMgr.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/core/base/IMgr.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/scripts/core/base/MessageBox.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/core/base/MessageBox.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/scripts/core/base/ResManager.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/core/base/ResManager.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/scripts/core/base/SingletonBase.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/core/base/SingletonBase.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/scripts/core/base/UIMgr.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/core/base/UIMgr.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/scripts/resupdate/ResUpdate.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/resupdate/ResUpdate.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/scripts/resupdate/RootPersist.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/resupdate/RootPersist.ts.
10:39:10.098 debug: Resolve file:///E:/M2Game/Client/assets/scripts/utils/Logger.ts from cce:/internal/x/prerequisite-imports as file:///E:/M2Game/Client/assets/scripts/utils/Logger.ts.
10:39:10.098 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/code-quality/cr.mjs.
10:39:10.098 debug: Detected change: cce:/internal/code-quality/cr.mjs. Last mtime: Tue Sep 16 2025 19:08:52 GMT+0800 (中国标准时间), Current mtime: Wed Sep 17 2025 10:39:03 GMT+0800 (中国标准时间)
10:39:10.098 debug: Inspect cce:/internal/code-quality/cr.mjs
10:39:10.098 debug: transform url: 'cce:/internal/code-quality/cr.mjs' costs: 8.30 ms
10:39:10.098 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
10:39:10.098 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
10:39:10.098 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
10:39:10.098 debug: Resolve cc/env from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as external dependency cc/env.
10:39:10.098 debug: Resolve ./builtin-pipeline-settings from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
10:39:10.098 debug: Resolve ./builtin-pipeline-pass from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
10:39:10.098 debug: Resolve ./builtin-pipeline from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
10:39:10.098 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/code-quality/cr.mjs.
10:39:10.098 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
10:39:10.098 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
10:39:10.098 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
10:39:10.098 debug: Resolve cc/env from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as external dependency cc/env.
10:39:10.098 debug: Resolve ./builtin-pipeline-types from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
10:39:10.098 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
10:39:10.098 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
10:39:10.098 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
10:39:10.098 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/code-quality/cr.mjs.
10:39:10.098 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
10:39:10.101 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
10:39:10.101 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
10:39:10.101 debug: Resolve ./builtin-pipeline-settings from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
10:39:10.101 debug: Resolve cc/env from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as external dependency cc/env.
10:39:10.101 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/code-quality/cr.mjs.
10:39:10.101 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
10:39:10.101 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
10:39:10.101 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
10:39:10.101 debug: Resolve cc/env from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as external dependency cc/env.
10:39:10.101 debug: Resolve ./builtin-pipeline-types from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
10:39:10.101 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
10:39:10.101 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
10:39:10.101 debug: Resolve cc from file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
10:39:10.101 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as cce:/internal/code-quality/cr.mjs.
10:39:10.101 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as cce:/internal/x/cc.
10:39:10.101 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as cce:/internal/x/cc.
10:39:10.101 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as cce:/internal/x/cc.
10:39:10.101 debug: Resolve db://assets/bundles/common/script/app/MyApp from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts.
10:39:10.101 debug: Resolve db://assets/bundles/common/script/const/BundleConst from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/const/BundleConst.ts.
10:39:10.101 debug: Resolve db://assets/scripts/core/base/Bundle from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/scripts/core/base/Bundle.ts.
10:39:10.101 debug: Resolve db://assets/scripts/core/base/ResManager from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/scripts/core/base/ResManager.ts.
10:39:10.102 debug: Resolve db://assets/scripts/core/base/UIMgr from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/scripts/core/base/UIMgr.ts.
10:39:10.102 debug: Resolve db://assets/scripts/utils/Logger from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/scripts/utils/Logger.ts.
10:39:10.102 debug: Resolve ./event/EventManager from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/event/EventManager.ts.
10:39:10.102 debug: Resolve ./event/HomeUIEvent from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/event/HomeUIEvent.ts.
10:39:10.102 debug: Resolve ./ui/friend/FriendUI from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/ui/friend/FriendUI.ts.
10:39:10.102 debug: Resolve ./ui/gameui/DevLoginUI from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/ui/gameui/DevLoginUI.ts.
10:39:10.102 debug: Resolve ./ui/home/<USER>///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>
10:39:10.102 debug: Resolve ./ui/home/<USER>///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>
10:39:10.102 debug: Resolve ./ui/home/<USER>///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>
10:39:10.102 debug: Resolve ./ui/home/<USER>///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>
10:39:10.102 debug: Resolve ./ui/mail/MailUI from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/ui/mail/MailUI.ts.
10:39:10.102 debug: Resolve ./ui/pk/PKUI from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/ui/pk/PKUI.ts.
10:39:10.102 debug: Resolve ./ui/plane/PlaneUI from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/PlaneUI.ts.
10:39:10.102 debug: Resolve ./ui/shop/ShopUI from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/ui/shop/ShopUI.ts.
10:39:10.102 debug: Resolve ./ui/skyisland/SkyIslandUI from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/ui/skyisland/SkyIslandUI.ts.
10:39:10.102 debug: Resolve ./ui/story/StoryUI from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/ui/story/StoryUI.ts.
10:39:10.102 debug: Resolve ./ui/talent/TalentUI from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/ui/talent/TalentUI.ts.
10:39:10.102 debug: Resolve ./ui/task/TaskUI from file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts as file:///E:/M2Game/Client/assets/bundles/common/script/ui/task/TaskUI.ts.
10:39:10.102 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts as cce:/internal/code-quality/cr.mjs.
10:39:10.102 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts as cce:/internal/x/cc.
10:39:10.102 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts as cce:/internal/x/cc.
10:39:10.102 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts as cce:/internal/x/cc.
10:39:10.102 debug: Resolve ../../../../scripts/core/base/ResManager from file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts as file:///E:/M2Game/Client/assets/scripts/core/base/ResManager.ts.
10:39:10.102 debug: Resolve ../audio/audioManager from file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts as file:///E:/M2Game/Client/assets/bundles/common/script/audio/audioManager.ts.
10:39:10.102 debug: Resolve ../game/manager/GlobalDataManager from file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts as file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/GlobalDataManager.ts.
10:39:10.102 debug: Resolve ../luban/LubanMgr from file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts as file:///E:/M2Game/Client/assets/bundles/common/script/luban/LubanMgr.ts.
10:39:10.102 debug: Resolve ../network/NetMgr from file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts as file:///E:/M2Game/Client/assets/bundles/common/script/network/NetMgr.ts.
10:39:10.102 debug: Resolve ../plane/PlaneManager from file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts as file:///E:/M2Game/Client/assets/bundles/common/script/plane/PlaneManager.ts.
10:39:10.102 debug: Resolve ../platformsdk/IPlatformSDK from file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts as file:///E:/M2Game/Client/assets/bundles/common/script/platformsdk/IPlatformSDK.ts.
10:39:10.102 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/scripts/core/base/ResManager.ts as cce:/internal/code-quality/cr.mjs.
10:39:10.102 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/core/base/ResManager.ts as cce:/internal/x/cc.
10:39:10.102 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/core/base/ResManager.ts as cce:/internal/x/cc.
10:39:10.102 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/core/base/ResManager.ts as cce:/internal/x/cc.
10:39:10.102 debug: Resolve ./IMgr from file:///E:/M2Game/Client/assets/scripts/core/base/ResManager.ts as file:///E:/M2Game/Client/assets/scripts/core/base/IMgr.ts.
10:39:10.102 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/core/base/IMgr.ts as cce:/internal/x/cc.
10:39:10.102 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/core/base/IMgr.ts as cce:/internal/x/cc.
10:39:10.102 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/core/base/IMgr.ts as cce:/internal/x/cc.
10:39:10.102 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/bundles/common/script/audio/audioManager.ts as cce:/internal/code-quality/cr.mjs.
10:39:10.102 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/audio/audioManager.ts as cce:/internal/x/cc.
10:39:10.102 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/audio/audioManager.ts as cce:/internal/x/cc.
10:39:10.102 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/audio/audioManager.ts as cce:/internal/x/cc.
10:39:10.102 debug: Resolve db://assets/bundles/common/script/app/MyApp from file:///E:/M2Game/Client/assets/bundles/common/script/audio/audioManager.ts as file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts.
10:39:10.102 debug: Resolve db://assets/scripts/resupdate/RootPersist from file:///E:/M2Game/Client/assets/bundles/common/script/audio/audioManager.ts as file:///E:/M2Game/Client/assets/scripts/resupdate/RootPersist.ts.
10:39:10.102 debug: Resolve ../../../../scripts/core/base/IMgr from file:///E:/M2Game/Client/assets/bundles/common/script/audio/audioManager.ts as file:///E:/M2Game/Client/assets/scripts/core/base/IMgr.ts.
10:39:10.102 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/resupdate/RootPersist.ts as cce:/internal/x/cc.
10:39:10.103 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/resupdate/RootPersist.ts as cce:/internal/x/cc.
10:39:10.103 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/resupdate/RootPersist.ts as cce:/internal/x/cc.
10:39:10.103 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/GlobalDataManager.ts as cce:/internal/code-quality/cr.mjs.
10:39:10.103 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/GlobalDataManager.ts as cce:/internal/x/cc.
10:39:10.103 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/GlobalDataManager.ts as cce:/internal/x/cc.
10:39:10.103 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/GlobalDataManager.ts as cce:/internal/x/cc.
10:39:10.103 debug: Resolve ../../../../../scripts/core/base/IMgr from file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/GlobalDataManager.ts as file:///E:/M2Game/Client/assets/scripts/core/base/IMgr.ts.
10:39:10.103 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/bundles/common/script/luban/LubanMgr.ts as cce:/internal/code-quality/cr.mjs.
10:39:10.103 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/luban/LubanMgr.ts as cce:/internal/x/cc.
10:39:10.103 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/luban/LubanMgr.ts as cce:/internal/x/cc.
10:39:10.103 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/luban/LubanMgr.ts as cce:/internal/x/cc.
10:39:10.103 debug: Resolve ../../../../scripts/core/base/IMgr from file:///E:/M2Game/Client/assets/bundles/common/script/luban/LubanMgr.ts as file:///E:/M2Game/Client/assets/scripts/core/base/IMgr.ts.
10:39:10.103 debug: Resolve ../autogen/luban/schema from file:///E:/M2Game/Client/assets/bundles/common/script/luban/LubanMgr.ts as file:///E:/M2Game/Client/assets/bundles/common/script/autogen/luban/schema.ts.
10:39:10.103 debug: Resolve ../const/BundleConst from file:///E:/M2Game/Client/assets/bundles/common/script/luban/LubanMgr.ts as file:///E:/M2Game/Client/assets/bundles/common/script/const/BundleConst.ts.
10:39:10.103 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/autogen/luban/schema.ts as cce:/internal/x/cc.
10:39:10.103 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/const/BundleConst.ts as cce:/internal/x/cc.
10:39:10.103 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/bundles/common/script/network/NetMgr.ts as cce:/internal/code-quality/cr.mjs.
10:39:10.103 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/network/NetMgr.ts as cce:/internal/x/cc.
10:39:10.103 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/network/NetMgr.ts as cce:/internal/x/cc.
10:39:10.103 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/network/NetMgr.ts as cce:/internal/x/cc.
10:39:10.103 debug: Resolve db://assets/scripts/core/base/IMgr from file:///E:/M2Game/Client/assets/bundles/common/script/network/NetMgr.ts as file:///E:/M2Game/Client/assets/scripts/core/base/IMgr.ts.
10:39:10.103 debug: Resolve db://assets/scripts/utils/Logger from file:///E:/M2Game/Client/assets/bundles/common/script/network/NetMgr.ts as file:///E:/M2Game/Client/assets/scripts/utils/Logger.ts.
10:39:10.103 debug: Resolve long from file:///E:/M2Game/Client/assets/bundles/common/script/network/NetMgr.ts as file:///E:/M2Game/Client/node_modules/long/index.js.
10:39:10.103 debug: Create cjs interop url for 'file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.js'
10:39:10.103 debug: Resolve ../autogen/pb/cs_proto.js from file:///E:/M2Game/Client/assets/bundles/common/script/network/NetMgr.ts as file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.mjs?cjs=&original=.js.
10:39:10.106 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/utils/Logger.ts as cce:/internal/x/cc.
10:39:10.106 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/utils/Logger.ts as cce:/internal/x/cc.
10:39:10.106 debug: Resolve cc from file:///E:/M2Game/Client/assets/scripts/utils/Logger.ts as cce:/internal/x/cc.
10:39:10.106 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.mjs?cjs=&original=.js as cce:/internal/code-quality/cr.mjs.
10:39:10.106 debug: Resolve ./cs_proto.js from file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.mjs?cjs=&original=.js as file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.js.
10:39:10.106 debug: Detected change: file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.js. Last mtime: Mon Sep 15 2025 18:33:14 GMT+0800 (中国标准时间)@3c91fcd0-2c60-4096-a1f6-a93ea5a2458d, Current mtime: Mon Sep 15 2025 18:33:14 GMT+0800 (中国标准时间)
10:39:10.106 debug: Inspect file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.js
10:39:10.106 debug: transform url: 'file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.js' costs: 2009.50 ms
10:39:10.106 debug: Resolve cce:/internal/ml/cjs-loader.mjs from file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.mjs?cjs=&original=.js as cce:/internal/ml/cjs-loader.mjs.
10:39:10.106 debug: Detected change: cce:/internal/ml/cjs-loader.mjs. Last mtime: Tue Sep 16 2025 19:08:52 GMT+0800 (中国标准时间), Current mtime: Wed Sep 17 2025 10:39:03 GMT+0800 (中国标准时间)
10:39:10.106 debug: Inspect cce:/internal/ml/cjs-loader.mjs
10:39:10.106 debug: transform url: 'cce:/internal/ml/cjs-loader.mjs' costs: 52.70 ms
10:39:10.106 debug: Resolve ./cs_proto.js from file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.mjs?cjs=&original=.js as file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.js.
10:39:10.106 debug: Resolve ./cs_proto.js from file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.mjs?cjs=&original=.js as file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.js.
10:39:10.106 debug: Resolve cce:/internal/ml/cjs-loader.mjs from file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.js as cce:/internal/ml/cjs-loader.mjs.
10:39:10.106 debug: Resolve protobufjs/minimal.js from file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.js as file:///E:/M2Game/Client/node_modules/protobufjs/minimal.js.
10:39:10.106 debug: Resolve cce:/internal/ml/cjs-loader.mjs from file:///E:/M2Game/Client/node_modules/protobufjs/minimal.js as cce:/internal/ml/cjs-loader.mjs.
10:39:10.106 debug: Resolve ./src/index-minimal from file:///E:/M2Game/Client/node_modules/protobufjs/minimal.js as file:///E:/M2Game/Client/node_modules/protobufjs/src/index-minimal.js.
10:39:10.106 debug: Resolve cce:/internal/ml/cjs-loader.mjs from file:///E:/M2Game/Client/node_modules/protobufjs/src/index-minimal.js as cce:/internal/ml/cjs-loader.mjs.
10:39:10.106 debug: Resolve ./writer from file:///E:/M2Game/Client/node_modules/protobufjs/src/index-minimal.js as file:///E:/M2Game/Client/node_modules/protobufjs/src/writer.js.
10:39:10.106 debug: Resolve ./writer_buffer from file:///E:/M2Game/Client/node_modules/protobufjs/src/index-minimal.js as file:///E:/M2Game/Client/node_modules/protobufjs/src/writer_buffer.js.
10:39:10.106 debug: Resolve ./reader from file:///E:/M2Game/Client/node_modules/protobufjs/src/index-minimal.js as file:///E:/M2Game/Client/node_modules/protobufjs/src/reader.js.
10:39:10.106 debug: Resolve ./reader_buffer from file:///E:/M2Game/Client/node_modules/protobufjs/src/index-minimal.js as file:///E:/M2Game/Client/node_modules/protobufjs/src/reader_buffer.js.
10:39:10.106 debug: Resolve ./util/minimal from file:///E:/M2Game/Client/node_modules/protobufjs/src/index-minimal.js as file:///E:/M2Game/Client/node_modules/protobufjs/src/util/minimal.js.
10:39:10.106 debug: Resolve ./rpc from file:///E:/M2Game/Client/node_modules/protobufjs/src/index-minimal.js as file:///E:/M2Game/Client/node_modules/protobufjs/src/rpc.js.
10:39:10.106 debug: Resolve ./roots from file:///E:/M2Game/Client/node_modules/protobufjs/src/index-minimal.js as file:///E:/M2Game/Client/node_modules/protobufjs/src/roots.js.
10:39:10.106 debug: Resolve cce:/internal/ml/cjs-loader.mjs from file:///E:/M2Game/Client/node_modules/protobufjs/src/writer.js as cce:/internal/ml/cjs-loader.mjs.
10:39:10.106 debug: Resolve ./util/minimal from file:///E:/M2Game/Client/node_modules/protobufjs/src/writer.js as file:///E:/M2Game/Client/node_modules/protobufjs/src/util/minimal.js.
10:39:10.106 debug: Resolve cce:/internal/ml/cjs-loader.mjs from file:///E:/M2Game/Client/node_modules/protobufjs/src/util/minimal.js as cce:/internal/ml/cjs-loader.mjs.
10:39:10.106 debug: Resolve @protobufjs/aspromise from file:///E:/M2Game/Client/node_modules/protobufjs/src/util/minimal.js as file:///E:/M2Game/Client/node_modules/@protobufjs/aspromise/index.js.
10:39:10.106 debug: Resolve @protobufjs/base64 from file:///E:/M2Game/Client/node_modules/protobufjs/src/util/minimal.js as file:///E:/M2Game/Client/node_modules/@protobufjs/base64/index.js.
10:39:10.106 debug: Resolve @protobufjs/eventemitter from file:///E:/M2Game/Client/node_modules/protobufjs/src/util/minimal.js as file:///E:/M2Game/Client/node_modules/@protobufjs/eventemitter/index.js.
10:39:10.106 debug: Resolve @protobufjs/float from file:///E:/M2Game/Client/node_modules/protobufjs/src/util/minimal.js as file:///E:/M2Game/Client/node_modules/@protobufjs/float/index.js.
10:39:10.106 debug: Resolve @protobufjs/inquire from file:///E:/M2Game/Client/node_modules/protobufjs/src/util/minimal.js as file:///E:/M2Game/Client/node_modules/@protobufjs/inquire/index.js.
10:39:10.106 debug: Resolve @protobufjs/utf8 from file:///E:/M2Game/Client/node_modules/protobufjs/src/util/minimal.js as file:///E:/M2Game/Client/node_modules/@protobufjs/utf8/index.js.
10:39:10.106 debug: Resolve @protobufjs/pool from file:///E:/M2Game/Client/node_modules/protobufjs/src/util/minimal.js as file:///E:/M2Game/Client/node_modules/@protobufjs/pool/index.js.
10:39:10.106 debug: Resolve ./longbits from file:///E:/M2Game/Client/node_modules/protobufjs/src/util/minimal.js as file:///E:/M2Game/Client/node_modules/protobufjs/src/util/longbits.js.
10:39:10.106 debug: Resolve cce:/internal/ml/cjs-loader.mjs from file:///E:/M2Game/Client/node_modules/@protobufjs/aspromise/index.js as cce:/internal/ml/cjs-loader.mjs.
10:39:10.106 debug: Resolve cce:/internal/ml/cjs-loader.mjs from file:///E:/M2Game/Client/node_modules/@protobufjs/base64/index.js as cce:/internal/ml/cjs-loader.mjs.
10:39:10.106 debug: Resolve cce:/internal/ml/cjs-loader.mjs from file:///E:/M2Game/Client/node_modules/@protobufjs/eventemitter/index.js as cce:/internal/ml/cjs-loader.mjs.
10:39:10.106 debug: Resolve cce:/internal/ml/cjs-loader.mjs from file:///E:/M2Game/Client/node_modules/@protobufjs/float/index.js as cce:/internal/ml/cjs-loader.mjs.
10:39:10.106 debug: Resolve cce:/internal/ml/cjs-loader.mjs from file:///E:/M2Game/Client/node_modules/@protobufjs/inquire/index.js as cce:/internal/ml/cjs-loader.mjs.
10:39:10.106 debug: Resolve cce:/internal/ml/cjs-loader.mjs from file:///E:/M2Game/Client/node_modules/@protobufjs/utf8/index.js as cce:/internal/ml/cjs-loader.mjs.
10:39:10.106 debug: Resolve cce:/internal/ml/cjs-loader.mjs from file:///E:/M2Game/Client/node_modules/@protobufjs/pool/index.js as cce:/internal/ml/cjs-loader.mjs.
10:39:10.106 debug: Resolve cce:/internal/ml/cjs-loader.mjs from file:///E:/M2Game/Client/node_modules/protobufjs/src/util/longbits.js as cce:/internal/ml/cjs-loader.mjs.
10:39:10.106 debug: Resolve ../util/minimal from file:///E:/M2Game/Client/node_modules/protobufjs/src/util/longbits.js as file:///E:/M2Game/Client/node_modules/protobufjs/src/util/minimal.js.
10:39:10.106 debug: Resolve cce:/internal/ml/cjs-loader.mjs from file:///E:/M2Game/Client/node_modules/protobufjs/src/writer_buffer.js as cce:/internal/ml/cjs-loader.mjs.
10:39:10.106 debug: Resolve ./writer from file:///E:/M2Game/Client/node_modules/protobufjs/src/writer_buffer.js as file:///E:/M2Game/Client/node_modules/protobufjs/src/writer.js.
10:39:10.106 debug: Resolve ./util/minimal from file:///E:/M2Game/Client/node_modules/protobufjs/src/writer_buffer.js as file:///E:/M2Game/Client/node_modules/protobufjs/src/util/minimal.js.
10:39:10.106 debug: Resolve cce:/internal/ml/cjs-loader.mjs from file:///E:/M2Game/Client/node_modules/protobufjs/src/reader.js as cce:/internal/ml/cjs-loader.mjs.
10:39:10.106 debug: Resolve ./util/minimal from file:///E:/M2Game/Client/node_modules/protobufjs/src/reader.js as file:///E:/M2Game/Client/node_modules/protobufjs/src/util/minimal.js.
10:39:10.106 debug: Resolve cce:/internal/ml/cjs-loader.mjs from file:///E:/M2Game/Client/node_modules/protobufjs/src/reader_buffer.js as cce:/internal/ml/cjs-loader.mjs.
10:39:10.106 debug: Resolve ./reader from file:///E:/M2Game/Client/node_modules/protobufjs/src/reader_buffer.js as file:///E:/M2Game/Client/node_modules/protobufjs/src/reader.js.
10:39:10.106 debug: Resolve ./util/minimal from file:///E:/M2Game/Client/node_modules/protobufjs/src/reader_buffer.js as file:///E:/M2Game/Client/node_modules/protobufjs/src/util/minimal.js.
10:39:10.106 debug: Resolve cce:/internal/ml/cjs-loader.mjs from file:///E:/M2Game/Client/node_modules/protobufjs/src/rpc.js as cce:/internal/ml/cjs-loader.mjs.
10:39:10.106 debug: Resolve ./rpc/service from file:///E:/M2Game/Client/node_modules/protobufjs/src/rpc.js as file:///E:/M2Game/Client/node_modules/protobufjs/src/rpc/service.js.
10:39:10.106 debug: Resolve cce:/internal/ml/cjs-loader.mjs from file:///E:/M2Game/Client/node_modules/protobufjs/src/rpc/service.js as cce:/internal/ml/cjs-loader.mjs.
10:39:10.106 debug: Resolve ../util/minimal from file:///E:/M2Game/Client/node_modules/protobufjs/src/rpc/service.js as file:///E:/M2Game/Client/node_modules/protobufjs/src/util/minimal.js.
10:39:10.106 debug: Resolve cce:/internal/ml/cjs-loader.mjs from file:///E:/M2Game/Client/node_modules/protobufjs/src/roots.js as cce:/internal/ml/cjs-loader.mjs.
10:39:10.106 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/bundles/common/script/plane/PlaneManager.ts as cce:/internal/code-quality/cr.mjs.
10:39:10.106 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/plane/PlaneManager.ts as cce:/internal/x/cc.
10:39:10.106 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/plane/PlaneManager.ts as cce:/internal/x/cc.
10:39:10.106 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/plane/PlaneManager.ts as cce:/internal/x/cc.
10:39:10.106 debug: Resolve db://assets/bundles/common/script/ui/Plane from file:///E:/M2Game/Client/assets/bundles/common/script/plane/PlaneManager.ts as file:///E:/M2Game/Client/assets/bundles/common/script/ui/Plane.ts.
10:39:10.106 debug: Resolve db://assets/scripts/core/base/IMgr from file:///E:/M2Game/Client/assets/bundles/common/script/plane/PlaneManager.ts as file:///E:/M2Game/Client/assets/scripts/core/base/IMgr.ts.
10:39:10.106 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/bundles/common/script/ui/Plane.ts as cce:/internal/code-quality/cr.mjs.
10:39:10.106 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/ui/Plane.ts as cce:/internal/x/cc.
10:39:10.106 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/ui/Plane.ts as cce:/internal/x/cc.
10:39:10.106 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/ui/Plane.ts as cce:/internal/x/cc.
10:39:10.106 debug: Resolve db://assets/bundles/common/script/app/MyApp from file:///E:/M2Game/Client/assets/bundles/common/script/ui/Plane.ts as file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts.
10:39:10.106 debug: Resolve ../plane/StateDefine from file:///E:/M2Game/Client/assets/bundles/common/script/ui/Plane.ts as file:///E:/M2Game/Client/assets/bundles/common/script/plane/StateDefine.ts.
10:39:10.106 debug: Resolve ../plane/StateMachine from file:///E:/M2Game/Client/assets/bundles/common/script/ui/Plane.ts as file:///E:/M2Game/Client/assets/bundles/common/script/plane/StateMachine.ts.
10:39:10.106 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/plane/StateDefine.ts as cce:/internal/x/cc.
10:39:10.106 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/bundles/common/script/plane/StateMachine.ts as cce:/internal/code-quality/cr.mjs.
10:39:10.106 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/plane/StateMachine.ts as cce:/internal/x/cc.
10:39:10.106 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/plane/StateMachine.ts as cce:/internal/x/cc.
10:39:10.106 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/plane/StateMachine.ts as cce:/internal/x/cc.
10:39:10.106 debug: Resolve ./StateDefine from file:///E:/M2Game/Client/assets/bundles/common/script/plane/StateMachine.ts as file:///E:/M2Game/Client/assets/bundles/common/script/plane/StateDefine.ts.
10:39:10.106 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/bundles/common/script/platformsdk/IPlatformSDK.ts as cce:/internal/code-quality/cr.mjs.
10:39:10.106 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/platformsdk/IPlatformSDK.ts as cce:/internal/x/cc.
10:39:10.106 debug: Resolve ./WXLogin from file:///E:/M2Game/Client/assets/bundles/common/script/platformsdk/IPlatformSDK.ts as file:///E:/M2Game/Client/assets/bundles/common/script/platformsdk/WXLogin.ts.
10:39:10.106 debug: Resolve ./DevLogin from file:///E:/M2Game/Client/assets/bundles/common/script/platformsdk/IPlatformSDK.ts as file:///E:/M2Game/Client/assets/bundles/common/script/platformsdk/DevLogin.ts.
10:39:10.106 debug: Resolve cc/env from file:///E:/M2Game/Client/assets/bundles/common/script/platformsdk/IPlatformSDK.ts as external dependency cc/env.
10:39:10.106 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/bundles/common/script/platformsdk/WXLogin.ts as cce:/internal/code-quality/cr.mjs.
10:39:10.106 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/platformsdk/WXLogin.ts as cce:/internal/x/cc.
10:39:10.106 debug: Resolve ../../../../scripts/utils/Logger from file:///E:/M2Game/Client/assets/bundles/common/script/platformsdk/WXLogin.ts as file:///E:/M2Game/Client/assets/scripts/utils/Logger.ts.
10:39:10.106 debug: Create cjs interop url for 'file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.js'
10:39:10.106 debug: Resolve ../autogen/pb/cs_proto.js from file:///E:/M2Game/Client/assets/bundles/common/script/platformsdk/WXLogin.ts as file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.mjs?cjs=&original=.js.
10:39:10.106 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///E:/M2Game/Client/assets/bundles/common/script/platformsdk/DevLogin.ts as cce:/internal/code-quality/cr.mjs.
10:39:10.106 debug: Resolve cc from file:///E:/M2Game/Client/assets/bundles/common/script/platformsdk/DevLogin.ts as cce:/internal/x/cc.
10:39:10.106 debug: Resolve ../../../../scripts/utils/Logger from file:///E:/M2Game/Client/assets/bundles/common/script/platformsdk/DevLogin.ts as file:///E:/M2Game/Client/assets/scripts/utils/Logger.ts.
10:39:10.106 debug: Create cjs interop url for 'file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.js'
10:39:10.106 debug: Resolve ../autogen/pb/cs_proto.js from file:///E:/M2Game/Client/assets/bundles/common/script/platformsdk/DevLogin.ts as file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.mjs?cjs=&original=.js.
10:39:10.106 debug: Resolve ./DevLoginData from file:///E:/M2Game/Client/assets/bundles/common/script/platformsdk/DevLogin.ts as file:///E:/M2Game/Client/assets/bundles/common/script/platformsdk/DevLoginData.ts.
