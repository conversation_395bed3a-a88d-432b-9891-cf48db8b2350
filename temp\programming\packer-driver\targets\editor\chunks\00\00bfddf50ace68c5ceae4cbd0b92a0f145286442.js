System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, Font, instantiate, Label, Prefab, Tween, v3, warn, MyApp, SingletonBase, GameIns, GameResourceList, UIAnimMethods, EnemyEffectLayer, Tools, HurtEffectManager, _crd;

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/bundles/common/script/app/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfSingletonBase(extras) {
    _reporterNs.report("SingletonBase", "../../../../../scripts/core/base/SingletonBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameResourceList(extras) {
    _reporterNs.report("GameResourceList", "../const/GameResourceList", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIAnimMethods(extras) {
    _reporterNs.report("UIAnimMethods", "../ui/base/UIAnimMethods", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyEffectLayer(extras) {
    _reporterNs.report("EnemyEffectLayer", "../ui/layer/EnemyEffectLayer", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTools(extras) {
    _reporterNs.report("Tools", "../utils/Tools", _context.meta, extras);
  }

  _export("HurtEffectManager", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      Font = _cc.Font;
      instantiate = _cc.instantiate;
      Label = _cc.Label;
      Prefab = _cc.Prefab;
      Tween = _cc.Tween;
      v3 = _cc.v3;
      warn = _cc.warn;
    }, function (_unresolved_2) {
      MyApp = _unresolved_2.MyApp;
    }, function (_unresolved_3) {
      SingletonBase = _unresolved_3.SingletonBase;
    }, function (_unresolved_4) {
      GameIns = _unresolved_4.GameIns;
    }, function (_unresolved_5) {
      GameResourceList = _unresolved_5.default;
    }, function (_unresolved_6) {
      UIAnimMethods = _unresolved_6.default;
    }, function (_unresolved_7) {
      EnemyEffectLayer = _unresolved_7.default;
    }, function (_unresolved_8) {
      Tools = _unresolved_8.Tools;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "332b8evRfBBEqiCFOV8hH5Y", "HurtEffectManager", undefined);

      __checkObsolete__(['Font', 'instantiate', 'Label', 'Node', 'Prefab', 'Tween', 'v3', 'Vec3', 'warn']);

      _export("HurtEffectManager", HurtEffectManager = class HurtEffectManager extends (_crd && SingletonBase === void 0 ? (_reportPossibleCrUseOfSingletonBase({
        error: Error()
      }), SingletonBase) : SingletonBase) {
        constructor(...args) {
          super(...args);
          this.ratio = 0.8;
          this.hurtNum = null;
          this.m_hurtNums = new Map();
          this.m_hurtFont = new Map();
        }

        /**
         * 预加载资源
         */
        preLoad() {
          if (!this.hurtNum) {
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.addLoadCount(1);
            (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.load((_crd && GameResourceList === void 0 ? (_reportPossibleCrUseOfGameResourceList({
              error: Error()
            }), GameResourceList) : GameResourceList).HurtNum, Prefab, (error, prefab) => {
              this.hurtNum = prefab;
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).battleManager.checkLoadFinish();
            });
          }

          if (!this.m_hurtFont) {
            this.m_hurtFont = new Map();
          }

          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.addLoadCount(1);
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.loadDir((_crd && GameResourceList === void 0 ? (_reportPossibleCrUseOfGameResourceList({
            error: Error()
          }), GameResourceList) : GameResourceList).font_hurtNum, Font, (error, fonts) => {
            fonts.forEach(font => {
              if (font) {
                this.m_hurtFont.set(font.name, font);
              }
            });
            this.m_hurtNums.clear();
            this.m_hurtFont.forEach((font, name) => {
              const instances = [];

              for (let i = 0; i < 3; i++) {
                if (!this.hurtNum) continue;
                const labelNode = instantiate(this.hurtNum);
                const label = labelNode.getComponent(Label);
                label.string = "";
                label.font = font;
                instances.push(label);
              }

              this.m_hurtNums.set(name, instances);
            });
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.checkLoadFinish();
          });
        }
        /**
         * 清理资源
         */


        clear() {
          (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).clearMapForCompArr(this.m_hurtNums);
        }

        createHurtNumByType(position, damage, isCirt = false) {
          if (damage <= 0) return;
          const fontType = isCirt ? "yellowHurtNum" : "whiteHurtNum";
          const lab = this.GetHurtNumsByCount(fontType, damage);

          if (lab && (_crd && EnemyEffectLayer === void 0 ? (_reportPossibleCrUseOfEnemyEffectLayer({
            error: Error()
          }), EnemyEffectLayer) : EnemyEffectLayer).me.hurtNumLayer) {
            lab.node.parent = (_crd && EnemyEffectLayer === void 0 ? (_reportPossibleCrUseOfEnemyEffectLayer({
              error: Error()
            }), EnemyEffectLayer) : EnemyEffectLayer).me.hurtNumLayer;
            lab.node.setPosition(position.x, position.y - 30);
            this.startHurtAni(lab, fontType);
          }
        }

        GetHurtNumsByCount(fontType, damage) {
          let hurtNum = null;
          const pool = this.m_hurtNums.get(fontType);

          if (pool) {
            if (pool.length > 0) {
              hurtNum = pool.pop();
            } else {
              hurtNum = instantiate(this.hurtNum).getComponent(Label);
              hurtNum.font = this.m_hurtFont.get(fontType);
            }
          }

          hurtNum.node.opacity = 255;
          hurtNum.node.active = true;
          hurtNum.string = Math.ceil(damage).toString();
          return hurtNum;
        }

        startHurtAni(hurtNum, fontType) {
          const ratio = this.ratio;
          Tween.stopAllByTarget(hurtNum.node);
          let tween;

          switch (fontType) {
            case "whiteHurtNum":
              hurtNum.node.setScale(0.15, 0.15);
              tween = new Tween(hurtNum.node).to((_crd && UIAnimMethods === void 0 ? (_reportPossibleCrUseOfUIAnimMethods({
                error: Error()
              }), UIAnimMethods) : UIAnimMethods).fromTo(1, 6), {
                scale: v3(1.53 * ratio, 1.53 * ratio)
              }).to((_crd && UIAnimMethods === void 0 ? (_reportPossibleCrUseOfUIAnimMethods({
                error: Error()
              }), UIAnimMethods) : UIAnimMethods).fromTo(6, 11), {
                scale: v3(0.47 * ratio, 0.47 * ratio)
              }).to((_crd && UIAnimMethods === void 0 ? (_reportPossibleCrUseOfUIAnimMethods({
                error: Error()
              }), UIAnimMethods) : UIAnimMethods).fromTo(11, 32), {
                position: v3(hurtNum.node.x, hurtNum.node.y + 13),
                scale: v3(0.47 * ratio, 0.47 * ratio)
              });
              break;

            case "yellowHurtNum":
              hurtNum.node.setScale(0.16, 0.16);
              tween = new Tween(hurtNum.node).to((_crd && UIAnimMethods === void 0 ? (_reportPossibleCrUseOfUIAnimMethods({
                error: Error()
              }), UIAnimMethods) : UIAnimMethods).fromTo(1, 6), {
                scale: v3(1.75 * ratio, 1.75 * ratio)
              }).to((_crd && UIAnimMethods === void 0 ? (_reportPossibleCrUseOfUIAnimMethods({
                error: Error()
              }), UIAnimMethods) : UIAnimMethods).fromTo(6, 9), {
                scale: v3(0.44 * ratio, 0.44 * ratio)
              }).to((_crd && UIAnimMethods === void 0 ? (_reportPossibleCrUseOfUIAnimMethods({
                error: Error()
              }), UIAnimMethods) : UIAnimMethods).fromTo(9, 12), {
                scale: v3(0.52 * ratio, 0.52 * ratio)
              }).to((_crd && UIAnimMethods === void 0 ? (_reportPossibleCrUseOfUIAnimMethods({
                error: Error()
              }), UIAnimMethods) : UIAnimMethods).fromTo(12, 31), {
                position: v3(hurtNum.node.x, hurtNum.node.y + 21),
                scale: v3(0.52 * ratio, 0.52 * ratio)
              });
              break;

            default:
              warn("Unknown font type in createHurtNumInTarget");
          }

          tween.call(() => {
            this.pushHurtNums(fontType, hurtNum);
          }).start();
        }

        pushHurtNums(fontType, hurtNum) {
          if (hurtNum && hurtNum.node) {
            hurtNum.string = "";
            hurtNum.node.active = false;
            const pool = this.m_hurtNums.get(fontType);

            if (pool) {
              pool.push(hurtNum);
            } else {
              hurtNum.node.destroy();
            }
          }
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=00bfddf50ace68c5ceae4cbd0b92a0f145286442.js.map