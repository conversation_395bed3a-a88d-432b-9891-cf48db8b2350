{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/PlaneBase.ts"], "names": ["_decorator", "Label", "Node", "Sprite", "tween", "Tween", "EffectType", "AttributeConst", "AttributeData", "Entity", "GameEnum", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>llComp", "ccclass", "property", "PlaneBase", "enemy", "isDead", "type", "bDamageable", "maxHp", "curHp", "collide<PERSON>omp", "_skillComp", "_buffComp", "_attributeData", "init", "addComp", "skillComp", "buff<PERSON><PERSON>p", "attribute", "colliderEnabled", "value", "isEnable", "CastSkill", "skillID", "Cast", "addHp", "heal", "Math", "min", "updateHpUI", "hurt", "damage", "cutHp", "playHurtAnim", "to<PERSON><PERSON>", "newHp", "max", "destroyType", "EnemyDestroyType", "Die", "hpBar", "fill<PERSON><PERSON><PERSON>", "hpAniSprite", "duration", "abs", "stopAllByTarget", "to", "call", "start", "hpfont", "string", "toFixed", "ApplyBuffEffect", "buff", "effectData", "Kill", "Hurt", "param", "length", "AttrMaxHPPer", "ApplyBuffAttributeEffect", "MaxHP", "AttrMaxHPAdd", "AttrHPRecoveryPer", "HPRecovery", "AttrHPRecoveryAdd", "AttrHPRecoveryMaxHPPerAdd", "HPRecoveryRate", "AttrAttackPer", "Attack", "AttrAttackAdd", "AttrAttackBossPer", "AttackBoss", "AttrAttackNormalPer", "AttackNormal", "AttrFortunatePer", "Fortunate", "AttrFortunateAdd", "AttrMissAdd", "MissRate", "AttrBulletHurtResistancePer", "BulletHurtResistance", "AttrBulletHurtResistanceAdd", "AttrCollisionHurtResistancePer", "CollisionHurtResistance", "AttrCollisionHurtResistanceAdd", "AttrFinalScoreAdd", "FinalScoreRate", "AttrKillScoreAdd", "KillScoreRate", "AttrEnergyRecoveryPerAdd", "EnergyRecoveryRate", "AttrEnergyRecoveryAdd", "EnergyRecovery", "AttrPickRadiusPer", "PickRadius", "AttrPickRadiusAdd", "AttrBombMax", "BombMax", "AttrBombHurtAdd", "BombHurt", "AttrBombHurtPer", "key", "isPer", "addModify", "id", "RemoveBuffEffect", "removeModify", "setAnimSpeed", "speed"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,K,OAAAA,K;;AAG3BC,MAAAA,U,iBAAAA,U;;AACbC,MAAAA,c,iBAAAA,c;;AACAC,MAAAA,a,iBAAAA,a;;AAIFC,MAAAA,M;;AACEC,MAAAA,Q,iBAAAA,Q;;AACFC,MAAAA,Q;;AACAC,MAAAA,S;;;;;;;;;OAXD;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBd,U;;yBAeTe,S,WADpBF,OAAO,CAAC,WAAD,C,UAGHC,QAAQ,CAACZ,IAAD,C,UAERY,QAAQ,CAACX,MAAD,C,UAERW,QAAQ,CAACX,MAAD,C,UAERW,QAAQ,CAACb,KAAD,C,2BATb,MACqBc,SADrB;AAAA;AAAA,4BAC8C;AAAA;AAAA;;AAAA;;AAAA;;AAKb;AALa;;AAOP;AAPO;;AASb;AATa,eAW1CC,KAX0C,GAWlC,IAXkC;AAW5B;AAX4B,eAY1CC,MAZ0C,GAYjC,KAZiC;AAY1B;AAZ0B,eAa1CC,IAb0C,GAanC,CAbmC;AAahC;AAbgC,eAc1CC,WAd0C,GAcnB,IAdmB;AAcb;AAda,eAgB1CC,KAhB0C,GAgB1B,CAhB0B;AAAA,eAiB1CC,KAjB0C,GAiB1B,CAjB0B;AAAA,eAmB1CC,WAnB0C,GAmB8B,IAnB9B;AAmBoC;AAnBpC,eAqBlCC,UArBkC,GAqBH,IArBG;AAAA,eAsBlCC,SAtBkC,GAsBL,IAtBK;AAwB1C;AAxB0C,eAyBlCC,cAzBkC,GAyBF;AAAA;AAAA,+CAzBE;AAAA;;AA2B1CC,QAAAA,IAAI,GAAG;AACH,eAAKH,UAAL,GAAkB;AAAA;AAAA,uCAAlB;AACA,eAAKI,OAAL,CAAa,OAAb,EAAsB,KAAKJ,UAA3B;AACA,eAAKC,SAAL,GAAiB;AAAA;AAAA,qCAAjB;AACA,eAAKG,OAAL,CAAa,MAAb,EAAqB,KAAKH,SAA1B;AACA,gBAAME,IAAN;AACH;;AAEY,YAATE,SAAS,GAAG;AACZ,iBAAO,KAAKL,UAAZ;AACH;;AAEW,YAARM,QAAQ,GAAG;AACX,iBAAO,KAAKL,SAAZ;AACH;;AAEY,YAATM,SAAS,GAAkB;AAC3B,iBAAO,KAAKL,cAAZ;AACH;;AAEkB,YAAfM,eAAe,CAACC,KAAD,EAAiB;AAChC,cAAI,KAAKV,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAiBW,QAAjB,GAA4BD,KAA5B;AACH;AACJ;;AACkB,YAAfD,eAAe,GAAY;AAC3B,iBAAO,KAAKT,WAAL,GAAmB,KAAKA,WAAL,CAAiBW,QAApC,GAA+C,KAAtD;AACH;;AAEDC,QAAAA,SAAS,CAACC,OAAD,EAAkB;AACvB,eAAKP,SAAL,CAAeQ,IAAf,CAAoB,IAApB,EAA0BD,OAA1B;AACH;;AAEDE,QAAAA,KAAK,CAACC,IAAD,EAAe;AAChB,eAAKjB,KAAL,GAAakB,IAAI,CAACC,GAAL,CACT,KAAKpB,KADI,EAET,KAAKC,KAAL,GAAaiB,IAFJ,CAAb;AAIA,eAAKG,UAAL;AAAkB;AACrB;;AAEDC,QAAAA,IAAI,CAACC,MAAD,EAAiB;AACjB,cAAI,KAAK1B,MAAT,EAAiB;AACb;AACH;;AACD,eAAK2B,KAAL,CAAWD,MAAX;AACA,eAAKE,YAAL;;AACA,cAAI,KAAKxB,KAAL,IAAc,CAAlB,EAAqB;AACjB,iBAAKyB,KAAL;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIF,QAAAA,KAAK,CAACD,MAAD,EAAiB;AAClB,gBAAMI,KAAK,GAAG,KAAK1B,KAAL,GAAasB,MAA3B;AACA,eAAKtB,KAAL,GAAakB,IAAI,CAACS,GAAL,CAAS,CAAT,EAAYD,KAAZ,CAAb;AAEA,eAAKN,UAAL;AACH;;AAEDK,QAAAA,KAAK,CAACG,WAAsC,GAAG;AAAA;AAAA,kCAASC,gBAAT,CAA0BC,GAApE,EAAkF;AACnF,cAAI,KAAKlC,MAAT,EAAiB;AACb,mBAAO,KAAP;AACH;;AACD,eAAKA,MAAL,GAAc,IAAd;AACA,eAAKc,eAAL,GAAuB,KAAvB;AACA,iBAAO,IAAP;AACH;AACD;AACJ;AACA;;;AACIU,QAAAA,UAAU,GAAG;AACT,cAAI,KAAKW,KAAT,EAAgB;AACZ;AACA,iBAAKA,KAAL,CAAWC,SAAX,GAAuB,KAAKhC,KAAL,GAAa,KAAKD,KAAzC;;AAEA,gBAAI,KAAKkC,WAAT,EAAsB;AAClB;AACA,oBAAMC,QAAQ,GAAGhB,IAAI,CAACiB,GAAL,CAAS,KAAKF,WAAL,CAAiBD,SAAjB,GAA6B,KAAKD,KAAL,CAAWC,SAAjD,CAAjB;AAEAhD,cAAAA,KAAK,CAACoD,eAAN,CAAsB,KAAKH,WAA3B,EAJkB,CAKlB;;AACAlD,cAAAA,KAAK,CAAC,KAAKkD,WAAN,CAAL,CACKI,EADL,CACQH,QADR,EACkB;AAAEF,gBAAAA,SAAS,EAAE,KAAKD,KAAL,CAAWC;AAAxB,eADlB,EAEKM,IAFL,CAEU,MAAM,CAEX,CAJL,EAKKC,KALL;AAMH;AACJ,WAlBQ,CAoBT;;;AACA,eAAKC,MAAL,KAAgB,KAAKA,MAAL,CAAaC,MAAb,GAAsB,KAAKzC,KAAL,CAAW0C,OAAX,CAAmB,CAAnB,CAAtC;AACH;;AAEDlB,QAAAA,YAAY,GAAG,CACX;AACH;;AAEDmB,QAAAA,eAAe,CAACC,IAAD,EAAoBC,UAApB,EAA6C;AACxD,kBAAQA,UAAU,CAAChD,IAAnB;AACI,iBAAK;AAAA;AAAA,0CAAWiD,IAAhB;AACI,mBAAKrB,KAAL;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWsB,IAAhB;AACI,kBAAIF,UAAU,CAACG,KAAX,CAAiBC,MAAjB,IAA2B,CAA/B,EAAkC;AAC9B,qBAAK5B,IAAL,CAAUwB,UAAU,CAACG,KAAX,CAAiB,CAAjB,CAAV;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAWE,YAAhB;AACI,mBAAKC,wBAAL,CAA8BP,IAA9B,EAAoC;AAAA;AAAA,oDAAeQ,KAAnD,EAA0DP,UAA1D,EAAsE,IAAtE;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWQ,YAAhB;AACI,mBAAKF,wBAAL,CAA8BP,IAA9B,EAAoC;AAAA;AAAA,oDAAeQ,KAAnD,EAA0DP,UAA1D,EAAsE,KAAtE;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWS,iBAAhB;AACI,mBAAKH,wBAAL,CAA8BP,IAA9B,EAAoC;AAAA;AAAA,oDAAeW,UAAnD,EAA+DV,UAA/D,EAA2E,IAA3E;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWW,iBAAhB;AACI,mBAAKL,wBAAL,CAA8BP,IAA9B,EAAoC;AAAA;AAAA,oDAAeW,UAAnD,EAA+DV,UAA/D,EAA2E,KAA3E;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWY,yBAAhB;AACI,mBAAKN,wBAAL,CAA8BP,IAA9B,EAAoC;AAAA;AAAA,oDAAec,cAAnD,EAAmEb,UAAnE,EAA+E,KAA/E;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWc,aAAhB;AACI,mBAAKR,wBAAL,CAA8BP,IAA9B,EAAoC;AAAA;AAAA,oDAAegB,MAAnD,EAA2Df,UAA3D,EAAuE,IAAvE;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWgB,aAAhB;AACI,mBAAKV,wBAAL,CAA8BP,IAA9B,EAAoC;AAAA;AAAA,oDAAegB,MAAnD,EAA2Df,UAA3D,EAAuE,KAAvE;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWiB,iBAAhB;AACI,mBAAKX,wBAAL,CAA8BP,IAA9B,EAAoC;AAAA;AAAA,oDAAemB,UAAnD,EAA+DlB,UAA/D,EAA2E,IAA3E;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWmB,mBAAhB;AACI,mBAAKb,wBAAL,CAA8BP,IAA9B,EAAoC;AAAA;AAAA,oDAAeqB,YAAnD,EAAiEpB,UAAjE,EAA6E,IAA7E;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWqB,gBAAhB;AACI,mBAAKf,wBAAL,CAA8BP,IAA9B,EAAoC;AAAA;AAAA,oDAAeuB,SAAnD,EAA8DtB,UAA9D,EAA0E,IAA1E;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWuB,gBAAhB;AACI,mBAAKjB,wBAAL,CAA8BP,IAA9B,EAAoC;AAAA;AAAA,oDAAeuB,SAAnD,EAA8DtB,UAA9D,EAA0E,KAA1E;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWwB,WAAhB;AACI,mBAAKlB,wBAAL,CAA8BP,IAA9B,EAAoC;AAAA;AAAA,oDAAe0B,QAAnD,EAA6DzB,UAA7D,EAAyE,KAAzE;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAW0B,2BAAhB;AACI,mBAAKpB,wBAAL,CAA8BP,IAA9B,EAAoC;AAAA;AAAA,oDAAe4B,oBAAnD,EAAyE3B,UAAzE,EAAqF,IAArF;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAW4B,2BAAhB;AACI,mBAAKtB,wBAAL,CAA8BP,IAA9B,EAAoC;AAAA;AAAA,oDAAe4B,oBAAnD,EAAyE3B,UAAzE,EAAqF,KAArF;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAW6B,8BAAhB;AACI,mBAAKvB,wBAAL,CAA8BP,IAA9B,EAAoC;AAAA;AAAA,oDAAe+B,uBAAnD,EAA4E9B,UAA5E,EAAwF,IAAxF;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAW+B,8BAAhB;AACI,mBAAKzB,wBAAL,CAA8BP,IAA9B,EAAoC;AAAA;AAAA,oDAAe+B,uBAAnD,EAA4E9B,UAA5E,EAAwF,KAAxF;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWgC,iBAAhB;AACI,mBAAK1B,wBAAL,CAA8BP,IAA9B,EAAoC;AAAA;AAAA,oDAAekC,cAAnD,EAAmEjC,UAAnE,EAA+E,IAA/E;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWkC,gBAAhB;AACI,mBAAK5B,wBAAL,CAA8BP,IAA9B,EAAoC;AAAA;AAAA,oDAAeoC,aAAnD,EAAkEnC,UAAlE,EAA8E,IAA9E;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWoC,wBAAhB;AACI,mBAAK9B,wBAAL,CAA8BP,IAA9B,EAAoC;AAAA;AAAA,oDAAesC,kBAAnD,EAAuErC,UAAvE,EAAmF,KAAnF;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWsC,qBAAhB;AACI,mBAAKhC,wBAAL,CAA8BP,IAA9B,EAAoC;AAAA;AAAA,oDAAewC,cAAnD,EAAmEvC,UAAnE,EAA+E,KAA/E;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWwC,iBAAhB;AACI,mBAAKlC,wBAAL,CAA8BP,IAA9B,EAAoC;AAAA;AAAA,oDAAe0C,UAAnD,EAA+DzC,UAA/D,EAA2E,IAA3E;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAW0C,iBAAhB;AACI,mBAAKpC,wBAAL,CAA8BP,IAA9B,EAAoC;AAAA;AAAA,oDAAe0C,UAAnD,EAA+DzC,UAA/D,EAA2E,KAA3E;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAW2C,WAAhB;AACI,mBAAKrC,wBAAL,CAA8BP,IAA9B,EAAoC;AAAA;AAAA,oDAAe6C,OAAnD,EAA4D5C,UAA5D,EAAwE,KAAxE;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAW6C,eAAhB;AACI,mBAAKvC,wBAAL,CAA8BP,IAA9B,EAAoC;AAAA;AAAA,oDAAe+C,QAAnD,EAA6D9C,UAA7D,EAAyE,KAAzE;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAW+C,eAAhB;AACI,mBAAKzC,wBAAL,CAA8BP,IAA9B,EAAoC;AAAA;AAAA,oDAAe+C,QAAnD,EAA6D9C,UAA7D,EAAyE,IAAzE;AACA;;AACJ;AACI;AArFR;AAuFH;;AACOM,QAAAA,wBAAwB,CAACP,IAAD,EAAoBiD,GAApB,EAAiChD,UAAjC,EAA0DiD,KAA1D,EAA0E;AACtG,cAAI,CAAClD,IAAL,EAAW;AACP;AACH;;AACD,cAAIC,UAAU,CAACG,KAAX,CAAiBC,MAAjB,GAA0B,CAA9B,EAAiC;AAC7B;AACH;;AACD,eAAKxC,SAAL,CAAesF,SAAf,CAAyBnD,IAAI,CAACoD,EAA9B,EAAkCH,GAAlC,EAAuChD,UAAU,CAACG,KAAX,CAAiB,CAAjB,CAAvC,EAA4D8C,KAA5D;AACH;;AACDG,QAAAA,gBAAgB,CAACrD,IAAD,EAAaC,UAAb,EAAsC;AAClD,eAAKpC,SAAL,CAAeyF,YAAf,CAA4BtD,IAAI,CAACoD,EAAjC;AACH;;AAEDG,QAAAA,YAAY,CAACC,KAAD,EAAgB,CACxB;AACH;;AAzOyC,O;;;;;iBAGpB,I;;;;;;;iBAEC,I;;;;;;;iBAEM,I;;;;;;;iBAEN,I", "sourcesContent": ["import { _decorator, Label, Node, Sprite, tween, Tween } from 'cc';\r\nconst { ccclass, property } = _decorator;\r\n\r\nimport { EffectParam, EffectType } from 'db://assets/bundles/common/script/autogen/luban/schema';\r\nimport { AttributeConst } from 'db://assets/bundles/common/script/const/AttributeConst';\r\nimport { AttributeData } from 'db://assets/bundles/common/script/data/base/AttributeData';\r\nimport FBoxCollider from 'db://assets/bundles/common/script/game/collider-system/FBoxCollider';\r\nimport FCircleCollider from 'db://assets/bundles/common/script/game/collider-system/FCircleCollider';\r\nimport FPolygonCollider from 'db://assets/bundles/common/script/game/collider-system/FPolygonCollider';\r\nimport Entity from 'db://assets/bundles/common/script/game/ui/base/Entity';\r\nimport { GameEnum } from '../../const/GameEnum';\r\nimport BuffComp, { Buff } from './skill/BuffComp';\r\nimport SkillComp from './skill/SkillComp';\r\n\r\n\r\n@ccclass('PlaneBase')\r\nexport default class PlaneBase extends Entity {\r\n\r\n    @property(Node)\r\n    hpNode: Node | null = null;\r\n    @property(Sprite)\r\n    hpBar: Sprite | null = null; // 血条\r\n    @property(Sprite)\r\n    hpAniSprite: Sprite | null = null; // 血条动画条\r\n    @property(Label)\r\n    hpfont: Label | null = null; // 血条文本\r\n\r\n    enemy = true; // 是否为敌机\r\n    isDead = false; // 是否死亡\r\n    type = 0; // 敌人类型\r\n    bDamageable: boolean = true; // 是否可以被造成伤害\r\n\r\n    maxHp: number = 0;\r\n    curHp: number = 0;\r\n\r\n    collideComp: FCircleCollider | FBoxCollider | FPolygonCollider | null = null; // 碰撞组件\r\n\r\n    private _skillComp: SkillComp | null = null;\r\n    private _buffComp: BuffComp | null = null;\r\n\r\n    // TODO 临时做法，后续应该挪到 PlaneBase\r\n    private _attributeData: AttributeData = new AttributeData();\r\n\r\n    init() {\r\n        this._skillComp = new SkillComp();\r\n        this.addComp(\"skill\", this._skillComp);\r\n        this._buffComp = new BuffComp();\r\n        this.addComp(\"buff\", this._buffComp)\r\n        super.init();\r\n    }\r\n\r\n    get skillComp() {\r\n        return this._skillComp!;\r\n    }\r\n\r\n    get buffComp() {\r\n        return this._buffComp!;\r\n    }\r\n\r\n    get attribute(): AttributeData {\r\n        return this._attributeData;\r\n    }\r\n\r\n    set colliderEnabled(value: boolean) {\r\n        if (this.collideComp) {\r\n            this.collideComp.isEnable = value;\r\n        }\r\n    }\r\n    get colliderEnabled(): boolean {\r\n        return this.collideComp ? this.collideComp.isEnable : false;\r\n    }\r\n\r\n    CastSkill(skillID: number) {\r\n        this.skillComp.Cast(this, skillID);\r\n    }\r\n\r\n    addHp(heal: number) {\r\n        this.curHp = Math.min(\r\n            this.maxHp,\r\n            this.curHp + heal\r\n        );\r\n        this.updateHpUI();;\r\n    }\r\n\r\n    hurt(damage: number) {\r\n        if (this.isDead) {\r\n            return;\r\n        }\r\n        this.cutHp(damage);\r\n        this.playHurtAnim();\r\n        if (this.curHp <= 0) {\r\n            this.toDie();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 减少血量\r\n     * @param {number} damage 受到的伤害值\r\n     */\r\n    cutHp(damage: number) {\r\n        const newHp = this.curHp - damage;\r\n        this.curHp = Math.max(0, newHp);\r\n\r\n        this.updateHpUI();\r\n    }\r\n\r\n    toDie(destroyType: GameEnum.EnemyDestroyType = GameEnum.EnemyDestroyType.Die): boolean {\r\n        if (this.isDead) {\r\n            return false\r\n        }\r\n        this.isDead = true;\r\n        this.colliderEnabled = false;\r\n        return true\r\n    }\r\n    /**\r\n     * 更新血量显示\r\n     */\r\n    updateHpUI() {\r\n        if (this.hpBar) {\r\n            // 更新血条前景的填充范围\r\n            this.hpBar.fillRange = this.curHp / this.maxHp;\r\n\r\n            if (this.hpAniSprite) {\r\n                // 计算血条动画时间\r\n                const duration = Math.abs(this.hpAniSprite.fillRange - this.hpBar.fillRange);\r\n\r\n                Tween.stopAllByTarget(this.hpAniSprite);\r\n                // 血条中间部分的动画\r\n                tween(this.hpAniSprite)\r\n                    .to(duration, { fillRange: this.hpBar.fillRange })\r\n                    .call(() => {\r\n\r\n                    })\r\n                    .start();\r\n            }\r\n        }\r\n\r\n        // 更新血量文字\r\n        this.hpfont && (this.hpfont!.string = this.curHp.toFixed(0));\r\n    }\r\n\r\n    playHurtAnim() {\r\n        // 子类实现\r\n    }\r\n\r\n    ApplyBuffEffect(buff: Buff | null, effectData: EffectParam) {\r\n        switch (effectData.type) {\r\n            case EffectType.Kill:\r\n                this.toDie();\r\n                break;\r\n            case EffectType.Hurt:\r\n                if (effectData.param.length >= 1) {\r\n                    this.hurt(effectData.param[0]);\r\n                }\r\n                break;\r\n            case EffectType.AttrMaxHPPer:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.MaxHP, effectData, true);\r\n                break;\r\n            case EffectType.AttrMaxHPAdd:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.MaxHP, effectData, false);\r\n                break;\r\n            case EffectType.AttrHPRecoveryPer:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.HPRecovery, effectData, true);\r\n                break;\r\n            case EffectType.AttrHPRecoveryAdd:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.HPRecovery, effectData, false);\r\n                break;\r\n            case EffectType.AttrHPRecoveryMaxHPPerAdd:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.HPRecoveryRate, effectData, false);\r\n                break;\r\n            case EffectType.AttrAttackPer:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.Attack, effectData, true);\r\n                break;\r\n            case EffectType.AttrAttackAdd:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.Attack, effectData, false);\r\n                break;\r\n            case EffectType.AttrAttackBossPer:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.AttackBoss, effectData, true);\r\n                break;\r\n            case EffectType.AttrAttackNormalPer:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.AttackNormal, effectData, true);\r\n                break;\r\n            case EffectType.AttrFortunatePer:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.Fortunate, effectData, true);\r\n                break;\r\n            case EffectType.AttrFortunateAdd:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.Fortunate, effectData, false);\r\n                break;\r\n            case EffectType.AttrMissAdd:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.MissRate, effectData, false);\r\n                break;\r\n            case EffectType.AttrBulletHurtResistancePer:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.BulletHurtResistance, effectData, true);\r\n                break;\r\n            case EffectType.AttrBulletHurtResistanceAdd:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.BulletHurtResistance, effectData, false);\r\n                break;\r\n            case EffectType.AttrCollisionHurtResistancePer:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.CollisionHurtResistance, effectData, true);\r\n                break;\r\n            case EffectType.AttrCollisionHurtResistanceAdd:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.CollisionHurtResistance, effectData, false);\r\n                break;\r\n            case EffectType.AttrFinalScoreAdd:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.FinalScoreRate, effectData, true);\r\n                break;\r\n            case EffectType.AttrKillScoreAdd:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.KillScoreRate, effectData, true);\r\n                break;\r\n            case EffectType.AttrEnergyRecoveryPerAdd:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.EnergyRecoveryRate, effectData, false);\r\n                break;\r\n            case EffectType.AttrEnergyRecoveryAdd:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.EnergyRecovery, effectData, false);\r\n                break;\r\n            case EffectType.AttrPickRadiusPer:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.PickRadius, effectData, true);\r\n                break;\r\n            case EffectType.AttrPickRadiusAdd:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.PickRadius, effectData, false);\r\n                break;\r\n            case EffectType.AttrBombMax:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.BombMax, effectData, false);\r\n                break;\r\n            case EffectType.AttrBombHurtAdd:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.BombHurt, effectData, false);\r\n                break;\r\n            case EffectType.AttrBombHurtPer:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.BombHurt, effectData, true);\r\n                break;\r\n            default:\r\n                break;\r\n        }\r\n    }\r\n    private ApplyBuffAttributeEffect(buff: Buff | null, key: number, effectData: EffectParam, isPer: boolean) {\r\n        if (!buff) {\r\n            return;\r\n        }\r\n        if (effectData.param.length < 1) {\r\n            return;\r\n        }\r\n        this.attribute.addModify(buff.id, key, effectData.param[0], isPer);\r\n    }\r\n    RemoveBuffEffect(buff: Buff, effectData: EffectParam) {\r\n        this.attribute.removeModify(buff.id);\r\n    }\r\n\r\n    setAnimSpeed(speed: number) {\r\n        // 子类实现\r\n    }\r\n}"]}