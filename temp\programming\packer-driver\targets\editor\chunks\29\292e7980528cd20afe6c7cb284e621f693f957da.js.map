{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/mainPlane/MainPlane.ts"], "names": ["_decorator", "instantiate", "Node", "Prefab", "size", "UIOpacity", "view", "MyApp", "AttributeConst", "Plane", "Bullet", "Emitter", "FBoxCollider", "ColliderGroupType", "GameConst", "GameResourceList", "GameIns", "eEntityTag", "EffectLayer", "PlaneBase", "ccclass", "property", "MainPlane", "m_moveEnable", "emitter<PERSON>omp", "_hurtActTime", "_hurtActDuration", "_planeData", "_plane", "_fireEnable", "onLoad", "collide<PERSON>omp", "getComponent", "addComponent", "init", "groupType", "PLAYER", "colliderEnabled", "update", "dt", "battleManager", "animSpeed", "initPlane", "planeData", "plane", "planeMgr", "getPlane", "planeParent", "<PERSON><PERSON><PERSON><PERSON>", "addTag", "Player", "setEmitter", "setFireEnable", "curHp", "getFinalAttributeByKey", "MaxHP", "maxHp", "path", "EmitterPrefabPath", "resMgr", "loadAsync", "then", "prefab", "node", "NodeEmitter", "setPosition", "setEntity", "setIsActive", "initBattle", "active", "updateHpUI", "planeIn", "targetY", "getVisibleSize", "height", "setMoveAble", "hpNode", "opacity", "scheduleOnce", "onEnter", "begine", "battleQuit", "onCollide", "collision", "damage", "entity", "getAttack", "hurt", "onControl", "posX", "posY", "mainPlaneManager", "planeFightData", "die", "isLeft", "position", "x", "onMoveCommand", "Math", "min", "max", "ViewHeight", "relife", "gameDataManager", "reviveCount", "revive", "Attack", "to<PERSON><PERSON>", "_playDieAnim", "playHurtAnim", "me", "showRedScreen", "battleFail", "enable", "isContinue", "onPlaneIn", "attribute", "setAnimSpeed", "speed"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;;AACxDC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,c,iBAAAA,c;;AAGAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,O,iBAAAA,O;;AACFC,MAAAA,Y;;AACaC,MAAAA,iB,iBAAAA,iB;;AACXC,MAAAA,S,iBAAAA,S;;AACFC,MAAAA,gB;;AACEC,MAAAA,O,kBAAAA,O;;AACAC,MAAAA,U,kBAAAA,U;;AACFC,MAAAA,W;;AACAC,MAAAA,S;;;;;;;;;OAED;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBrB,U;;2BAIjBsB,S,WADZF,OAAO,CAAC,WAAD,C,UAGHC,QAAQ,CAACnB,IAAD,C,UAERmB,QAAQ,CAACnB,IAAD,C,2BALb,MACaoB,SADb;AAAA;AAAA,kCACyC;AAAA;AAAA;;AAAA;;AAAA;;AAAA,eAOrCC,YAPqC,GAOtB,IAPsB;AAOhB;AAPgB,eAQrCC,WARqC,GAQP,IARO;AAQD;AARC,eAUrCC,YAVqC,GAUtB,CAVsB;AAUnB;AAVmB,eAWrCC,gBAXqC,GAWlB,GAXkB;AAWb;AAXa,eAarCC,UAbqC,GAaN,IAbM;AAaD;AAbC,eAcrCC,MAdqC,GAcd,IAdc;AAcT;AAdS,eAerCC,WAfqC,GAevB,IAfuB;AAAA;;AAelB;AAGnBC,QAAAA,MAAM,GAAG;AACL,eAAKC,WAAL,GAAmB,KAAKC,YAAL;AAAA;AAAA,+CAAmC,KAAKC,YAAL;AAAA;AAAA,2CAAtD;AACA,eAAKF,WAAL,CAAkBG,IAAlB,CAAuB,IAAvB,EAA6B9B,IAAI,CAAC,EAAD,EAAK,EAAL,CAAjC,EAFK,CAEuC;;AAC5C,eAAK2B,WAAL,CAAkBI,SAAlB,GAA8B;AAAA;AAAA,sDAAkBC,MAAhD;AACA,eAAKC,eAAL,GAAuB,KAAvB;AACH;;AAEDC,QAAAA,MAAM,CAACC,EAAD,EAAa;AACfA,UAAAA,EAAE,GAAGA,EAAE,GAAG;AAAA;AAAA,kCAAQC,aAAR,CAAsBC,SAAhC;AACA,eAAKhB,YAAL,IAAqBc,EAArB;AACH;;AAEDG,QAAAA,SAAS,CAACC,SAAD,EAAuB;AAAA;;AAC5B,eAAKhB,UAAL,GAAkBgB,SAAlB,CAD4B,CAG5B;;AACA,cAAIC,KAAK,GAAG;AAAA;AAAA,8BAAMC,QAAN,CAAeC,QAAf,CAAwBH,SAAxB,CAAZ;AACA,eAAKf,MAAL,GAAcgB,KAAK,CAACZ,YAAN;AAAA;AAAA,6BAAd;AACA,oCAAKe,WAAL,+BAAkBC,QAAlB,CAA2BJ,KAA3B;AAEA,eAAKK,MAAL,CAAY;AAAA;AAAA,wCAAWC,MAAvB,EAR4B,CAU5B;;AACA,eAAKC,UAAL,GAX4B,CAY5B;;AACA,eAAKC,aAAL,CAAmB,KAAnB;AAEA,eAAKC,KAAL,uBAAa,KAAK1B,UAAlB,qBAAa,iBAAiB2B,sBAAjB,CAAwC;AAAA;AAAA,gDAAeC,KAAvD,CAAb;AAA2E;AAC3E,eAAKC,KAAL,GAAa,KAAKH,KAAlB;AACH;;AAEDF,QAAAA,UAAU,GAAG;AACT;AACA,cAAIM,IAAI,GAAG;AAAA;AAAA,oDAAiBC,iBAAjB,GAAqC,iBAAhD;AACA;AAAA;AAAA,8BAAMC,MAAN,CAAaC,SAAb,CAAuBH,IAAvB,EAA6BtD,MAA7B,EAAqC0D,IAArC,CAA2CC,MAAD,IAAY;AAAA;;AAClD,gBAAIC,IAAI,GAAG9D,WAAW,CAAC6D,MAAD,CAAtB;AACA,sCAAKE,WAAL,+BAAkBhB,QAAlB,CAA2Be,IAA3B;AACAA,YAAAA,IAAI,CAACE,WAAL,CAAiB,CAAjB,EAAoB,CAApB;AAEA,iBAAKzC,WAAL,GAAmBuC,IAAI,CAAC/B,YAAL;AAAA;AAAA,mCAAnB;AACA,iBAAKR,WAAL,CAAkB0C,SAAlB,CAA4B,IAA5B;AACA,iBAAK1C,WAAL,CAAkB2C,WAAlB,CAA8B,KAAKtC,WAAnC;AACH,WARD;AASH;;AAEDuC,QAAAA,UAAU,GAAG;AACT,eAAKL,IAAL,CAAUM,MAAV,GAAmB,IAAnB;AACA,eAAKhC,eAAL,GAAuB,KAAvB;AACA,eAAKiC,UAAL;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,OAAO,GAAS;AACZ,gBAAMC,OAAO,GAAG,CAAClE,IAAI,CAACmE,cAAL,GAAsBC,MAAvB,GAAgC,GAAhD;AACA,eAAKX,IAAL,CAAUE,WAAV,CAAsB,CAAtB,EAAyBO,OAAzB;AACA,eAAKG,WAAL,CAAiB,KAAjB;AACA,eAAKC,MAAL,CAAa5C,YAAb,CAA0B3B,SAA1B,EAAsCwE,OAAtC,GAAgD,CAAhD;AAEA,eAAKd,IAAL,CAAU/B,YAAV,CAAuB3B,SAAvB,EAAmCwE,OAAnC,GAA6C,CAA7C;AACA,eAAKC,YAAL,CAAkB,MAAM;AAAA;;AACpB,iBAAKf,IAAL,CAAU/B,YAAV,CAAuB3B,SAAvB,EAAmCwE,OAAnC,GAA6C,GAA7C;AACA,iCAAKjD,MAAL,0BAAamD,OAAb,CAAqB,MAAM;AACvB,mBAAKH,MAAL,CAAa5C,YAAb,CAA0B3B,SAA1B,EAAsCwE,OAAtC,GAAgD,GAAhD;AACA,mBAAKG,MAAL;AACH,aAHD;AAIH,WAND,EAMG,GANH;AAOH;AAED;AACJ;AACA;;;AACIC,QAAAA,UAAU,GAAG,CAEZ;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,SAAS,CAACC,SAAD,EAAuB;AAE5B,cAAIC,MAAM,GAAG,CAAb;;AACA,cAAID,SAAS,CAACE,MAAV;AAAA;AAAA,+BAAJ,EAAwC;AACpCD,YAAAA,MAAM,GAAGD,SAAS,CAACE,MAAV,CAAkBC,SAAlB,EAAT;AACH;;AAED,cAAIF,MAAM,GAAG,CAAb,EAAgB;AACZ,iBAAKG,IAAL,CAAUH,MAAV;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACII,QAAAA,SAAS,CAACC,IAAD,EAAeC,IAAf,EAA6B;AAClC,cAAI,CAAC;AAAA;AAAA,kCAAQC,gBAAR,CAAyBC,cAAzB,CAAwCC,GAAzC,IAAgD,KAAKtE,YAAzD,EAAuE;AAAA;;AACnE,gBAAIuE,MAAM,GAAGL,IAAI,GAAG,KAAK1B,IAAL,CAAUgC,QAAV,CAAmBC,CAAvC;AACA,kCAAKpE,MAAL,2BAAaqE,aAAb,CAA2BH,MAA3B,EAFmE,CAInE;;AACAL,YAAAA,IAAI,GAAGS,IAAI,CAACC,GAAL,CAAS,GAAT,EAAcV,IAAd,CAAP;AACAA,YAAAA,IAAI,GAAGS,IAAI,CAACE,GAAL,CAAS,CAAC,GAAV,EAAeX,IAAf,CAAP;AACAC,YAAAA,IAAI,GAAGQ,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYT,IAAZ,CAAP;AACAA,YAAAA,IAAI,GAAGQ,IAAI,CAACE,GAAL,CAAS,CAAC;AAAA;AAAA,wCAAUC,UAApB,EAAgCX,IAAhC,CAAP;AACA,iBAAK3B,IAAL,CAAUE,WAAV,CAAsBwB,IAAtB,EAA4BC,IAA5B;AACH;AACJ;;AAGDY,QAAAA,MAAM,GAAG;AACL;AAAA;AAAA,kCAAQC,eAAR,CAAwBC,WAAxB,IAAuC,CAAvC,CADK,CACqC;AAC1C;;AACA;AAAA;AAAA,kCAAQb,gBAAR,CAAyBC,cAAzB,CAAyCC,GAAzC,GAA+C,KAA/C,CAHK,CAGiD;;AACtD;AAAA;AAAA,kCAAQF,gBAAR,CAAyBC,cAAzB,CAAyCa,MAAzC,GAAkD,IAAlD,CAJK,CAImD;;AACxD,eAAK3B,YAAL,CAAkB,MAAM;AACpB;AAAA;AAAA,oCAAQa,gBAAR,CAAyBC,cAAzB,CAAyCa,MAAzC,GAAkD,KAAlD;AACH,WAFD,EAEG,GAFH;AAIA,eAAKpD,KAAL,GAAa,KAAKG,KAAlB,CATK,CASoB;;AACzB,eAAKc,UAAL;AAAkB,WAVb,CAUe;AACvB;AAED;AACJ;AACA;AACA;;;AACIgB,QAAAA,SAAS,GAAW;AAAA;;AAChB,iBAAO,2BAAK3D,UAAL,uCAAiB2B,sBAAjB,CAAwC;AAAA;AAAA,gDAAeoD,MAAvD,MAAkE,CAAzE;AACH;;AAEDC,QAAAA,KAAK,GAAY;AACb,cAAI,CAAC,MAAMA,KAAN,EAAL,EAAoB;AAChB,mBAAO,KAAP;AACH,WAHY,CAKb;;;AACA;AAAA;AAAA,kCAAQhB,gBAAR,CAAyBC,cAAzB,CAAyCC,GAAzC,GAA+C,IAA/C,CANa,CAQb;;AACA,eAAKe,YAAL;;AACA,iBAAO,IAAP;AACH,SAnKoC,CAqKrC;;;AACAC,QAAAA,YAAY,GAAG;AACX,cAAI,KAAKpF,YAAL,GAAoB,KAAKC,gBAA7B,EAA+C;AAC3C,iBAAKD,YAAL,GAAoB,CAApB,CAD2C,CAE3C;;AACA;AAAA;AAAA,4CAAYqF,EAAZ,CAAeC,aAAf;AACH;AACJ;AAED;AACJ;AACA;;;AACIH,QAAAA,YAAY,GAAG;AACX;AAAA;AAAA,kCAAQpE,aAAR,CAAsBwE,UAAtB;AACH;AAED;AACJ;AACA;AACA;;;AACIrC,QAAAA,WAAW,CAACsC,MAAD,EAAkB;AACzB,eAAK1F,YAAL,GAAoB0F,MAApB;AACH;;AAED7D,QAAAA,aAAa,CAAC6D,MAAD,EAAkB;AAC3B,eAAKpF,WAAL,GAAmBoF,MAAnB;;AACA,cAAI,KAAKzF,WAAT,EAAqB;AACjB,iBAAKA,WAAL,CAAkB2C,WAAlB,CAA8B8C,MAA9B;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIjC,QAAAA,MAAM,CAACkC,UAAU,GAAG,KAAd,EAAqB;AACvB,cAAIA,UAAJ,EAAgB;AACZ,iBAAK9D,aAAL,CAAmB,IAAnB;AACA,iBAAKuB,WAAL,CAAiB,IAAjB;AACA,iBAAKtC,eAAL,GAAuB,IAAvB;AACH,WAJD,MAIO;AACH;AAAA;AAAA,oCAAQG,aAAR,CAAsB2E,SAAtB;AACH;AACJ;;AAEY,YAATC,SAAS,GAAkB;AAC3B,iBAAO,KAAKzF,UAAZ;AACH;;AAED0F,QAAAA,YAAY,CAACC,KAAD,EAAgB;AACxB,cAAI,KAAK1F,MAAT,EAAgB;AACZ,iBAAKA,MAAL,CAAYyF,YAAZ,CAAyBC,KAAzB;AACH;AACJ;;AA1NoC,O;;;;;iBAGV,I;;;;;;;iBAEA,I", "sourcesContent": ["import { _decorator, instantiate, Node, Prefab, size, UIOpacity, view } from \"cc\";\r\nimport { MyApp } from \"db://assets/bundles/common/script/app/MyApp\";\r\nimport { AttributeConst } from \"db://assets/bundles/common/script/const/AttributeConst\";\r\nimport { AttributeData } from \"db://assets/bundles/common/script/data/base/AttributeData\";\r\nimport { PlaneData } from \"db://assets/bundles/common/script/data/plane/PlaneData\";\r\nimport { Plane } from \"db://assets/bundles/common/script/ui/Plane\";\r\nimport { Bullet } from \"../../../bullet/Bullet\";\r\nimport { Emitter } from \"../../../bullet/Emitter\";\r\nimport FBoxCollider from \"../../../collider-system/FBoxCollider\";\r\nimport FCollider, { ColliderGroupType } from \"../../../collider-system/FCollider\";\r\nimport { GameConst } from \"../../../const/GameConst\";\r\nimport GameResourceList from \"../../../const/GameResourceList\";\r\nimport { GameIns } from \"../../../GameIns\";\r\nimport { eEntityTag } from \"../../base/Entity\";\r\nimport EffectLayer from \"../../layer/EffectLayer\";\r\nimport PlaneBase from \"../PlaneBase\";\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n\r\n@ccclass(\"MainPlane\")\r\nexport class MainPlane extends PlaneBase {\r\n\r\n    @property(Node)\r\n    planeParent: Node | null = null;\r\n    @property(Node)\r\n    NodeEmitter: Node | null = null;\r\n\r\n    m_moveEnable = true; // 是否允许移动\r\n    emitterComp: Emitter | null = null; // 发射器\r\n\r\n    _hurtActTime = 0; // 受伤动画时间\r\n    _hurtActDuration = 0.5; // 受伤动画持续时间\r\n\r\n    _planeData: PlaneData | null = null;//飞机数据\r\n    _plane: Plane | null = null;//飞机显示节点\r\n    _fireEnable = true;//是否允许射击\r\n\r\n\r\n    onLoad() {\r\n        this.collideComp = this.getComponent(FBoxCollider) || this.addComponent(FBoxCollider);\r\n        this.collideComp!.init(this, size(40, 40)); // 初始化碰撞组件\r\n        this.collideComp!.groupType = ColliderGroupType.PLAYER;\r\n        this.colliderEnabled = false;\r\n    }\r\n\r\n    update(dt: number) {\r\n        dt = dt * GameIns.battleManager.animSpeed;\r\n        this._hurtActTime += dt;\r\n    }\r\n\r\n    initPlane(planeData: PlaneData) {\r\n        this._planeData = planeData;\r\n\r\n        //加载飞机显示\r\n        let plane = MyApp.planeMgr.getPlane(planeData);\r\n        this._plane = plane.getComponent(Plane);\r\n        this.planeParent?.addChild(plane);\r\n\r\n        this.addTag(eEntityTag.Player);\r\n\r\n        //设置飞机发射组件\r\n        this.setEmitter();\r\n        // 禁用射击\r\n        this.setFireEnable(false);\r\n\r\n        this.curHp = this._planeData?.getFinalAttributeByKey(AttributeConst.MaxHP);;\r\n        this.maxHp = this.curHp;\r\n    }\r\n\r\n    setEmitter() {\r\n        //后期根据飞机的数据，加载不同的发送组件预制体\r\n        let path = GameResourceList.EmitterPrefabPath + \"Emitter_main_01\";\r\n        MyApp.resMgr.loadAsync(path, Prefab).then((prefab) => {\r\n            let node = instantiate(prefab);\r\n            this.NodeEmitter?.addChild(node);\r\n            node.setPosition(0, 0);\r\n\r\n            this.emitterComp = node.getComponent(Emitter);\r\n            this.emitterComp!.setEntity(this);\r\n            this.emitterComp!.setIsActive(this._fireEnable);\r\n        });\r\n    }\r\n\r\n    initBattle() {\r\n        this.node.active = true;\r\n        this.colliderEnabled = false;\r\n        this.updateHpUI();\r\n    }\r\n\r\n    /**\r\n     * 主飞机入场动画\r\n     */\r\n    planeIn(): void {\r\n        const targetY = -view.getVisibleSize().height * 0.7;\r\n        this.node.setPosition(0, targetY)\r\n        this.setMoveAble(false)\r\n        this.hpNode!.getComponent(UIOpacity)!.opacity = 0;\r\n\r\n        this.node.getComponent(UIOpacity)!.opacity = 0;\r\n        this.scheduleOnce(() => {\r\n            this.node.getComponent(UIOpacity)!.opacity = 255;\r\n            this._plane?.onEnter(() => {\r\n                this.hpNode!.getComponent(UIOpacity)!.opacity = 255;\r\n                this.begine();\r\n            });\r\n        }, 0.7);\r\n    }\r\n\r\n    /**\r\n     * 退出战斗\r\n     */\r\n    battleQuit() {\r\n\r\n    }\r\n\r\n    /**\r\n     * 碰撞处理\r\n     * @param {Object} collision 碰撞对象\r\n     */\r\n    onCollide(collision: FCollider) {\r\n\r\n        let damage = 0;\r\n        if (collision.entity instanceof Bullet) {\r\n            damage = collision.entity!.getAttack();\r\n        }\r\n\r\n        if (damage > 0) {\r\n            this.hurt(damage)\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 控制飞机移动\r\n     * @param {number} moveX 水平方向的移动量\r\n     * @param {number} moveY 垂直方向的移动量\r\n     */\r\n    onControl(posX: number, posY: number) {\r\n        if (!GameIns.mainPlaneManager.planeFightData.die && this.m_moveEnable) {\r\n            let isLeft = posX < this.node.position.x;\r\n            this._plane?.onMoveCommand(isLeft);\r\n\r\n            // 限制飞机移动范围\r\n            posX = Math.min(375, posX);\r\n            posX = Math.max(-375, posX);\r\n            posY = Math.min(0, posY);\r\n            posY = Math.max(-GameConst.ViewHeight, posY);\r\n            this.node.setPosition(posX, posY);\r\n        }\r\n    }\r\n\r\n\r\n    relife() {\r\n        GameIns.gameDataManager.reviveCount += 1; // 增加复活次数\r\n        // this.playRelifeAim(); // 播放复活动画\r\n        GameIns.mainPlaneManager.planeFightData!.die = false; // 设置飞机为非死亡状态\r\n        GameIns.mainPlaneManager.planeFightData!.revive = true; // 设置复活状态\r\n        this.scheduleOnce(() => {\r\n            GameIns.mainPlaneManager.planeFightData!.revive = false;\r\n        }, 0.5);\r\n\r\n        this.curHp = this.maxHp; // 恢复满血\r\n        this.updateHpUI();; // 触发血量更新事件\r\n    }\r\n\r\n    /**\r\n     * 获取攻击力\r\n     * @returns {number} 当前攻击力\r\n     */\r\n    getAttack(): number {\r\n        return this._planeData?.getFinalAttributeByKey(AttributeConst.Attack) || 0;\r\n    }\r\n\r\n    toDie(): boolean {\r\n        if (!super.toDie()) {\r\n            return false;\r\n        }\r\n\r\n        // 设置玩家状态为死亡\r\n        GameIns.mainPlaneManager.planeFightData!.die = true;\r\n\r\n        // 播放死亡动画\r\n        this._playDieAnim();\r\n        return true;\r\n    }\r\n\r\n    //实现父类的方法\r\n    playHurtAnim() {\r\n        if (this._hurtActTime > this._hurtActDuration) {\r\n            this._hurtActTime = 0;\r\n            // 显示红屏效果\r\n            EffectLayer.me.showRedScreen();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 播放死亡动画\r\n     */\r\n    _playDieAnim() {\r\n        GameIns.battleManager.battleFail();\r\n    }\r\n\r\n    /**\r\n     * 设置飞机是否可移动\r\n     * @param {boolean} enable 是否可移动\r\n     */\r\n    setMoveAble(enable: boolean) {\r\n        this.m_moveEnable = enable;\r\n    }\r\n\r\n    setFireEnable(enable: boolean) {\r\n        this._fireEnable = enable;\r\n        if (this.emitterComp){\r\n            this.emitterComp!.setIsActive(enable);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 开始战斗\r\n     * @param {boolean} isContinue 是否继续战斗\r\n     */\r\n    begine(isContinue = false) {\r\n        if (isContinue) {\r\n            this.setFireEnable(true);\r\n            this.setMoveAble(true);\r\n            this.colliderEnabled = true;\r\n        } else {\r\n            GameIns.battleManager.onPlaneIn();\r\n        }\r\n    }\r\n\r\n    get attribute(): AttributeData {\r\n        return this._planeData!;\r\n    }\r\n\r\n    setAnimSpeed(speed: number) {\r\n        if (this._plane){\r\n            this._plane.setAnimSpeed(speed);\r\n        }\r\n    }\r\n}"]}