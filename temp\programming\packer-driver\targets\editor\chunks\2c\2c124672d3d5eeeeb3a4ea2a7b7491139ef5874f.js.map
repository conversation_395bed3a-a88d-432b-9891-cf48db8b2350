{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/pk/PKUI.ts"], "names": ["_decorator", "Node", "MyApp", "csproto", "BundleName", "PopupUI", "ButtonPlus", "HomeUI", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "PKHistoryUI", "PKRewardIcon", "MessageBox", "ccclass", "property", "PKUI", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "getBundleName", "HomePK", "getUIOption", "isClickBgCloseUI", "onLoad", "btnClose", "addClick", "closeUI", "btnHistory", "onHistoryClick", "friendPk", "getComponentInChildren", "onFriendPk", "goldCoinPk", "onGoldCoinPk", "diamondPk", "onDiamondPk", "highDiamondPk", "onHighDiamondPk", "setData", "data", "PKRewardIcon2", "getComponentsInChildren", "for<PERSON>ach", "icon", "key", "node", "name", "value", "toast", "netMgr", "sendMessage", "cs", "CS_CMD", "CS_CMD_GAME_PVP_MATCH", "game_pvp_match", "map_id", "openUI", "onShow", "onHide", "onClose", "onDestroy"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;;AACZC,MAAAA,K,iBAAAA,K;;AACFC,MAAAA,O;;AACEC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,Y,kBAAAA,Y;;AACAC,MAAAA,U,kBAAAA,U;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBf,U;;sBAGjBgB,I,WADZF,OAAO,CAAC,MAAD,C,UAEHC,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ,CAACd,IAAD,C,UAERc,QAAQ,CAACd,IAAD,C,UAERc,QAAQ,CAACd,IAAD,C,UAERc,QAAQ,CAACd,IAAD,C,2BAZb,MACae,IADb;AAAA;AAAA,4BACiC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAcT,eAANC,MAAM,GAAW;AAAE,iBAAO,gBAAP;AAA0B;;AACrC,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,OAAf;AAAwB;;AACjC,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,MAAlB;AAA2B;;AAC1C,eAAXC,WAAW,GAAU;AAAE,iBAAO;AAAEC,YAAAA,gBAAgB,EAAE;AAApB,WAAP;AAAmC;;AAC9DC,QAAAA,MAAM,GAAS;AACrB,eAAKC,QAAL,CAAeC,QAAf,CAAwB,KAAKC,OAA7B,EAAsC,IAAtC;AACA,eAAKC,UAAL,CAAiBF,QAAjB,CAA0B,KAAKG,cAA/B,EAA+C,IAA/C;AACA,eAAKC,QAAL,CAAeC,sBAAf;AAAA;AAAA,wCAAmDL,QAAnD,CAA4D,KAAKM,UAAjE,EAA6E,IAA7E;AACA,eAAKC,UAAL,CAAiBF,sBAAjB;AAAA;AAAA,wCAAqDL,QAArD,CAA8D,KAAKQ,YAAnE,EAAiF,IAAjF;AACA,eAAKC,SAAL,CAAgBJ,sBAAhB;AAAA;AAAA,wCAAoDL,QAApD,CAA6D,KAAKU,WAAlE,EAA+E,IAA/E;AACA,eAAKC,aAAL,CAAoBN,sBAApB;AAAA;AAAA,wCAAwDL,QAAxD,CAAiE,KAAKY,eAAtE,EAAuF,IAAvF;AAEA,eAAKR,QAAL,CAAeC,sBAAf;AAAA;AAAA,4CAAqDQ,OAArD,CAA6D,GAA7D;AAEA,cAAIC,IAAI,GAAG;AACP5B,YAAAA,YAAY,EAAE,GADP;AAEP6B,YAAAA,aAAa,EAAE;AAFR,WAAX;AAIA,eAAKJ,aAAL,CAAoBK,uBAApB;AAAA;AAAA,4CAA2DC,OAA3D,CAAmEC,IAAI,IAAI;AAAA;;AACvE,kBAAMC,GAAG,GAAGD,IAAI,CAACE,IAAL,CAAUC,IAAtB;AACA,kBAAMC,KAAK,gBAAGR,IAAI,CAACK,GAAD,CAAP,wBAAgB,CAA3B,CAFuE,CAEzC;;AAC9BD,YAAAA,IAAI,CAACL,OAAL,CAAaS,KAAb;AACH,WAJD;AAKH;;AACOhB,QAAAA,UAAU,GAAG;AACjB;AAAA;AAAA,wCAAWiB,KAAX,CAAiB,MAAjB;AACA;AAAA;AAAA,8BAAMC,MAAN,CAAaC,WAAb,CAAyB;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBC,qBAA3C,EAAkE;AAAEC,YAAAA,cAAc,EAAE;AAAEC,cAAAA,MAAM,EAAE;AAAV;AAAlB,WAAlE;AACH;;AACOtB,QAAAA,YAAY,GAAG;AACnB;AAAA;AAAA,8BAAMuB,MAAN;AAAA;AAAA,kCAAsB,MAAtB;AACH;;AACOrB,QAAAA,WAAW,GAAG;AAClB;AAAA;AAAA,8BAAMqB,MAAN;AAAA;AAAA,kCAAsB,MAAtB;AACH;;AACOnB,QAAAA,eAAe,GAAG;AACtB;AAAA;AAAA,8BAAMmB,MAAN;AAAA;AAAA,kCAAsB,MAAtB;AACH;;AAEY,cAAP9B,OAAO,GAAG;AACZ;AAAA;AAAA,8BAAMA,OAAN,CAAcX,IAAd;AACA,gBAAM;AAAA;AAAA,8BAAMyC,MAAN;AAAA;AAAA,+BAAN;AACH;;AACmB,cAAd5B,cAAc,GAAG;AACnB;AAAA;AAAA,8BAAMF,OAAN,CAAcX,IAAd;AACA,gBAAM;AAAA;AAAA,8BAAMyC,MAAN;AAAA;AAAA,yCAAN;AACH;;AACW,cAANC,MAAM,GAAkB,CAE7B;;AACW,cAANC,MAAM,GAAkB,CAC7B;;AACY,cAAPC,OAAO,GAAkB,CAC9B;;AACSC,QAAAA,SAAS,GAAS,CAC3B;;AApE4B,O;;;;;iBAEC,I;;;;;;;iBAEE,I;;;;;;;iBAER,I;;;;;;;iBAEE,I;;;;;;;iBAED,I;;;;;;;iBAEI,I", "sourcesContent": ["import { _decorator, Node } from 'cc';\r\nimport { MyApp } from 'db://assets/bundles/common/script/app/MyApp';\r\nimport csproto from 'db://assets/bundles/common/script/autogen/pb/cs_proto.js';\r\nimport { BundleName } from 'db://assets/bundles/common/script/const/BundleConst';\r\nimport { PopupUI } from 'db://assets/bundles/common/script/ui/common/PopupUI';\r\nimport { ButtonPlus } from 'db://assets/bundles/common/script/ui/common/components/button/ButtonPlus';\r\nimport { HomeUI } from 'db://assets/bundles/common/script/ui/home/<USER>';\r\nimport { BaseUI, UILayer, UIMgr, UIOpt } from 'db://assets/scripts/core/base/UIMgr';\r\nimport { PKHistoryUI } from './PKHistoryUI';\r\nimport { PKRewardIcon } from './PKRewardIcon';\r\nimport { MessageBox } from 'db://assets/scripts/core/base/MessageBox';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('PKUI')\r\nexport class PKUI extends BaseUI {\r\n    @property(ButtonPlus)\r\n    btnClose: ButtonPlus | null = null;\r\n    @property(ButtonPlus)\r\n    btnHistory: ButtonPlus | null = null;\r\n    @property(Node)\r\n    friendPk: Node | null = null;\r\n    @property(Node)\r\n    goldCoinPk: Node | null = null;\r\n    @property(Node)\r\n    diamondPk: Node | null = null;\r\n    @property(Node)\r\n    highDiamondPk: Node | null = null;\r\n\r\n    public static getUrl(): string { return \"prefab/ui/PKUI\"; }\r\n    public static getLayer(): UILayer { return UILayer.Default }\r\n    public static getBundleName(): string { return BundleName.HomePK; }\r\n    public static getUIOption(): UIOpt { return { isClickBgCloseUI: true } }\r\n    protected onLoad(): void {\r\n        this.btnClose!.addClick(this.closeUI, this);\r\n        this.btnHistory!.addClick(this.onHistoryClick, this);\r\n        this.friendPk!.getComponentInChildren(ButtonPlus)!.addClick(this.onFriendPk, this);\r\n        this.goldCoinPk!.getComponentInChildren(ButtonPlus)!.addClick(this.onGoldCoinPk, this);\r\n        this.diamondPk!.getComponentInChildren(ButtonPlus)!.addClick(this.onDiamondPk, this);\r\n        this.highDiamondPk!.getComponentInChildren(ButtonPlus)!.addClick(this.onHighDiamondPk, this);\r\n\r\n        this.friendPk!.getComponentInChildren(PKRewardIcon)!.setData(888);\r\n\r\n        let data = {\r\n            PKRewardIcon: 123,\r\n            PKRewardIcon2: 456,\r\n        };\r\n        this.highDiamondPk!.getComponentsInChildren(PKRewardIcon)!.forEach(icon => {\r\n            const key = icon.node.name as keyof typeof data;\r\n            const value = data[key] ?? 0; // 默认值为 0\r\n            icon.setData(value);\r\n        });\r\n    }\r\n    private onFriendPk() {\r\n        MessageBox.toast(\"点击了1\");\r\n        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_GAME_PVP_MATCH, { game_pvp_match: { map_id: 1 } });\r\n    }\r\n    private onGoldCoinPk() {\r\n        UIMgr.openUI(PopupUI, \"点击了2\");\r\n    }\r\n    private onDiamondPk() {\r\n        UIMgr.openUI(PopupUI, \"点击了3\");\r\n    }\r\n    private onHighDiamondPk() {\r\n        UIMgr.openUI(PopupUI, \"点击了4\");\r\n    }\r\n\r\n    async closeUI() {\r\n        UIMgr.closeUI(PKUI);\r\n        await UIMgr.openUI(HomeUI)\r\n    }\r\n    async onHistoryClick() {\r\n        UIMgr.closeUI(PKUI);\r\n        await UIMgr.openUI(PKHistoryUI)\r\n    }\r\n    async onShow(): Promise<void> {\r\n\r\n    }\r\n    async onHide(): Promise<void> {\r\n    }\r\n    async onClose(): Promise<void> {\r\n    }\r\n    protected onDestroy(): void {\r\n    }\r\n}\r\n\r\n\r\n"]}