{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/GameIns.ts"], "names": ["_GameIns", "SingletonBase", "FColliderManager", "BattleManager", "BossManager", "EnemyManager", "GameDataManager", "GamePlaneManager", "GameRuleManager", "HurtEffectManager", "MainPlaneManager", "StageManager", "WaveManager", "gameMainUI", "battleManager", "getInstance", "boss<PERSON><PERSON><PERSON>", "enemyManager", "gameDataManager", "gameRuleManager", "hurtEffectManager", "mainPlaneManager", "gamePlaneManager", "stageManager", "waveManager", "fColliderManager", "instance", "GameIns"], "mappings": ";;;6NAeMA,Q;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAfGC,MAAAA,a,iBAAAA,a;;AACFC,MAAAA,gB;;AACEC,MAAAA,a,iBAAAA,a;;AACAC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,e,iBAAAA,e;;AACAC,MAAAA,gB,iBAAAA,gB;;AACAC,MAAAA,e,iBAAAA,e;;AACAC,MAAAA,iB,kBAAAA,iB;;AACAC,MAAAA,gB,kBAAAA,gB;;AACAC,MAAAA,Y,kBAAAA,Y;;AACFC,MAAAA,W;;;;;;;AAEP;AAEMZ,MAAAA,Q,GAAN,MAAMA,QAAN;AAAA;AAAA,0CAA+C;AAAA;AAAA;AAAA,eAa3Ca,UAb2C,GAab,IAba;AAAA;;AAC1B,YAAbC,aAAa,GAAG;AAAE,iBAAO;AAAA;AAAA,8CAAcC,WAAd;AAAA;AAAA,6CAAP;AAAiE;;AACxE,YAAXC,WAAW,GAAG;AAAE,iBAAO;AAAA;AAAA,0CAAYD,WAAZ;AAAA;AAAA,yCAAP;AAA2D;;AAC/D,YAAZE,YAAY,GAAG;AAAE,iBAAO;AAAA;AAAA,4CAAaF,WAAb;AAAA;AAAA,2CAAP;AAA8D;;AAChE,YAAfG,eAAe,GAAG;AAAE,iBAAO;AAAA;AAAA,kDAAgBH,WAAhB;AAAA;AAAA,iDAAP;AAAuE;;AAC5E,YAAfI,eAAe,GAAG;AAAE,iBAAO;AAAA;AAAA,kDAAgBJ,WAAhB;AAAA;AAAA,iDAAP;AAAuE;;AAC1E,YAAjBK,iBAAiB,GAAG;AAAE,iBAAO;AAAA;AAAA,sDAAkBL,WAAlB;AAAA;AAAA,qDAAP;AAA6E;;AACnF,YAAhBM,gBAAgB,GAAG;AAAE,iBAAO;AAAA;AAAA,oDAAiBN,WAAjB;AAAA;AAAA,mDAAP;AAA0E;;AAC/E,YAAhBO,gBAAgB,GAAG;AAAE,iBAAO;AAAA;AAAA,oDAAiBP,WAAjB;AAAA;AAAA,mDAAP;AAA0E;;AACnF,YAAZQ,YAAY,GAAG;AAAE,iBAAO;AAAA;AAAA,4CAAaR,WAAb;AAAA;AAAA,2CAAP;AAA8D;;AACpE,YAAXS,WAAW,GAAG;AAAE,iBAAO;AAAA;AAAA,0CAAYT,WAAZ;AAAA;AAAA,yCAAP;AAA2D;;AAC3D,YAAhBU,gBAAgB,GAAG;AAAE,iBAAO;AAAA;AAAA,oDAAiBC,QAAxB;AAAmC;;AAXjB,O;;yBAkBlCC,O,GAAoB3B,QAAQ,CAACe,WAAT,CAA+Bf,QAA/B,C", "sourcesContent": ["import { SingletonBase } from \"../../../../scripts/core/base/SingletonBase\";\r\nimport FColliderManager from \"./collider-system/FColliderManager\";\r\nimport { BattleManager } from \"./manager/BattleManager\";\r\nimport { BossManager } from \"./manager/BossManager\";\r\nimport { EnemyManager } from \"./manager/EnemyManager\";\r\nimport { GameDataManager } from \"./manager/GameDataManager\";\r\nimport { GamePlaneManager } from \"./manager/GamePlaneManager\";\r\nimport { GameRuleManager } from \"./manager/GameRuleManager\";\r\nimport { HurtEffectManager } from \"./manager/HurtEffectManager\";\r\nimport { MainPlaneManager } from \"./manager/MainPlaneManager\";\r\nimport { StageManager } from \"./manager/StageManager\";\r\nimport WaveManager from \"./manager/WaveManager\";\r\nimport { GameMain } from \"./scenes/GameMain\";\r\n;\r\n\r\nclass _GameIns extends SingletonBase<_GameIns> {\r\n    get battleManager() { return BattleManager.getInstance<BattleManager>(BattleManager); }\r\n    get bossManager() { return BossManager.getInstance<BossManager>(BossManager); }\r\n    get enemyManager() { return EnemyManager.getInstance<EnemyManager>(EnemyManager); }\r\n    get gameDataManager() { return GameDataManager.getInstance<GameDataManager>(GameDataManager); }\r\n    get gameRuleManager() { return GameRuleManager.getInstance<GameRuleManager>(GameRuleManager); }\r\n    get hurtEffectManager() { return HurtEffectManager.getInstance<HurtEffectManager>(HurtEffectManager); }\r\n    get mainPlaneManager() { return MainPlaneManager.getInstance<MainPlaneManager>(MainPlaneManager); }\r\n    get gamePlaneManager() { return GamePlaneManager.getInstance<GamePlaneManager>(GamePlaneManager); }\r\n    get stageManager() { return StageManager.getInstance<StageManager>(StageManager); }\r\n    get waveManager() { return WaveManager.getInstance<WaveManager>(WaveManager); }\r\n    get fColliderManager() { return FColliderManager.instance; }\r\n\r\n    gameMainUI: GameMain | null = null;\r\n}\r\n\r\n\r\n\r\nexport const GameIns: _GameIns = _GameIns.getInstance<_GameIns>(_GameIns);"]}