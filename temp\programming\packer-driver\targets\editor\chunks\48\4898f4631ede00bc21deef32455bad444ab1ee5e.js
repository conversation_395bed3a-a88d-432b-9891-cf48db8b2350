System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, PopupUI, ToastUI, UIMgr, MessageBox, _crd;

  function _reportPossibleCrUseOfPopupUI(extras) {
    _reporterNs.report("PopupUI", "db://assets/bundles/common/script/ui/common/PopupUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfToastUI(extras) {
    _reporterNs.report("ToastUI", "db://assets/bundles/common/script/ui/common/ToastUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "./UIMgr", _context.meta, extras);
  }

  _export("MessageBox", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      PopupUI = _unresolved_2.PopupUI;
    }, function (_unresolved_3) {
      ToastUI = _unresolved_3.ToastUI;
    }, function (_unresolved_4) {
      UIMgr = _unresolved_4.UIMgr;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "0cbcaRVKQpLSp3iiPY+VaU7", "MessageBox", undefined);

      // 定义回调函数类型
      _export("MessageBox", MessageBox = class MessageBox {
        // 强制显示确认+取消按钮，不需要取消函数回调，不传onCancel
        static confirm(content, onConfirm, onCancel) {
          const cancelHandler = onCancel || (() => {});

          this.show(content, onConfirm, cancelHandler);
        } // 只显示确认按钮，调用这个


        static show(content, onConfirm, onCancel) {
          // 统一调用 UIMgr
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && PopupUI === void 0 ? (_reportPossibleCrUseOfPopupUI({
            error: Error()
          }), PopupUI) : PopupUI, content, onConfirm, onCancel);
        }

        static toast(content) {
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && ToastUI === void 0 ? (_reportPossibleCrUseOfToastUI({
            error: Error()
          }), ToastUI) : ToastUI, content);
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=4898f4631ede00bc21deef32455bad444ab1ee5e.js.map