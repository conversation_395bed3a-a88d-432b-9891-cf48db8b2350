System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Node, tween, Vec3, BaseUI, UILayer, UIMgr, BundleName, ButtonPlus, _dec, _dec2, _dec3, _class, _class2, _descriptor, _descriptor2, _crd, ccclass, property, RewardUI;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfBaseUI(extras) {
    _reporterNs.report("BaseUI", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUILayer(extras) {
    _reporterNs.report("UILayer", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIOpt(extras) {
    _reporterNs.report("UIOpt", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBundleName(extras) {
    _reporterNs.report("BundleName", "../../const/BundleConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfButtonPlus(extras) {
    _reporterNs.report("ButtonPlus", "./components/button/ButtonPlus", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Node = _cc.Node;
      tween = _cc.tween;
      Vec3 = _cc.Vec3;
    }, function (_unresolved_2) {
      BaseUI = _unresolved_2.BaseUI;
      UILayer = _unresolved_2.UILayer;
      UIMgr = _unresolved_2.UIMgr;
    }, function (_unresolved_3) {
      BundleName = _unresolved_3.BundleName;
    }, function (_unresolved_4) {
      ButtonPlus = _unresolved_4.ButtonPlus;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "f8ea6v9v5VFOKbxcnAPnd6L", "RewardUI", undefined);

      __checkObsolete__(['_decorator', 'Node', 'Tween', 'tween', 'Vec3']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("RewardUI", RewardUI = (_dec = ccclass('RewardUI'), _dec2 = property(Node), _dec3 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec(_class = (_class2 = class RewardUI extends (_crd && BaseUI === void 0 ? (_reportPossibleCrUseOfBaseUI({
        error: Error()
      }), BaseUI) : BaseUI) {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "nodeReward", _descriptor, this);

          _initializerDefineProperty(this, "btnGet", _descriptor2, this);

          this.activeTweens = [];
          this.activeTimeouts = [];
        }

        static getUrl() {
          return "prefab/ui/RewardUI";
        }

        static getLayer() {
          return (_crd && UILayer === void 0 ? (_reportPossibleCrUseOfUILayer({
            error: Error()
          }), UILayer) : UILayer).Top;
        }

        static getBundleName() {
          return (_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
            error: Error()
          }), BundleName) : BundleName).Home;
        }

        static getUIOption() {
          return {
            isClickBgCloseUI: false
          };
        }

        onLoad() {
          this.btnGet.addClick(this.onGetClick, this);
        }

        async onGetClick() {
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).closeUI(RewardUI);
        }

        async onShow(rewardID) {
          this.showNodesSequentiallyWithScale();
        }

        showNodesSequentiallyWithScale() {
          const children = this.nodeReward.children;
          if (!children || children.length === 0) return; // 初始隐藏所有子节点

          children.forEach(child => {
            child.active = false;
          }); // 显示第一个节点并启动动画

          this.showNodeWithHalfScale(children, 0);
        }

        showNodeWithHalfScale(children, index) {
          if (index >= children.length) return;
          const child = children[index];
          child.active = true;
          child.setScale(new Vec3(0, 0, 1)); // 前半部分动画：缩放到一半

          const halfScaleTween = tween(child).to(0.03, {
            scale: new Vec3(0.5, 0.5, 1)
          }, {
            easing: 'quadOut'
          }).call(() => {
            // 缩放到一半时，触发下一个节点
            this.showNodeWithHalfScale(children, index + 1);
          }).start(); // 后半部分动画：从一半缩放到完整

          const fullScaleTween = tween(child).to(0.03, {
            scale: new Vec3(1, 1, 1)
          }, {
            easing: 'quadOut'
          }).start();
          this.activeTweens.push(halfScaleTween, fullScaleTween);
        }

        async onHide() {}

        async onClose() {
          this.stopAllEffects(); // 停止所有动画
          // 可选：重置节点状态

          const children = this.nodeReward.children;

          if (children) {
            children.forEach(child => {
              child.active = false;
              child.setScale(new Vec3(1, 1, 1)); // 恢复默认缩放
            });
          }
        }

        stopAllEffects() {
          // 停止所有 tween 动画
          this.activeTweens.forEach(tween => {
            tween.stop();
          });
          this.activeTweens = []; // 清除所有 setTimeout

          this.activeTimeouts.forEach(timeoutId => {
            clearTimeout(timeoutId);
          });
          this.activeTimeouts = [];
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "nodeReward", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "btnGet", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=625e6cba8f4c4ba267f1facaca621bd736ffce94.js.map