{"version": 3, "sources": ["cce:/internal/x/prerequisite-imports"], "names": ["requests", "request", "_err"], "mappings": ";;;;;;AACA;AAEA,YAAM,CAAC,YAAY;AACf,cAAMA,QAAQ,GAAG,CAAC,uCAAD,EAA4J,uCAA5J,EAA4T,uCAA5T,EAAge,uCAAhe,EAAioB,uCAAjoB,EAA4xB,uCAA5xB,EAAg7B,uCAAh7B,EAAsgC,uCAAtgC,EAA0lC,uCAA1lC,EAAurC,uCAAvrC,EAAsxC,wCAAtxC,EAAo3C,wCAAp3C,EAAm9C,wCAAn9C,EAA+iD,wCAA/iD,EAA0oD,wCAA1oD,EAAiuD,wCAAjuD,EAAm0D,wCAAn0D,EAAg6D,wCAAh6D,EAA2/D,wCAA3/D,EAA6lE,wCAA7lE,EAA6rE,wCAA7rE,EAAwxE,wCAAxxE,EAA43E,wCAA53E,EAAi9E,wCAAj9E,EAA0iF,wCAA1iF,EAA+nF,wCAA/nF,EAAmuF,wCAAnuF,EAAk0F,wCAAl0F,EAA25F,wCAA35F,EAAq/F,wCAAr/F,EAAklG,wCAAllG,EAA8qG,wCAA9qG,EAA2wG,wCAA3wG,EAAk2G,wCAAl2G,EAA+7G,wCAA/7G,EAA8iH,wCAA9iH,EAAipH,wCAAjpH,EAA8uH,wCAA9uH,EAA40H,wCAA50H,EAA66H,wCAA76H,EAA+gI,wCAA/gI,EAAgnI,wCAAhnI,EAAwtI,wCAAxtI,EAAy0I,wCAAz0I,EAA27I,wCAA37I,EAAsiJ,wCAAtiJ,EAA6pJ,wCAA7pJ,EAAqxJ,wCAArxJ,EAAs4J,wCAAt4J,EAAk/J,wCAAl/J,EAAimK,wCAAjmK,EAA0sK,wCAA1sK,EAA0zK,wCAA1zK,EAA06K,wCAA16K,EAAshL,wCAAthL,EAA8nL,wCAA9nL,EAA6tL,wCAA7tL,EAA2zL,wCAA3zL,EAAi6L,wCAAj6L,EAA8/L,wCAA9/L,EAAkmM,wCAAlmM,EAAgsM,wCAAhsM,EAA8xM,wCAA9xM,EAAq4M,wCAAr4M,EAAm+M,wCAAn+M,EAAikN,wCAAjkN,EAA8pN,wCAA9pN,EAAowN,wCAApwN,EAA22N,wCAA32N,EAAs9N,wCAAt9N,EAAokO,wCAApkO,EAA8qO,wCAA9qO,EAAyxO,wCAAzxO,EAAo4O,wCAAp4O,EAA2+O,wCAA3+O,EAA0kP,wCAA1kP,EAAyqP,wCAAzqP,EAA6wP,wCAA7wP,EAAk3P,wCAAl3P,EAAq9P,wCAAr9P,EAAyjQ,wCAAzjQ,EAAgqQ,wCAAhqQ,EAAwwQ,wCAAxwQ,EAA+2Q,wCAA/2Q,EAAw9Q,wCAAx9Q,EAAikR,wCAAjkR,EAAyqR,wCAAzqR,EAA6wR,wCAA7wR,EAAg3R,wCAAh3R,EAA68R,wCAA78R,EAAyiS,wCAAziS,EAAwoS,wCAAxoS,EAAyuS,wCAAzuS,EAAy0S,wCAAz0S,EAA26S,wCAA36S,EAAygT,wCAAzgT,EAA+mT,wCAA/mT,EAAotT,wCAAptT,EAAwzT,wCAAxzT,EAA45T,wCAA55T,EAAqgU,yCAArgU,EAAsmU,yCAAtmU,EAAusU,yCAAvsU,EAAyyU,yCAAzyU,EAA84U,yCAA94U,EAAg/U,yCAAh/U,EAAmlV,yCAAnlV,EAAsrV,yCAAtrV,EAAwxV,yCAAxxV,EAA03V,yCAA13V,EAAi+V,yCAAj+V,EAA0kW,yCAA1kW,EAAurW,yCAAvrW,EAAmyW,yCAAnyW,EAA04W,yCAA14W,EAAk/W,yCAAl/W,EAAwlX,yCAAxlX,EAAorX,yCAAprX,EAA6wX,yCAA7wX,EAAw2X,yCAAx2X,EAAi8X,yCAAj8X,EAAsjY,yCAAtjY,EAAwrY,yCAAxrY,EAAszY,yCAAtzY,EAA+6Y,yCAA/6Y,EAA0hZ,yCAA1hZ,EAAwnZ,yCAAxnZ,EAA0uZ,yCAA1uZ,EAAi2Z,yCAAj2Z,EAAs9Z,yCAAt9Z,EAA4ka,yCAA5ka,EAAmra,yCAAnra,EAA4wa,yCAA5wa,EAAq2a,yCAAr2a,EAAk8a,yCAAl8a,EAA8hb,yCAA9hb,EAA2nb,yCAA3nb,EAA0tb,yCAA1tb,EAA6zb,yCAA7zb,EAAg6b,yCAAh6b,EAA8/b,yCAA9/b,EAAilc,yCAAjlc,EAAorc,yCAAprc,EAAgxc,yCAAhxc,EAA62c,yCAA72c,EAAw8c,yCAAx8c,EAAoid,yCAApid,EAAwod,yCAAxod,EAA2vd,yCAA3vd,EAAk3d,yCAAl3d,EAAm+d,yCAAn+d,EAAole,yCAAple,EAAqse,yCAArse,EAA8ye,yCAA9ye,EAA25e,yCAA35e,EAA2/e,yCAA3/e,EAA4lf,yCAA5lf,EAA6rf,yCAA7rf,EAAkyf,yCAAlyf,EAA+3f,yCAA/3f,EAA89f,yCAA99f,EAA4jgB,yCAA5jgB,EAA4pgB,yCAA5pgB,EAA6vgB,yCAA7vgB,EAA81gB,yCAA91gB,EAA07gB,yCAA17gB,EAAqhhB,yCAArhhB,EAA8mhB,yCAA9mhB,EAA4shB,yCAA5shB,EAAoyhB,yCAApyhB,EAAq4hB,yCAAr4hB,EAA2+hB,yCAA3+hB,EAAmliB,yCAAnliB,EAAmriB,yCAAnriB,EAAgxiB,yCAAhxiB,EAAy2iB,yCAAz2iB,EAAk8iB,yCAAl8iB,EAAkijB,yCAAlijB,EAA8njB,yCAA9njB,EAA2tjB,yCAA3tjB,EAAgzjB,yCAAhzjB,EAAw5jB,yCAAx5jB,EAA4/jB,yCAA5/jB,EAA0lkB,yCAA1lkB,EAAqrkB,yCAArrkB,EAAqykB,yCAArykB,EAAq5kB,yCAAr5kB,EAA8glB,yCAA9glB,EAA2nlB,yCAA3nlB,EAAgvlB,yCAAhvlB,EAAm2lB,yCAAn2lB,EAA47lB,yCAA57lB,EAA+hmB,yCAA/hmB,EAA4nmB,yCAA5nmB,EAA8tmB,yCAA9tmB,EAAyzmB,yCAAzzmB,EAAs5mB,yCAAt5mB,EAA++mB,yCAA/+mB,EAA0lnB,yCAA1lnB,EAAgsnB,yCAAhsnB,EAA8wnB,yCAA9wnB,EAAk2nB,yCAAl2nB,EAAg7nB,yCAAh7nB,EAAigoB,yCAAjgoB,EAAgloB,yCAAhloB,EAA8poB,yCAA9poB,EAA6uoB,yCAA7uoB,EAA0zoB,yCAA1zoB,EAA44oB,yCAA54oB,EAAw9oB,yCAAx9oB,EAA2ipB,yCAA3ipB,EAAiopB,yCAAjopB,EAAotpB,yCAAptpB,EAAwypB,yCAAxypB,EAA43pB,yCAA53pB,EAA28pB,yCAA38pB,EAAiiqB,yCAAjiqB,EAAonqB,yCAApnqB,EAAysqB,yCAAzsqB,EAAgxqB,yCAAhxqB,EAA+1qB,yCAA/1qB,EAA66qB,yCAA76qB,EAA0/qB,yCAA1/qB,EAAqkrB,yCAArkrB,EAAsprB,yCAAtprB,EAAuurB,yCAAvurB,EAA2zrB,yCAA3zrB,EAAu4rB,yCAAv4rB,EAAu9rB,yCAAv9rB,EAAyisB,yCAAzisB,CAAjB;;AACA,aAAK,MAAMC,OAAX,IAAsBD,QAAtB,EAAgC;AAC5B,cAAI;AACA,kBAAMC,OAAO,EAAb;AACH,WAFD,CAEE,OAAOC,IAAP,EAAa,CACX;AACH;AACJ;AACJ,OATK,GAAN", "sourcesContent": ["\n// Auto generated represents the prerequisite imports of project modules.\n\nawait (async () => {\n    const requests = [() => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/audio/audioManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/autogen/luban/schema.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.js\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/const/AttributeConst.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/const/BundleConst.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/DataManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/bag/Bag.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/base/AttributeData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/base/BaseInfo.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/equip/Equip.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/equip/EquipCombine.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/equip/EquipSlots.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/fight/Rogue.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/game_level/GameLevel.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/gm/GM.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/mail/Mail.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/pk/PK.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/plane/PlaneCacheInfo.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/plane/PlaneData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/task/Task.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/event/DataEvent.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/event/EventManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/event/HomeUIEvent.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/event/PlaneUIEvent.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/GameIns.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/Bullet.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/BulletPerformanceMonitor.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/BulletSystem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/Easing.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/Emitter.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/EventGroup.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/EventRunner.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/ObjectPool.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/PropertyContainer.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/actions/BulletEventActions.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/actions/EmitterEventActions.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/actions/IEventAction.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/conditions/BulletEventConditions.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/conditions/EmitterEventConditions.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/conditions/IEventCondition.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/collider-system/FBoxCollider.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/collider-system/FCircleCollider.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/collider-system/FCollider.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/collider-system/FColliderManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/collider-system/FPolygonCollider.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/collider-system/Intersection.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/collider-system/QuadTree.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/const/GameConst.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/const/GameEnum.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/const/GameResourceList.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/data/BossData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/data/BulletEventData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/data/EnemyData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/data/EnemyWave.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/data/MainPlaneFightData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/data/StageData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/data/TrackData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/data/WaveData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/data/bullet/BulletData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/data/bullet/EmitterData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/data/bullet/EventActionType.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/data/bullet/EventConditionType.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/data/bullet/EventGroupData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/data/bullet/ExpressionValue.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/dyncTerrain/EmittierTerrain.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/dyncTerrain/RandTerrain.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/event/GameEvent.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/level/LevelItem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/level/LevelItemEvent.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/BattleManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/BossManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/EnemyManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/GameDataManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/GamePlaneManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/GameRuleManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/GlobalDataManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/HurtEffectManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/MainPlaneManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/StageManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/WaveManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/move/IMovable.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/move/Movable.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/scenes/GameMain.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/test/ColliderTest.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/base/BaseComp.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/base/Controller.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/base/Entity.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/base/TrackComponent.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/base/UIAnimMethods.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/layer/BattleLayer.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/layer/EffectLayer.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/layer/EnemyEffectLayer.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/layer/GameInUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/GameMapRun.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelBaseUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelCondition.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelElemUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelEventUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelLayerUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelWaveUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/PlaneBase.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/boss/BossPlane.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/enemy/EnemyPlane.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/enemy/EnemyPlaneBase.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/mainPlane/MainPlane.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/skill/BuffComp.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/skill/SkillComp.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/weapon/Weapon.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/utils/Helper.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/utils/RPN.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/utils/Tools.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/game/wave/Wave.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/condition/LevelDataEventCondtion.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/condition/LevelDataEventCondtionDelayDistance.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/condition/LevelDataEventCondtionDelayTime.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/condition/LevelDataEventCondtionWave.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/condition/newCondition.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/leveldata.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/trigger/LevelDataEventTrigger.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/trigger/LevelDataEventTriggerAudio.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/trigger/LevelDataEventTriggerLog.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/trigger/LevelDataEventTriggerWave.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/trigger/newTrigger.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/luban/LubanMgr.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/network/NetMgr.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/plane/PlaneManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/plane/StateDefine.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/plane/StateMachine.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/platformsdk/DevLogin.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/platformsdk/DevLoginData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/platformsdk/IPlatformSDK.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/platformsdk/WXLogin.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/Plane.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/AnnouncementUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/PopupUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/RewardUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/TextUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/ToastUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/TopBlockInputUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/SelectList/uiSelect.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/SelectList/uiSelectItem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/button/ButtonPlus.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/button/DragButton.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/dropdown/DropDown.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/list/List.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/list/ListItem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/friend/FriendAddUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/friend/FriendCellUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/friend/FriendListUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/friend/FriendStrangerUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/friend/FriendUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/gameui/DevLoginUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/gameui/LoadingUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/gameui/RatioScaler.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/gameui/game/MBoomUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/gameui/res/PlaneRes.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/DialogueUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/RogueSelectIcon.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/RogueUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/mail/MailCellUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/mail/MailUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/main/MainUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/pk/PKHistoryCellUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/pk/PKHistoryUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/pk/PKRewardIcon.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/pk/PKUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/PlaneCombineResultUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/PlaneEquipInfoUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/PlaneTypes.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/PlaneUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/back_pack/BagGrid.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/back_pack/BagItem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/back_pack/SortTypeDropdown.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/back_pack/Tabs.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/display/CombineDisplay.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/display/EquipDisplay.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/shop/ShopUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/skyisland/SkyIslandUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/story/BuidingUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/story/BuildingInfoUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/story/StoryUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/talent/TalentUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/task/TaskUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/task/components/ProgressPanel.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/task/components/TaskItem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/gm/script/GmEntry.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/gm/script/ui/GmButtonUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/gm/script/ui/GmUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/emitter/EmitterEditor.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/gizmos/EmitterGizmo.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/gizmos/GizmoDrawer.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/gizmos/GizmoManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/gizmos/GizmoUtils.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/gizmos/LevelEventGizmo.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/gizmos/WaveGizmo.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/LevelEditorBaseUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/LevelEditorCondition.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/LevelEditorElemUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/LevelEditorEventUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/LevelEditorLayerUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/LevelEditorUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/LevelEditorWaveParam.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/LevelEditorWaveUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/preview/WavePreview.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/utils.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/planeview/PlaneView.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/AAA/init_cs_proto.js\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/core/base/Bundle.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/core/base/IMgr.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/core/base/MessageBox.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/core/base/ResManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/core/base/SingletonBase.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/core/base/UIMgr.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/resupdate/ResUpdate.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/resupdate/RootPersist.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/utils/Logger.ts\")];\n    for (const request of requests) {\n        try {\n            await request();\n        } catch (_err) {\n            // The error should have been caught by executor.\n        }\n    }\n})();\n    "]}