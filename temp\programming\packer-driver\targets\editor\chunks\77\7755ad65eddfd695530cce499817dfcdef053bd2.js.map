{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/layer/GameInUI.ts"], "names": ["_decorator", "Component", "Node", "EventManager", "GameEvent", "ccclass", "property", "GameInUI", "onEnable", "tipNode", "active", "Instance", "on", "GameStart", "onEventGameStart", "onDisable", "off"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;;AAEzBC,MAAAA,Y;;AACEC,MAAAA,S,iBAAAA,S;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBN,U;;0BAGjBO,Q,WADZF,OAAO,CAAC,UAAD,C,UAGHC,QAAQ,CAACJ,IAAD,C,2BAHb,MACaK,QADb,SAC8BN,SAD9B,CACwC;AAAA;AAAA;;AAAA;AAAA;;AAK1BO,QAAAA,QAAQ,GAAS;AACvB,eAAKC,OAAL,CAAcC,MAAd,GAAuB,IAAvB;AACA;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,EAAtB,CAAyB;AAAA;AAAA,sCAAUC,SAAnC,EAA8C,KAAKC,gBAAnD,EAAqE,IAArE;AACH;;AAESC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,4CAAaJ,QAAb,CAAsBK,GAAtB,CAA0B;AAAA;AAAA,sCAAUH,SAApC,EAA+C,KAAKC,gBAApD,EAAsE,IAAtE;AACH;;AAEDA,QAAAA,gBAAgB,GAAG;AACf,eAAKL,OAAL,CAAcC,MAAd,GAAuB,KAAvB;AACH;;AAhBmC,O;;;;;iBAGb,I", "sourcesContent": ["import { _decorator, Component, Node } from 'cc';\r\nimport { MyApp } from '../../../app/MyApp';\r\nimport EventManager from '../../../event/EventManager';\r\nimport { GameEvent } from '../../event/GameEvent';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('GameInUI')\r\nexport class GameInUI extends Component {\r\n\r\n    @property(Node)\r\n    tipNode: Node | null = null\r\n\r\n    protected onEnable(): void {\r\n        this.tipNode!.active = true;\r\n        EventManager.Instance.on(GameEvent.GameStart, this.onEventGameStart, this);\r\n    }\r\n\r\n    protected onDisable(): void {\r\n        EventManager.Instance.off(GameEvent.GameStart, this.onEventGameStart, this);\r\n    }\r\n\r\n    onEventGameStart() {\r\n        this.tipNode!.active = false;\r\n    }\r\n}\r\n\r\n\r\n"]}