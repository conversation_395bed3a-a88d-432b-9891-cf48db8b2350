{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/task/components/ProgressPanel.ts"], "names": ["_decorator", "Component", "Label", "Node", "ProgressBar", "Sprite", "ccclass", "property", "ProgressPanel", "_rewardNodes", "_progressNumberNodes", "_statusColors", "_numbers", "onLoad", "rewardParentNode", "children", "for<PERSON>ach", "node", "push", "progressNumberParentNode", "init", "taskList", "index", "renderReward", "renderProgressNumber", "icon", "getComponentsInChildren", "mask", "number", "getComponentInChildren", "bg", "string"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,M,OAAAA,M;;;;;;;;;OAEpD;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBP,U;;+BAGjBQ,a,WADZF,OAAO,CAAC,eAAD,C,UAEHC,QAAQ,CAACJ,IAAD,C,UAERI,QAAQ,CAACJ,IAAD,C,UAERI,QAAQ,CAACJ,IAAD,C,UAERI,QAAQ,CAACH,WAAD,C,UAERG,QAAQ,CAACJ,IAAD,C,2BAVb,MACaK,aADb,SACmCP,SADnC,CAC6C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAYjCQ,YAZiC,GAYV,EAZU;AAAA,eAajCC,oBAbiC,GAaF,EAbE;AAAA,eAcjCC,aAdiC,GAcP,CAAC,SAAD,EAAY,SAAZ,CAdO;AAAA,eAejCC,QAfiC,GAeZ,CAAC,IAAD,EAAO,IAAP,EAAa,IAAb,EAAmB,IAAnB,EAAyB,KAAzB,CAfY;AAAA;;AAgB/BC,QAAAA,MAAM,GAAS;AAAA;;AACrB,wCAAKC,gBAAL,mCAAuBC,QAAvB,CAAgCC,OAAhC,CAAyCC,IAAD,IAAU;AAC9C,iBAAKR,YAAL,CAAkBS,IAAlB,CAAuBD,IAAvB;AACH,WAFD;AAGA,wCAAKE,wBAAL,mCAA+BJ,QAA/B,CAAwCC,OAAxC,CAAiDC,IAAD,IAAU;AACtD,iBAAKP,oBAAL,CAA0BQ,IAA1B,CAA+BD,IAA/B;AACH,WAFD;AAGH;;AAEDG,QAAAA,IAAI,CAACC,QAAD,EAAqC;AACrC,eAAKZ,YAAL,CAAkBO,OAAlB,CAA0B,CAACC,IAAD,EAAOK,KAAP,KAAiB;AACvC,iBAAKC,YAAL,CAAkBN,IAAlB,EAAwBK,KAAxB;AACH,WAFD;;AAGA,eAAKZ,oBAAL,CAA0BM,OAA1B,CAAkC,CAACC,IAAD,EAAOK,KAAP,KAAiB;AAC/C,iBAAKE,oBAAL,CAA0BP,IAA1B,EAAgCK,KAAhC;AACH,WAFD;AAGH;;AAEOC,QAAAA,YAAY,CAACN,IAAD,EAAaK,KAAb,EAA4B;AAC5C,gBAAMG,IAAI,GAAGR,IAAI,CAACS,uBAAL,CAA6BrB,MAA7B,EAAqC,CAArC,CAAb;AACA,gBAAMsB,IAAI,GAAGV,IAAI,CAACS,uBAAL,CAA6BrB,MAA7B,EAAqC,CAArC,CAAb;AACH;;AAEOmB,QAAAA,oBAAoB,CAACP,IAAD,EAAaK,KAAb,EAA4B;AACpD,gBAAMM,MAAM,GAAGX,IAAI,CAACY,sBAAL,CAA4B3B,KAA5B,CAAf;AACA,gBAAM4B,EAAE,GAAGb,IAAI,CAACY,sBAAL,CAA4BxB,MAA5B,CAAX;AACAuB,UAAAA,MAAM,CAACG,MAAP,GAAgB,KAAKnB,QAAL,CAAcU,KAAd,CAAhB;AACH;;AA3CwC,O;;;;;iBAET,I;;;;;;;iBAEQ,I;;;;;;;iBAEL,I;;;;;;;iBAEJ,I;;;;;;;iBAEI,I", "sourcesContent": ["import { _decorator, Component, Label, Node, ProgressBar, Sprite } from 'cc';\nimport csproto from 'db://assets/bundles/common/script/autogen/pb/cs_proto.js';\nconst { ccclass, property } = _decorator;\n\n@ccclass('ProgressPanel')\nexport class ProgressPanel extends Component {\n    @property(Node)\n    rewardParentNode: Node | null = null;\n    @property(Node)\n    progressNumberParentNode: Node | null = null;\n    @property(Node)\n    progressStartNumber: Node | null = null;\n    @property(ProgressBar)\n    progress: ProgressBar | null = null;\n    @property(Node)\n    countDownParentNode: Node | null = null;\n\n    private _rewardNodes: Node[] = [];\n    private _progressNumberNodes: Node[] = [];\n    private _statusColors: string[] = [\"#A5A5A5\", \"#FFCE33\"];\n    private _numbers: string[] = [\"20\", \"40\", \"60\", \"80\", \"100\"];\n    protected onLoad(): void {\n        this.rewardParentNode?.children.forEach((node) => {\n            this._rewardNodes.push(node);\n        })\n        this.progressNumberParentNode?.children.forEach((node) => {\n            this._progressNumberNodes.push(node);\n        })\n    }\n\n    init(taskList: csproto.cs.ICSTaskInfo[]) {\n        this._rewardNodes.forEach((node, index) => {\n            this.renderReward(node, index);\n        })\n        this._progressNumberNodes.forEach((node, index) => {\n            this.renderProgressNumber(node, index);\n        })\n    }\n\n    private renderReward(node: Node, index: number) {\n        const icon = node.getComponentsInChildren(Sprite)[0];\n        const mask = node.getComponentsInChildren(Sprite)[1];\n    }\n\n    private renderProgressNumber(node: Node, index: number) {\n        const number = node.getComponentInChildren(Label)!;\n        const bg = node.getComponentInChildren(Sprite)!;\n        number.string = this._numbers[index];\n    }\n}"]}