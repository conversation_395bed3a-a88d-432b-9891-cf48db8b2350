System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9", "__unresolved_10"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, director, EPhysics2DDrawFlags, find, Node, PhysicsSystem2D, ResolutionPolicy, view, BottomUI, HomeUI, TopUI, UIMgr, MBoomUI, GameIns, BulletSystem, GameConst, GameEnum, BattleLayer, _dec, _dec2, _dec3, _dec4, _class, _class2, _descriptor, _descriptor2, _descriptor3, _crd, ccclass, property, GameMain;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfBottomUI(extras) {
    _reporterNs.report("BottomUI", "db://assets/bundles/common/script/ui/home/<USER>", _context.meta, extras);
  }

  function _reportPossibleCrUseOfHomeUI(extras) {
    _reporterNs.report("HomeUI", "db://assets/bundles/common/script/ui/home/<USER>", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTopUI(extras) {
    _reporterNs.report("TopUI", "db://assets/bundles/common/script/ui/home/<USER>", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "../../../../../scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMBoomUI(extras) {
    _reporterNs.report("MBoomUI", "../../ui/gameui/game/MBoomUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletSystem(extras) {
    _reporterNs.report("BulletSystem", "../bullet/BulletSystem", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFCollider(extras) {
    _reporterNs.report("FCollider", "../collider-system/FCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "../const/GameConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameEnum(extras) {
    _reporterNs.report("GameEnum", "../const/GameEnum", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBattleLayer(extras) {
    _reporterNs.report("BattleLayer", "../ui/layer/BattleLayer", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      director = _cc.director;
      EPhysics2DDrawFlags = _cc.EPhysics2DDrawFlags;
      find = _cc.find;
      Node = _cc.Node;
      PhysicsSystem2D = _cc.PhysicsSystem2D;
      ResolutionPolicy = _cc.ResolutionPolicy;
      view = _cc.view;
    }, function (_unresolved_2) {
      BottomUI = _unresolved_2.BottomUI;
    }, function (_unresolved_3) {
      HomeUI = _unresolved_3.HomeUI;
    }, function (_unresolved_4) {
      TopUI = _unresolved_4.TopUI;
    }, function (_unresolved_5) {
      UIMgr = _unresolved_5.UIMgr;
    }, function (_unresolved_6) {
      MBoomUI = _unresolved_6.MBoomUI;
    }, function (_unresolved_7) {
      GameIns = _unresolved_7.GameIns;
    }, function (_unresolved_8) {
      BulletSystem = _unresolved_8.BulletSystem;
    }, function (_unresolved_9) {
      GameConst = _unresolved_9.GameConst;
    }, function (_unresolved_10) {
      GameEnum = _unresolved_10.GameEnum;
    }, function (_unresolved_11) {
      BattleLayer = _unresolved_11.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "4d37fhOdolO05cNRnZmQ6z7", "GameMain", undefined);

      __checkObsolete__(['_decorator', 'Component', 'director', 'EPhysics2DDrawFlags', 'find', 'Node', 'PhysicsSystem2D', 'ResolutionPolicy', 'view']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("GameMain", GameMain = (_dec = ccclass('GameMain'), _dec2 = property(_crd && BattleLayer === void 0 ? (_reportPossibleCrUseOfBattleLayer({
        error: Error()
      }), BattleLayer) : BattleLayer), _dec3 = property(Node), _dec4 = property(Node), _dec(_class = (_class2 = class GameMain extends Component {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "BattleLayer", _descriptor, this);

          _initializerDefineProperty(this, "gameEnd", _descriptor2, this);

          _initializerDefineProperty(this, "CoverBg", _descriptor3, this);
        }

        onLoad() {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameMainUI = this;
          this.CoverBg.active = true;
          (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
            error: Error()
          }), BulletSystem) : BulletSystem).bulletParent = this.BattleLayer.selfBulletLayer; //设置子弹父节点
          // 设置分辨率适配策略

          let fitType = ResolutionPolicy.SHOW_ALL;

          if (view.getVisibleSize().height / view.getVisibleSize().width * 750 >= 1134) {
            //高度大于 16:9
            fitType = ResolutionPolicy.SHOW_ALL;
          } else {
            //宽屏,比如平板或者电脑
            fitType = ResolutionPolicy.FIXED_HEIGHT;
          }

          view.setResolutionPolicy(fitType);
          view.resizeWithBrowserSize(true);
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).fColliderManager.enable = true;
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).fColliderManager.setGlobalColliderEnterCall((colliderA, colliderB) => {
            var _colliderA$entity, _colliderB$entity;

            (_colliderA$entity = colliderA.entity) == null || _colliderA$entity.onCollide == null || _colliderA$entity.onCollide(colliderB);
            (_colliderB$entity = colliderB.entity) == null || _colliderB$entity.onCollide == null || _colliderB$entity.onCollide(colliderA);
          });

          if ((_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).ColliderDraw) {
            PhysicsSystem2D.instance.enable = true;
            PhysicsSystem2D.instance.debugDrawFlags = EPhysics2DDrawFlags.Aabb;
          }
        }

        start() {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.startLoading();
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && MBoomUI === void 0 ? (_reportPossibleCrUseOfMBoomUI({
            error: Error()
          }), MBoomUI) : MBoomUI);
        }

        showGameResult(isSuccess) {
          this.gameEnd.active = true;
          let LabelWin = find("LabelWin", this.gameEnd);
          let LabelFail = find("LabelFail", this.gameEnd);
          LabelWin.active = isSuccess;
          LabelFail.active = !isSuccess;
        }

        async onBtnAgainClicked() {
          this.gameEnd.active = false;
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.mainReset();
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).closeUI(_crd && MBoomUI === void 0 ? (_reportPossibleCrUseOfMBoomUI({
            error: Error()
          }), MBoomUI) : MBoomUI);
          await (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && HomeUI === void 0 ? (_reportPossibleCrUseOfHomeUI({
            error: Error()
          }), HomeUI) : HomeUI);
          await (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && BottomUI === void 0 ? (_reportPossibleCrUseOfBottomUI({
            error: Error()
          }), BottomUI) : BottomUI);
          await (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && TopUI === void 0 ? (_reportPossibleCrUseOfTopUI({
            error: Error()
          }), TopUI) : TopUI);
          director.loadScene("Main");
        }
        /**
         * 每帧更新逻辑
         * @param deltaTime 时间增量
         */


        update(deltaTime) {
          // 限制 deltaTime 的最大值
          if (deltaTime > 0.2) {
            deltaTime = 0.016666666666667; // 约等于 1/60 秒
          }

          switch ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.gameType) {
            case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).GameType.Common:
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).battleManager.update(deltaTime);
              break;
          }
        }

        lateUpdate(dt) {
          if ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameRuleManager.isInBattle() || (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameRuleManager.isGameWillOver()) {
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).fColliderManager.update(dt);
          }
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "BattleLayer", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "gameEnd", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "CoverBg", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=79dbbe89fd729c78558a41e16ca4562ac2d5abe2.js.map