{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/scenes/GameMain.ts"], "names": ["_decorator", "Component", "director", "EPhysics2DDrawFlags", "find", "Node", "PhysicsSystem2D", "ResolutionPolicy", "view", "BottomUI", "HomeUI", "TopUI", "UIMgr", "MBoomUI", "GameIns", "BulletSystem", "GameConst", "GameEnum", "BattleLayer", "ccclass", "property", "GameMain", "onLoad", "gameMainUI", "CoverBg", "active", "bulletParent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fitType", "SHOW_ALL", "getVisibleSize", "height", "width", "FIXED_HEIGHT", "setResolutionPolicy", "resizeWithBrowserSize", "fColliderManager", "enable", "setGlobalColliderEnterCall", "colliderA", "colliderB", "entity", "onCollide", "ColliderDraw", "instance", "debugDrawFlags", "<PERSON><PERSON><PERSON>", "start", "battleManager", "startLoading", "openUI", "showGameResult", "isSuccess", "gameEnd", "LabelWin", "LabelFail", "onBtnAgainClicked", "mainReset", "closeUI", "loadScene", "update", "deltaTime", "gameType", "GameType", "Common", "lateUpdate", "dt", "gameRuleManager", "isInBattle", "isGameWillOver"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,mB,OAAAA,mB;AAAqBC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,e,OAAAA,e;AAAiBC,MAAAA,gB,OAAAA,gB;AAAkBC,MAAAA,I,OAAAA,I;;AACrGC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,Y,iBAAAA,Y;;AAEAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,Q,kBAAAA,Q;;AACFC,MAAAA,W;;;;;;;;;OAED;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBpB,U;;0BAGjBqB,Q,WADZF,OAAO,CAAC,UAAD,C,UAIHC,QAAQ;AAAA;AAAA,qC,UAGRA,QAAQ,CAACf,IAAD,C,UAERe,QAAQ,CAACf,IAAD,C,2BATb,MACagB,QADb,SAC8BpB,SAD9B,CACwC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAY1BqB,QAAAA,MAAM,GAAS;AACrB;AAAA;AAAA,kCAAQC,UAAR,GAAqB,IAArB;AACA,eAAKC,OAAL,CAAcC,MAAd,GAAuB,IAAvB;AACA;AAAA;AAAA,4CAAaC,YAAb,GAA4B,KAAKR,WAAL,CAAkBS,eAA9C,CAHqB,CAG0C;AAE/D;;AACA,cAAIC,OAAO,GAAGrB,gBAAgB,CAACsB,QAA/B;;AACA,cAAIrB,IAAI,CAACsB,cAAL,GAAsBC,MAAtB,GAA+BvB,IAAI,CAACsB,cAAL,GAAsBE,KAArD,GAA6D,GAA7D,IAAoE,IAAxE,EAA6E;AAAC;AAC1EJ,YAAAA,OAAO,GAAGrB,gBAAgB,CAACsB,QAA3B;AACH,WAFD,MAEK;AAAC;AACFD,YAAAA,OAAO,GAAGrB,gBAAgB,CAAC0B,YAA3B;AACH;;AACDzB,UAAAA,IAAI,CAAC0B,mBAAL,CAAyBN,OAAzB;AACApB,UAAAA,IAAI,CAAC2B,qBAAL,CAA2B,IAA3B;AAEA;AAAA;AAAA,kCAAQC,gBAAR,CAAyBC,MAAzB,GAAkC,IAAlC;AACA;AAAA;AAAA,kCAAQD,gBAAR,CAAyBE,0BAAzB,CAAoD,CAACC,SAAD,EAAuBC,SAAvB,KAAgD;AAAA;;AAChG,iCAAAD,SAAS,CAACE,MAAV,+BAAkBC,SAAlB,8BAAkBA,SAAlB,CAA8BF,SAA9B;AACA,iCAAAA,SAAS,CAACC,MAAV,+BAAkBC,SAAlB,8BAAkBA,SAAlB,CAA8BH,SAA9B;AACH,WAHD;;AAKA,cAAI;AAAA;AAAA,sCAAUI,YAAd,EAA4B;AACxBrC,YAAAA,eAAe,CAACsC,QAAhB,CAAyBP,MAAzB,GAAkC,IAAlC;AACA/B,YAAAA,eAAe,CAACsC,QAAhB,CAAyBC,cAAzB,GAA0C1C,mBAAmB,CAAC2C,IAA9D;AACH;AACJ;;AAEDC,QAAAA,KAAK,GAAG;AACJ;AAAA;AAAA,kCAAQC,aAAR,CAAsBC,YAAtB;AACA;AAAA;AAAA,8BAAMC,MAAN;AAAA;AAAA;AACH;;AAEDC,QAAAA,cAAc,CAACC,SAAD,EAAqB;AAC/B,eAAKC,OAAL,CAAc5B,MAAd,GAAuB,IAAvB;AAEA,cAAI6B,QAAQ,GAAGlD,IAAI,CAAC,UAAD,EAAa,KAAKiD,OAAlB,CAAnB;AACA,cAAIE,SAAS,GAAGnD,IAAI,CAAC,WAAD,EAAc,KAAKiD,OAAnB,CAApB;AACAC,UAAAA,QAAQ,CAAE7B,MAAV,GAAmB2B,SAAnB;AACAG,UAAAA,SAAS,CAAE9B,MAAX,GAAoB,CAAC2B,SAArB;AACH;;AAEsB,cAAjBI,iBAAiB,GAAG;AACtB,eAAKH,OAAL,CAAc5B,MAAd,GAAuB,KAAvB;AACA;AAAA;AAAA,kCAAQuB,aAAR,CAAsBS,SAAtB;AACA;AAAA;AAAA,8BAAMC,OAAN;AAAA;AAAA;AACA,gBAAM;AAAA;AAAA,8BAAMR,MAAN;AAAA;AAAA,+BAAN;AACA,gBAAM;AAAA;AAAA,8BAAMA,MAAN;AAAA;AAAA,mCAAN;AACA,gBAAM;AAAA;AAAA,8BAAMA,MAAN;AAAA;AAAA,6BAAN;AACAhD,UAAAA,QAAQ,CAACyD,SAAT,CAAmB,MAAnB;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,MAAM,CAACC,SAAD,EAA0B;AAC5B;AACA,cAAIA,SAAS,GAAG,GAAhB,EAAqB;AACjBA,YAAAA,SAAS,GAAG,iBAAZ,CADiB,CACc;AAClC;;AAED,kBAAQ;AAAA;AAAA,kCAAQb,aAAR,CAAsBc,QAA9B;AACI,iBAAK;AAAA;AAAA,sCAASC,QAAT,CAAkBC,MAAvB;AACI;AAAA;AAAA,sCAAQhB,aAAR,CAAsBY,MAAtB,CAA6BC,SAA7B;AACA;AAHR;AAKH;;AAEDI,QAAAA,UAAU,CAACC,EAAD,EAAmB;AACzB,cAAI;AAAA;AAAA,kCAAQC,eAAR,CAAwBC,UAAxB,MAAwC;AAAA;AAAA,kCAAQD,eAAR,CAAwBE,cAAxB,EAA5C,EAAsF;AAClF;AAAA;AAAA,oCAAQjC,gBAAR,CAAyBwB,MAAzB,CAAgCM,EAAhC;AACH;AACJ;;AApFmC,O;;;;;iBAIF,I;;;;;;;iBAGX,I;;;;;;;iBAEA,I", "sourcesContent": ["import { _decorator, Component, director, EPhysics2DDrawFlags, find, Node, PhysicsSystem2D, ResolutionPolicy, view } from 'cc';\r\nimport { BottomUI } from 'db://assets/bundles/common/script/ui/home/<USER>';\r\nimport { HomeUI } from 'db://assets/bundles/common/script/ui/home/<USER>';\r\nimport { TopUI } from 'db://assets/bundles/common/script/ui/home/<USER>';\r\nimport { UIMgr } from '../../../../../scripts/core/base/UIMgr';\r\nimport { MBoomUI } from '../../ui/gameui/game/MBoomUI';\r\nimport { GameIns } from '../GameIns';\r\nimport { BulletSystem } from '../bullet/BulletSystem';\r\nimport FCollider from '../collider-system/FCollider';\r\nimport { GameConst } from '../const/GameConst';\r\nimport { GameEnum } from '../const/GameEnum';\r\nimport BattleLayer from '../ui/layer/BattleLayer';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('GameMain')\r\nexport class GameMain extends Component {\r\n\r\n\r\n    @property(BattleLayer)\r\n    BattleLayer: BattleLayer | null = null;\r\n\r\n    @property(Node)\r\n    gameEnd: Node | null = null;\r\n    @property(Node)\r\n    CoverBg: Node | null = null;\r\n\r\n\r\n    protected onLoad(): void {\r\n        GameIns.gameMainUI = this;\r\n        this.CoverBg!.active = true;\r\n        BulletSystem.bulletParent = this.BattleLayer!.selfBulletLayer!;//设置子弹父节点\r\n\r\n        // 设置分辨率适配策略\r\n        let fitType = ResolutionPolicy.SHOW_ALL\r\n        if (view.getVisibleSize().height / view.getVisibleSize().width * 750 >= 1134){//高度大于 16:9\r\n            fitType = ResolutionPolicy.SHOW_ALL\r\n        }else{//宽屏,比如平板或者电脑\r\n            fitType = ResolutionPolicy.FIXED_HEIGHT\r\n        }\r\n        view.setResolutionPolicy(fitType);\r\n        view.resizeWithBrowserSize(true);\r\n\r\n        GameIns.fColliderManager.enable = true;\r\n        GameIns.fColliderManager.setGlobalColliderEnterCall((colliderA: FCollider, colliderB: FCollider) => {\r\n            colliderA.entity?.onCollide?.(colliderB);\r\n            colliderB.entity?.onCollide?.(colliderA);\r\n        });\r\n\r\n        if (GameConst.ColliderDraw) {\r\n            PhysicsSystem2D.instance.enable = true;\r\n            PhysicsSystem2D.instance.debugDrawFlags = EPhysics2DDrawFlags.Aabb;\r\n        }\r\n    }\r\n\r\n    start() {\r\n        GameIns.battleManager.startLoading();\r\n        UIMgr.openUI(MBoomUI)\r\n    }\r\n\r\n    showGameResult(isSuccess: boolean) {\r\n        this.gameEnd!.active = true;\r\n\r\n        let LabelWin = find(\"LabelWin\", this.gameEnd!);\r\n        let LabelFail = find(\"LabelFail\", this.gameEnd!);\r\n        LabelWin!.active = isSuccess;\r\n        LabelFail!.active = !isSuccess;\r\n    }\r\n\r\n    async onBtnAgainClicked() {\r\n        this.gameEnd!.active = false;\r\n        GameIns.battleManager.mainReset();\r\n        UIMgr.closeUI(MBoomUI)\r\n        await UIMgr.openUI(HomeUI)\r\n        await UIMgr.openUI(BottomUI)\r\n        await UIMgr.openUI(TopUI)\r\n        director.loadScene(\"Main\");\r\n    }\r\n\r\n    /**\r\n     * 每帧更新逻辑\r\n     * @param deltaTime 时间增量\r\n     */\r\n    update(deltaTime: number): void {\r\n        // 限制 deltaTime 的最大值\r\n        if (deltaTime > 0.2) {\r\n            deltaTime = 0.016666666666667; // 约等于 1/60 秒\r\n        }\r\n\r\n        switch (GameIns.battleManager.gameType) {\r\n            case GameEnum.GameType.Common:\r\n                GameIns.battleManager.update(deltaTime);\r\n                break;\r\n        }\r\n    }\r\n\r\n    lateUpdate(dt: number): void {\r\n        if (GameIns.gameRuleManager.isInBattle() || GameIns.gameRuleManager.isGameWillOver()) {\r\n            GameIns.fColliderManager.update(dt);\r\n        }\r\n    }\r\n}\r\n\r\n\r\n"]}