{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/level/utils.ts"], "names": ["LevelEditorUtils", "_decorator", "assetManager", "CCFloat", "CCInteger", "Enum", "Node", "Prefab", "LayerSplicingMode", "LayerType", "ccclass", "property", "LayerTypeZh", "Background", "Random", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "LayerSplicingModeZh", "node_height", "fix_height", "random_height", "LayerRandomRange", "displayName", "constructor", "min", "max", "LevelScrollLayerUI", "type", "visible", "splicingMode", "splicingModeZh", "value", "LevelRandTerrainUI", "LevelRandTerrainsLayerUI", "LevelRandTerrainsLayersUI", "LevelEmittierLayerUI", "<PERSON><PERSON><PERSON><PERSON>", "group", "typeZh", "LevelBackgroundLayer", "backgroundsNode", "getOrAddNode", "node_parent", "name", "node", "getChildByName", "<PERSON><PERSON><PERSON><PERSON>", "getOrAddComp", "classConstructor", "comp", "getComponent", "addComponent", "loadByPath", "path", "Promise", "resolve", "uuid", "Editor", "Message", "request", "loadAny", "err", "asset", "console", "warn"], "mappings": ";;;+KA8HaA,gB;;;;;;;;;;;;;;;;;;;;;;;;;AA9HOC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,Y,OAAAA,Y;AAAqBC,MAAAA,O,OAAAA,O;AAASC,MAAAA,S,OAAAA,S;AAAsBC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;;AACvFC,MAAAA,iB,iBAAAA,iB;AAAmBC,MAAAA,S,iBAAAA,S;;;;;;;;;OAEtB;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBV,U;;6BAElBW,W,aAAAA,W;AAAAA,QAAAA,W,CAAAA,W,mBACH;AAAA;AAAA,oCAAUC,U;AADPD,QAAAA,W,CAAAA,W,mBAEH;AAAA;AAAA,oCAAUE,M;AAFPF,QAAAA,W,CAAAA,W,mBAGH;AAAA;AAAA,oCAAUG,M;AAHPH,QAAAA,W,CAAAA,W,yBAIF;AAAA;AAAA,oCAAUI,Q;eAJRJ,W;;;AAOPK,MAAAA,mB,aAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB,2CACQ;AAAA;AAAA,oDAAkBC,W;AAD1BD,QAAAA,mB,CAAAA,mB,2CAEQ;AAAA;AAAA,oDAAkBE,U;AAF1BF,QAAAA,mB,CAAAA,mB,2CAGQ;AAAA;AAAA,oDAAkBG,a;eAH1BH,mB;QAAAA,mB;;kCAOQI,gB,WADZX,OAAO,CAAC,kBAAD,C,UAEHC,QAAQ,CAAC;AAAEW,QAAAA,WAAW,EAAE;AAAf,OAAD,C,UAGRX,QAAQ,CAAC;AAAEW,QAAAA,WAAW,EAAE;AAAf,OAAD,C,2BALb,MACaD,gBADb,CAC8B;AAO1BE,QAAAA,WAAW,CAACC,GAAW,GAAG,CAAf,EAAkBC,GAAW,GAAG,CAAhC,EAAmC;AAAA;;AAAA;;AAC1C,eAAKD,GAAL,GAAWA,GAAX;AACA,eAAKC,GAAL,GAAWA,GAAX;AACH;;AAVyB,O;;;;;iBAEL,C;;;;;;;iBAGP,C;;;;oCASLC,kB,YADZhB,OAAO,CAAC,0BAAD,C,UAEHC,QAAQ,CAAC;AAACgB,QAAAA,IAAI,EAAE,CAACpB,MAAD,CAAP;AAAiBe,QAAAA,WAAW,EAAE;AAA9B,OAAD,C,UAERX,QAAQ,CAAC;AAACgB,QAAAA,IAAI,EAAEvB,SAAP;AAAkBkB,QAAAA,WAAW,EAAE;AAA/B,OAAD,C,UAGRX,QAAQ,CAAC;AAACiB,QAAAA,OAAO,EAAE;AAAV,OAAD,C,UAERjB,QAAQ,CAAC;AAACgB,QAAAA,IAAI,EAAEtB,IAAI,CAACY,mBAAD,CAAX;AAAkCK,QAAAA,WAAW,EAAC;AAA9C,OAAD,C,UAGRX,QAAQ,CAAC;AAACgB,QAAAA,IAAI,EAAEN,gBAAP;AAAyBC,QAAAA,WAAW,EAAC,OAArC;AACNM,QAAAA,OAAO,EAAE,YAAmC;AACxC,iBAAO,KAAKC,YAAL,KAAsB;AAAA;AAAA,sDAAkBT,aAA/C;AACH;AAHK,OAAD,C,WAORT,QAAQ,CAAC;AAACgB,QAAAA,IAAI,EAAEN,gBAAP;AAAyBC,QAAAA,WAAW,EAAC,OAArC;AACNM,QAAAA,OAAO,EAAE,YAAmC;AACxC,iBAAO,KAAKC,YAAL,KAAsB;AAAA;AAAA,sDAAkBT,aAA/C;AACH;AAHK,OAAD,C,6BAnBb,MACaM,kBADb,CACgC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AASH,YAAdI,cAAc,GAAwB;AAAE,iBAAO,KAAKD,YAAZ;AAA4D;;AACtF,YAAdC,cAAc,CAACC,KAAD,EAA6B;AAAE,eAAKF,YAAL,GAAoBE,KAApB;AAA4D;;AAVxF,O;;;;;iBAEK,E;;;;;;;iBAET,G;;;;;;;iBAGiB;AAAA;AAAA,sDAAkBb,W;;;;;;;iBAUhB,IAAIG,gBAAJ,E;;;;;;;iBAMA,IAAIA,gBAAJ,E;;;;oCAIlCW,kB,aADZtB,OAAO,CAAC,0BAAD,C,WAEHC,QAAQ,CAAC;AAACgB,QAAAA,IAAI,EAAEvB,SAAP;AAAkBkB,QAAAA,WAAW,EAAE;AAA/B,OAAD,C,WAGRX,QAAQ,CAAC;AAACgB,QAAAA,IAAI,EAAEpB,MAAP;AAAee,QAAAA,WAAW,EAAE;AAA5B,OAAD,C,8BALb,MACaU,kBADb,CACgC;AAAA;AAAA;;AAAA;AAAA,UAKiB;;;AALjB,O;;;;;iBAEJ,G;;;;;;;iBAGe,I;;;;0CAI9BC,wB,aADZvB,OAAO,CAAC,gCAAD,C,WAEHC,QAAQ,CAAC;AAACgB,QAAAA,IAAI,EAAEvB,SAAP;AAAkBkB,QAAAA,WAAW,EAAE;AAA/B,OAAD,C,WAGRX,QAAQ,CAAC;AAACgB,QAAAA,IAAI,EAAE,CAACK,kBAAD,CAAP;AAA6BV,QAAAA,WAAW,EAAE;AAA1C,OAAD,C,gCALb,MACaW,wBADb,CACsC;AAAA;AAAA;;AAAA;AAAA;;AAAA,O;;;;;iBAEV,G;;;;;;;iBAGsB,E;;;;2CAIrCC,yB,aADZxB,OAAO,CAAC,iCAAD,C,WAEHC,QAAQ,CAAC;AAACgB,QAAAA,IAAI,EAAE,CAACM,wBAAD,CAAP;AAAmCX,QAAAA,WAAW,EAAE;AAAhD,OAAD,C,gCAFb,MACaY,yBADb,CACuC;AAAA;AAAA;AAAA;;AAAA,O;;;;;iBAEkB,E;;;;sCAI5CC,oB,aADZzB,OAAO,CAAC,4BAAD,C,WAEHC,QAAQ,CAAC;AAACgB,QAAAA,IAAI,EAAE,CAACpB,MAAD,CAAP;AAAiBe,QAAAA,WAAW,EAAE;AAA9B,OAAD,C,gCAFb,MACaa,oBADb,CACkC;AAAA;AAAA;AAAA;;AAAA,O;;;;;iBAEK,E;;;;4BAI1BC,U,aADZ1B,OAAO,CAAC,kBAAD,C,WAEHC,QAAQ,CAAC;AAACW,QAAAA,WAAW,EAAE;AAAd,OAAD,C,WAERX,QAAQ,CAAC;AAACgB,QAAAA,IAAI,EAAEvB,SAAP;AAAkBkB,QAAAA,WAAW,EAAE;AAA/B,OAAD,C,WAERX,QAAQ,CAAC;AAACgB,QAAAA,IAAI,EAAErB,IAAP;AAAa+B,QAAAA,KAAK,EAAE;AAApB,OAAD,C,WAER1B,QAAQ,CAAC;AAACgB,QAAAA,IAAI,EAACxB,OAAN;AAAemB,QAAAA,WAAW,EAAC,IAA3B;AAAiCe,QAAAA,KAAK,EAAE;AAAxC,OAAD,C,WAER1B,QAAQ,CAAC;AAACgB,QAAAA,IAAI,EAAEtB,IAAI,CAACO,WAAD,CAAX;AAA0BU,QAAAA,WAAW,EAAC,MAAtC;AAA8Ce,QAAAA,KAAK,EAAE;AAArD,OAAD,C,WAIR1B,QAAQ,CAAC;AAAEgB,QAAAA,IAAI,EAAE,CAACD,kBAAD,CAAR;AAA8BJ,QAAAA,WAAW,EAAE,KAA3C;AAAkDe,QAAAA,KAAK,EAAE,MAAzD;AACNT,QAAAA,OAAO,EAAE,YAA2B;AAChC,iBAAO,KAAKD,IAAL,KAAc;AAAA;AAAA,sCAAUZ,MAA/B;AACH;AAHK,OAAD,C,WAORJ,QAAQ,CAAC;AAACgB,QAAAA,IAAI,EAAE,CAACO,yBAAD,CAAP;AAAoCZ,QAAAA,WAAW,EAAC,KAAhD;AAAuDe,QAAAA,KAAK,EAAE,MAA9D;AACNT,QAAAA,OAAO,EAAE,YAA2B;AAChC,iBAAO,KAAKD,IAAL,KAAc;AAAA;AAAA,sCAAUb,MAA/B;AACH;AAHK,OAAD,C,gCArBb,MACasB,UADb,CACwB;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAYbT,IAZa,GAYK;AAAA;AAAA,sCAAUd,UAZf;;AAAA;;AAAA;AAAA;;AAUH,YAANyB,MAAM,GAAgB;AAAE,iBAAO,KAAKX,IAAZ;AAA4C;;AAC9D,YAANW,MAAM,CAACP,KAAD,EAAqB;AAAE,eAAKJ,IAAL,GAAYI,KAAZ;AAA2C;;AAX/D,O;;;;;iBAEI,E;;;;;;;iBAEA,C;;;;;;;iBAEG,I;;;;;;;iBAEJ,E;;;;;;;iBAUqB,E;;;;;;;iBAOO,E;;;;sCAI1CQ,oB,aADZ7B,OAAO,CAAC,4BAAD,C,WAEHC,QAAQ,CAAC;AAACgB,QAAAA,IAAI,EAAE,CAACpB,MAAD,CAAP;AAAiBe,QAAAA,WAAW,EAAE,KAA9B;AAAoCe,QAAAA,KAAK,EAAE;AAA3C,OAAD,C,gCAFb,MACaE,oBADb,SAC0CH,UAD1C,CACqD;AAAA;AAAA;;AAAA;;AAAA,eAI1CI,eAJ0C,GAIb,IAJa;AAAA;;AAAA,O;;;;;iBAElB,E;;;;kCAKtBxC,gB,GAAN,MAAMA,gBAAN,CAAuB;AACA,eAAZyC,YAAY,CAACC,WAAD,EAAoBC,IAApB,EAAwC;AAC9D,cAAIC,IAAI,GAAGF,WAAW,CAAEG,cAAb,CAA4BF,IAA5B,CAAX;;AACA,cAAIC,IAAI,IAAI,IAAZ,EAAkB;AACdA,YAAAA,IAAI,GAAG,IAAItC,IAAJ,CAASqC,IAAT,CAAP;AACAD,YAAAA,WAAW,CAACI,QAAZ,CAAqBF,IAArB;AACH;;AACD,iBAAOA,IAAP;AACH;;AAEyB,eAAZG,YAAY,CAAsBH,IAAtB,EAAkCI,gBAAlC,EAAkG;AACxH,cAAIC,IAAI,GAAGL,IAAI,CAACM,YAAL,CAAkBF,gBAAlB,CAAX;;AACA,cAAIC,IAAI,IAAI,IAAZ,EAAkB;AACdA,YAAAA,IAAI,GAAGL,IAAI,CAACO,YAAL,CAAkBH,gBAAlB,CAAP;AACH;;AACD,iBAAOC,IAAP;AACH,SAhByB,CAkB1B;;;AAC8B,qBAAVG,UAAU,CAAkBC,IAAlB,EAAiD;AAC3E,cAAIA,IAAI,IAAI,IAAR,IAAgBA,IAAI,IAAI,EAA5B,EAAgC;AAC5B,mBAAOC,OAAO,CAACC,OAAR,CAAgB,IAAhB,CAAP;AACH,WAH0E,CAK3E;;;AACA,gBAAMC,IAAI,GAAG,MAAMC,MAAM,CAACC,OAAP,CAAeC,OAAf,CAAuB,UAAvB,EAAmC,YAAnC,EAAiDN,IAAjD,CAAnB;AACA,iBAAO,IAAIC,OAAJ,CAAuBC,OAAD,IAAa;AACtCrD,YAAAA,YAAY,CAAC0D,OAAb,CAAwBJ,IAAxB,EAA8B,CAACK,GAAD,EAAMC,KAAN,KAAmB;AAC7C,kBAAID,GAAJ,EAAS;AACLE,gBAAAA,OAAO,CAACC,IAAR,CAAc,2BAA0BX,IAAK,EAA7C,EAAgDQ,GAAhD;AACAN,gBAAAA,OAAO,CAAC,IAAD,CAAP;AACA;AACH;;AACDA,cAAAA,OAAO,CAACO,KAAD,CAAP;AACH,aAPD;AAQH,WATM,CAAP;AAUH;;AApCyB,O", "sourcesContent": ["import { __private, _decorator, assetManager, Asset, CCFloat, CCInteger, Component, Enum, Node, Prefab } from \"cc\";\r\nimport { LayerSplicingMode, LayerType } from \"db://assets/bundles/common/script/leveldata/leveldata\";\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\nexport enum LayerTypeZh {\r\n    背景 = LayerType.Background,\r\n    随机 = LayerType.Random,\r\n    滚动 = LayerType.Scroll,\r\n    发射组 = LayerType.Emittier\r\n}\r\n\r\nenum LayerSplicingModeZh {\r\n    节点高度拼接 = LayerSplicingMode.node_height,\r\n    固定高度拼接 = LayerSplicingMode.fix_height,\r\n    随机高度拼接 = LayerSplicingMode.random_height\r\n}\r\n\r\n@ccclass('LayerRandomRange')\r\nexport class LayerRandomRange {\r\n    @property({ displayName: \"最小值\" })\r\n    public min: number = 0;\r\n    \r\n    @property({ displayName: \"最大值\" })\r\n    max: number = 0;\r\n\r\n    constructor(min: number = 0, max: number = 0) {\r\n        this.min = min;\r\n        this.max = max;\r\n    }\r\n}\r\n\r\n@ccclass('LevelEditorScrollLayerUI')\r\nexport class LevelScrollLayerUI { \r\n    @property({type: [Prefab], displayName: '滚动体'})\r\n    public scrollPrefabs: Prefab[] = [];\r\n    @property({type: CCInteger, displayName: \"权重\"})\r\n    public weight: number = 100;\r\n\r\n    @property({visible: false})\r\n    public splicingMode: LayerSplicingMode = LayerSplicingMode.node_height;\r\n    @property({type: Enum(LayerSplicingModeZh), displayName:\"拼接模式\",})\r\n    public get splicingModeZh(): LayerSplicingModeZh { return this.splicingMode as unknown as LayerSplicingModeZh;}\r\n    public set splicingModeZh(value: LayerSplicingModeZh) { this.splicingMode = value as unknown as LayerSplicingMode; }\r\n    @property({type: LayerRandomRange, displayName:\"X偏移范围\",\r\n        visible: function(this: LevelScrollLayerUI) {\r\n            return this.splicingMode === LayerSplicingMode.random_height;\r\n        }\r\n    })\r\n    \r\n    public splicingOffsetX: LayerRandomRange = new LayerRandomRange(); \r\n    @property({type: LayerRandomRange, displayName:\"Y偏移范围\",\r\n        visible: function(this: LevelScrollLayerUI) {\r\n            return this.splicingMode === LayerSplicingMode.random_height;\r\n        }\r\n    })\r\n    public splicingOffsetY: LayerRandomRange = new LayerRandomRange(); \r\n}\r\n\r\n@ccclass('LevelEditorRandTerrainUI')\r\nexport class LevelRandTerrainUI {\r\n    @property({type: CCInteger, displayName: \"权重\"})\r\n    public weight: number = 100;\r\n\r\n    @property({type: Prefab, displayName: \"地形组预制体\"})\r\n    public terrainElement: Prefab | null = null; // 可以是TerrainElem、DynamicTerrains的预制体\r\n}\r\n\r\n@ccclass('LevelEditorRandTerrainsLayerUI')\r\nexport class LevelRandTerrainsLayerUI {\r\n    @property({type: CCInteger, displayName: \"权重\"})\r\n    public weight: number = 100;\r\n\r\n    @property({type: [LevelRandTerrainUI], displayName: \"地形策略\"})\r\n    public dynamicTerrain: LevelRandTerrainUI[] = []; \r\n}\r\n\r\n@ccclass('LevelEditorRandTerrainsLayersUI')\r\nexport class LevelRandTerrainsLayersUI {\r\n    @property({type: [LevelRandTerrainsLayerUI], displayName: \"地形策略组\"})\r\n    public dynamicTerrains: LevelRandTerrainsLayerUI[] = []; \r\n}\r\n\r\n@ccclass('LevelEditorEmittierLayerUI')\r\nexport class LevelEmittierLayerUI {\r\n    @property({type: [Prefab], displayName: \"发射体\"})\r\n    public emittierPrefabs: Prefab[] = [];\r\n}\r\n\r\n@ccclass('LevelEditorLayer')\r\nexport class LevelLayer {\r\n    @property({displayName: \"备注\"})\r\n    public remark: string = \"\";\r\n    @property({type: CCInteger, displayName: \"层级顺序\",})\r\n    public zIndex: number = 0;\r\n    @property({type: Node, group: \"层级信息\"})\r\n    public node: Node | null = null;\r\n    @property({type:CCFloat, displayName:\"速度\", group: \"层级信息\"})\r\n    public speed: number = 10;\r\n    @property({type: Enum(LayerTypeZh), displayName:\"地形类型\", group: \"地形信息\"})\r\n    public get typeZh(): LayerTypeZh { return this.type as unknown as LayerTypeZh;}\r\n    public set typeZh(value: LayerTypeZh) { this.type = value as unknown as LayerType;}\r\n    public type: LayerType = LayerType.Background;\r\n    @property({ type: [LevelScrollLayerUI], displayName: \"滚动组\", group: \"地形信息\",\r\n        visible: function(this: LevelLayer) {\r\n            return this.type === LayerType.Scroll;\r\n        }\r\n    })\r\n    public scrollLayers: LevelScrollLayerUI[] = [];\r\n\r\n    @property({type: [LevelRandTerrainsLayersUI], displayName:\"随机组\", group: \"地形信息\",\r\n        visible: function(this: LevelLayer) {\r\n            return this.type === LayerType.Random;\r\n        },\r\n    })\r\n    public randomLayers: LevelRandTerrainsLayersUI[] = [];\r\n}\r\n\r\n@ccclass('LevelEditorBackgroundLayer')\r\nexport class LevelBackgroundLayer extends LevelLayer {\r\n    @property({type: [Prefab], displayName: '背景组',group: \"地形信息\"})\r\n    public backgrounds: Prefab[] = [];\r\n\r\n    public backgroundsNode: Node|null = null;\r\n}\r\n\r\nexport class LevelEditorUtils {\r\n    public static getOrAddNode(node_parent: Node, name: string): Node {\r\n        var node = node_parent!.getChildByName(name);\r\n        if (node == null) {\r\n            node = new Node(name);\r\n            node_parent.addChild(node);\r\n        }\r\n        return node;\r\n    }\r\n\r\n    public static getOrAddComp<T extends Component>(node: Node, classConstructor: __private.__types_globals__Constructor<T>): T {\r\n        var comp = node.getComponent(classConstructor);\r\n        if (comp == null) {\r\n            comp = node.addComponent(classConstructor);\r\n        }\r\n        return comp;\r\n    }\r\n    \r\n    // 仅在编辑器中使用\r\n    public static async loadByPath<T extends Asset>(path: string): Promise<T|null> {\r\n        if (path == null || path == \"\") {\r\n            return Promise.resolve(null);\r\n        }\r\n        \r\n        // @ts-ignore\r\n        const uuid = await Editor.Message.request('asset-db', 'query-uuid', path);\r\n        return new Promise<T | null>((resolve) => {\r\n            assetManager.loadAny<T>(uuid, (err, asset: T) => {\r\n                if (err) {\r\n                    console.warn(`Failed to load by path: ${path}`, err);\r\n                    resolve(null);\r\n                    return;\r\n                }\r\n                resolve(asset);\r\n            });\r\n        });\r\n    }\r\n}\r\n\r\n"]}