System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, isValid, Material, size, sp, v2, TrackComponent, GameEnum, Tools, GameIns, TrackGroup, FBoxCollider, ColliderGroupType, EnemyPlaneBase, MyApp, _dec, _dec2, _class, _class2, _descriptor, _crd, ccclass, property, RAND_POINT, BossPlane;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfTrackComponent(extras) {
    _reporterNs.report("TrackComponent", "../../base/TrackComponent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameEnum(extras) {
    _reporterNs.report("GameEnum", "../../../const/GameEnum", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTools(extras) {
    _reporterNs.report("Tools", "../../../utils/Tools", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTrackGroup(extras) {
    _reporterNs.report("TrackGroup", "../../../data/EnemyWave", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFBoxCollider(extras) {
    _reporterNs.report("FBoxCollider", "../../../collider-system/FBoxCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfColliderGroupType(extras) {
    _reporterNs.report("ColliderGroupType", "../../../collider-system/FCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyPlaneBase(extras) {
    _reporterNs.report("EnemyPlaneBase", "../enemy/EnemyPlaneBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyData(extras) {
    _reporterNs.report("EnemyData", "../../../data/EnemyData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "../../../../app/MyApp", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      isValid = _cc.isValid;
      Material = _cc.Material;
      size = _cc.size;
      sp = _cc.sp;
      v2 = _cc.v2;
    }, function (_unresolved_2) {
      TrackComponent = _unresolved_2.default;
    }, function (_unresolved_3) {
      GameEnum = _unresolved_3.GameEnum;
    }, function (_unresolved_4) {
      Tools = _unresolved_4.Tools;
    }, function (_unresolved_5) {
      GameIns = _unresolved_5.GameIns;
    }, function (_unresolved_6) {
      TrackGroup = _unresolved_6.TrackGroup;
    }, function (_unresolved_7) {
      FBoxCollider = _unresolved_7.default;
    }, function (_unresolved_8) {
      ColliderGroupType = _unresolved_8.ColliderGroupType;
    }, function (_unresolved_9) {
      EnemyPlaneBase = _unresolved_9.default;
    }, function (_unresolved_10) {
      MyApp = _unresolved_10.MyApp;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "b08277TuKlEIY3rWaTkBUdv", "BossPlane", undefined);

      __checkObsolete__(['_decorator', 'isValid', 'Material', 'size', 'sp', 'v2']);

      ({
        ccclass,
        property
      } = _decorator);
      RAND_POINT = [v2(-100, -320), v2(-100, -250), v2(100, -320), v2(100, -250)];

      _export("default", BossPlane = (_dec = ccclass("BossPlane"), _dec2 = property(sp.Skeleton), _dec(_class = (_class2 = class BossPlane extends (_crd && EnemyPlaneBase === void 0 ? (_reportPossibleCrUseOfEnemyPlaneBase({
        error: Error()
      }), EnemyPlaneBase) : EnemyPlaneBase) {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "spinePlane", _descriptor, this);

          this._idleName = "idle1";
          this._formIndex = -1;
          //形态索引
          //随机移动参数
          this._posX = 0;
          this._posY = 0;
          this._moveToX = 0;
          this._moveToY = 0;
          this._moveSpeed = 0;
          this._bArriveDes = false;
          //是否达到目标点
          // //下一个航点
          this._nextWayPointTime = 0;
          this._nextWayPointX = 0;
          this._nextWayPointY = 0;
          this._nextWayPointInterval = 0;
          this._nextWaySpeed = 0;
          this._action = -1;
          this._bFirstWayPoint = false;
          this.tip = "";
          //boss警告提示文本
          this._hurtActDuration = 0.2;
        }

        // 受伤动画持续时间
        onLoad() {
          this._trackCom = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).addScript(this.node, _crd && TrackComponent === void 0 ? (_reportPossibleCrUseOfTrackComponent({
            error: Error()
          }), TrackComponent) : TrackComponent);
        }

        update(dt) {
          dt = dt * (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.animSpeed;

          if (this._hurtActDuration > 0) {
            this._hurtActDuration -= dt;

            if (this._hurtActDuration <= 0) {
              this.resetRoleEffect();
            }
          }
        }

        initPlane(data) {
          super.initPlane(data);
          this._bFirstWayPoint = true;
          this.bDamageable = false;

          this._initCollide();

          this._initTrack();

          this.setFormIndex(0);
        }

        _initTrack() {
          this._trackCom.setTrackGroupOverCall(() => {
            if (this._action === (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).BossAction.Appear) {
              this._trackCom.setTrackAble(false);

              this.setAction((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                error: Error()
              }), GameEnum) : GameEnum).BossAction.Transform);
            }
          });
        }

        _initCollide() {
          this.collideComp = this.getComponent(_crd && FBoxCollider === void 0 ? (_reportPossibleCrUseOfFBoxCollider({
            error: Error()
          }), FBoxCollider) : FBoxCollider) || this.addComponent(_crd && FBoxCollider === void 0 ? (_reportPossibleCrUseOfFBoxCollider({
            error: Error()
          }), FBoxCollider) : FBoxCollider);
          this.collideComp.init(this, size(250, 250)); // 初始化碰撞组件

          this.collideComp.groupType = (_crd && ColliderGroupType === void 0 ? (_reportPossibleCrUseOfColliderGroupType({
            error: Error()
          }), ColliderGroupType) : ColliderGroupType).ENEMY_NORMAL;
          this.colliderEnabled = false;
        }
        /**
        * 设置形态索引（一般boss会有好几段变形，要等策划设计）
        * @param index 形态索引
        */


        setFormIndex(index) {
          this._formIndex = index;
          this._idleName = `idle1`;
          this.setAction((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).BossAction.Appear);
        }

        setAction(action) {
          if (this._action !== action) {
            this._action = action;
            let BossAction = (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).BossAction;

            switch (this._action) {
              case BossAction.Normal:
                this._playSkel(this._idleName, true, () => {});

                this.bDamageable = true;
                break;

              case BossAction.Appear:
                this._playSkel(`enter${this._formIndex + 1}`, true, () => {});

                this._startAppearTrack();

                break;

              case BossAction.Transform:
                this._playSkel(`ready${this._formIndex + 1}`, false, () => {
                  this.transformEnd();
                });

                break;

              case BossAction.AttackPrepare:
                this.scheduleOnce(() => {
                  this.setAction(BossAction.AttackIng);
                });
                break;

              case BossAction.AttackIng:
              case BossAction.AttackOver:
                break;

              case BossAction.Blast:
                break;

              default:
            }
          }
        }

        _playSkel(animName, loop, callback) {
          this.spinePlane.setCompleteListener(() => {
            callback == null || callback();
          });
          this.spinePlane.setAnimation(0, animName, loop);
        }
        /**
         * 设置提示信息
         * @param tip 提示信息
         */


        setTip(tip) {
          this.tip = tip;
        }
        /**
        * 变形结束
        */


        transformEnd() {
          if (this.tip !== "") {
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.bossChangeFinish(this.tip);
          } else {
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.bossFightStart();
          }
        }
        /**
         * 开始战斗
         */


        startBattle() {
          this._startNormalTrack();

          this.setAction((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).BossAction.Normal);
          this.colliderEnabled = true;
        }
        /**
         * 更新游戏逻辑
         * @param deltaTime 每帧时间
         */


        updateGameLogic(deltaTime) {
          if (!this.isDead) {
            this.updateAction(deltaTime);
          }
        }

        updateAction(deltaTime) {
          let BossAction = (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).BossAction;

          switch (this._action) {
            case BossAction.Normal:
              this._processNextWayPoint(deltaTime);

              this._updateMove(deltaTime);

              this._processNextAttack(deltaTime);

              break;

            case BossAction.Appear:
              this._updateMove(deltaTime);

              if (this._bArriveDes) {
                this.setAction(BossAction.Transform);
              }

              break;

            case BossAction.Transform:
              break;

            case BossAction.AttackPrepare:
              this._processNextWayPoint(deltaTime);

              break;

            case BossAction.AttackIng:
              this._processNextWayPoint(deltaTime);

              this._udpateShoot(deltaTime);

              break;

            case BossAction.AttackOver:
              this._processNextWayPoint(deltaTime);

              this.setAction(BossAction.Normal);
              break;

            case BossAction.Blast:
              break;
          }
        }
        /**
         * 开始出现轨迹
         */


        _startAppearTrack() {
          // 0,150,16,150
          //出现的轨迹，暂时写死，等波次管理器做完，再接入新的轨迹系统
          const trackGroup = new (_crd && TrackGroup === void 0 ? (_reportPossibleCrUseOfTrackGroup({
            error: Error()
          }), TrackGroup) : TrackGroup)();
          trackGroup.loopNum = 1;
          trackGroup.trackIDs = [16];
          trackGroup.speeds = [150];
          trackGroup.trackIntervals = [0];

          this._trackCom.init(this, [trackGroup], [], v2(0, 150));

          this._trackCom.setTrackAble(true);

          this._trackCom.startTrack();
        }
        /**
         * 开始正常轨迹,boss随机移动位置
         */


        _startNormalTrack() {
          this._trackCom.setTrackAble(false);

          this.setAction((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).BossAction.Normal);
        }
        /**
         * 移动到指定位置
         * @param x X 坐标
         * @param y Y 坐标
         * @param speed 移动速度
         * @param transformMove 是否为变形移动
         */


        moveToPos(x, y, speed) {
          this._moveToX = x;
          this._moveToY = y;
          this._moveSpeed = speed;
          this._bArriveDes = false;
        }

        setPos(x, y, update = true) {
          this.node.setPosition(x, y);
          this._posX = x;
          this._posY = y;
        }
        /**
         * 处理下一个路径点
         * @param deltaTime 每帧时间
         */


        _processNextWayPoint(deltaTime) {
          if (this._bArriveDes) {
            this._nextWayPointTime += deltaTime;

            if (this._nextWayPointTime > this._nextWayPointInterval) {
              this._nextWayPointInterval = 0.15;
              this._nextWayPointTime = 0;

              if (this._bFirstWayPoint) {
                this._bFirstWayPoint = false;
              } else {
                const index = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                  error: Error()
                }), Tools) : Tools).random_int(0, RAND_POINT.length - 1);
                this._nextWayPointX = RAND_POINT[index].x;
                this._nextWayPointY = RAND_POINT[index].y;
                this._nextWaySpeed = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                  error: Error()
                }), Tools) : Tools).getRandomInArray([2, 2.2]);
                this.moveToPos(this._nextWayPointX, this._nextWayPointY, this._nextWaySpeed);
              }
            }
          }
        }

        _updateMove(deltaTime) {
          if (this._action === (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).BossAction.Appear) {
            // 如果 Boss 在出现阶段或有轨迹组，则更新轨迹逻辑
            this._trackCom.updateGameLogic(deltaTime);
          } else if (!this._bArriveDes) {
            const deltaX = this._moveToX - this._posX;
            const deltaY = this._moveToY - this._posY;
            const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
            let moveX = 0;
            let moveY = 0; // 如果距离小于等于移动速度，则直接到达目标点

            if (distance <= this._moveSpeed) {
              moveX = deltaX;
              moveY = deltaY;
            } // 否则按比例移动
            else {
              moveX = this._moveSpeed * deltaX / distance;
              moveY = this._moveSpeed * deltaY / distance;
            } // 更新位置


            this._posX += moveX;
            this._posY += moveY;
            this.setPos(this._posX, this._posY); // 检查是否到达目的地（当移动量很小时认为已到达）

            this._bArriveDes = Math.abs(moveX) < 0.5 && Math.abs(moveY) < 0.5;
          }
        }
        /**
         * 处理下一次攻击
         * @param deltaTime 每帧时间
         */


        _processNextAttack(deltaTime) {// if (this._shootAble && this._action === GameEnum.BossAction.Normal) {
          //     this._nextAttackTime += deltaTime;
          //     if (this._nextAttackTime > this._nextAttackInterval) {
          //         this._nextAttackInterval = Tools.getRandomInArray(this._data!.attackIntervals)!;
          //         this._nextAttackTime = 0;
          //         let attackAction = null;
          //         if (this._bOrderAttack) {
          //             const randomIndex = Tools.getRandomInArray(this._orderAtkArr)!;
          //             Tools.arrRemove(this._orderAtkArr, randomIndex);
          //             attackAction = this._atkActions[randomIndex];
          //             this._orderIndex++;
          //             if (this._orderIndex > this._atkActions.length - 1) {
          //                 this._bOrderAttack = false;
          //             }
          //         } else {
          //             attackAction = Tools.getRandomInArray(this._atkActions);
          //         }
          //         if (attackAction) {
          //             this._bAttackMove = attackAction.bAtkMove;
          //             this._attackID = attackAction.atkActId;
          //             this._attackPoints.splice(0);
          //             for (const pointId of attackAction.atkPointId) {
          //                 const pointData = this._atkPointDatas[pointId];
          //                 if (pointData[0]) {
          //                     let attackPoint = this._atkPointsPool[pointId]
          //                     if (!attackPoint) {
          //                         const pointNode = new Node();
          //                         this.node.addChild(pointNode);
          //                         attackPoint = pointNode.addComponent(AttackPoint);
          //                         this._atkPointsPool.push(attackPoint);
          //                     }
          //                     attackPoint.initForBoss(pointData[1], this);
          //                     this._attackPoints.push(attackPoint);
          //                 }
          //             }
          //             if (this._attackPoints.length > 0) {
          //                 this.setAction(GameEnum.BossAction.AttackPrepare);
          //             }
          //         }
          //     }
          // }
        }
        /**
         * 更新射击逻辑
         * @param deltaTime 每帧时间
         */


        async _udpateShoot(deltaTime) {// if (this._shootAble) {
          //     let allAttacksOver = true;
          //     for (const attackPoint of this._attackPoints) {
          //         await attackPoint.updateGameLogic(deltaTime);
          //         if (!attackPoint.isAttackOver()) {
          //             allAttacksOver = false;
          //         }
          //     }
          //     if (allAttacksOver) {
          //         this.setAction(GameEnum.BossAction.AttackOver);
          //     }
          // }
        } //由于boss资源不是新的，暂时把闪白效果，写在这里


        playHurtAnim() {
          this.playFlashAnim();
        } // 播放闪白动画


        async playFlashAnim() {
          let material = await (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.loadAsync("effect/flash/flash", Material);

          if (material && isValid(this.spinePlane)) {
            this.spinePlane.customMaterial = material;
          }

          this._hurtActDuration = 0.2;
        }

        resetRoleEffect() {
          this._hurtActDuration = 0;
          this.spinePlane.customMaterial = null;
        }

        setAnimSpeed(speed) {
          if (this.spinePlane && isValid(this.spinePlane)) {
            this.spinePlane.timeScale = speed;
          }
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "spinePlane", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=92be7c6d1d73422263a08ecc828e5842c8e4c823.js.map