{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/EnemyManager.ts"], "names": ["EnemyManager", "Prefab", "instantiate", "MyApp", "SingletonBase", "GameEnum", "GameResourceList", "EnemyData", "TrackData", "GameIns", "BattleLayer", "EnemyPlane", "Tools", "enemies", "_planeArr", "constructor", "_normalCount", "_trackDatas", "Map", "_pfPlane", "_planePool", "_willDeadPlane", "initConfig", "tracks", "lubanTables", "TbResTrack", "getDataList", "track", "trackData", "loadJson", "set", "trackID", "preLoad", "battleManager", "addLoadCount", "resMgr", "load", "error", "prefab", "checkLoadFinish", "addPlane", "id", "planeData", "planeId", "node", "pop", "createNewPlane", "me", "addEnemy", "plane", "getComponent", "initPlane", "pushPlane", "Error", "planes", "removeAllAlivePlane", "isDead", "will<PERSON><PERSON><PERSON>", "arrC<PERSON>ain", "push", "updateGameLogic", "deltaTime", "i", "length", "removeAble", "removePlaneForIndex", "type", "EnemyType", "<PERSON><PERSON><PERSON>", "Ship", "splice", "mainReset", "subReset", "destroy", "Normal", "removeFromParent", "clear", "isEnemyOver", "getNormalPlaneCount", "getTrackDataForID", "trackId", "get", "index", "_willRemovePlane", "setAnimSpeed", "speed"], "mappings": ";;;qNAaaA,Y;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAbEC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;;AACdC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,a,iBAAAA,a;;AACAC,MAAAA,Q,iBAAAA,Q;;AACFC,MAAAA,gB;;AACEC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,O,iBAAAA,O;;AACFC,MAAAA,W;;AACAC,MAAAA,U;;AACEC,MAAAA,K,kBAAAA,K;;;;;;;;;8BAGIZ,Y,GAAN,MAAMA,YAAN;AAAA;AAAA,0CAAuD;AAQ/C,YAAPa,OAAO,GAAG;AACV,iBAAO,KAAKC,SAAZ;AACH;;AAEDC,QAAAA,WAAW,GAAG;AACV;AADU,eAXdC,YAWc,GAXC,CAWD;AAAA,eAVdC,WAUc,GAVwB,IAAIC,GAAJ,EAUxB;AAAA,eATdC,QASc,GATY,IASZ;AAAA,eARdC,UAQc,GARO,EAQP;AAAA,eAPdN,SAOc,GAPY,EAOZ;AAAA,eANdO,cAMc,GANiB,EAMjB;AAEV,eAAKC,UAAL;AACH;;AAEDA,QAAAA,UAAU,GAAG;AACT,cAAIC,MAAM,GAAG;AAAA;AAAA,8BAAMC,WAAN,CAAkBC,UAAlB,CAA6BC,WAA7B,EAAb;;AACA,eAAK,IAAIC,KAAT,IAAkBJ,MAAlB,EAA0B;AACtB,kBAAMK,SAAS,GAAG;AAAA;AAAA,yCAAlB;AACAA,YAAAA,SAAS,CAACC,QAAV,CAAmBF,KAAnB;;AACA,iBAAKV,WAAL,CAAiBa,GAAjB,CAAqBF,SAAS,CAACG,OAA/B,EAAwCH,SAAxC;AACH;AACJ;AAID;AACJ;AACA;AACA;;;AACWI,QAAAA,OAAO,GAAG;AACb;AAAA;AAAA,kCAAQC,aAAR,CAAsBC,YAAtB,CAAmC,CAAnC;AACA;AAAA;AAAA,8BAAMC,MAAN,CAAaC,IAAb,CAAkB;AAAA;AAAA,oDAAiBzB,UAAnC,EAA+CV,MAA/C,EAAuD,CAACoC,KAAD,EAAaC,MAAb,KAAgC;AACnF,iBAAKnB,QAAL,GAAgBmB,MAAhB;AACA;AAAA;AAAA,oCAAQL,aAAR,CAAsBM,eAAtB;AACH,WAHD;AAIH;;AAGMC,QAAAA,QAAQ,CAACC,EAAD,EAAab,SAAb,EAA6B;AACxC,cAAI;AACA,kBAAMc,SAAS,GAAG;AAAA;AAAA,yCAAlB;AACAA,YAAAA,SAAS,CAACC,OAAV,GAAoBF,EAApB;AAEA,gBAAIG,IAAI,GAAG,KAAKxB,UAAL,CAAgByB,GAAhB,MAA0B,KAAKC,cAAL,EAArC;AACA;AAAA;AAAA,4CAAYC,EAAZ,CAAeC,QAAf,CAAwBJ,IAAxB;AAEA,kBAAMK,KAAK,GAAGL,IAAI,CAACM,YAAL;AAAA;AAAA,yCAAd;AACAD,YAAAA,KAAK,CAAEE,SAAP,CAAiBT,SAAjB,EAA4Bd,SAA5B;AACA,iBAAKwB,SAAL,CAAeH,KAAf;AAEA,iBAAKjC,YAAL;AACA,mBAAOiC,KAAP;AACH,WAbD,CAaE,OAAOZ,KAAP,EAAc;AACZ,mBAAO,IAAP;AACH;AACJ;;AAEDS,QAAAA,cAAc,GAAS;AACnB,cAAI,CAAC,KAAK3B,QAAV,EAAoB;AAChB,kBAAM,IAAIkC,KAAJ,CAAU,wDAAV,CAAN;AACH;;AACD,gBAAMT,IAAU,GAAG1C,WAAW,CAAC,KAAKiB,QAAN,CAA9B;AACA,iBAAOyB,IAAP;AACH;AAED;AACJ;AACA;;;AACc,YAANU,MAAM,GAAiB;AACvB,iBAAO,KAAKxC,SAAZ;AACH;AAED;AACJ;AACA;;;AACIyC,QAAAA,mBAAmB,GAAG;AAClB,eAAK,MAAMN,KAAX,IAAoB,KAAKnC,SAAzB,EAAoC;AAChC,gBAAI,CAACmC,KAAK,CAACO,MAAX,EAAmB;AACfP,cAAAA,KAAK,CAACQ,UAAN;AACH;AACJ;AACJ;AAED;AACJ;AACA;AACA;;;AACIL,QAAAA,SAAS,CAACH,KAAD,EAAoB;AACzB,cAAI,CAAC;AAAA;AAAA,8BAAMS,UAAN,CAAiB,KAAK5C,SAAtB,EAAiCmC,KAAjC,CAAL,EAA8C;AAC1C,iBAAKnC,SAAL,CAAe6C,IAAf,CAAoBV,KAApB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIW,QAAAA,eAAe,CAACC,SAAD,EAAoB;AAC/B,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKhD,SAAL,CAAeiD,MAAnC,EAA2CD,CAAC,EAA5C,EAAgD;AAC5C,kBAAMb,KAAK,GAAG,KAAKnC,SAAL,CAAegD,CAAf,CAAd;;AACA,gBAAIb,KAAK,CAACe,UAAV,EAAsB;AAClB,mBAAKC,mBAAL,CAAyBH,CAAzB;AACAA,cAAAA,CAAC;AACJ,aAHD,MAGO;AACH,kBAAIb,KAAK,CAACO,MAAV,EAAkB;AACd,oBAAIP,KAAK,CAACiB,IAAN,KAAe;AAAA;AAAA,0CAASC,SAAT,CAAmBC,MAAlC,IAA4CnB,KAAK,CAACiB,IAAN,KAAe;AAAA;AAAA,0CAASC,SAAT,CAAmBE,IAAlF,EAAwF;AACpF,uBAAKhD,cAAL,CAAoBsC,IAApB,CAAyBV,KAAzB;;AACA,uBAAKnC,SAAL,CAAewD,MAAf,CAAsBR,CAAtB,EAAyB,CAAzB;;AACAA,kBAAAA,CAAC;AACD;AACH;AACJ;;AACDb,cAAAA,KAAK,CAACW,eAAN,CAAsBC,SAAtB;AACH;AACJ;;AAED,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKzC,cAAL,CAAoB0C,MAAxC,EAAgDD,CAAC,EAAjD,EAAqD;AACjD,kBAAMb,KAAK,GAAG,KAAK5B,cAAL,CAAoByC,CAApB,CAAd;;AACA,gBAAIb,KAAK,CAACe,UAAV,EAAsB;AAClB,mBAAKC,mBAAL,CAAyBH,CAAzB,EAA4B,IAA5B;AACAA,cAAAA,CAAC;AACJ,aAHD,MAGO;AACHb,cAAAA,KAAK,CAACW,eAAN,CAAsBC,SAAtB;AACH;AACJ;AACJ;AAGD;AACJ;AACA;;;AACIU,QAAAA,SAAS,GAAG;AACR,eAAKC,QAAL,GADQ,CAGR;AACA;AAEA;;AACA,eAAK,MAAMvB,KAAX,IAAoB,KAAK7B,UAAzB,EAAqC;AACjC6B,YAAAA,KAAK,CAACwB,OAAN;AACH;;AACD,eAAKrD,UAAL,CAAgBkD,MAAhB,CAAuB,CAAvB,EAVQ,CAaR;;;AACA,eAAK,MAAMrB,KAAX,IAAoB,KAAK5B,cAAzB,EAAyC;AACrC,gBAAI4B,KAAK,IAAIA,KAAK,CAACL,IAAnB,EAAyB;AACrBK,cAAAA,KAAK,CAACL,IAAN,CAAW6B,OAAX;AACH;AACJ;;AACD,eAAKpD,cAAL,GAAsB,EAAtB;AACH;AAED;AACJ;AACA;;;AACImD,QAAAA,QAAQ,GAAG;AACP,cAAIL,SAAS,GAAG;AAAA;AAAA,oCAASA,SAAzB;;AACA,eAAK,MAAMlB,KAAX,IAAoB,KAAKnC,SAAzB,EAAoC;AAChC,oBAAQmC,KAAK,CAACiB,IAAd;AACI,mBAAKC,SAAS,CAACO,MAAf;AACIzB,gBAAAA,KAAK,CAACQ,UAAN;;AACA,qBAAKrC,UAAL,CAAgBuC,IAAhB,CAAqBV,KAAK,CAACL,IAA3B;;AACA;AAJR;;AAMAK,YAAAA,KAAK,CAACL,IAAN,CAAW+B,gBAAX;AACH;;AACD,eAAK7D,SAAL,CAAewD,MAAf,CAAsB,CAAtB;AACH;AAGD;AACJ;AACA;;;AACIM,QAAAA,KAAK,GAAG,CAEP;AAED;AACJ;AACA;;;AACIC,QAAAA,WAAW,GAAY;AACnB,iBAAO,KAAK/D,SAAL,CAAeiD,MAAf,KAA0B,CAAjC;AACH;AAED;AACJ;AACA;;;AACIe,QAAAA,mBAAmB,GAAW;AAC1B,iBAAO,KAAK9D,YAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACI+D,QAAAA,iBAAiB,CAACC,OAAD,EAAoC;AACjD,cAAIpD,SAA2B,GAAG,IAAlC;;AACA,cAAI;AACAA,YAAAA,SAAS,GAAG,KAAKX,WAAL,CAAiBgE,GAAjB,CAAqBD,OAArB,KAAiC,IAA7C;AACH,WAFD,CAEE,OAAO3C,KAAP,EAAc;AACZ;AAAA;AAAA,gCAAMA,KAAN,CAAa,uBAAsB2C,OAAQ,EAA3C,EAA8C3C,KAA9C;AACH;;AACD,iBAAOT,SAAP;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIqC,QAAAA,mBAAmB,CAACiB,KAAD,EAAgB1B,MAAe,GAAG,KAAlC,EAAyC;AACxD,cAAIA,MAAJ,EAAY;AACR,iBAAK2B,gBAAL,CAAsB,KAAK9D,cAAL,CAAoB6D,KAApB,CAAtB;;AACA,iBAAK7D,cAAL,CAAoBiD,MAApB,CAA2BY,KAA3B,EAAkC,CAAlC;AACH,WAHD,MAGO;AACH,iBAAKC,gBAAL,CAAsB,KAAKrE,SAAL,CAAeoE,KAAf,CAAtB;;AACA,iBAAKpE,SAAL,CAAewD,MAAf,CAAsBY,KAAtB,EAA6B,CAA7B;AACH;AACJ;AAGD;AACJ;AACA;AACA;;;AACIC,QAAAA,gBAAgB,CAAClC,KAAD,EAAoB;AAChC,cAAIkB,SAAS,GAAG;AAAA;AAAA,oCAASA,SAAzB;;AACA,eAAK,MAAMlB,KAAX,IAAoB,KAAKnC,SAAzB,EAAoC;AAChC,oBAAQmC,KAAK,CAACiB,IAAd;AACI,mBAAKC,SAAS,CAACO,MAAf;AACI,qBAAK1D,YAAL;;AACA,qBAAKI,UAAL,CAAgBuC,IAAhB,CAAqBV,KAAK,CAACL,IAA3B;;AACA;AAJR;AAMH;;AACDK,UAAAA,KAAK,CAACL,IAAN,CAAW+B,gBAAX;AACH;;AAEDS,QAAAA,YAAY,CAACC,KAAD,EAAgB;AACxB,eAAK,MAAMpC,KAAX,IAAoB,KAAKnC,SAAzB,EAAoC;AAChCmC,YAAAA,KAAK,CAACmC,YAAN,CAAmBC,KAAnB;AACH;AACJ;;AAtPyD,O", "sourcesContent": ["import { Node, Prefab, instantiate } from \"cc\";\r\nimport { MyApp } from 'db://assets/bundles/common/script/app/MyApp';\r\nimport { SingletonBase } from \"../../../../../scripts/core/base/SingletonBase\";\r\nimport { GameEnum } from \"../const/GameEnum\";\r\nimport GameResourceList from \"../const/GameResourceList\";\r\nimport { EnemyData } from \"../data/EnemyData\";\r\nimport { TrackData } from \"../data/TrackData\";\r\nimport { GameIns } from \"../GameIns\";\r\nimport BattleLayer from \"../ui/layer/BattleLayer\";\r\nimport EnemyPlane from \"../ui/plane/enemy/EnemyPlane\";\r\nimport { Tools } from \"../utils/Tools\";\r\n\r\n\r\nexport class EnemyManager extends SingletonBase<EnemyManager> {\r\n    _normalCount = 0;\r\n    _trackDatas: Map<number, TrackData> = new Map();\r\n    _pfPlane: Prefab | null = null;\r\n    _planePool: Node[] = [];\r\n    _planeArr: EnemyPlane[] = [];\r\n    _willDeadPlane: EnemyPlane[] = [];\r\n\r\n    get enemies() {\r\n        return this._planeArr;\r\n    }\r\n\r\n    constructor() {\r\n        super();\r\n        this.initConfig();\r\n    }\r\n\r\n    initConfig() {\r\n        let tracks = MyApp.lubanTables.TbResTrack.getDataList();\r\n        for (let track of tracks) {\r\n            const trackData = new TrackData();\r\n            trackData.loadJson(track);\r\n            this._trackDatas.set(trackData.trackID, trackData);\r\n        }\r\n    }\r\n\r\n\r\n\r\n    /**\r\n     * 预加载资源\r\n     * @param stage 当前关卡\r\n     */\r\n    public preLoad() {\r\n        GameIns.battleManager.addLoadCount(1);\r\n        MyApp.resMgr.load(GameResourceList.EnemyPlane, Prefab, (error: any, prefab: Prefab) => {\r\n            this._pfPlane = prefab;\r\n            GameIns.battleManager.checkLoadFinish();\r\n        });\r\n    }\r\n\r\n\r\n    public addPlane(id: number, trackData: any) {\r\n        try {\r\n            const planeData = new EnemyData();\r\n            planeData.planeId = id;\r\n\r\n            let node = this._planePool.pop()! || this.createNewPlane()!;\r\n            BattleLayer.me.addEnemy(node);\r\n\r\n            const plane = node.getComponent(EnemyPlane);\r\n            plane!.initPlane(planeData, trackData);\r\n            this.pushPlane(plane!);\r\n\r\n            this._normalCount++;\r\n            return plane;\r\n        } catch (error) {\r\n            return null;\r\n        }\r\n    }\r\n\r\n    createNewPlane(): Node {\r\n        if (!this._pfPlane) {\r\n            throw new Error(\"Plane prefab is not initialized. Call preLoad() first.\");\r\n        }\r\n        const node: Node = instantiate(this._pfPlane);\r\n        return node;\r\n    }\r\n\r\n    /**\r\n     * 获取所有敌机\r\n     */\r\n    get planes(): EnemyPlane[] {\r\n        return this._planeArr;\r\n    }\r\n\r\n    /**\r\n     * 移除所有存活的敌机\r\n     */\r\n    removeAllAlivePlane() {\r\n        for (const plane of this._planeArr) {\r\n            if (!plane.isDead) {\r\n                plane.willRemove();\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 添加敌机到管理器\r\n     * @param plane 敌机对象\r\n     */\r\n    pushPlane(plane: EnemyPlane) {\r\n        if (!Tools.arrContain(this._planeArr, plane)) {\r\n            this._planeArr.push(plane);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 更新游戏逻辑\r\n     * @param deltaTime 每帧时间\r\n     */\r\n    updateGameLogic(deltaTime: number) {\r\n        for (let i = 0; i < this._planeArr.length; i++) {\r\n            const plane = this._planeArr[i];\r\n            if (plane.removeAble) {\r\n                this.removePlaneForIndex(i);\r\n                i--;\r\n            } else {\r\n                if (plane.isDead) {\r\n                    if (plane.type === GameEnum.EnemyType.Turret || plane.type === GameEnum.EnemyType.Ship) {\r\n                        this._willDeadPlane.push(plane);\r\n                        this._planeArr.splice(i, 1);\r\n                        i--;\r\n                        continue;\r\n                    }\r\n                }\r\n                plane.updateGameLogic(deltaTime);\r\n            }\r\n        }\r\n\r\n        for (let i = 0; i < this._willDeadPlane.length; i++) {\r\n            const plane = this._willDeadPlane[i];\r\n            if (plane.removeAble) {\r\n                this.removePlaneForIndex(i, true);\r\n                i--;\r\n            } else {\r\n                plane.updateGameLogic(deltaTime);\r\n            }\r\n        }\r\n    }\r\n\r\n\r\n    /**\r\n* 重置主关卡\r\n*/\r\n    mainReset() {\r\n        this.subReset();\r\n\r\n        // this._mainStage = -1;\r\n        // this._subStage = -1;\r\n\r\n        // 清理飞机池\r\n        for (const plane of this._planePool) {\r\n            plane.destroy();\r\n        }\r\n        this._planePool.splice(0);\r\n\r\n\r\n        // 清理即将死亡的飞机\r\n        for (const plane of this._willDeadPlane) {\r\n            if (plane && plane.node) {\r\n                plane.node.destroy();\r\n            }\r\n        }\r\n        this._willDeadPlane = [];\r\n    }\r\n\r\n    /**\r\n     * 重置子关卡\r\n     */\r\n    subReset() {\r\n        let EnemyType = GameEnum.EnemyType;\r\n        for (const plane of this._planeArr) {\r\n            switch (plane.type) {\r\n                case EnemyType.Normal:\r\n                    plane.willRemove();\r\n                    this._planePool.push(plane.node);\r\n                    break;\r\n            }\r\n            plane.node.removeFromParent();\r\n        }\r\n        this._planeArr.splice(0);\r\n    }\r\n\r\n\r\n    /**\r\n     * 清理敌人管理器\r\n     */\r\n    clear() {\r\n\r\n    }\r\n\r\n    /**\r\n     * 检查敌人是否全部消灭\r\n     */\r\n    isEnemyOver(): boolean {\r\n        return this._planeArr.length === 0;\r\n    }\r\n\r\n    /**\r\n     * 获取普通敌机数量\r\n     */\r\n    getNormalPlaneCount(): number {\r\n        return this._normalCount;\r\n    }\r\n\r\n    /**\r\n     * 根据轨迹 ID 获取轨迹数据\r\n     * @param trackId 轨迹 ID\r\n     */\r\n    getTrackDataForID(trackId: number): TrackData | null {\r\n        let trackData: TrackData | null = null;\r\n        try {\r\n            trackData = this._trackDatas.get(trackId) || null;\r\n        } catch (error) {\r\n            Tools.error(`getTrackData error: ${trackId}`, error);\r\n        }\r\n        return trackData;\r\n    }\r\n\r\n    /**\r\n     * 根据索引移除敌机\r\n     * @param index 索引\r\n     * @param isDead 是否为死亡敌机\r\n     */\r\n    removePlaneForIndex(index: number, isDead: boolean = false) {\r\n        if (isDead) {\r\n            this._willRemovePlane(this._willDeadPlane[index]);\r\n            this._willDeadPlane.splice(index, 1);\r\n        } else {\r\n            this._willRemovePlane(this._planeArr[index]);\r\n            this._planeArr.splice(index, 1);\r\n        }\r\n    }\r\n\r\n\r\n    /**\r\n     * 处理即将移除的敌机\r\n     * @param plane 敌机对象\r\n     */\r\n    _willRemovePlane(plane: EnemyPlane) {\r\n        let EnemyType = GameEnum.EnemyType;\r\n        for (const plane of this._planeArr) {\r\n            switch (plane.type) {\r\n                case EnemyType.Normal:\r\n                    this._normalCount--;\r\n                    this._planePool.push(plane.node);\r\n                    break;\r\n            }\r\n        }\r\n        plane.node.removeFromParent();\r\n    }\r\n\r\n    setAnimSpeed(speed: number) {\r\n        for (const plane of this._planeArr) {\r\n            plane.setAnimSpeed(speed);\r\n        }\r\n    }\r\n}"]}