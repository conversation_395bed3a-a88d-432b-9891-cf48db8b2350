System.register(["cc", "cc/env"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, EDITOR, _dec, _dec2, _class, _crd, ccclass, property, executeInEditMode, EmittierTerrain;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
    }, function (_ccEnv) {
      EDITOR = _ccEnv.EDITOR;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "b1dcb5RE6ZN+7KA2ed/t53E", "EmittierTerrain", undefined);

      __checkObsolete__(['_decorator', 'assetManager', 'CCInteger', 'Component', 'instantiate', 'Prefab', 'v2', 'Vec2']);

      ({
        ccclass,
        property,
        executeInEditMode
      } = _decorator);

      _export("EmittierTerrain", EmittierTerrain = (_dec = ccclass('EmittierTerrain'), _dec2 = executeInEditMode(), _dec(_class = _dec2(_class = class EmittierTerrain extends Component {
        onLoad() {
          if (EDITOR) {
            this.node.removeAllChildren();

            this._loadElems();
          }
        }

        update() {}

        play(bPlay) {
          if (EDITOR) {
            if (bPlay) {}
          }
        }

        onDestroy() {
          this.node.removeAllChildren();
        }

        _loadElems() {}

      }) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=96c32fab17874662fe571d9d1f0c7869747cc6b0.js.map