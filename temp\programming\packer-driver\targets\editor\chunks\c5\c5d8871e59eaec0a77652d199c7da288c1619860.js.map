{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/BattleManager.ts"], "names": ["BattleManager", "Rect", "SingletonBase", "UIMgr", "LoadingUI", "BulletSystem", "GameEnum", "GameIns", "GameMapRun", "EventManager", "GameEvent", "gameType", "GameType", "Common", "initBattleEnd", "gameStart", "animSpeed", "_gameTime", "mainStage", "subStage", "_loadTotal", "_loadCount", "setBattleInfo", "planeData", "mainPlaneManager", "setPlaneData", "mainReset", "enemyManager", "boss<PERSON><PERSON><PERSON>", "waveManager", "reset", "instance", "clear", "hurtEffectManager", "gameRuleManager", "subReset", "checkLoadFinish", "loadingUI", "get", "updateProgress", "closeUI", "initBattle", "addLoadCount", "count", "startLoading", "gameSortie", "preload", "preLoad", "initData", "stageManager", "mainPlane", "planeIn", "init", "onPlaneIn", "beginBattle", "Instance", "emit", "GameStart", "begine", "update", "dt", "isGameOver", "gamePlaneManager", "enemyTarget", "isInBattle", "isGameWillOver", "updateGameLogic", "tick", "setTouchState", "is<PERSON><PERSON>ch", "setAnimSpeed", "battleDie", "gamePause", "battleFail", "gameMainUI", "showGameResult", "hpNode", "active", "endBattle", "battleSucc", "checkStage", "startNextBattle", "destroy", "gameOver", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "bossWillEnter", "setFireEnable", "moveAble", "bossFightStart", "getRatio", "isGameType"], "mappings": ";;;4LAaaA,a;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAZMC,MAAAA,I,OAAAA,I;;AAEVC,MAAAA,a,iBAAAA,a;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,O,iBAAAA,O;;AACFC,MAAAA,U;;AACAC,MAAAA,Y;;AACEC,MAAAA,S,kBAAAA,S;;;;;;;;;+BAEIV,a,GAAN,MAAMA,aAAN;AAAA;AAAA,0CAAyD;AAAA;AAAA;AAAA,eAE5DW,QAF4D,GAEjD;AAAA;AAAA,oCAASC,QAAT,CAAkBC,MAF+B;AAAA,eAG5DC,aAH4D,GAG5C,KAH4C;AAAA,eAI5DC,SAJ4D,GAIhD,KAJgD;AAAA,eAK5DC,SAL4D,GAKhD,CALgD;AAAA,eAM5DC,SAN4D,GAMhD,CANgD;AAAA,eAQ5DC,SAR4D,GAQhD,CARgD;AAAA,eAS5DC,QAT4D,GASjD,CATiD;AAAA,eAW5DC,UAX4D,GAW/C,CAX+C;AAAA,eAY5DC,UAZ4D,GAY/C,CAZ+C;AAAA;;AAc5DC,QAAAA,aAAa,CAACJ,SAAD,EAAoBC,QAApB,EAAsCI,SAAtC,EAA4D;AACrE,eAAKL,SAAL,GAAiBA,SAAjB;AACA,eAAKC,QAAL,GAAgBA,QAAhB;AACA;AAAA;AAAA,kCAAQK,gBAAR,CAAyBC,YAAzB,CAAsCF,SAAtC;AACH;;AAEDG,QAAAA,SAAS,GAAG;AACR;AAAA;AAAA,kCAAQC,YAAR,CAAqBD,SAArB;AACA;AAAA;AAAA,kCAAQE,WAAR,CAAoBF,SAApB;AACA;AAAA;AAAA,kCAAQG,WAAR,CAAoBC,KAApB;AACA;AAAA;AAAA,kCAAQN,gBAAR,CAAyBE,SAAzB;AACA;AAAA;AAAA,wCAAWK,QAAX,CAAqBD,KAArB;AACA;AAAA;AAAA,wCAAWC,QAAX,CAAqBC,KAArB;AACA;AAAA;AAAA,kCAAQC,iBAAR,CAA0BD,KAA1B;AACA;AAAA;AAAA,kCAAQE,eAAR,CAAwBJ,KAAxB;AACH;;AAEDK,QAAAA,QAAQ,GAAG;AACP;AAAA;AAAA,kCAAQD,eAAR,CAAwBJ,KAAxB;AACA;AAAA;AAAA,kCAAQD,WAAR,CAAoBC,KAApB;AACA;AAAA;AAAA,kCAAQH,YAAR,CAAqBQ,QAArB;AACA;AAAA;AAAA,kCAAQP,WAAR,CAAoBO,QAApB;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,eAAe,GAAG;AACd,eAAKf,UAAL;AACA,cAAIgB,SAAS,GAAG;AAAA;AAAA,8BAAMC,GAAN;AAAA;AAAA,qCAAhB;AACAD,UAAAA,SAAS,CAACE,cAAV,CAAyB,KAAKlB,UAAL,GAAkB,KAAKD,UAAhD;;AACA,cAAI,KAAKC,UAAL,IAAmB,KAAKD,UAA5B,EAAwC;AACpC;AAAA;AAAA,gCAAMoB,OAAN;AAAA;AAAA;AACA,iBAAKC,UAAL;AACH;AAEJ;;AAEDC,QAAAA,YAAY,CAACC,KAAD,EAAgB;AACxB,eAAKvB,UAAL,IAAmBuB,KAAnB;AACH;;AAEDC,QAAAA,YAAY,GAAG;AACX;AAAA;AAAA,kCAAQV,eAAR,CAAwBW,UAAxB;AACA;AAAA;AAAA,kCAAQrB,gBAAR,CAAyBsB,OAAzB;AACA;AAAA;AAAA,kCAAQb,iBAAR,CAA0Bc,OAA1B,GAHW,CAGyB;;AACpC;AAAA;AAAA,wCAAWhB,QAAX,CAAqBiB,QAArB,CAA8B,KAAK9B,SAAnC,EAJW,CAImC;;AAC9C;AAAA;AAAA,kCAAQS,YAAR,CAAqBoB,OAArB,GALW,CAKoB;;AAC/B;AAAA;AAAA,kCAAQnB,WAAR,CAAoBmB,OAApB,GANW,CAMmB;AACjC;;AAIDN,QAAAA,UAAU,GAAG;AACT;AAAA;AAAA,kCAAQQ,YAAR,CAAqBR,UAArB,CAAgC,KAAKvB,SAArC,EAAgD,KAAKC,QAArD;AACA;AAAA;AAAA,kCAAQK,gBAAR,CAAyB0B,SAAzB,CAAoCT,UAApC;AACA;AAAA;AAAA,kCAAQjB,gBAAR,CAAyB0B,SAAzB,CAAoCC,OAApC;AACA;AAAA;AAAA,4CAAaC,IAAb,CAAkB,IAAInD,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,GAAf,EAAoB,IAApB,CAAlB;AACH;;AAEDoD,QAAAA,SAAS,GAAG;AACR,eAAKvC,aAAL,GAAqB,IAArB;AACH;;AAEDwC,QAAAA,WAAW,GAAG;AACV,cAAI,KAAKxC,aAAL,IAAsB,CAAC,KAAKC,SAAhC,EAA2C;AACvC,iBAAKA,SAAL,GAAiB,IAAjB;AACA;AAAA;AAAA,8CAAawC,QAAb,CAAsBC,IAAtB,CAA2B;AAAA;AAAA,wCAAUC,SAArC;AACA;AAAA;AAAA,oCAAQR,YAAR,CAAqBlC,SAArB;AACA;AAAA;AAAA,oCAAQc,WAAR,CAAoBd,SAApB;AACA;AAAA;AAAA,oCAAQmB,eAAR,CAAwBnB,SAAxB;AAEA;AAAA;AAAA,oCAAQS,gBAAR,CAAyB0B,SAAzB,CAAoCQ,MAApC,CAA2C,IAA3C;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,MAAM,CAACC,EAAD,EAAa;AACfA,UAAAA,EAAE,GAAGA,EAAE,GAAG,KAAK5C,SAAf;;AACA,cAAI;AAAA;AAAA,kCAAQkB,eAAR,CAAwB2B,UAAxB,EAAJ,EAA0C;AACtC,gBAAI;AAAA;AAAA,oCAAQC,gBAAZ,EAA8B;AAC1B;AAAA;AAAA,sCAAQA,gBAAR,CAAyBC,WAAzB,GAAuC,IAAvC;AACH;;AACD;AACH;;AAED,cAAI;AAAA;AAAA,kCAAQ7B,eAAR,CAAwB8B,UAAxB,MAAwC;AAAA;AAAA,kCAAQ9B,eAAR,CAAwB+B,cAAxB,EAA5C,EAAsF;AAClF;AAAA;AAAA,oCAAQH,gBAAR,CAAyBH,MAAzB,CAAgCC,EAAhC;AACA;AAAA;AAAA,oCAAQ/B,WAAR,CAAoBqC,eAApB,CAAoCN,EAApC;AACA;AAAA;AAAA,oCAAQjC,YAAR,CAAqBuC,eAArB,CAAqCN,EAArC;AACA;AAAA;AAAA,oCAAQhC,WAAR,CAAoBsC,eAApB,CAAoCN,EAApC;AAEA;AAAA;AAAA,oCAAQ1B,eAAR,CAAwBgC,eAAxB,CAAwCN,EAAxC,EANkF,CAQlF;;AACA;AAAA;AAAA,8CAAaO,IAAb,CAAkBP,EAAlB;AAEA,iBAAK3C,SAAL,IAAkB2C,EAAlB;AACH,WAZD,MAYO,IAAI;AAAA;AAAA,kCAAQE,gBAAZ,EAA8B;AACjC;AAAA;AAAA,oCAAQA,gBAAR,CAAyBC,WAAzB,GAAuC,IAAvC;AACH;AACJ;;AAEDK,QAAAA,aAAa,CAACC,OAAD,EAAmB;AAC5B,cAAIA,OAAJ,EAAY;AACR,iBAAKf,WAAL,GADQ,CAER;;AACA,iBAAKtC,SAAL,GAAiB,CAAjB;AACH,WAJD,MAIK;AACD;AACA,iBAAKA,SAAL,GAAiB,GAAjB;AACH;;AACD;AAAA;AAAA,kCAAQW,YAAR,CAAqB2C,YAArB,CAAkC,KAAKtD,SAAvC;AACA;AAAA;AAAA,kCAAQY,WAAR,CAAoB0C,YAApB,CAAiC,KAAKtD,SAAtC;AACA;AAAA;AAAA,kCAAQQ,gBAAR,CAAyB0B,SAAzB,CAAoCoB,YAApC,CAAiD,KAAKtD,SAAtD;AACH;AAED;AACJ;AACA;;;AACmB,cAATuD,SAAS,GAAG;AACd;AAAA;AAAA,kCAAQrC,eAAR,CAAwBsC,SAAxB;AACH,SA3I2D,CA6I5D;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACJ;AACA;;;AACIC,QAAAA,UAAU,GAAG;AACT;AAAA;AAAA,kCAAQC,UAAR,CAAoBC,cAApB,CAAmC,KAAnC;AACA;AAAA;AAAA,kCAAQnD,gBAAR,CAAyB0B,SAAzB,CAAoC0B,MAApC,CAA4CC,MAA5C,GAAqD,KAArD;AACA,eAAKC,SAAL;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,UAAU,GAAG;AACT;AAAA;AAAA,kCAAQvD,gBAAR,CAAyB0B,SAAzB,CAAoC0B,MAApC,CAA4CC,MAA5C,GAAqD,KAArD;AACA,eAAKC,SAAL;;AAEA,cAAI;AAAA;AAAA,kCAAQ7B,YAAR,CAAqB+B,UAArB,CAAgC,KAAK9D,SAArC,EAAgD,KAAKC,QAAL,GAAgB,CAAhE,CAAJ,EAAwE;AACpE,iBAAK8D,eAAL;AACH,WAFD,MAEO;AACH;AAAA;AAAA,oCAAQP,UAAR,CAAoBC,cAApB,CAAmC,IAAnC;AACH;AACJ;AACD;AACJ;AACA;;;AACIM,QAAAA,eAAe,GAAG;AACd,eAAK9C,QAAL;AACA,eAAKhB,QAAL,IAAiB,CAAjB;AACA,eAAKsB,UAAL;AACH;AAED;AACJ;AACA;;;AACIqC,QAAAA,SAAS,GAAG;AACR;AAAA;AAAA,4CAAaI,OAAb;AACA;AAAA;AAAA,kCAAQhD,eAAR,CAAwBiD,QAAxB;AAEA,eAAKpE,SAAL,GAAiB,KAAjB;AACA,eAAKD,aAAL,GAAqB,KAArB;AACH;AAGD;AACJ;AACA;AACA;;;AACIsE,QAAAA,gBAAgB,CAACC,QAAD,EAAmB,CAC/B;AACA;AACA;AACA;AACA;AACA;AACH;;AAEDC,QAAAA,aAAa,GAAG;AACZ;AAAA;AAAA,kCAAQ9D,gBAAR,CAAyB0B,SAAzB,CAAoCqC,aAApC,CAAkD,KAAlD;AACA;AAAA;AAAA,kCAAQ/D,gBAAR,CAAyBgE,QAAzB,GAAoC,KAApC;AACH;AACD;AACJ;AACA;;;AACIC,QAAAA,cAAc,GAAG;AACb;AAAA;AAAA,kCAAQjE,gBAAR,CAAyB0B,SAAzB,CAAoCqC,aAApC,CAAkD,IAAlD;AACA;AAAA;AAAA,kCAAQ/D,gBAAR,CAAyBgE,QAAzB,GAAoC,IAApC;AAEA;AAAA;AAAA,kCAAQ5D,WAAR,CAAoB6D,cAApB;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,QAAQ,GAAG;AACP,iBAAO,QAAP,CADO,CACU;AACpB;;AAEDC,QAAAA,UAAU,CAAChF,QAAD,EAAmB;AACzB,iBAAO,KAAKA,QAAL,IAAiBA,QAAxB;AACH;;AArO2D,O", "sourcesContent": ["\r\nimport { director, Rect } from \"cc\";\r\nimport { PlaneData } from \"db://assets/bundles/common/script/data/plane/PlaneData\";\r\nimport { SingletonBase } from \"../../../../../scripts/core/base/SingletonBase\";\r\nimport { UIMgr } from \"../../../../../scripts/core/base/UIMgr\";\r\nimport { LoadingUI } from \"../../ui/gameui/LoadingUI\";\r\nimport { BulletSystem } from \"../bullet/BulletSystem\";\r\nimport { GameEnum } from \"../const/GameEnum\";\r\nimport { GameIns } from \"../GameIns\";\r\nimport GameMapRun from \"../ui/map/GameMapRun\";\r\nimport EventManager from \"../../event/EventManager\";\r\nimport { GameEvent } from \"../event/GameEvent\";\r\n\r\nexport class BattleManager extends SingletonBase<BattleManager> {\r\n\r\n    gameType = GameEnum.GameType.Common;\r\n    initBattleEnd = false;\r\n    gameStart = false;\r\n    animSpeed = 1;\r\n    _gameTime = 0;\r\n\r\n    mainStage = 0;\r\n    subStage = 0;\r\n\r\n    _loadTotal = 0;\r\n    _loadCount = 0;\r\n\r\n    setBattleInfo(mainStage: number, subStage: number, planeData: PlaneData) {\r\n        this.mainStage = mainStage;\r\n        this.subStage = subStage;\r\n        GameIns.mainPlaneManager.setPlaneData(planeData);\r\n    }\r\n\r\n    mainReset() {\r\n        GameIns.enemyManager.mainReset();\r\n        GameIns.bossManager.mainReset();\r\n        GameIns.waveManager.reset();\r\n        GameIns.mainPlaneManager.mainReset();\r\n        GameMapRun.instance!.reset();\r\n        GameMapRun.instance!.clear();\r\n        GameIns.hurtEffectManager.clear();\r\n        GameIns.gameRuleManager.reset();\r\n    }\r\n\r\n    subReset() {\r\n        GameIns.gameRuleManager.reset();\r\n        GameIns.waveManager.reset();\r\n        GameIns.enemyManager.subReset();\r\n        GameIns.bossManager.subReset();\r\n    }\r\n\r\n    /**\r\n     * 检查所有资源是否加载完成\r\n     */\r\n    checkLoadFinish() {\r\n        this._loadCount++;\r\n        let loadingUI = UIMgr.get(LoadingUI)\r\n        loadingUI.updateProgress(this._loadCount / this._loadTotal)\r\n        if (this._loadCount >= this._loadTotal) {\r\n            UIMgr.closeUI(LoadingUI)\r\n            this.initBattle();\r\n        }\r\n\r\n    }\r\n\r\n    addLoadCount(count: number) {\r\n        this._loadTotal += count;\r\n    }\r\n\r\n    startLoading() {\r\n        GameIns.gameRuleManager.gameSortie();\r\n        GameIns.mainPlaneManager.preload();\r\n        GameIns.hurtEffectManager.preLoad();//伤害特效资源\r\n        GameMapRun.instance!.initData(this.mainStage);//地图背景初始化\r\n        GameIns.enemyManager.preLoad();//敌人资源\r\n        GameIns.bossManager.preLoad();//boss资源\r\n    }\r\n\r\n\r\n\r\n    initBattle() {\r\n        GameIns.stageManager.initBattle(this.mainStage, this.subStage);\r\n        GameIns.mainPlaneManager.mainPlane!.initBattle();\r\n        GameIns.mainPlaneManager.mainPlane!.planeIn();\r\n        BulletSystem.init(new Rect(0, 0, 750, 1334));\r\n    }\r\n\r\n    onPlaneIn() {\r\n        this.initBattleEnd = true;\r\n    }\r\n\r\n    beginBattle() {\r\n        if (this.initBattleEnd && !this.gameStart) {\r\n            this.gameStart = true;\r\n            EventManager.Instance.emit(GameEvent.GameStart)\r\n            GameIns.stageManager.gameStart();\r\n            GameIns.waveManager.gameStart();\r\n            GameIns.gameRuleManager.gameStart();\r\n\r\n            GameIns.mainPlaneManager.mainPlane!.begine(true);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 更新游戏逻辑\r\n     * @param {number} dt 每帧的时间间隔\r\n     */\r\n    update(dt: number) {\r\n        dt = dt * this.animSpeed;\r\n        if (GameIns.gameRuleManager.isGameOver()) {\r\n            if (GameIns.gamePlaneManager) {\r\n                GameIns.gamePlaneManager.enemyTarget = null;\r\n            }\r\n            return;\r\n        }\r\n\r\n        if (GameIns.gameRuleManager.isInBattle() || GameIns.gameRuleManager.isGameWillOver()) {\r\n            GameIns.gamePlaneManager.update(dt);\r\n            GameIns.waveManager.updateGameLogic(dt);\r\n            GameIns.enemyManager.updateGameLogic(dt);\r\n            GameIns.bossManager.updateGameLogic(dt);\r\n\r\n            GameIns.gameRuleManager.updateGameLogic(dt);\r\n\r\n            //子弹发射器系统\r\n            BulletSystem.tick(dt);\r\n\r\n            this._gameTime += dt;\r\n        } else if (GameIns.gamePlaneManager) {\r\n            GameIns.gamePlaneManager.enemyTarget = null;\r\n        }\r\n    }\r\n\r\n    setTouchState(isTouch: boolean) {\r\n        if (isTouch){\r\n            this.beginBattle();\r\n            // director.getScheduler().setTimeScale(1);\r\n            this.animSpeed = 1;\r\n        }else{\r\n            // director.getScheduler().setTimeScale(0.3);\r\n            this.animSpeed = 0.2;\r\n        }\r\n        GameIns.enemyManager.setAnimSpeed(this.animSpeed);\r\n        GameIns.bossManager.setAnimSpeed(this.animSpeed);\r\n        GameIns.mainPlaneManager.mainPlane!.setAnimSpeed(this.animSpeed);\r\n    }\r\n\r\n    /**\r\n     * 战斗失败逻辑\r\n     */\r\n    async battleDie() {\r\n        GameIns.gameRuleManager.gamePause();\r\n    }\r\n\r\n    //     /**\r\n    //      * 战斗复活逻辑\r\n    //      */\r\n    //     relifeBattle() {\r\n    //         GameIns.eventManager.emit(GameEvent.MainRelife);\r\n    //         GameIns.gameRuleManager.gameResume();\r\n    //     }\r\n\r\n    /**\r\n     * 战斗失败结算\r\n     */\r\n    battleFail() {\r\n        GameIns.gameMainUI!.showGameResult(false);\r\n        GameIns.mainPlaneManager.mainPlane!.hpNode!.active = false;\r\n        this.endBattle();\r\n    }\r\n\r\n    /**\r\n     * 战斗胜利逻辑\r\n     */\r\n    battleSucc() {\r\n        GameIns.mainPlaneManager.mainPlane!.hpNode!.active = false;\r\n        this.endBattle();\r\n\r\n        if (GameIns.stageManager.checkStage(this.mainStage, this.subStage + 1)) {\r\n            this.startNextBattle();\r\n        } else {\r\n            GameIns.gameMainUI!.showGameResult(true);\r\n        }\r\n    }\r\n    /**\r\n * 继续下一场战斗\r\n */\r\n    startNextBattle() {\r\n        this.subReset();\r\n        this.subStage += 1;\r\n        this.initBattle();\r\n    }\r\n\r\n    /**\r\n     * 结束战斗\r\n     */\r\n    endBattle() {\r\n        BulletSystem.destroy();\r\n        GameIns.gameRuleManager.gameOver();\r\n\r\n        this.gameStart = false;\r\n        this.initBattleEnd = false;\r\n    }\r\n\r\n\r\n    /**\r\n     * Boss切换完成\r\n     * @param {string} bossName Boss名称\r\n     */\r\n    bossChangeFinish(bossName: string) {\r\n        // const bossEnterDialog = GameIns.uiManager.getDialog(BossEnterDialog.default);\r\n        // if (bossEnterDialog) {\r\n        //     bossEnterDialog.node.active = true;\r\n        //     GameIns.mainPlaneManager.moveAble = false;\r\n        //     bossEnterDialog.showTips(bossName);\r\n        // }\r\n    }\r\n\r\n    bossWillEnter() {\r\n        GameIns.mainPlaneManager.mainPlane!.setFireEnable(false);\r\n        GameIns.mainPlaneManager.moveAble = false;\r\n    }\r\n    /**\r\n     * 开始Boss战斗\r\n     */\r\n    bossFightStart() {\r\n        GameIns.mainPlaneManager.mainPlane!.setFireEnable(true);\r\n        GameIns.mainPlaneManager.moveAble = true;\r\n\r\n        GameIns.bossManager.bossFightStart();\r\n    }\r\n\r\n    /**\r\n     * 获取屏幕比例\r\n     * @returns {number} 屏幕比例\r\n     */\r\n    getRatio() {\r\n        return 0.666667; // 固定比例值\r\n    }\r\n\r\n    isGameType(gameType: number) {\r\n        return this.gameType == gameType;\r\n    }\r\n}"]}