System.register(["__unresolved_0", "cc", "cc/env", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, assetManager, JsonAsset, EDITOR, IMgr, cfg, BundleName, _dec, _class, _class2, _crd, ccclass, property, LubanMgr;

  function _reportPossibleCrUseOfIMgr(extras) {
    _reporterNs.report("IMgr", "../../../../scripts/core/base/IMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBundleName(extras) {
    _reporterNs.report("BundleName", "../const/BundleConst", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      assetManager = _cc.assetManager;
      JsonAsset = _cc.JsonAsset;
    }, function (_ccEnv) {
      EDITOR = _ccEnv.EDITOR;
    }, function (_unresolved_2) {
      IMgr = _unresolved_2.IMgr;
    }, function (_unresolved_3) {
      cfg = _unresolved_3;
    }, function (_unresolved_4) {
      BundleName = _unresolved_4.BundleName;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "a3a62bIqipLwrj7c4gC3qgj", "LubanMgr", undefined);

      __checkObsolete__(['_decorator', 'assetManager', 'JsonAsset']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("LubanMgr", LubanMgr = (_dec = ccclass("LubanMgr"), _dec(_class = (_class2 = class LubanMgr extends (_crd && IMgr === void 0 ? (_reportPossibleCrUseOfIMgr({
        error: Error()
      }), IMgr) : IMgr) {
        constructor(...args) {
          super(...args);
          this._table = null;
        }

        init() {
          super.init();
        }

        load() {
          return new Promise((resolve, reject) => {
            const bundle = assetManager.bundles.get((_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
              error: Error()
            }), BundleName) : BundleName).Luban);
            bundle.loadDir("./", JsonAsset, (err, assets) => {
              if (err) {
                reject(err);
                return;
              }

              var dataMap = new Map();

              for (let asset of assets) {
                dataMap.set(asset.name, asset);
              }

              this._table = new cfg.Tables(file => {
                if (dataMap.has(file)) {
                  return dataMap.get(file).json;
                }

                console.warn(`LubanMgr: File ${file} not found in loaded assets.`);
                return null;
              });
              resolve();
            });
          });
        } // 仅在编辑器环境下使用


        async initInEditor() {
          if (this._table != null || !EDITOR) {
            return;
          } // 遍历assets/bundles/luban/ 目录下的json文件


          const root_dir = 'db://assets/bundles/luban/';
          const pattern = root_dir + '**/*.json';

          try {
            // @ts-ignore
            const res = await Editor.Message.request('asset-db', 'query-assets', {
              pattern
            });
            const arr = Array.isArray(res) ? res : Array.isArray(res[0]) ? res[0] : [];
            const files = arr.map(a => a.path).filter(a => a.startsWith(root_dir)); // console.log('LubanMgr: Found files:', files);

            let LoadedCount = 0;
            var dataMap = new Map(); // Load each JSON file's content

            for (let filePath of files) {
              try {
                // @ts-ignore
                const uuid = await Editor.Message.request('asset-db', 'query-uuid', filePath + '.json');
                assetManager.loadAny(uuid, (err, jsonAsset) => {
                  if (err) {
                    console.warn(`LubanMgr: Failed to load asset ${filePath}:`, err);
                    return;
                  }

                  dataMap.set(jsonAsset.name, jsonAsset);
                  LoadedCount++;
                });
              } catch (fileError) {
                console.warn(`LubanMgr: Failed to load file ${filePath}:`, fileError);
              }
            } // Wait until all files are loaded


            while (LoadedCount < files.length) {
              await new Promise(res => setTimeout(res, 100)); // wait 100ms
            }

            this._table = new cfg.Tables(file => {
              if (dataMap.has(file)) {
                return dataMap.get(file).json;
              }

              return null;
            });
          } catch (error) {
            console.error("LubanMgr: Failed to initialize in editor:", error);
            throw error;
          }
        }

        get table() {
          return this._table;
        }

      }, _class2.LUBAN_PATH = 'luban/', _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=c72d28c7f685b05510553bbce697b446b9d1636d.js.map