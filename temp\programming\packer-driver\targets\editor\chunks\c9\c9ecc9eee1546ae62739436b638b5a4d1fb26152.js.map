{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/task/components/TaskItem.ts"], "names": ["_decorator", "Component", "Label", "Node", "ProgressBar", "Sprite", "comm", "logError", "MyApp", "ButtonPlus", "ccclass", "property", "TaskItem", "onLoad", "taskJumpBtn", "addClick", "onClickTaskJumpBtn", "taskGetRewardBtn", "onClickTaskGetRewardBtn", "onRender", "taskInfo", "taskCfg", "lubanTables", "TbResTask", "get", "task_id", "taskDesc", "string", "taskGoal", "desc", "progressLabel", "progress", "params", "progressBar", "status", "TASK_STATUS", "TASK_STATUS_COMPLETE", "node", "active", "taskDoneNode", "TASK_STATUS_NORMAL", "TASK_STATUS_AWARD_DONE"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,M,OAAAA,M;;AACxCC,MAAAA,I,iBAAAA,I;;AACTC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U;;0BAGjBY,Q,WADZF,OAAO,CAAC,UAAD,C,UAEHC,QAAQ,CAACT,KAAD,C,UAERS,QAAQ,CAACP,WAAD,C,UAERO,QAAQ,CAACT,KAAD,C,UAERS,QAAQ,CAACN,MAAD,C,UAERM,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ,CAACR,IAAD,C,2BAdb,MACaS,QADb,SAC8BX,SAD9B,CACwC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAgB1BY,QAAAA,MAAM,GAAS;AACrB,eAAKC,WAAL,CAAkBC,QAAlB,CAA2B,KAAKC,kBAAhC,EAAoD,IAApD;AACA,eAAKC,gBAAL,CAAuBF,QAAvB,CAAgC,KAAKG,uBAArC,EAA8D,IAA9D;AACH;;AAEDC,QAAAA,QAAQ,CAACC,QAAD,EAAyC;AAC7C,gBAAMC,OAAO,GAAG;AAAA;AAAA,8BAAMC,WAAN,CAAkBC,SAAlB,CAA4BC,GAA5B,CAAgCJ,QAAQ,CAACK,OAAzC,CAAhB;;AACA,cAAI,CAACJ,OAAL,EAAc;AACV;AAAA;AAAA,sCAAS,UAAT,EAAsB,WAAUD,QAAQ,CAACK,OAAQ,YAAjD;AACA;AACH;;AACD,eAAKC,QAAL,CAAeC,MAAf,GAAwBN,OAAO,CAACO,QAAR,CAAiBC,IAAzC;AACA,eAAKC,aAAL,CAAoBH,MAApB,GAA8B,GAAEP,QAAQ,CAACW,QAAS,IAAGV,OAAO,CAACO,QAAR,CAAiBI,MAAjB,CAAwB,CAAxB,CAA2B,EAAhF;AACA,eAAKC,WAAL,CAAkBF,QAAlB,GAA6BX,QAAQ,CAACW,QAAT,GAAqBV,OAAO,CAACO,QAAR,CAAiBI,MAAjB,CAAwB,CAAxB,CAAlD;;AACA,kBAAQZ,QAAQ,CAACc,MAAjB;AACI,iBAAK;AAAA;AAAA,8BAAKC,WAAL,CAAiBC,oBAAtB;AACI,mBAAKnB,gBAAL,CAAuBoB,IAAvB,CAA4BC,MAA5B,GAAqC,IAArC;AACA,mBAAKxB,WAAL,CAAkBuB,IAAlB,CAAuBC,MAAvB,GAAgC,KAAhC;AACA,mBAAKC,YAAL,CAAmBD,MAAnB,GAA4B,KAA5B;AACA;;AACJ,iBAAK;AAAA;AAAA,8BAAKH,WAAL,CAAiBK,kBAAtB;AACI,mBAAKvB,gBAAL,CAAuBoB,IAAvB,CAA4BC,MAA5B,GAAqC,IAArC;AACA,mBAAKxB,WAAL,CAAkBuB,IAAlB,CAAuBC,MAAvB,GAAgC,KAAhC;AACA,mBAAKC,YAAL,CAAmBD,MAAnB,GAA4B,KAA5B;AACA;;AACJ,iBAAK;AAAA;AAAA,8BAAKH,WAAL,CAAiBM,sBAAtB;AACI,mBAAKxB,gBAAL,CAAuBoB,IAAvB,CAA4BC,MAA5B,GAAqC,KAArC;AACA,mBAAKxB,WAAL,CAAkBuB,IAAlB,CAAuBC,MAAvB,GAAgC,KAAhC;AACA,mBAAKC,YAAL,CAAmBD,MAAnB,GAA4B,IAA5B;AACA;;AACJ;AACI;AAAA;AAAA,wCAAS,UAAT,EAAsB,WAAUlB,QAAQ,CAACK,OAAQ,iBAAgBL,QAAQ,CAACc,MAAO,EAAjF;AACA;AAlBR,WAT6C,CA6B7C;;AACH;;AAEOlB,QAAAA,kBAAkB,GAAS,CAElC;;AAEOE,QAAAA,uBAAuB,GAAS,CAEvC;;AA3DmC,O;;;;;iBAEX,I;;;;;;;iBAES,I;;;;;;;iBAEJ,I;;;;;;;iBAEJ,I;;;;;;;iBAEO,I;;;;;;;iBAEK,I;;;;;;;iBAEV,I", "sourcesContent": ["import { _decorator, Component, Label, Node, ProgressBar, Sprite } from 'cc';\nimport csproto, { comm } from 'db://assets/bundles/common/script/autogen/pb/cs_proto.js';\nimport { logError } from 'db://assets/scripts/utils/Logger';\nimport { MyApp } from '../../../app/MyApp';\nimport { ButtonPlus } from '../../common/components/button/ButtonPlus';\nconst { ccclass, property } = _decorator;\n\n@ccclass('TaskItem')\nexport class TaskItem extends Component {\n    @property(Label)\n    taskDesc: Label | null = null;\n    @property(ProgressBar)\n    progressBar: ProgressBar | null = null;\n    @property(Label)\n    progressLabel: Label | null = null;\n    @property(Sprite)\n    taskIcon: Sprite | null = null;\n    @property(ButtonPlus)\n    taskJumpBtn: ButtonPlus | null = null;\n    @property(ButtonPlus)\n    taskGetRewardBtn: ButtonPlus | null = null;\n    @property(Node)\n    taskDoneNode: Node | null = null;\n\n    protected onLoad(): void {\n        this.taskJumpBtn!.addClick(this.onClickTaskJumpBtn, this);\n        this.taskGetRewardBtn!.addClick(this.onClickTaskGetRewardBtn, this);\n    }\n\n    onRender(taskInfo: csproto.cs.ICSTaskInfo): void {\n        const taskCfg = MyApp.lubanTables.TbResTask.get(taskInfo.task_id!);\n        if (!taskCfg) {\n            logError(\"TaskItem\", `task id ${taskInfo.task_id} not found`)\n            return\n        }\n        this.taskDesc!.string = taskCfg.taskGoal.desc;\n        this.progressLabel!.string = `${taskInfo.progress}/${taskCfg.taskGoal.params[1]}`;\n        this.progressBar!.progress = taskInfo.progress! / taskCfg.taskGoal.params[1]\n        switch (taskInfo.status) {\n            case comm.TASK_STATUS.TASK_STATUS_COMPLETE:\n                this.taskGetRewardBtn!.node.active = true;\n                this.taskJumpBtn!.node.active = false\n                this.taskDoneNode!.active = false;\n                break;\n            case comm.TASK_STATUS.TASK_STATUS_NORMAL:\n                this.taskGetRewardBtn!.node.active = true;\n                this.taskJumpBtn!.node.active = false\n                this.taskDoneNode!.active = false;\n                break;\n            case comm.TASK_STATUS.TASK_STATUS_AWARD_DONE:\n                this.taskGetRewardBtn!.node.active = false;\n                this.taskJumpBtn!.node.active = false\n                this.taskDoneNode!.active = true;\n                break;\n            default:\n                logError(\"TaskItem\", `task id ${taskInfo.task_id} status error ${taskInfo.status}`)\n                break;\n        }\n        // this.taskIcon!.spriteFrame = MyApp.resMgr.getSpriteFrame(taskCfg.taskIcon);\n    }\n\n    private onClickTaskJumpBtn(): void {\n\n    }\n\n    private onClickTaskGetRewardBtn(): void {\n\n    }\n}"]}