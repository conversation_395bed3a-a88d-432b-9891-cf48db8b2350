{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/friend/FriendUI.ts"], "names": ["_decorator", "instantiate", "Label", "Node", "Prefab", "MyApp", "BundleName", "ButtonPlus", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "EventMgr", "PlaneUIEvent", "HomeUI", "Tabs", "TabStatus", "MessageBox", "ccclass", "property", "FriendUI", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "getBundleName", "HomeFriend", "getUIOption", "isClickBgCloseUI", "onLoad", "tabs", "init", "updateLabelAfterColon", "LabelTimes", "tabBagBtn", "getComponentInChildren", "LabelUpdate", "timestamp", "date", "Date", "hours", "getHours", "toString", "padStart", "minutes", "getMinutes", "now", "btnClose", "addClick", "closeUI", "btnGet", "onPower", "btnIgnoreAll", "onIgnore", "btnAgreeAll", "onAgree", "btnRefreshAll", "onRefresh", "on", "TabChange", "onTabChange", "panel2", "active", "panel3", "node2", "prefab", "resMgr", "loadAsync", "panel1", "<PERSON><PERSON><PERSON><PERSON>", "tabStatus", "Bag", "node1", "label", "args", "originalText", "string", "colonIndex", "indexOf", "formattedValue", "length", "join", "prefix", "substring", "openUI", "show", "onShow", "onHide", "onClose", "onDestroy", "targetOff"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;;AACtCC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Y,iBAAAA,Y;;AAEAC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,I,iBAAAA,I;;AACAC,MAAAA,S,kBAAAA,S;;AACAC,MAAAA,U,kBAAAA,U;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBlB,U;;0BAGjBmB,Q,WADZF,OAAO,CAAC,UAAD,C,UAGHC,QAAQ;AAAA;AAAA,mC,UAGRA,QAAQ;AAAA;AAAA,uB,UAGRA,QAAQ,CAACf,IAAD,C,UAERe,QAAQ,CAACf,IAAD,C,UAERe,QAAQ,CAACf,IAAD,C,UAGRe,QAAQ,CAACf,IAAD,C,UAERe,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ,CAAChB,KAAD,C,WAERgB,QAAQ,CAAChB,KAAD,C,WAGRgB,QAAQ,CAACf,IAAD,C,WAERe,QAAQ;AAAA;AAAA,mC,WAERA,QAAQ;AAAA;AAAA,mC,WAERA,QAAQ;AAAA;AAAA,mC,2BA/Bb,MACaC,QADb;AAAA;AAAA,4BACqC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAiCb,eAANC,MAAM,GAAW;AAAE,iBAAO,oBAAP;AAA8B;;AACzC,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,OAAf;AAAyB;;AAClC,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,UAAlB;AAA+B;;AAC9C,eAAXC,WAAW,GAAU;AAAE,iBAAO;AAAEC,YAAAA,gBAAgB,EAAE;AAApB,WAAP;AAAmC;;AAClD,cAANC,MAAM,GAAkB;AACpC,eAAKC,IAAL,CAAWC,IAAX;AACA,eAAKC,qBAAL,CAA2B,KAAKC,UAAhC,EAA6C,IAA7C,EAAmD,KAAnD;AACA,eAAKD,qBAAL,CAA2B,KAAKF,IAAL,CAAWI,SAAX,CAAsBC,sBAAtB,CAA6C/B,KAA7C,CAA3B,EAAiF,IAAjF,EAAuF,KAAvF;AACA,eAAK4B,qBAAL,CAA2B,KAAKI,WAAhC,EACI,CAAEC,SAAD,IAAuB;AACpB,kBAAMC,IAAI,GAAG,IAAIC,IAAJ,CAASF,SAAT,CAAb;AACA,kBAAMG,KAAK,GAAGF,IAAI,CAACG,QAAL,GAAgBC,QAAhB,GAA2BC,QAA3B,CAAoC,CAApC,EAAuC,GAAvC,CAAd;AACA,kBAAMC,OAAO,GAAGN,IAAI,CAACO,UAAL,GAAkBH,QAAlB,GAA6BC,QAA7B,CAAsC,CAAtC,EAAyC,GAAzC,CAAhB;AACA,mBAAQ,GAAEH,KAAM,IAAGI,OAAQ,GAA3B;AACH,WALD,EAKGL,IAAI,CAACO,GAAL,KAAa,IAAI,EAAJ,GAAS,EAAT,GAAc,IAL9B,CADJ;AAQA,eAAKC,QAAL,CAAeC,QAAf,CAAwB,KAAKC,OAA7B,EAAsC,IAAtC;AACA,eAAKC,MAAL,CAAaF,QAAb,CAAsB,KAAKG,OAA3B,EAAoC,IAApC;AACA,eAAKC,YAAL,CAAmBJ,QAAnB,CAA4B,KAAKK,QAAjC,EAA2C,IAA3C;AACA,eAAKC,WAAL,CAAkBN,QAAlB,CAA2B,KAAKO,OAAhC,EAAyC,IAAzC;AACA,eAAKC,aAAL,CAAoBR,QAApB,CAA6B,KAAKS,SAAlC,EAA6C,IAA7C;AACA;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,4CAAaC,SAAzB,EAAoC,KAAKC,WAAzC,EAAsD,IAAtD;AAEA,eAAKC,MAAL,CAAaC,MAAb,GAAsB,KAAtB;AACA,eAAKC,MAAL,CAAaD,MAAb,GAAsB,KAAtB;AACA,eAAKE,KAAL,CAAYF,MAAZ,GAAqB,KAArB;AAEA,cAAIG,MAAM,GAAG,MAAM;AAAA;AAAA,8BAAMC,MAAN,CAAaC,SAAb,CAAuB;AAAA;AAAA,wCAAWzC,UAAlC,EAA8C,wBAA9C,EAAwEpB,MAAxE,CAAnB;AACA,eAAK8D,MAAL,CAAaC,QAAb,CAAsBlE,WAAW,CAAC8D,MAAD,CAAjC;AACAA,UAAAA,MAAM,GAAG,MAAM;AAAA;AAAA,8BAAMC,MAAN,CAAaC,SAAb,CAAuB;AAAA;AAAA,wCAAWzC,UAAlC,EAA8C,uBAA9C,EAAuEpB,MAAvE,CAAf;AACA,eAAKuD,MAAL,CAAaQ,QAAb,CAAsBlE,WAAW,CAAC8D,MAAD,CAAjC;AACAA,UAAAA,MAAM,GAAG,MAAM;AAAA;AAAA,8BAAMC,MAAN,CAAaC,SAAb,CAAuB;AAAA;AAAA,wCAAWzC,UAAlC,EAA8C,4BAA9C,EAA4EpB,MAA5E,CAAf;AACA,eAAKyD,MAAL,CAAaM,QAAb,CAAsBlE,WAAW,CAAC8D,MAAD,CAAjC;AACH;;AACOL,QAAAA,WAAW,CAACU,SAAD,EAAuB;AACtC,cAAIA,SAAS,IAAI;AAAA;AAAA,sCAAUC,GAA3B,EAAgC;AAC5B,iBAAKC,KAAL,CAAYV,MAAZ,GAAqB,IAArB;AACA,iBAAKE,KAAL,CAAYF,MAAZ,GAAqB,KAArB;AACA,iBAAKM,MAAL,CAAaN,MAAb,GAAsB,IAAtB;AACA,iBAAKD,MAAL,CAAaC,MAAb,GAAsB,KAAtB;AACA,iBAAKC,MAAL,CAAaD,MAAb,GAAsB,KAAtB;AACH,WAND,MAMO;AACH,iBAAKU,KAAL,CAAYV,MAAZ,GAAqB,KAArB;AACA,iBAAKE,KAAL,CAAYF,MAAZ,GAAqB,IAArB;AACA,iBAAKM,MAAL,CAAaN,MAAb,GAAsB,KAAtB;AACA,iBAAKD,MAAL,CAAaC,MAAb,GAAsB,IAAtB;AACA,iBAAKC,MAAL,CAAaD,MAAb,GAAsB,IAAtB;AACH;AACJ;;AACM9B,QAAAA,qBAAqB,CAACyC,KAAD,EAAe,GAAGC,IAAlB,EAAwC;AAChE,gBAAMC,YAAY,GAAGF,KAAK,CAACG,MAA3B;AACA,cAAIC,UAAU,GAAGF,YAAY,CAACG,OAAb,CAAqB,GAArB,CAAjB;;AACA,cAAID,UAAU,KAAK,CAAC,CAApB,EAAuB;AACnBA,YAAAA,UAAU,GAAGF,YAAY,CAACG,OAAb,CAAqB,GAArB,CAAb,CADmB,CACqB;AAC3C;;AACD,cAAIC,cAAJ;;AACA,cAAIL,IAAI,CAACM,MAAL,KAAgB,CAApB,EAAuB;AACnBD,YAAAA,cAAc,GAAGL,IAAI,CAAC,CAAD,CAArB;AACH,WAFD,MAEO,IAAIA,IAAI,CAACM,MAAL,KAAgB,CAApB,EAAuB;AAC1BD,YAAAA,cAAc,GAAI,GAAEL,IAAI,CAAC,CAAD,CAAI,IAAGA,IAAI,CAAC,CAAD,CAAI,EAAvC;AACH,WAFM,MAEA,IAAIA,IAAI,CAACM,MAAL,GAAc,CAAlB,EAAqB;AACxBD,YAAAA,cAAc,GAAGL,IAAI,CAACO,IAAL,CAAU,GAAV,CAAjB;AACH,WAFM,MAEA;AACHF,YAAAA,cAAc,GAAG,EAAjB;AACH;;AACD,cAAIF,UAAU,KAAK,CAAC,CAApB,EAAuB;AACnBJ,YAAAA,KAAK,CAACG,MAAN,GAAgB,GAAED,YAAa,IAAGI,cAAe,EAAjD;AACA;AACH;;AACD,gBAAMG,MAAM,GAAGP,YAAY,CAACQ,SAAb,CAAuB,CAAvB,EAA0BN,UAAU,GAAG,CAAvC,CAAf,CApBgE,CAoBN;;AAC1DJ,UAAAA,KAAK,CAACG,MAAN,GAAgB,GAAEM,MAAO,GAAEH,cAAe,EAA1C;AACH;;AAEY,cAAP9B,OAAO,GAAG;AACZ;AAAA;AAAA,8BAAMA,OAAN,CAAc5B,QAAd;AACA,gBAAM;AAAA;AAAA,8BAAM+D,MAAN;AAAA;AAAA,+BAAN;AACH;;AACOjC,QAAAA,OAAO,GAAG;AACd;AAAA;AAAA,wCAAWkC,IAAX,CAAgB,MAAhB;AACH;;AACOhC,QAAAA,QAAQ,GAAG;AACf;AAAA;AAAA,wCAAWgC,IAAX,CAAgB,MAAhB;AACH;;AACO9B,QAAAA,OAAO,GAAG;AACd;AAAA;AAAA,wCAAW8B,IAAX,CAAgB,MAAhB;AACH;;AACO5B,QAAAA,SAAS,GAAG;AAChB;AAAA;AAAA,wCAAW4B,IAAX,CAAgB,OAAhB;AACH;;AAEW,cAANC,MAAM,GAAkB,CAE7B;;AACW,cAANC,MAAM,GAAkB,CAC7B;;AACY,cAAPC,OAAO,GAAkB,CAC9B;;AACSC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AApIgC,O;;;;;iBAGH,I;;;;;;;iBAGV,I;;;;;;;iBAGE,I;;;;;;;iBAEA,I;;;;;;;iBAEA,I;;;;;;;iBAGD,I;;;;;;;iBAEO,I;;;;;;;iBAED,I;;;;;;;iBAEC,I;;;;;;;iBAGP,I;;;;;;;iBAEa,I;;;;;;;iBAED,I;;;;;;;iBAEE,I", "sourcesContent": ["import { _decorator, instantiate, Label, Node, Prefab } from 'cc';\r\nimport { MyApp } from 'db://assets/bundles/common/script/app/MyApp';\r\nimport { BundleName } from 'db://assets/bundles/common/script/const/BundleConst';\r\nimport { ButtonPlus } from 'db://assets/bundles/common/script/ui/common/components/button/ButtonPlus';\r\nimport { BaseUI, UILayer, UIMgr, UIOpt } from 'db://assets/scripts/core/base/UIMgr';\r\nimport { EventMgr } from '../../event/EventManager';\r\nimport { PlaneUIEvent } from '../../event/PlaneUIEvent';\r\nimport { PopupUI } from '../common/PopupUI';\r\nimport { HomeUI } from '../home/<USER>';\r\nimport { Tabs } from '../plane/components/back_pack/Tabs';\r\nimport { TabStatus } from '../plane/PlaneTypes';\r\nimport { MessageBox } from 'db://assets/scripts/core/base/MessageBox';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('FriendUI')\r\nexport class FriendUI extends BaseUI {\r\n\r\n    @property(ButtonPlus)\r\n    btnClose: ButtonPlus | null = null;\r\n\r\n    @property(Tabs)\r\n    tabs: Tabs | null = null;\r\n\r\n    @property(Node)\r\n    panel1: Node | null = null;\r\n    @property(Node)\r\n    panel2: Node | null = null;\r\n    @property(Node)\r\n    panel3: Node | null = null;\r\n\r\n    @property(Node)\r\n    node1: Node | null = null;\r\n    @property(ButtonPlus)\r\n    btnGet: ButtonPlus | null = null;\r\n    @property(Label)\r\n    LabelTimes: Label | null = null;\r\n    @property(Label)\r\n    LabelUpdate: Label | null = null;\r\n\r\n    @property(Node)\r\n    node2: Node | null = null;\r\n    @property(ButtonPlus)\r\n    btnIgnoreAll: ButtonPlus | null = null;\r\n    @property(ButtonPlus)\r\n    btnAgreeAll: ButtonPlus | null = null;\r\n    @property(ButtonPlus)\r\n    btnRefreshAll: ButtonPlus | null = null;\r\n\r\n    public static getUrl(): string { return \"prefab/ui/FriendUI\"; }\r\n    public static getLayer(): UILayer { return UILayer.Default; }\r\n    public static getBundleName(): string { return BundleName.HomeFriend; }\r\n    public static getUIOption(): UIOpt { return { isClickBgCloseUI: true } }\r\n    protected async onLoad(): Promise<void> {\r\n        this.tabs!.init();\r\n        this.updateLabelAfterColon(this.LabelTimes!, \"50\", \"100\");\r\n        this.updateLabelAfterColon(this.tabs!.tabBagBtn!.getComponentInChildren(Label)!, \"30\", \"100\");\r\n        this.updateLabelAfterColon(this.LabelUpdate!,\r\n            ((timestamp: number) => {\r\n                const date = new Date(timestamp);\r\n                const hours = date.getHours().toString().padStart(2, '0');\r\n                const minutes = date.getMinutes().toString().padStart(2, '0');\r\n                return `${hours}时${minutes}分`;\r\n            })(Date.now() + 3 * 60 * 60 * 1000)\r\n        );\r\n        this.btnClose!.addClick(this.closeUI, this);\r\n        this.btnGet!.addClick(this.onPower, this);\r\n        this.btnIgnoreAll!.addClick(this.onIgnore, this);\r\n        this.btnAgreeAll!.addClick(this.onAgree, this);\r\n        this.btnRefreshAll!.addClick(this.onRefresh, this);\r\n        EventMgr.on(PlaneUIEvent.TabChange, this.onTabChange, this);\r\n\r\n        this.panel2!.active = false;\r\n        this.panel3!.active = false;\r\n        this.node2!.active = false;\r\n\r\n        let prefab = await MyApp.resMgr.loadAsync(BundleName.HomeFriend, \"prefab/ui/FriendListUI\", Prefab);\r\n        this.panel1!.addChild(instantiate(prefab));\r\n        prefab = await MyApp.resMgr.loadAsync(BundleName.HomeFriend, \"prefab/ui/FriendAddUI\", Prefab);\r\n        this.panel2!.addChild(instantiate(prefab));\r\n        prefab = await MyApp.resMgr.loadAsync(BundleName.HomeFriend, \"prefab/ui/FriendStrangerUI\", Prefab);\r\n        this.panel3!.addChild(instantiate(prefab));\r\n    }\r\n    private onTabChange(tabStatus: TabStatus) {\r\n        if (tabStatus == TabStatus.Bag) {\r\n            this.node1!.active = true;\r\n            this.node2!.active = false;\r\n            this.panel1!.active = true;\r\n            this.panel2!.active = false;\r\n            this.panel3!.active = false;\r\n        } else {\r\n            this.node1!.active = false;\r\n            this.node2!.active = true;\r\n            this.panel1!.active = false;\r\n            this.panel2!.active = true;\r\n            this.panel3!.active = true;\r\n        }\r\n    }\r\n    public updateLabelAfterColon(label: Label, ...args: string[]): void {\r\n        const originalText = label.string;\r\n        let colonIndex = originalText.indexOf(\":\");\r\n        if (colonIndex === -1) {\r\n            colonIndex = originalText.indexOf(\"：\"); // 中文冒号\r\n        }\r\n        let formattedValue: string;\r\n        if (args.length === 1) {\r\n            formattedValue = args[0];\r\n        } else if (args.length === 2) {\r\n            formattedValue = `${args[0]}/${args[1]}`;\r\n        } else if (args.length > 2) {\r\n            formattedValue = args.join(\",\");\r\n        } else {\r\n            formattedValue = \"\";\r\n        }\r\n        if (colonIndex === -1) {\r\n            label.string = `${originalText}:${formattedValue}`;\r\n            return;\r\n        }\r\n        const prefix = originalText.substring(0, colonIndex + 1); // 包含冒号\r\n        label.string = `${prefix}${formattedValue}`;\r\n    }\r\n\r\n    async closeUI() {\r\n        UIMgr.closeUI(FriendUI);\r\n        await UIMgr.openUI(HomeUI)\r\n    }\r\n    private onPower() {\r\n        MessageBox.show(\"一键收赠\");\r\n    }\r\n    private onIgnore() {\r\n        MessageBox.show(\"全部忽略\");\r\n    }\r\n    private onAgree() {\r\n        MessageBox.show(\"全部同意\");\r\n    }\r\n    private onRefresh() {\r\n        MessageBox.show(\"刷新陌生人\");\r\n    }\r\n\r\n    async onShow(): Promise<void> {\r\n\r\n    }\r\n    async onHide(): Promise<void> {\r\n    }\r\n    async onClose(): Promise<void> {\r\n    }\r\n    protected onDestroy(): void {\r\n        EventMgr.targetOff(this);\r\n    }\r\n}"]}