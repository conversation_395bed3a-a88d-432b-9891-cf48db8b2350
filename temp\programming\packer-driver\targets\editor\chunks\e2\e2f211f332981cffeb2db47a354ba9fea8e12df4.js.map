{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts"], "names": ["_decorator", "ResManager", "audioManager", "GlobalDataManager", "LubanMgr", "NetMgr", "PlaneManager", "CreateLoginSDK", "ccclass", "MyApp", "ManagerPool", "_updateContainer", "_lateUpdateC<PERSON>r", "_lubanMgr", "_netMgr", "_resMgr", "_platformSDK", "_globalDataManager", "_planeManager", "_audioManager", "GetInstance", "_instance", "init", "push", "instance", "for<PERSON>ach", "manager", "onUpdate", "bind", "onLateUpdate", "update", "deltaTime", "i", "length", "lateUpdate", "netMgr", "lubanMgr", "lubanTables", "table", "platformSDK", "resMgr", "globalMgr", "planeMgr", "audioMgr"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AAEAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,Y,iBAAAA,Y;;AAEAC,MAAAA,iB,iBAAAA,iB;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,c,iBAAAA,c;;;;;;;;;OAEH;AAAEC,QAAAA;AAAF,O,GAAcR,U;;uBAGPS,K,WADZD,OAAO,CAAC,OAAD,C,2BAAR,MACaC,KADb,CACmB;AAAA;AAAA,eAQPC,WARO,GAQe,EARf;AAAA,eASPC,gBATO,GAS0B,EAT1B;AAAA,eAUPC,oBAVO,GAUkC,EAVlC;AAAA,eAYPC,SAZO,GAYsB,IAZtB;AAAA,eAaPC,OAbO,GAakB,IAblB;AAAA,eAcPC,OAdO,GAcsB,IAdtB;AAAA,eAePC,YAfO,GAe6B,IAf7B;AAAA,eAgBPC,kBAhBO,GAgBwC,IAhBxC;AAAA,eAiBPC,aAjBO,GAiB8B,IAjB9B;AAAA,eAkBPC,aAlBO,GAkB8B,IAlB9B;AAAA;;AAEU,eAAXC,WAAW,GAAU;AAC/B,cAAI,CAACX,KAAK,CAACY,SAAX,EAAsB;AAClBZ,YAAAA,KAAK,CAACY,SAAN,GAAkB,IAAIZ,KAAJ,EAAlB;AACH;;AACD,iBAAOA,KAAK,CAACY,SAAb;AACH;;AAaDC,QAAAA,IAAI,GAAG;AACH,eAAKZ,WAAL,CAAiBa,IAAjB,CAAsB;AAAA;AAAA,4CAAaC,QAAnC;AACA,eAAKX,SAAL,GAAiB;AAAA;AAAA,qCAAjB;AACA,eAAKH,WAAL,CAAiBa,IAAjB,CAAsB,KAAKV,SAA3B;AACA,eAAKC,OAAL,GAAe;AAAA;AAAA,iCAAf;AACA,eAAKJ,WAAL,CAAiBa,IAAjB,CAAsB,KAAKT,OAA3B;AACA,eAAKC,OAAL,GAAe;AAAA;AAAA,wCAAWS,QAA1B;AACA,eAAKd,WAAL,CAAiBa,IAAjB,CAAsB,KAAKR,OAA3B;AACA,eAAKC,YAAL,GAAoB;AAAA;AAAA,iDAApB;AACA,eAAKC,kBAAL,GAA0B;AAAA;AAAA,uDAA1B;AACA,eAAKC,aAAL,GAAqB;AAAA;AAAA,6CAArB;AACA,eAAKC,aAAL,GAAqB;AAAA;AAAA,4CAAaK,QAAlC;AAEA,eAAKd,WAAL,CAAiBe,OAAjB,CAAyBC,OAAO,IAAI;AAChCA,YAAAA,OAAO,CAACJ,IAAR;;AACA,iBAAKX,gBAAL,CAAsBY,IAAtB,CAA2BG,OAAO,CAACC,QAAR,CAAiBC,IAAjB,CAAsBF,OAAtB,CAA3B;;AACA,iBAAKd,oBAAL,CAA0BW,IAA1B,CAA+BG,OAAO,CAACG,YAAR,CAAqBD,IAArB,CAA0BF,OAA1B,CAA/B;AACH,WAJD;AAKH;;AACDI,QAAAA,MAAM,CAACC,SAAD,EAA0B;AAC5B,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKrB,gBAAL,CAAsBsB,MAA1C,EAAkDD,CAAC,EAAnD,EAAuD;AACnD,iBAAKrB,gBAAL,CAAsBqB,CAAtB,EAAyBD,SAAzB;AACH;AACJ;;AAEDG,QAAAA,UAAU,GAAS;AACf,eAAK,IAAIF,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKpB,oBAAL,CAA0BqB,MAA9C,EAAsDD,CAAC,EAAvD,EAA2D;AACvD,iBAAKpB,oBAAL,CAA0BoB,CAA1B;AACH;AACJ;;AAEgB,mBAANG,MAAM,GAAW;AACxB,iBAAO1B,KAAK,CAACW,WAAN,GAAoBN,OAA3B;AACH;;AACkB,mBAARsB,QAAQ,GAAa;AAC5B,iBAAO3B,KAAK,CAACW,WAAN,GAAoBP,SAA3B;AACH;;AACqB,mBAAXwB,WAAW,GAAe;AACjC,iBAAO5B,KAAK,CAACW,WAAN,GAAoBP,SAApB,CAA+ByB,KAAtC;AACH;;AACqB,mBAAXC,WAAW,GAAiB;AACnC,iBAAO9B,KAAK,CAACW,WAAN,GAAoBJ,YAA3B;AACH;;AACgB,mBAANwB,MAAM,GAAe;AAC5B,iBAAO/B,KAAK,CAACW,WAAN,GAAoBL,OAA3B;AACH;;AACmB,mBAAT0B,SAAS,GAAsB;AACtC,iBAAOhC,KAAK,CAACW,WAAN,GAAoBH,kBAA3B;AACH;;AACkB,mBAARyB,QAAQ,GAAiB;AAChC,iBAAOjC,KAAK,CAACW,WAAN,GAAoBF,aAA3B;AACH;;AACkB,mBAARyB,QAAQ,GAAiB;AAChC,iBAAOlC,KAAK,CAACW,WAAN,GAAoBD,aAA3B;AACH;;AA1Ec,O,UACAE,S,GAA0B,I", "sourcesContent": ["import { _decorator } from \"cc\";\nimport { FnOnLateUpdate, FnOnUpdate, IMgr } from \"../../../../scripts/core/base/IMgr\";\nimport { ResManager } from \"../../../../scripts/core/base/ResManager\";\nimport { audioManager } from '../audio/audioManager';\nimport * as cfg from '../autogen/luban/schema';\nimport { GlobalDataManager } from \"../game/manager/GlobalDataManager\";\nimport { LubanMgr } from \"../luban/LubanMgr\";\nimport { NetMgr } from \"../network/NetMgr\";\nimport { PlaneManager } from \"../plane/PlaneManager\";\nimport { CreateLoginSDK, IPlatformSDK } from \"../platformsdk/IPlatformSDK\";\n\nconst { ccclass } = _decorator;\n\n@ccclass(\"MyApp\")\nexport class MyApp {\n    private static _instance: MyApp | null = null;\n    public static GetInstance(): MyApp {\n        if (!MyApp._instance) {\n            MyApp._instance = new MyApp();\n        }\n        return MyApp._instance!;\n    }\n    private ManagerPool: IMgr[] = [];\n    private _updateContainer: FnOnUpdate[] = [];\n    private _lateUpdateContainer: FnOnLateUpdate[] = [];\n\n    private _lubanMgr: LubanMgr | null = null;\n    private _netMgr: NetMgr | null = null;\n    private _resMgr: ResManager | null = null;\n    private _platformSDK: IPlatformSDK | null = null;\n    private _globalDataManager: GlobalDataManager | null = null;\n    private _planeManager: PlaneManager | null = null;\n    private _audioManager: audioManager | null = null;\n\n    init() {\n        this.ManagerPool.push(audioManager.instance);\n        this._lubanMgr = new LubanMgr();\n        this.ManagerPool.push(this._lubanMgr);\n        this._netMgr = new NetMgr();\n        this.ManagerPool.push(this._netMgr);\n        this._resMgr = ResManager.instance;\n        this.ManagerPool.push(this._resMgr);\n        this._platformSDK = CreateLoginSDK();\n        this._globalDataManager = new GlobalDataManager();\n        this._planeManager = new PlaneManager();\n        this._audioManager = audioManager.instance\n\n        this.ManagerPool.forEach(manager => {\n            manager.init();\n            this._updateContainer.push(manager.onUpdate.bind(manager));\n            this._lateUpdateContainer.push(manager.onLateUpdate.bind(manager));\n        });\n    }\n    update(deltaTime: number): void {\n        for (let i = 0; i < this._updateContainer.length; i++) {\n            this._updateContainer[i](deltaTime);\n        }\n    }\n\n    lateUpdate(): void {\n        for (let i = 0; i < this._lateUpdateContainer.length; i++) {\n            this._lateUpdateContainer[i]();\n        }\n    }\n\n    static get netMgr(): NetMgr {\n        return MyApp.GetInstance()._netMgr!;\n    }\n    static get lubanMgr(): LubanMgr {\n        return MyApp.GetInstance()._lubanMgr!;\n    }\n    static get lubanTables(): cfg.Tables {\n        return MyApp.GetInstance()._lubanMgr!.table;\n    }\n    static get platformSDK(): IPlatformSDK {\n        return MyApp.GetInstance()._platformSDK!;\n    }\n    static get resMgr(): ResManager {\n        return MyApp.GetInstance()._resMgr!;\n    }\n    static get globalMgr(): GlobalDataManager {\n        return MyApp.GetInstance()._globalDataManager!;\n    }\n    static get planeMgr(): PlaneManager {\n        return MyApp.GetInstance()._planeManager!;\n    }\n    static get audioMgr(): audioManager {\n        return MyApp.GetInstance()._audioManager!;\n    }\n}"]}