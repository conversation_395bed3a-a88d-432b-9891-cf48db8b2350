System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, MyApp, csproto, logError, Task, _crd;

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/bundles/common/script/app/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfcsproto(extras) {
    _reporterNs.report("csproto", "db://assets/bundles/common/script/autogen/pb/cs_proto.js", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIData(extras) {
    _reporterNs.report("IData", "db://assets/bundles/common/script/data/DataManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogError(extras) {
    _reporterNs.report("logError", "db://assets/scripts/utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfResTaskClass(extras) {
    _reporterNs.report("ResTaskClass", "../../autogen/luban/schema", _context.meta, extras);
  }

  _export("Task", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      MyApp = _unresolved_2.MyApp;
    }, function (_unresolved_3) {
      csproto = _unresolved_3.default;
    }, function (_unresolved_4) {
      logError = _unresolved_4.logError;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "937a5sNu2NPOKTaYU3HSRiv", "Task", undefined);

      _export("Task", Task = class Task {
        constructor() {
          // 任务集合
          this.taskMap = new Map();
        }

        init() {
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.registerHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_TASK_GET_INFO, this.onGetTaskInfoMsg, this);
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.registerHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_TASK_GET_REWARD, this.onGetTaskRewardMsg, this);
        } // 任务奖励


        onGetTaskRewardMsg(msg) {// const taskId = msg.body?.task_get_reward?.reward_list
          // if (taskId) {
          //     const taskCfg = MyApp.lubanTables.TbResTask.get();
          //     if (taskCfg) {
          //         this.taskMap.set(taskCfg.taskClass, t);
          //     }
          // }
        } // 全任务信息


        onGetTaskInfoMsg(msg) {
          var _msg$body;

          const taskList = ((_msg$body = msg.body) == null || (_msg$body = _msg$body.task_get_info) == null ? void 0 : _msg$body.task_list) || [];
          taskList.forEach(t => {
            const taskCfg = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).lubanTables.TbResTask.get(t.task_id);

            if (!taskCfg) {
              (_crd && logError === void 0 ? (_reportPossibleCrUseOflogError({
                error: Error()
              }), logError) : logError)("Task", `task id ${t.task_id} not found`);
              return;
            }

            let taskList = this.taskMap.get(taskCfg.taskClass) || [];
            taskList.push(t);
            this.taskMap.set(taskCfg.taskClass, taskList);
          });
        }

        getTaskListByClass(taskClass) {
          return this.taskMap.get(taskClass) || [];
        }

        getTaskByTaskId(taskId, taskClass) {
          if (taskClass) {
            const taskList = this.taskMap.get(taskClass) || [];
            return taskList.find(t => t.task_id === taskId);
          }

          for (const taskList of this.taskMap.values()) {
            const task = taskList.find(t => t.task_id === taskId);

            if (task) {
              return task;
            }
          }

          return undefined;
        }

        update() {}

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=f54b4cf4f8d03920cd9c87dfb4881bddc6fe68ed.js.map