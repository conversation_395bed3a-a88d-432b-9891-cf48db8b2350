{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/data/task/Task.ts"], "names": ["Task", "MyApp", "csproto", "logError", "taskMap", "Map", "init", "netMgr", "registerHandler", "cs", "CS_CMD", "CS_CMD_TASK_GET_INFO", "onGetTaskInfoMsg", "CS_CMD_TASK_GET_REWARD", "onGetTaskRewardMsg", "msg", "taskList", "body", "task_get_info", "task_list", "for<PERSON>ach", "t", "taskCfg", "lubanTables", "TbResTask", "get", "task_id", "taskClass", "push", "set", "getTaskListByClass", "getTaskByTaskId", "taskId", "find", "values", "task", "undefined", "update"], "mappings": ";;;wDAMaA,I;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AANJC,MAAAA,K,iBAAAA,K;;AACFC,MAAAA,O;;AAEEC,MAAAA,Q,iBAAAA,Q;;;;;;;sBAGIH,I,GAAN,MAAMA,IAAN,CAA4B;AAAA;AAC/B;AAD+B,eAE/BI,OAF+B,GAEwB,IAAIC,GAAJ,EAFxB;AAAA;;AAIxBC,QAAAA,IAAI,GAAS;AAChB;AAAA;AAAA,8BAAMC,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBC,oBAA/C,EAAqE,KAAKC,gBAA1E,EAA4F,IAA5F;AACA;AAAA;AAAA,8BAAML,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBG,sBAA/C,EAAuE,KAAKC,kBAA5E,EAAgG,IAAhG;AACH,SAP8B,CAS/B;;;AACQA,QAAAA,kBAAkB,CAACC,GAAD,EAA0B,CAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACH,SAlB8B,CAoB/B;;;AACQH,QAAAA,gBAAgB,CAACG,GAAD,EAA0B;AAAA;;AAC9C,gBAAMC,QAAQ,GAAG,cAAAD,GAAG,CAACE,IAAJ,oCAAUC,aAAV,+BAAyBC,SAAzB,KAAsC,EAAvD;AACAH,UAAAA,QAAQ,CAACI,OAAT,CAAiBC,CAAC,IAAI;AAClB,kBAAMC,OAAO,GAAG;AAAA;AAAA,gCAAMC,WAAN,CAAkBC,SAAlB,CAA4BC,GAA5B,CAAgCJ,CAAC,CAACK,OAAlC,CAAhB;;AACA,gBAAI,CAACJ,OAAL,EAAc;AACV;AAAA;AAAA,wCAAS,MAAT,EAAkB,WAAUD,CAAC,CAACK,OAAQ,YAAtC;AACA;AACH;;AACD,gBAAIV,QAAQ,GAAG,KAAKZ,OAAL,CAAaqB,GAAb,CAAiBH,OAAO,CAACK,SAAzB,KAAuC,EAAtD;AACAX,YAAAA,QAAQ,CAACY,IAAT,CAAcP,CAAd;AACA,iBAAKjB,OAAL,CAAayB,GAAb,CAAiBP,OAAO,CAACK,SAAzB,EAAoCX,QAApC;AACH,WATD;AAUH;;AAEDc,QAAAA,kBAAkB,CAACH,SAAD,EAAoD;AAClE,iBAAO,KAAKvB,OAAL,CAAaqB,GAAb,CAAiBE,SAAjB,KAA+B,EAAtC;AACH;;AACDI,QAAAA,eAAe,CAACC,MAAD,EAAiBL,SAAjB,EAA+E;AAC1F,cAAIA,SAAJ,EAAe;AACX,kBAAMX,QAAQ,GAAG,KAAKZ,OAAL,CAAaqB,GAAb,CAAiBE,SAAjB,KAA+B,EAAhD;AACA,mBAAOX,QAAQ,CAACiB,IAAT,CAAcZ,CAAC,IAAIA,CAAC,CAACK,OAAF,KAAcM,MAAjC,CAAP;AACH;;AACD,eAAK,MAAMhB,QAAX,IAAuB,KAAKZ,OAAL,CAAa8B,MAAb,EAAvB,EAA8C;AAC1C,kBAAMC,IAAI,GAAGnB,QAAQ,CAACiB,IAAT,CAAcZ,CAAC,IAAIA,CAAC,CAACK,OAAF,KAAcM,MAAjC,CAAb;;AACA,gBAAIG,IAAJ,EAAU;AACN,qBAAOA,IAAP;AACH;AACJ;;AACD,iBAAOC,SAAP;AACH;;AAEDC,QAAAA,MAAM,GAAS,CACd;;AArD8B,O", "sourcesContent": ["import { MyApp } from \"db://assets/bundles/common/script/app/MyApp\";\nimport csproto from 'db://assets/bundles/common/script/autogen/pb/cs_proto.js';\nimport { IData } from \"db://assets/bundles/common/script/data/DataManager\";\nimport { logError } from \"db://assets/scripts/utils/Logger\";\nimport { ResTaskClass } from \"../../autogen/luban/schema\";\n\nexport class Task implements IData {\n    // 任务集合\n    taskMap: Map<ResTaskClass, csproto.cs.ICSTaskInfo[]> = new Map();\n\n    public init(): void {\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_TASK_GET_INFO, this.onGetTaskInfoMsg, this)\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_TASK_GET_REWARD, this.onGetTaskRewardMsg, this)\n    }\n\n    // 任务奖励\n    private onGetTaskRewardMsg(msg: csproto.cs.IS2CMsg) {\n        // const taskId = msg.body?.task_get_reward?.reward_list\n        // if (taskId) {\n        //     const taskCfg = MyApp.lubanTables.TbResTask.get();\n        //     if (taskCfg) {\n        //         this.taskMap.set(taskCfg.taskClass, t);\n        //     }\n        // }\n    }\n\n    // 全任务信息\n    private onGetTaskInfoMsg(msg: csproto.cs.IS2CMsg) {\n        const taskList = msg.body?.task_get_info?.task_list || [];\n        taskList.forEach(t => {\n            const taskCfg = MyApp.lubanTables.TbResTask.get(t.task_id!)\n            if (!taskCfg) {\n                logError(\"Task\", `task id ${t.task_id} not found`)\n                return\n            }\n            let taskList = this.taskMap.get(taskCfg.taskClass) || [];\n            taskList.push(t);\n            this.taskMap.set(taskCfg.taskClass, taskList);\n        })\n    }\n\n    getTaskListByClass(taskClass: ResTaskClass): csproto.cs.ICSTaskInfo[] {\n        return this.taskMap.get(taskClass) || [];\n    }\n    getTaskByTaskId(taskId: number, taskClass?: ResTaskClass): csproto.cs.ICSTaskInfo | undefined {\n        if (taskClass) {\n            const taskList = this.taskMap.get(taskClass) || [];\n            return taskList.find(t => t.task_id === taskId);\n        }\n        for (const taskList of this.taskMap.values()) {\n            const task = taskList.find(t => t.task_id === taskId);\n            if (task) {\n                return task;\n            }\n        }\n        return undefined;\n    }\n\n    update(): void {\n    }\n}\n"]}