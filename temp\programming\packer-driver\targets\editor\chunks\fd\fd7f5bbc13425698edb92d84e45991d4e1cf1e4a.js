System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, CCFloat, CCInteger, Component, WaveData, eSpawnOrder, eWaveCompletion, GameIns, _dec, _dec2, _dec3, _dec4, _dec5, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _dec6, _dec7, _dec8, _dec9, _dec10, _class4, _class5, _descriptor5, _descriptor6, _descriptor7, _descriptor8, _dec11, _dec12, _dec13, _dec14, _class7, _class8, _descriptor9, _crd, ccclass, property, executeInEditMode, menu, WaveTrack, WaveTrackGroup, Wave;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfWaveData(extras) {
    _reporterNs.report("WaveData", "../data/WaveData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeSpawnOrder(extras) {
    _reporterNs.report("eSpawnOrder", "../data/WaveData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeWaveCompletion(extras) {
    _reporterNs.report("eWaveCompletion", "../data/WaveData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "db://assets/bundles/common/script/game/GameIns", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      CCFloat = _cc.CCFloat;
      CCInteger = _cc.CCInteger;
      Component = _cc.Component;
    }, function (_unresolved_2) {
      WaveData = _unresolved_2.WaveData;
      eSpawnOrder = _unresolved_2.eSpawnOrder;
      eWaveCompletion = _unresolved_2.eWaveCompletion;
    }, function (_unresolved_3) {
      GameIns = _unresolved_3.GameIns;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "194feuuQpZAtYZElOT4+NCI", "Wave", undefined);

      __checkObsolete__(['_decorator', 'CCBoolean', 'CCFloat', 'CCInteger', 'Component', 'Vec2']);

      ({
        ccclass,
        property,
        executeInEditMode,
        menu
      } = _decorator);

      _export("WaveTrack", WaveTrack = (_dec = ccclass('WaveTrack'), _dec2 = property(CCInteger), _dec3 = property(CCFloat), _dec4 = property(CCFloat), _dec5 = property(CCFloat), _dec(_class = (_class2 = class WaveTrack {
        constructor() {
          _initializerDefineProperty(this, "id", _descriptor, this);

          _initializerDefineProperty(this, "speed", _descriptor2, this);

          _initializerDefineProperty(this, "accelerate", _descriptor3, this);

          _initializerDefineProperty(this, "Interval", _descriptor4, this);
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "id", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "speed", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "accelerate", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "Interval", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      })), _class2)) || _class));

      _export("WaveTrackGroup", WaveTrackGroup = (_dec6 = ccclass('WaveTrackGroup'), _dec7 = property(CCInteger), _dec8 = property(CCInteger), _dec9 = property(CCInteger), _dec10 = property([WaveTrack]), _dec6(_class4 = (_class5 = class WaveTrackGroup {
        constructor() {
          _initializerDefineProperty(this, "type", _descriptor5, this);

          _initializerDefineProperty(this, "loopNum", _descriptor6, this);

          _initializerDefineProperty(this, "formIndex", _descriptor7, this);

          _initializerDefineProperty(this, "tracks", _descriptor8, this);
        }

      }, (_descriptor5 = _applyDecoratedDescriptor(_class5.prototype, "type", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class5.prototype, "loopNum", [_dec8], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      }), _descriptor7 = _applyDecoratedDescriptor(_class5.prototype, "formIndex", [_dec9], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      }), _descriptor8 = _applyDecoratedDescriptor(_class5.prototype, "tracks", [_dec10], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return [];
        }
      })), _class5)) || _class4));

      _export("Wave", Wave = (_dec11 = ccclass('Wave'), _dec12 = menu("怪物/波次"), _dec13 = executeInEditMode(), _dec14 = property({
        type: _crd && WaveData === void 0 ? (_reportPossibleCrUseOfWaveData({
          error: Error()
        }), WaveData) : WaveData
      }), _dec11(_class7 = _dec12(_class7 = _dec13(_class7 = (_class8 = class Wave extends Component {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "waveData", _descriptor9, this);

          /*
           * 以下是新的wave逻辑, 旧的逻辑主要在WaveManager与EnemyWave
           */
          this._isCompleted = false;
          this._waveElapsedTime = 0;
          this._nextSpawnTime = 0;
          this._totalWeight = 0;
          // 这个参数可能是时间，也可能是数量，取决于waveData.waveCompletion
          this._waveCompleteParam = 0;
          // 以下两个是用在waveCompletion == SpawnCount时的队列
          this._nextSpawnIndex = 0;
          this._spawnQueue = [];
        }

        // 当前波次是否已完成
        get isCompleted() {
          return this._isCompleted;
        }

        _reset() {
          this._isCompleted = false;
          this._waveElapsedTime = 0;
          this._nextSpawnTime = 0;
          this._nextSpawnIndex = 0;
          this._spawnQueue.length = 0; // this._spawnQueue = this.waveData.planeList;
        }

        onLoad() {
          if (this.waveData && this.waveData.spawnOrder === (_crd && eSpawnOrder === void 0 ? (_reportPossibleCrUseOfeSpawnOrder({
            error: Error()
          }), eSpawnOrder) : eSpawnOrder).Random) {
            this._totalWeight = 0; // add up _totalWeight if is random

            this.waveData.spawnGroup.forEach(group => {
              this._totalWeight += group.weight;
              group.selfWeight = this._totalWeight;
            });
          }
        }

        trigger() {
          this._reset(); // 对于固定数量的波次，可以预先生成队列，每次从里面取即可


          if (this.waveData) {
            this._waveCompleteParam = this.waveData.waveCompletionParam.eval();

            if (this.waveData.waveCompletion === (_crd && eWaveCompletion === void 0 ? (_reportPossibleCrUseOfeWaveCompletion({
              error: Error()
            }), eWaveCompletion) : eWaveCompletion).SpawnCount) {
              if (this.waveData.spawnOrder === (_crd && eSpawnOrder === void 0 ? (_reportPossibleCrUseOfeSpawnOrder({
                error: Error()
              }), eSpawnOrder) : eSpawnOrder).Random) {
                for (let i = 0; i < this._waveCompleteParam; i++) {
                  const randomWeight = Math.random() * this._totalWeight;

                  for (const group of this.waveData.spawnGroup) {
                    if (randomWeight <= group.selfWeight) {
                      this._spawnQueue.push(group.planeID);

                      break;
                    }
                  }
                }
              } else {
                for (let i = 0; i < this._waveCompleteParam; i++) {
                  // 通过取余实现循环
                  this._spawnQueue.push(this.waveData.spawnGroup[i % this.waveData.spawnGroup.length].planeID);
                }
              }
            }
          }
        } // tick wave


        tick(dtInMiliseconds) {
          if (this._isCompleted) return;
          this._waveElapsedTime += dtInMiliseconds;

          if (this.waveData.waveCompletion === (_crd && eWaveCompletion === void 0 ? (_reportPossibleCrUseOfeWaveCompletion({
            error: Error()
          }), eWaveCompletion) : eWaveCompletion).SpawnCount) {
            // 产出固定数量的波次
            if (this._waveElapsedTime >= this._nextSpawnTime) {
              if (!this.spawnFromQueue()) {
                this._isCompleted = true;
              }
            }
          } else {
            // 完全根据时间的波次
            if (this._waveElapsedTime >= this._waveCompleteParam) {
              this._isCompleted = true;
            } else {
              if (this._waveElapsedTime >= this._nextSpawnTime) {
                this.spawnFromGroup();
              }
            }
          }
        }

        spawnFromQueue() {
          if (this._nextSpawnIndex >= this._spawnQueue.length) {
            return false;
          }

          this.spawnSingleFromQueue(this._nextSpawnIndex++);
          this._nextSpawnTime = this._waveElapsedTime + this.waveData.spawnInterval.eval();
          return true;
        }

        spawnSingleFromQueue(index) {
          if (index >= this._spawnQueue.length) {
            return;
          }

          let spawnPos = this.waveData.spawnPos;
          let spawnAngle = this.waveData.spawnAngle.eval(); // let spawnSpeed = this.waveData.spawnSpeed.eval();

          this.createPlane(this._spawnQueue[index], spawnPos, spawnAngle);
        }

        spawnFromGroup() {
          let spawnPos = this.waveData.spawnPos;
          let spawnAngle = this.waveData.spawnAngle.eval(); // let spawnSpeed = this.waveData.spawnSpeed.eval();

          if (this.waveData.spawnOrder === (_crd && eSpawnOrder === void 0 ? (_reportPossibleCrUseOfeSpawnOrder({
            error: Error()
          }), eSpawnOrder) : eSpawnOrder).Random) {
            const randomWeight = Math.random() * this._totalWeight;

            for (const group of this.waveData.spawnGroup) {
              if (randomWeight <= group.selfWeight) {
                this.createPlane(group.planeID, spawnPos, spawnAngle);
                break;
              }
            }
          } else {
            this.createPlane(this.waveData.spawnGroup[this._nextSpawnIndex++ % this.waveData.spawnGroup.length].planeID, spawnPos, spawnAngle);
          }
        }

        async createPlane(planeId, pos, angle) {
          let enemy = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).enemyManager.addPlane(planeId, null);

          if (enemy) {
            // enemy.initTrack(this.waveData.trackGroups, this.waveData.liveParam, spawnPos.x, spawnPos.y);
            // enemy.setStandByTime(0);
            // console.log("createPlane", planeId, pos, angle, speed);
            const nodePosition = this.node.worldPosition;
            enemy.setPos(nodePosition.x + pos.x, nodePosition.y + pos.y); // enemy.setPos(pos.x, pos.y);
            // enemy.initMove(speed, angle, this.waveData.delayDestroy);
          }
        }

      }, (_descriptor9 = _applyDecoratedDescriptor(_class8.prototype, "waveData", [_dec14], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return new (_crd && WaveData === void 0 ? (_reportPossibleCrUseOfWaveData({
            error: Error()
          }), WaveData) : WaveData)();
        }
      })), _class8)) || _class7) || _class7) || _class7));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=fd7f5bbc13425698edb92d84e45991d4e1cf1e4a.js.map