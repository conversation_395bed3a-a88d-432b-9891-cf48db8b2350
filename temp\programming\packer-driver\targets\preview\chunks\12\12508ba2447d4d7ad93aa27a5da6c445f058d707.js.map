{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/AnnouncementUI.ts"], "names": ["_decorator", "Node", "RichText", "UITransform", "WebView", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "BundleName", "ButtonPlus", "ccclass", "property", "AnnouncementUI", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "Top", "getBundleName", "Home", "getUIOption", "isClickBgCloseUI", "onLoad", "btnOK", "addClick", "onOKClick", "closeUI", "onShow", "messgae", "webview", "evaluateJS", "webviewTransform", "node", "getComponent", "setContentSize", "wid", "content", "width", "hei", "richText", "height", "onHide", "onClose"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,O,OAAAA,O;;AACzCC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U;;gCAGjBY,c,WADZF,OAAO,CAAC,gBAAD,C,UAEHC,QAAQ,CAACV,IAAD,C,UAGRU,QAAQ;AAAA;AAAA,mC,UAGRA,QAAQ,CAACT,QAAD,C,UAGRS,QAAQ,CAACP,OAAD,C,2BAXb,MACaQ,cADb;AAAA;AAAA,4BAC2C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAanB,eAANC,MAAM,GAAW;AAAE,iBAAO,0BAAP;AAAoC;;AAC/C,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,GAAf;AAAoB;;AAC7B,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,IAAlB;AAAwB;;AACvC,eAAXC,WAAW,GAAU;AAC/B,iBAAO;AAAEC,YAAAA,gBAAgB,EAAE;AAApB,WAAP;AACH;;AAESC,QAAAA,MAAM,GAAS;AACrB,eAAKC,KAAL,CAAYC,QAAZ,CAAqB,KAAKC,SAA1B,EAAqC,IAArC;AACH;;AACKA,QAAAA,SAAS,GAAG;AAAA;AACd;AAAA;AAAA,gCAAMC,OAAN,CAAcZ,cAAd;AADc;AAEjB;;AAEKa,QAAAA,MAAM,CAACC,OAAD,EAAiC;AAAA;;AAAA;AACzC,gBAAI,KAAI,CAACC,OAAT,EAAkB;AACd;AACA,cAAA,KAAI,CAACA,OAAL,CAAaC,UAAb,0KAFc,CAQd;;;AACA,kBAAMC,gBAAgB,GAAG,KAAI,CAACF,OAAL,CAAaG,IAAb,CAAkBC,YAAlB,CAA+B5B,WAA/B,CAAzB;;AACA,kBAAI0B,gBAAJ,EAAsB;AAClBA,gBAAAA,gBAAgB,CAACG,cAAjB,CAAgC,GAAhC,EAAqC,GAArC;AACH;AACJ;;AAED,gBAAIC,GAAG,GAAG,KAAI,CAACC,OAAL,CAAcH,YAAd,CAA2B5B,WAA3B,EAAyCgC,KAAnD;;AACA,gBAAIC,GAAG,GAAG,KAAI,CAACC,QAAL,CAAeN,YAAf,CAA4B5B,WAA5B,EAA0CmC,MAApD;;AACAF,YAAAA,GAAG,GAAGA,GAAG,GAAG,GAAN,GAAY,GAAZ,GAAkBA,GAAxB;;AACA,YAAA,KAAI,CAACF,OAAL,CAAcH,YAAd,CAA2B5B,WAA3B,EAAyC6B,cAAzC,CAAwDC,GAAxD,EAA6DG,GAA7D;AAnByC;AAoB5C;;AACKG,QAAAA,MAAM,GAAkB;AAAA;AAC7B;;AACKC,QAAAA,OAAO,GAAkB;AAAA;AAE9B;;AApDsC,O;;;;;iBAEhB,I;;;;;;;iBAGI,I;;;;;;;iBAGC,I;;;;;;;iBAGF,I", "sourcesContent": ["import { _decorator, Node, RichText, UITransform, WebView } from 'cc';\r\nimport { BaseUI, UILayer, UIMgr, UIOpt } from \"db://assets/scripts/core/base/UIMgr\";\r\nimport { BundleName } from '../../const/BundleConst';\r\nimport { ButtonPlus } from './components/button/ButtonPlus';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('AnnouncementUI')\r\nexport class AnnouncementUI extends BaseUI {\r\n    @property(Node)\r\n    content: Node | null = null;\r\n\r\n    @property(ButtonPlus)\r\n    btnOK: ButtonPlus | null = null;\r\n\r\n    @property(RichText)\r\n    richText: RichText | null = null;\r\n\r\n    @property(WebView)\r\n    webview: WebView | null = null;\r\n\r\n    public static getUrl(): string { return \"prefab/ui/AnnouncementUI\"; }\r\n    public static getLayer(): UILayer { return UILayer.Top }\r\n    public static getBundleName(): string { return BundleName.Home }\r\n    public static getUIOption(): UIOpt {\r\n        return { isClickBgCloseUI: false }\r\n    }\r\n\r\n    protected onLoad(): void {\r\n        this.btnOK!.addClick(this.onOKClick, this);\r\n    }\r\n    async onOKClick() {\r\n        UIMgr.closeUI(AnnouncementUI);\r\n    }\r\n\r\n    async onShow(messgae: string): Promise<void> {\r\n        if (this.webview) {\r\n            // 注入 JavaScript 隐藏滚动条\r\n            this.webview.evaluateJS(`\r\n            document.body.style.overflow = 'hidden';\r\n            document.body.style.width = '700px';\r\n            document.body.style.height = '800px';\r\n        `);\r\n\r\n            // 设置 WebView 的节点大小\r\n            const webviewTransform = this.webview.node.getComponent(UITransform);\r\n            if (webviewTransform) {\r\n                webviewTransform.setContentSize(700, 800);\r\n            }\r\n        }\r\n\r\n        let wid = this.content!.getComponent(UITransform)!.width;\r\n        let hei = this.richText!.getComponent(UITransform)!.height;\r\n        hei = hei < 800 ? 800 : hei;\r\n        this.content!.getComponent(UITransform)!.setContentSize(wid, hei);\r\n    }\r\n    async onHide(): Promise<void> {\r\n    }\r\n    async onClose(): Promise<void> {\r\n\r\n    }\r\n}\r\n"]}