{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/GameMapRun.ts"], "names": ["_decorator", "Component", "JsonAsset", "resources", "view", "MyApp", "LevelData", "GameIns", "GameEnum", "LevelBaseUI", "ccclass", "property", "RAND_STRATEGY", "PRELOAD_STATE", "MAX_LEVEL_COUNT", "GameMapRun", "_levelList", "_chapterData", "undefined", "_lastSelectedId", "_initOver", "_preloadState", "NONE", "_levelLoadIndex", "_levelIndex", "_levelUIInfoList", "_levelTotalDuration", "_levelTotalHeight", "_levelDistance", "_levelDuration", "_levelSpeed", "MapSpeed", "ViewTop", "getVisibleSize", "height", "onLoad", "instance", "_initLevelList", "chapterID", "lubanTables", "TbResChapter", "get", "console", "log", "levelGroupList", "_randomSelection", "strategyList", "levelGroupCount", "strategy", "length", "levelGroupID", "levelGroupData", "TbResLevelGroup", "push", "normSTList", "normLevelCount", "normLevelST", "bossSTList", "bossLevelCount", "bossLevelST", "initData", "reset", "_loadNextLevelPrefab", "_initCurLevelData", "levelBaseUI", "node", "getComponent", "Promise", "resolve", "check", "background<PERSON>ayer", "backgrounds", "setTimeout", "CHECK", "initBattle", "update", "deltaTime", "battleManager", "animSpeed", "gameState", "gameRuleManager", "GameState", "Battle", "<PERSON><PERSON><PERSON>", "Ready", "WillOver", "Idle", "Over", "_tick", "_checkNextLevelLoading", "clear", "STList", "count", "results", "WEIGHT_PRUE", "totalWeight", "reduce", "sum", "item", "Weight", "i", "randomIndex", "Math", "floor", "random", "ID", "randomValue", "cumulativeWeight", "WEIGHT_NO_REPEAT", "tempList", "lastSelectedIndex", "findIndex", "selectedIndex", "j", "selectedId", "ORDER", "currentIndex", "bFristLevel", "remainingLevels", "levelCount", "levelsToLoad", "min", "LOADED", "loadIndex", "loadPromises", "levelID", "levelConfig", "TbResLevel", "prefabName", "prefab", "loadPromise", "load", "err", "error", "addComponent", "levelInfo", "levelIndex", "levelData", "fromJSON", "json", "levelPrefab", "levelBaseUIInfo", "_initLevelInfo", "totalTime", "speed", "all", "then", "catch", "LOADING", "getLevelTotalHeightByIndex", "levelBase", "switchLevel", "tick", "targetDistance", "finishedLevel", "shift", "id", "time"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;;AAC7CC,MAAAA,K,iBAAAA,K;;AAEAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,W,iBAAAA,W;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U;;AAEzBY,MAAAA,a,0BAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;eAAAA,a;QAAAA,a;;AAMAC,MAAAA,a,0BAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;eAAAA,a;QAAAA,a;;AAOCC,MAAAA,e,GAAkB,C,EAAG;;AAQ3B;AACA;AACA;AACA;yBAEqBC,U,WADpBL,OAAO,CAAC,YAAD,C,2BAAR,MACqBK,UADrB,SACwCd,SADxC,CACkD;AAAA;AAAA;AAAA,eAItCe,UAJsC,GAIf,EAJe;AAAA,eAKtCC,YALsC,GAKCC,SALD;AAAA,eAMtCC,eANsC,GAMZ,CAAC,CANW;AAAA,eAQtCC,SARsC,GAQ1B,KAR0B;AAAA,eAStCC,aATsC,GAStBR,aAAa,CAACS,IATQ;AAAA,eAWtCC,eAXsC,GAWpB,CAXoB;AAWjB;AAXiB,eAYtCC,WAZsC,GAYxB,CAZwB;AAYrB;AAZqB,eAatCC,gBAbsC,GAaK,EAbL;AAaS;AAbT,eAetCC,mBAfsC,GAeR,CAfQ;AAeL;AAfK,eAgBtCC,iBAhBsC,GAgBV,CAhBU;AAgBP;AAhBO,eAiBtCC,cAjBsC,GAiBb,CAjBa;AAiBV;AAjBU,eAkBtCC,cAlBsC,GAkBb,CAlBa;AAkBV;AAlBU,eAmBtCC,WAnBsC,GAmBhB,CAnBgB;AAAA;;AAmBb;AAEd,YAARC,QAAQ,GAAW;AAC1B,iBAAO,KAAKD,WAAZ;AACH;;AAEiB,YAAPE,OAAO,GAAW;AACzB,iBAAO5B,IAAI,CAAC6B,cAAL,GAAsBC,MAAtB,GAA+B,CAAC,GAAvC;AACH;;AAEDC,QAAAA,MAAM,GAAG;AACLpB,UAAAA,UAAU,CAACqB,QAAX,GAAsB,IAAtB;AACH,SA/B6C,CAiC9C;;;AACQC,QAAAA,cAAc,CAACC,SAAD,EAAoB;AACtC,eAAKrB,YAAL,GAAoB;AAAA;AAAA,8BAAMsB,WAAN,CAAkBC,YAAlB,CAA+BC,GAA/B,CAAmC,KAAnC,CAApB;AAA8D;;AAAe;;AAC7E,cAAI,KAAKxB,YAAL,IAAqB,IAAzB,EAA+B;AAC3ByB,YAAAA,OAAO,CAACC,GAAR,CAAY,YAAZ,EAA0B,sBAA1B;AACA;AACH,WALqC,CAOtC;;;AACA,cAAMC,cAAc,GAAG,KAAKC,gBAAL,CAAsB,KAAK5B,YAAL,CAAkB6B,YAAxC,EAAsD,KAAK7B,YAAL,CAAkB8B,eAAxE,EAAyF,KAAK9B,YAAL,CAAkB+B,QAA3G,CAAvB;;AACA,cAAIJ,cAAc,CAACK,MAAf,KAA0B,CAA9B,EAAiC;AAC7BP,YAAAA,OAAO,CAACC,GAAR,CAAY,YAAZ,EAA0B,yBAA1B;AACA;AACH,WAZqC,CActC;;;AACA,eAAK3B,UAAL,GAAkB,EAAlB;;AACA,eAAK,IAAMkC,YAAX,IAA2BN,cAA3B,EAA2C;AACvC,gBAAMO,cAAc,GAAG;AAAA;AAAA,gCAAMZ,WAAN,CAAkBa,eAAlB,CAAkCX,GAAlC,CAAsCS,YAAtC,CAAvB;;AACA,gBAAIC,cAAc,IAAI,IAAtB,EAA4B;AACxBT,cAAAA,OAAO,CAACC,GAAR,CAAY,YAAZ,EAA0B,yBAA1B;AACA;AACH;;AAED,iBAAK3B,UAAL,CAAgBqC,IAAhB,CAAqB,GAAG,KAAKR,gBAAL,CAAsBM,cAAc,CAACG,UAArC,EAAiDH,cAAc,CAACI,cAAhE,EAAgFJ,cAAc,CAACK,WAA/F,CAAxB;;AACA,iBAAKxC,UAAL,CAAgBqC,IAAhB,CAAqB,GAAG,KAAKR,gBAAL,CAAsBM,cAAc,CAACM,UAArC,EAAiDN,cAAc,CAACO,cAAhE,EAAgFP,cAAc,CAACQ,WAA/F,CAAxB;AACH;;AACDjB,UAAAA,OAAO,CAACC,GAAR,CAAY,YAAZ,EAA0B,cAA1B,EAA0C,KAAK3B,UAA/C;AACH;;AAEK4C,QAAAA,QAAQ,CAACtB,SAAD,EAAmC;AAAA;;AAAA;AAC7C,YAAA,KAAI,CAACuB,KAAL;;AACA,YAAA,KAAI,CAACxB,cAAL,CAAoBC,SAApB;;AACA,kBAAM,KAAI,CAACwB,oBAAL,CAA0B,IAA1B,CAAN;;AACA,YAAA,KAAI,CAACC,iBAAL;;AACA,gBAAMC,WAAW,GAAG,KAAI,CAACC,IAAL,CAAUC,YAAV;AAAA;AAAA,2CAApB;;AACA,gBAAIF,WAAJ,EAAiB;AACb,oBAAM,IAAIG,OAAJ,CAAmBC,OAAD,IAAa;AACjC;AACA,oBAAMC,KAAK,GAAG,MAAM;AAChB,sBAAIL,WAAW,CAACM,eAAZ,CAA6BC,WAA7B,CAA0CtB,MAA1C,GAAmD,CAAvD,EAA0D;AACtDmB,oBAAAA,OAAO;AACV,mBAFD,MAEO;AACHI,oBAAAA,UAAU,CAACH,KAAD,EAAQ,GAAR,CAAV,CADG,CACqB;AAC3B;AACJ,iBAND;;AAOAA,gBAAAA,KAAK;AACR,eAVK,CAAN;AAWH;;AACD,YAAA,KAAI,CAACjD,SAAL,GAAiB,IAAjB;AACA,YAAA,KAAI,CAACC,aAAL,GAAqBR,aAAa,CAAC4D,KAAnC;AApB6C;AAqBhD;;AAGDC,QAAAA,UAAU,GAAG,CAEZ;;AAEDC,QAAAA,MAAM,CAACC,SAAD,EAAoB;AACtBA,UAAAA,SAAS,GAAGA,SAAS,GAAG;AAAA;AAAA,kCAAQC,aAAR,CAAsBC,SAA9C;;AACA,cAAIF,SAAS,GAAG,GAAhB,EAAqB;AACjBA,YAAAA,SAAS,GAAG,iBAAZ;AACH;;AAED,cAAMG,SAAS,GAAG;AAAA;AAAA,kCAAQC,eAAR,CAAwBD,SAA1C;;AACA,cACIA,SAAS,KAAK;AAAA;AAAA,oCAASE,SAAT,CAAmBC,MAAjC,IACAH,SAAS,KAAK;AAAA;AAAA,oCAASE,SAAT,CAAmBE,MADjC,IAEAJ,SAAS,KAAK;AAAA;AAAA,oCAASE,SAAT,CAAmBG,KAFjC,IAGAL,SAAS,KAAK;AAAA;AAAA,oCAASE,SAAT,CAAmBI,QAHjC,IAIAN,SAAS,KAAK;AAAA;AAAA,oCAASE,SAAT,CAAmBK,IAJjC,IAKAP,SAAS,KAAK;AAAA;AAAA,oCAASE,SAAT,CAAmBM,IANrC,EAOE;AACE;AACH;;AAED,cAAI,CAAC,KAAKnE,SAAV,EAAqB;AACjB;AACH;;AAED,eAAKoE,KAAL,CAAWZ,SAAX;;AACA,eAAKa,sBAAL;AACH;;AAEDC,QAAAA,KAAK,GAAG,CACJ;AAEH;;AAED7B,QAAAA,KAAK,GAAG;AACJ;AACA;AACA,eAAKzC,SAAL,GAAiB,KAAjB;AACA,eAAKG,eAAL,GAAuB,CAAvB;AACA,eAAKC,WAAL,GAAmB,CAAnB;AACA,eAAKI,cAAL,GAAsB,CAAtB;AACA,eAAKF,mBAAL,GAA2B,CAA3B;AACA,eAAKC,iBAAL,GAAyB,CAAzB;AACA,eAAKE,cAAL,GAAsB,CAAtB;AACA,eAAKb,UAAL,GAAkB,EAAlB;AACA,eAAKS,gBAAL,GAAwB,EAAxB;AACA,eAAKR,YAAL,GAAoBC,SAApB;AACA,eAAKC,eAAL,GAAuB,CAAC,CAAxB;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACY0B,QAAAA,gBAAgB,CAAC8C,MAAD,EAAyBC,KAAzB,EAAwC5C,QAAxC,EAA2E;AAC/F,cAAI2C,MAAM,CAAC1C,MAAP,KAAkB,CAAlB,IAAuB2C,KAAK,IAAI,CAApC,EAAuC,OAAO,EAAP;AAEvC,cAAMC,OAAiB,GAAG,EAA1B;;AACA,cAAI7C,QAAQ,KAAKpC,aAAa,CAACkF,WAA/B,EAA4C;AACxC;AACA,gBAAMC,WAAW,GAAGJ,MAAM,CAACK,MAAP,CAAc,CAACC,GAAD,EAAMC,IAAN,KAAeD,GAAG,GAAGC,IAAI,CAACC,MAAxC,EAAgD,CAAhD,CAApB,CAFwC,CAIxC;;AACA,gBAAIJ,WAAW,KAAK,CAApB,EAAuB;AACnB,mBAAK,IAAIK,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGR,KAApB,EAA2BQ,CAAC,EAA5B,EAAgC;AAC5B,oBAAMC,WAAW,GAAGC,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACE,MAAL,KAAgBb,MAAM,CAAC1C,MAAlC,CAApB;AACA4C,gBAAAA,OAAO,CAACxC,IAAR,CAAasC,MAAM,CAACU,WAAD,CAAN,CAAoBI,EAAjC;AACH;;AACD,qBAAOZ,OAAP;AACH,aAXuC,CAaxC;;;AACA,iBAAK,IAAIO,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAGR,KAApB,EAA2BQ,EAAC,EAA5B,EAAgC;AAC5B;AACA,kBAAMM,WAAW,GAAGJ,IAAI,CAACE,MAAL,KAAgBT,WAApC,CAF4B,CAI5B;;AACA,kBAAIY,gBAAgB,GAAG,CAAvB;;AACA,mBAAK,IAAMT,IAAX,IAAmBP,MAAnB,EAA2B;AACvBgB,gBAAAA,gBAAgB,IAAIT,IAAI,CAACC,MAAzB;;AACA,oBAAIO,WAAW,GAAGC,gBAAlB,EAAoC;AAChCd,kBAAAA,OAAO,CAACxC,IAAR,CAAa6C,IAAI,CAACO,EAAlB;AACA;AACH;AACJ;AACJ;AACJ,WA5BD,MA4BO,IAAIzD,QAAQ,KAAKpC,aAAa,CAACgG,gBAA/B,EAAiD;AACpD;AACA,gBAAMb,YAAW,GAAGJ,MAAM,CAACK,MAAP,CAAc,CAACC,GAAD,EAAMC,IAAN,KAAeD,GAAG,GAAGC,IAAI,CAACC,MAAxC,EAAgD,CAAhD,CAApB,CAFoD,CAIpD;;;AACA,gBAAIJ,YAAW,KAAK,CAApB,EAAuB;AACnB,mBAAK,IAAIK,GAAC,GAAG,CAAb,EAAgBA,GAAC,GAAGR,KAApB,EAA2BQ,GAAC,EAA5B,EAAgC;AAC5B,oBAAIC,YAAW,GAAGC,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACE,MAAL,KAAgBb,MAAM,CAAC1C,MAAlC,CAAlB,CAD4B,CAE5B;;;AACA,oBAAImD,GAAC,GAAG,CAAJ,IAAST,MAAM,CAACU,YAAD,CAAN,CAAoBI,EAApB,KAA2BZ,OAAO,CAACO,GAAC,GAAG,CAAL,CAA/C,EAAwD;AACpD;AACAC,kBAAAA,YAAW,GAAG,CAACA,YAAW,GAAG,CAAf,IAAoBV,MAAM,CAAC1C,MAAzC;AACH;;AACD4C,gBAAAA,OAAO,CAACxC,IAAR,CAAasC,MAAM,CAACU,YAAD,CAAN,CAAoBI,EAAjC;AACH;;AACD,qBAAOZ,OAAP;AACH,aAhBmD,CAkBpD;;;AACA,gBAAMgB,QAAQ,GAAG,CAAC,GAAGlB,MAAJ,CAAjB;;AAEA,iBAAK,IAAIS,GAAC,GAAG,CAAb,EAAgBA,GAAC,GAAGR,KAApB,EAA2BQ,GAAC,EAA5B,EAAgC;AAC5B;AACA,kBAAI,KAAKjF,eAAL,KAAyB,CAAC,CAA9B,EAAiC;AAC7B,oBAAM2F,iBAAiB,GAAGD,QAAQ,CAACE,SAAT,CAAmBb,IAAI,IAAIA,IAAI,CAACO,EAAL,KAAY,KAAKtF,eAA5C,CAA1B;;AACA,oBAAI2F,iBAAiB,KAAK,CAAC,CAAvB,IAA4BA,iBAAiB,GAAGD,QAAQ,CAAC5D,MAAT,GAAkB,CAAtE,EAAyE;AACrE;AACA,mBAAC4D,QAAQ,CAACC,iBAAD,CAAT,EAA8BD,QAAQ,CAACC,iBAAiB,GAAG,CAArB,CAAtC,IACI,CAACD,QAAQ,CAACC,iBAAiB,GAAG,CAArB,CAAT,EAAkCD,QAAQ,CAACC,iBAAD,CAA1C,CADJ;AAEH;AACJ,eAT2B,CAW5B;;;AACA,kBAAMJ,YAAW,GAAGJ,IAAI,CAACE,MAAL,KAAgBT,YAApC,CAZ4B,CAc5B;;;AACA,kBAAIY,iBAAgB,GAAG,CAAvB;AACA,kBAAIK,aAAa,GAAG,CAAC,CAArB;;AAEA,mBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGJ,QAAQ,CAAC5D,MAA7B,EAAqCgE,CAAC,EAAtC,EAA0C;AACtCN,gBAAAA,iBAAgB,IAAIE,QAAQ,CAACI,CAAD,CAAR,CAAYd,MAAhC;;AACA,oBAAIO,YAAW,GAAGC,iBAAlB,EAAoC;AAChCK,kBAAAA,aAAa,GAAGC,CAAhB;AACA;AACH;AACJ,eAxB2B,CA0B5B;;;AACA,kBAAID,aAAa,KAAK,CAAC,CAAvB,EAA0B;AACtBA,gBAAAA,aAAa,GAAGH,QAAQ,CAAC5D,MAAT,GAAkB,CAAlC;AACH,eA7B2B,CA+B5B;;;AACA,kBAAMiE,UAAU,GAAGL,QAAQ,CAACG,aAAD,CAAR,CAAwBP,EAA3C;AACAZ,cAAAA,OAAO,CAACxC,IAAR,CAAa6D,UAAb,EAjC4B,CAmC5B;;AACA,mBAAK/F,eAAL,GAAuB+F,UAAvB;AACH;AACJ,WA3DM,MA2DA,IAAIlE,QAAQ,KAAKpC,aAAa,CAACuG,KAA/B,EAAsC;AACzC;AACA,gBAAIC,YAAY,GAAG,CAAnB;;AAEA,iBAAK,IAAIhB,GAAC,GAAG,CAAb,EAAgBA,GAAC,GAAGR,KAApB,EAA2BQ,GAAC,EAA5B,EAAgC;AAC5B;AACA,kBAAIT,MAAM,CAACyB,YAAD,CAAN,CAAqBX,EAArB,KAA4B,CAAhC,EAAmC;AAC/BW,gBAAAA,YAAY,GAAG,CAAf,CAD+B,CAG/B;;AACA,uBAAOA,YAAY,GAAGzB,MAAM,CAAC1C,MAAtB,IAAgC0C,MAAM,CAACyB,YAAD,CAAN,CAAqBX,EAArB,KAA4B,CAAnE,EAAsE;AAClEW,kBAAAA,YAAY;AACf,iBAN8B,CAQ/B;;;AACA,oBAAIA,YAAY,IAAIzB,MAAM,CAAC1C,MAA3B,EAAmC;AAC/B;AACH;AACJ,eAd2B,CAgB5B;;;AACA4C,cAAAA,OAAO,CAACxC,IAAR,CAAasC,MAAM,CAACyB,YAAD,CAAN,CAAqBX,EAAlC,EAjB4B,CAmB5B;;AACAW,cAAAA,YAAY,GApBgB,CAsB5B;;AACA,kBAAIA,YAAY,IAAIzB,MAAM,CAAC1C,MAA3B,EAAmC;AAC/BmE,gBAAAA,YAAY,GAAG,CAAf;AACH;AACJ;AACJ;;AAED,iBAAOvB,OAAP;AACH;;AAEa/B,QAAAA,oBAAoB,CAACuD,WAAD,EAA8C;AAAA;;AAAA;AAAA,gBAA7CA,WAA6C;AAA7CA,cAAAA,WAA6C,GAAtB,KAAsB;AAAA;;AAC5E;AACA,gBAAMC,eAAe,GAAGxG,eAAe,GAAG,MAAI,CAACW,gBAAL,CAAsBwB,MAAhE;AACAP,YAAAA,OAAO,CAACC,GAAR,CAAY,YAAZ,EAA0B,cAA1B,EAA0C,MAAI,CAAC1B,YAAL,CAAmBsG,UAA7D,EAAyE,uBAAzE,EAAkG,MAAI,CAAChG,eAAvG,EAAwH,kBAAxH,EAA4I+F,eAA5I;AACA,gBAAME,YAAY,GAAGlB,IAAI,CAACmB,GAAL,CAAS3G,eAAT,EAA0BwG,eAA1B,CAArB;;AAEA,gBAAIE,YAAY,IAAI,CAApB,EAAuB;AACnB9E,cAAAA,OAAO,CAACC,GAAR,CAAY,YAAZ,EAA0B,mBAA1B;AACA,cAAA,MAAI,CAACtB,aAAL,GAAqBR,aAAa,CAAC6G,MAAnC;AACA;AACH;;AAED,gBAAIC,SAAS,GAAG,MAAI,CAACpG,eAArB;AACA,gBAAMqG,YAA6B,GAAG,EAAtC;;AAb4E,2CAcrC;AACnC,kBAAMC,OAAO,GAAG,MAAI,CAAC7G,UAAL,CAAgB2G,SAAS,GAAGvB,CAA5B,CAAhB;AACA1D,cAAAA,OAAO,CAACC,GAAR,CAAY,YAAZ,EAA0B,mBAA1B,EAA+CgF,SAAS,GAAGvB,CAA3D;AACA,kBAAM0B,WAAW,GAAG;AAAA;AAAA,kCAAMvF,WAAN,CAAkBwF,UAAlB,CAA6BtF,GAA7B,CAAiCoF,OAAjC,CAApB;;AACA,kBAAIC,WAAW,IAAI,IAAnB,EAAyB;AACrBpF,gBAAAA,OAAO,CAACC,GAAR,CAAY,YAAZ,EAA0B,uBAA1B,EAAmDkF,OAAnD;AADqB;AAAA;AAAA;AAGxB;;AAED,kBAAMG,UAAU,mBAAiBF,WAAW,CAACG,MAA7C;AACA,kBAAMC,WAAW,GAAG,IAAI/D,OAAJ,CAAmBC,OAAD,IAAa;AAC/CjE,gBAAAA,SAAS,CAACgI,IAAV,CAAeH,UAAf,EAA2B9H,SAA3B,iCAAsC,WAAOkI,GAAP,EAAYH,MAAZ,EAAuB;AACzD,sBAAIG,GAAJ,EAAS;AACL1F,oBAAAA,OAAO,CAAC2F,KAAR,CAAc,YAAd,EAA4B,YAA5B,EAA0CD,GAA1C;AACAhE,oBAAAA,OAAO;AACP;AACH;;AAED,sBAAIJ,WAAW,GAAG,MAAI,CAACC,IAAL,CAAUC,YAAV;AAAA;AAAA,iDAAlB,CAPyD,CAQzD;;;AACA,sBAAIF,WAAW,IAAI,IAAnB,EAAyB;AACrBA,oBAAAA,WAAW,GAAG,MAAI,CAACC,IAAL,CAAUqE,YAAV;AAAA;AAAA,mDAAd;AACH;;AAED,sBAAMC,SAAS,GAAG;AACdV,oBAAAA,OAAO,EAAEA,OADK;AAEdN,oBAAAA,UAAU,EAAE,MAAI,CAACtG,YAAL,CAAmBsG,UAFjB;AAGdiB,oBAAAA,UAAU,EAAEb,SAAS,GAAGvB;AAHV,mBAAlB;AAMA,sBAAIqC,SAAS,GAAG;AAAA;AAAA,8CAAUC,QAAV,CAAmBT,MAAnB,oBAAmBA,MAAM,CAAEU,IAA3B,CAAhB;;AACA,sBAAItB,WAAJ,EAAiB;AACb,0BAAMrD,WAAW,CAAC4E,WAAZ,CAAwBH,SAAxB,EAAmCF,SAAnC,EAA8ClB,WAA9C,CAAN;AACH,mBAFD,MAEO;AACHrD,oBAAAA,WAAW,CAAC4E,WAAZ,CAAwBH,SAAxB,EAAmCF,SAAnC,EAA8ClB,WAA9C;AACH,mBAxBwD,CAyBzD;;;AACA,sBAAIwB,eAAe,GAAG,MAAI,CAACC,cAAL,CAAoBjB,OAApB,EAA6BY,SAAS,CAACM,SAAvC,EAAkDN,SAAS,CAACnE,eAAV,CAA0B0E,KAA5E,CAAtB;;AACA,kBAAA,MAAI,CAACvH,gBAAL,CAAsB4B,IAAtB,CAA2BwF,eAA3B;;AACAnG,kBAAAA,OAAO,CAACC,GAAR,CAAY,YAAZ,EAA0B,OAA1B,EAAmCkF,OAAnC,EAA4C,YAA5C,EAA0DU,SAAS,CAACC,UAApE,EAAgF,+BAAhF,EAAiH,MAAI,CAAC/G,gBAAL,CAAsBwB,MAAvI;AAEAmB,kBAAAA,OAAO;AACV,iBA/BD;AAgCH,eAjCmB,CAApB;AAmCAwD,cAAAA,YAAY,CAACvE,IAAb,CAAkB6E,WAAlB;AACH,aA5D2E;AAAA;;AAc5E,iBAAK,IAAI9B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGoB,YAApB,EAAkCpB,CAAC,EAAnC;AAAA;AAAA;AAAA;;AAgDA,gBAAIiB,WAAJ,EAAiB;AACb,oBAAMlD,OAAO,CAAC8E,GAAR,CAAYrB,YAAZ,CAAN,CADa,CACoB;;AACjC,cAAA,MAAI,CAACvG,aAAL,GAAqBR,aAAa,CAAC6G,MAAnC;AACA,cAAA,MAAI,CAACnG,eAAL,IAAwBiG,YAAxB;AACH,aAJD,MAIO;AACH,oBAAMrD,OAAO,CAAC8E,GAAR,CAAYrB,YAAZ,EACDsB,IADC,CACI,MAAM;AACR,gBAAA,MAAI,CAAC7H,aAAL,GAAqBR,aAAa,CAAC6G,MAAnC;AACA,gBAAA,MAAI,CAACnG,eAAL,IAAwBiG,YAAxB;AACA9E,gBAAAA,OAAO,CAACC,GAAR,CAAY,YAAZ,EAA0B,SAA1B;AACH,eALC,EAMDwG,KANC,CAMMf,GAAD,IAAS;AACZ1F,gBAAAA,OAAO,CAAC2F,KAAR,CAAc,UAAd,EAA0BD,GAA1B;AACH,eARC,CAAN;AASH;;AAED,gBAAI,MAAI,CAAC7G,eAAL,IAAwB,MAAI,CAACN,YAAL,CAAmBsG,UAA/C,EAA2D;AACvD,cAAA,MAAI,CAAChG,eAAL,GAAuB,CAAvB;AACAmB,cAAAA,OAAO,CAACC,GAAR,CAAY,YAAZ,EAA0B,kBAA1B;AACH;AAjF2E;AAkF/E;;AAEO8C,QAAAA,sBAAsB,GAAG;AAC7B,cAAI,KAAKpE,aAAL,KAAuBR,aAAa,CAAC4D,KAArC,IAA8C,KAAKhD,gBAAL,CAAsBwB,MAAtB,GAA+BnC,eAAjF,EAAkG;AAC9F4B,YAAAA,OAAO,CAACC,GAAR,CAAY,YAAZ,EAA0B,uBAA1B,EAAmD,KAAKnB,WAAxD,EAAqE,uBAArE,EAA8F,KAAKD,eAAnG;AACA,iBAAKF,aAAL,GAAqBR,aAAa,CAACuI,OAAnC;;AACA,iBAAKtF,oBAAL,GAA4BqF,KAA5B,CAAkCf,GAAG,IAAI;AACrC1F,cAAAA,OAAO,CAAC2F,KAAR,CAAc,YAAd,EAA4B,6BAA5B,EAA2DD,GAA3D;AACH,aAFD;AAGH;AACJ;;AAEOrE,QAAAA,iBAAiB,GAAG;AACxB,cAAI,KAAKvC,WAAL,IAAoB,KAAKP,YAAL,CAAmBsG,UAA3C,EAAuD;AACnD7E,YAAAA,OAAO,CAAC2F,KAAR,CAAc,YAAd,EAA4B,mBAA5B;AACA;AACH;;AAED,cAAMrE,WAAW,GAAG,KAAKC,IAAL,CAAUC,YAAV;AAAA;AAAA,yCAApB;AACA,eAAKvC,iBAAL,GAAyBqC,WAAW,CAAEqF,0BAAb,CAAwC,KAAK7H,WAA7C,CAAzB;AAEA,cAAM8H,SAAS,GAAG,KAAK7H,gBAAL,CAAsB,CAAtB,CAAlB;;AACA,cAAI6H,SAAJ,EAAe;AACXtF,YAAAA,WAAW,CAAEuF,WAAb,CAAyBD,SAAS,CAACN,KAAnC,EAA0CM,SAAS,CAACP,SAApD,EAA+D,KAAKvH,WAApE;AACA,iBAAKM,WAAL,GAAmBwH,SAAS,CAACN,KAA7B;AACA,iBAAKpH,cAAL,GAAsB,CAAtB;AACA,iBAAKC,cAAL,GAAsB,CAAtB;AAEA,iBAAKR,aAAL,GAAqBR,aAAa,CAAC4D,KAAnC;AACH;AACJ;;AAEOe,QAAAA,KAAK,CAACZ,SAAD,EAAoB;AAE7B,cAAI,KAAKpD,WAAL,IAAoB,KAAKP,YAAL,CAAmBsG,UAA3C,EAAuD;AACnD7E,YAAAA,OAAO,CAAC2F,KAAR,CAAc,YAAd,EAA4B,mBAA5B;AACA;AACH;;AAED,cAAI,KAAK1G,iBAAL,IAA0B,CAA9B,EAAiC;AAC7B;AACH;;AAED,eAAKE,cAAL,IAAuB+C,SAAvB;AACA,eAAKlD,mBAAL,IAA4BkD,SAA5B;AACA,eAAKhD,cAAL,IAAuB,KAAKG,QAAL,GAAgB6C,SAAvC;AAEA,cAAMZ,WAAW,GAAG,KAAKC,IAAL,CAAUC,YAAV;AAAA;AAAA,yCAApB;;AACA,cAAIF,WAAW,IAAI,IAAnB,EAAyB;AACrB;AACAA,YAAAA,WAAW,CAACwF,IAAZ,CAAiB5E,SAAjB;AACA,gBAAI6E,cAAc,GAAG,KAAK9H,iBAA1B;;AAEA,gBAAI,KAAKC,cAAL,IAAuB6H,cAA3B,EAA2C;AACvC,kBAAMC,aAAa,GAAG,KAAKjI,gBAAL,CAAsBkI,KAAtB,EAAtB;;AACAjH,cAAAA,OAAO,CAACC,GAAR,CAAY,YAAZ,EAA0B,OAA1B,EAAmC,KAAKnB,WAAxC,EAAqD,gCAArD,EAAuF,KAAKC,gBAAL,CAAsBwB,MAA7G;AACA,mBAAKzB,WAAL;;AAEA,kBAAI,KAAKA,WAAL,IAAoB,KAAKP,YAAL,CAAmBsG,UAA3C,EAAuD;AACnD,qBAAK/F,WAAL,GAAmB,CAAnB;AACAkB,gBAAAA,OAAO,CAACC,GAAR,CAAY,YAAZ,EAA0B,KAA1B,EAAiC,KAAK1B,YAAL,CAAmB2I,EAApD,EAAwD,SAAxD;AACH;;AAED,mBAAK7F,iBAAL;AACH;AACJ;AACJ;;AAEO+E,QAAAA,cAAc,CAACjB,OAAD,EAAkBgC,IAAlB,EAAgCb,KAAhC,EAAgE;AAClF,iBAAO;AACHnB,YAAAA,OAAO,EAAEA,OADN;AAEHkB,YAAAA,SAAS,EAAEc,IAFR;AAGHb,YAAAA,KAAK,EAAEA;AAHJ,WAAP;AAKH;;AA9a6C,O,UAEvC5G,Q,GAA8B,I", "sourcesContent": ["import { _decorator, Component, JsonAsset, resources, view } from 'cc';\r\nimport { MyApp } from 'db://assets/bundles/common/script/app/MyApp';\r\nimport { randStrategy, ResChapter } from '../../../autogen/luban/schema';\r\nimport { LevelData } from '../../../leveldata/leveldata';\r\nimport { GameIns } from '../../GameIns';\r\nimport { GameEnum } from '../../const/GameEnum';\r\nimport { LevelBaseUI } from './LevelBaseUI';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\nenum RAND_STRATEGY {\r\n    WEIGHT_PRUE = 1, // 纯权重随机\r\n    WEIGHT_NO_REPEAT = 2, //权重随机，不重复\r\n    ORDER = 3 // 按顺序\r\n}\r\n\r\nenum PRELOAD_STATE {\r\n    NONE = 0,\r\n    CHECK = 1,\r\n    LOADING = 2,\r\n    LOADED = 3\r\n}\r\n\r\nconst MAX_LEVEL_COUNT = 2; // 场景最大关卡数\r\n\r\ninterface LevelBaseUIInfo {\r\n    levelID: number;\r\n    totalTime: number;\r\n    speed: number;\r\n}\r\n\r\n/*\r\n * @description 加载策略：\r\n * 根据当前移动到的关卡，提前加载下一关的关卡（目前只提前加载的关卡数定为1）\r\n*/\r\n@ccclass('GameMapRun')\r\nexport default class GameMapRun extends Component {\r\n\r\n    static instance: GameMapRun | null = null;\r\n\r\n    private _levelList: number[] = [];\r\n    private _chapterData: ResChapter | undefined = undefined\r\n    private _lastSelectedId: number = -1;\r\n\r\n    private _initOver = false;\r\n    private _preloadState = PRELOAD_STATE.NONE;\r\n\r\n    private _levelLoadIndex = 0; // 当前关卡加载索引\r\n    private _levelIndex = 0; // 当前关卡索引（实时移动到的）\r\n    private _levelUIInfoList: Array<LevelBaseUIInfo> = []; //已经加载的关卡的基本信息\r\n\r\n    private _levelTotalDuration: number = 0; // 关卡总持续时间\r\n    private _levelTotalHeight: number = 0; // 关卡总高度\r\n    private _levelDistance: number = 0; // 当前关卡移动的距离\r\n    private _levelDuration: number = 0; // 当前关卡的持续时间\r\n    private _levelSpeed: number = 0; // 当前关卡的移动速度\r\n\r\n    public get MapSpeed(): number {\r\n        return this._levelSpeed;\r\n    }\r\n\r\n    public get ViewTop(): number {\r\n        return view.getVisibleSize().height * -0.5;\r\n    }\r\n\r\n    onLoad() {\r\n        GameMapRun.instance = this;\r\n    }\r\n\r\n    // 根据策略随机出关卡列表\r\n    private _initLevelList(chapterID: number) {\r\n        this._chapterData = MyApp.lubanTables.TbResChapter.get(70001);/*(chapterID)*/;\r\n        if (this._chapterData == null) {\r\n            console.log('GameMapRun', \" chapterData is null\");\r\n            return;\r\n        }\r\n\r\n        // 随机出关卡组\r\n        const levelGroupList = this._randomSelection(this._chapterData.strategyList, this._chapterData.levelGroupCount, this._chapterData.strategy);\r\n        if (levelGroupList.length === 0) {\r\n            console.log('GameMapRun', \" levelGroupList is null\");\r\n            return;\r\n        }\r\n\r\n        // 随机出关卡\r\n        this._levelList = [];\r\n        for (const levelGroupID of levelGroupList) {\r\n            const levelGroupData = MyApp.lubanTables.TbResLevelGroup.get(levelGroupID);\r\n            if (levelGroupData == null) {\r\n                console.log('GameMapRun', \" levelGroupData is null\");\r\n                continue;\r\n            }\r\n\r\n            this._levelList.push(...this._randomSelection(levelGroupData.normSTList, levelGroupData.normLevelCount, levelGroupData.normLevelST));\r\n            this._levelList.push(...this._randomSelection(levelGroupData.bossSTList, levelGroupData.bossLevelCount, levelGroupData.bossLevelST));\r\n        }\r\n        console.log('GameMapRun', ' _levelList ', this._levelList);\r\n    }\r\n\r\n    async initData(chapterID: number): Promise<void> {\r\n        this.reset();\r\n        this._initLevelList(chapterID);\r\n        await this._loadNextLevelPrefab(true);\r\n        this._initCurLevelData();\r\n        const levelBaseUI = this.node.getComponent<LevelBaseUI>(LevelBaseUI);\r\n        if (levelBaseUI) {\r\n            await new Promise<void>((resolve) => {\r\n                // 确保 LevelBaseUI 的初始化完成（例如背景加载）\r\n                const check = () => {\r\n                    if (levelBaseUI.backgroundLayer!.backgrounds!.length > 0) {\r\n                        resolve();\r\n                    } else {\r\n                        setTimeout(check, 100); // 轮询检查\r\n                    }\r\n                };\r\n                check();\r\n            });\r\n        }\r\n        this._initOver = true;\r\n        this._preloadState = PRELOAD_STATE.CHECK;\r\n    }\r\n\r\n\r\n    initBattle() {\r\n\r\n    }\r\n\r\n    update(deltaTime: number) {\r\n        deltaTime = deltaTime * GameIns.battleManager.animSpeed;\r\n        if (deltaTime > 0.2) {\r\n            deltaTime = 0.016666666666667;\r\n        }\r\n\r\n        const gameState = GameIns.gameRuleManager.gameState;\r\n        if (\r\n            gameState !== GameEnum.GameState.Battle &&\r\n            gameState !== GameEnum.GameState.Sortie &&\r\n            gameState !== GameEnum.GameState.Ready &&\r\n            gameState !== GameEnum.GameState.WillOver &&\r\n            gameState !== GameEnum.GameState.Idle &&\r\n            gameState !== GameEnum.GameState.Over\r\n        ) {\r\n            return;\r\n        }\r\n\r\n        if (!this._initOver) {\r\n            return;\r\n        }\r\n\r\n        this._tick(deltaTime);\r\n        this._checkNextLevelLoading();\r\n    }\r\n\r\n    clear() {\r\n        // 清理加载的资源\r\n\r\n    }\r\n\r\n    reset() {\r\n        // 重置地图数据\r\n        //this.node.removeAllChildren();\r\n        this._initOver = false;\r\n        this._levelLoadIndex = 0;\r\n        this._levelIndex = 0;\r\n        this._levelDistance = 0;\r\n        this._levelTotalDuration = 0;\r\n        this._levelTotalHeight = 0;\r\n        this._levelDuration = 0;\r\n        this._levelList = [];\r\n        this._levelUIInfoList = [];\r\n        this._chapterData = undefined;\r\n        this._lastSelectedId = -1;\r\n    }\r\n\r\n    /**\r\n     * 策略：\r\n     * 1.严格按权重比例随机选择元素\r\n     * 2.严格按权重比例随机选择元素，不重复\r\n     * 3.按顺序选择元素\r\n     * @param STList 带权重的元素数组\r\n     * @param count 需要选择的元素数量\r\n     * @returns 选中元素的ID数组\r\n     */\r\n    private _randomSelection(STList: randStrategy[], count: number, strategy: RAND_STRATEGY): number[] {\r\n        if (STList.length === 0 || count <= 0) return [];\r\n\r\n        const results: number[] = [];\r\n        if (strategy === RAND_STRATEGY.WEIGHT_PRUE) {\r\n            // 计算总权重\r\n            const totalWeight = STList.reduce((sum, item) => sum + item.Weight, 0);\r\n\r\n            // 如果所有权重都为0，则转为均匀随机\r\n            if (totalWeight === 0) {\r\n                for (let i = 0; i < count; i++) {\r\n                    const randomIndex = Math.floor(Math.random() * STList.length);\r\n                    results.push(STList[randomIndex].ID);\r\n                }\r\n                return results;\r\n            }\r\n\r\n            // 严格按权重比例随机选择\r\n            for (let i = 0; i < count; i++) {\r\n                // 生成[0, totalWeight)区间的随机数\r\n                const randomValue = Math.random() * totalWeight;\r\n\r\n                // 遍历查找随机数对应的元素\r\n                let cumulativeWeight = 0;\r\n                for (const item of STList) {\r\n                    cumulativeWeight += item.Weight;\r\n                    if (randomValue < cumulativeWeight) {\r\n                        results.push(item.ID);\r\n                        break;\r\n                    }\r\n                }\r\n            }\r\n        } else if (strategy === RAND_STRATEGY.WEIGHT_NO_REPEAT) {\r\n            // 计算总权重\r\n            const totalWeight = STList.reduce((sum, item) => sum + item.Weight, 0);\r\n\r\n            // 如果所有权重都为0，则转为均匀随机\r\n            if (totalWeight === 0) {\r\n                for (let i = 0; i < count; i++) {\r\n                    let randomIndex = Math.floor(Math.random() * STList.length);\r\n                    // 避免重复选择相同的ID\r\n                    if (i > 0 && STList[randomIndex].ID === results[i - 1]) {\r\n                        // 如果与上一次选择的相同，选择下一个（循环）\r\n                        randomIndex = (randomIndex + 1) % STList.length;\r\n                    }\r\n                    results.push(STList[randomIndex].ID);\r\n                }\r\n                return results;\r\n            }\r\n\r\n            // 创建副本以避免修改原始数据\r\n            const tempList = [...STList];\r\n\r\n            for (let i = 0; i < count; i++) {\r\n                // 如果上一次选择的ID存在，且它在当前列表中，调整其位置\r\n                if (this._lastSelectedId !== -1) {\r\n                    const lastSelectedIndex = tempList.findIndex(item => item.ID === this._lastSelectedId);\r\n                    if (lastSelectedIndex !== -1 && lastSelectedIndex < tempList.length - 1) {\r\n                        // 将上一次选择的ID与下一个元素交换位置\r\n                        [tempList[lastSelectedIndex], tempList[lastSelectedIndex + 1]] =\r\n                            [tempList[lastSelectedIndex + 1], tempList[lastSelectedIndex]];\r\n                    }\r\n                }\r\n\r\n                // 生成[0, totalWeight)区间的随机数\r\n                const randomValue = Math.random() * totalWeight;\r\n\r\n                // 遍历查找随机数对应的元素\r\n                let cumulativeWeight = 0;\r\n                let selectedIndex = -1;\r\n\r\n                for (let j = 0; j < tempList.length; j++) {\r\n                    cumulativeWeight += tempList[j].Weight;\r\n                    if (randomValue < cumulativeWeight) {\r\n                        selectedIndex = j;\r\n                        break;\r\n                    }\r\n                }\r\n\r\n                // 如果未找到有效索引，选择最后一个元素\r\n                if (selectedIndex === -1) {\r\n                    selectedIndex = tempList.length - 1;\r\n                }\r\n\r\n                // 获取选中的ID\r\n                const selectedId = tempList[selectedIndex].ID;\r\n                results.push(selectedId);\r\n\r\n                // 更新上一次选择的ID\r\n                this._lastSelectedId = selectedId;\r\n            }\r\n        } else if (strategy === RAND_STRATEGY.ORDER) {\r\n            // 按顺序选择元素，遇到ID为0时从数组开头重新开始\r\n            let currentIndex = 0;\r\n\r\n            for (let i = 0; i < count; i++) {\r\n                // 如果当前元素的ID为0，则重置到数组开头\r\n                if (STList[currentIndex].ID === 0) {\r\n                    currentIndex = 0;\r\n\r\n                    // 如果数组开头的元素ID也为0，则跳过所有ID为0的元素\r\n                    while (currentIndex < STList.length && STList[currentIndex].ID === 0) {\r\n                        currentIndex++;\r\n                    }\r\n\r\n                    // 如果所有元素ID都为0，则无法选择，跳出循环\r\n                    if (currentIndex >= STList.length) {\r\n                        break;\r\n                    }\r\n                }\r\n\r\n                // 选择当前元素\r\n                results.push(STList[currentIndex].ID);\r\n\r\n                // 移动到下一个元素\r\n                currentIndex++;\r\n\r\n                // 如果到达数组末尾，回到开头\r\n                if (currentIndex >= STList.length) {\r\n                    currentIndex = 0;\r\n                }\r\n            }\r\n        }\r\n\r\n        return results;\r\n    }\r\n\r\n    private async _loadNextLevelPrefab(bFristLevel: boolean = false): Promise<void> {\r\n        // 计算实际需要加载的关卡数量\r\n        const remainingLevels = MAX_LEVEL_COUNT - this._levelUIInfoList.length;\r\n        console.log('GameMapRun', ' levelCount:', this._chapterData!.levelCount, 'this._levelLoadIndex:', this._levelLoadIndex, 'remainingLevels:', remainingLevels);\r\n        const levelsToLoad = Math.min(MAX_LEVEL_COUNT, remainingLevels);\r\n\r\n        if (levelsToLoad <= 0) {\r\n            console.log('GameMapRun', ' no level to load');\r\n            this._preloadState = PRELOAD_STATE.LOADED;\r\n            return;\r\n        }\r\n\r\n        let loadIndex = this._levelLoadIndex;\r\n        const loadPromises: Promise<void>[] = [];\r\n        for (let i = 0; i < levelsToLoad; i++) {\r\n            const levelID = this._levelList[loadIndex + i];\r\n            console.log('GameMapRun', ' level LoadIndex:', loadIndex + i);\r\n            const levelConfig = MyApp.lubanTables.TbResLevel.get(levelID);\r\n            if (levelConfig == null) {\r\n                console.log('GameMapRun', ' level data not found', levelID);\r\n                return;\r\n            }\r\n\r\n            const prefabName = `game/level/${levelConfig.prefab}`;\r\n            const loadPromise = new Promise<void>((resolve) => {\r\n                resources.load(prefabName, JsonAsset, async (err, prefab) => {\r\n                    if (err) {\r\n                        console.error('GameMapRun', '加载关卡预制体失败:', err);\r\n                        resolve();\r\n                        return;\r\n                    }\r\n\r\n                    var levelBaseUI = this.node.getComponent(LevelBaseUI);\r\n                    //const nodeLayer = new Node(`chapter${this._chapterData.id}`);\r\n                    if (levelBaseUI == null) {\r\n                        levelBaseUI = this.node.addComponent(LevelBaseUI);\r\n                    }\r\n\r\n                    const levelInfo = {\r\n                        levelID: levelID,\r\n                        levelCount: this._chapterData!.levelCount,\r\n                        levelIndex: loadIndex + i\r\n                    };\r\n\r\n                    var levelData = LevelData.fromJSON(prefab?.json);\r\n                    if (bFristLevel) {\r\n                        await levelBaseUI.levelPrefab(levelData, levelInfo, bFristLevel);\r\n                    } else {\r\n                        levelBaseUI.levelPrefab(levelData, levelInfo, bFristLevel);\r\n                    }\r\n                    //this.node.addChild(nodeLayer);\r\n                    var levelBaseUIInfo = this._initLevelInfo(levelID, levelData.totalTime, levelData.backgroundLayer.speed);\r\n                    this._levelUIInfoList.push(levelBaseUIInfo);\r\n                    console.log('GameMapRun', '加载关卡:', levelID, 'loadIndex:', levelInfo.levelIndex, '_levelUIInfoList push length:', this._levelUIInfoList.length);\r\n\r\n                    resolve();\r\n                });\r\n            });\r\n\r\n            loadPromises.push(loadPromise);\r\n        }\r\n\r\n        if (bFristLevel) {\r\n            await Promise.all(loadPromises); // 首次加载需要等待\r\n            this._preloadState = PRELOAD_STATE.LOADED;\r\n            this._levelLoadIndex += levelsToLoad;\r\n        } else {\r\n            await Promise.all(loadPromises)\r\n                .then(() => {\r\n                    this._preloadState = PRELOAD_STATE.LOADED;\r\n                    this._levelLoadIndex += levelsToLoad;\r\n                    console.log('GameMapRun', '关卡预加载完成');\r\n                })\r\n                .catch((err) => {\r\n                    console.error('后台预加载失败:', err);\r\n                });\r\n        }\r\n\r\n        if (this._levelLoadIndex >= this._chapterData!.levelCount) {\r\n            this._levelLoadIndex = 0;\r\n            console.log('GameMapRun', '所有关卡已加载完成,循环加载开始');\r\n        }\r\n    }\r\n\r\n    private _checkNextLevelLoading() {\r\n        if (this._preloadState === PRELOAD_STATE.CHECK && this._levelUIInfoList.length < MAX_LEVEL_COUNT) {\r\n            console.log('GameMapRun', ' 开始预加载关卡: _levelIndex', this._levelIndex, 'this._levelLoadIndex:', this._levelLoadIndex);\r\n            this._preloadState = PRELOAD_STATE.LOADING;\r\n            this._loadNextLevelPrefab().catch(err => {\r\n                console.error('GameMapRun', ' Background loading failed:', err);\r\n            });\r\n        }\r\n    }\r\n\r\n    private _initCurLevelData() {\r\n        if (this._levelIndex >= this._chapterData!.levelCount) {\r\n            console.error('GameMapRun', ' no level to init');\r\n            return;\r\n        }\r\n\r\n        const levelBaseUI = this.node.getComponent<LevelBaseUI>(LevelBaseUI);\r\n        this._levelTotalHeight = levelBaseUI!.getLevelTotalHeightByIndex(this._levelIndex);\r\n\r\n        const levelBase = this._levelUIInfoList[0];\r\n        if (levelBase) {\r\n            levelBaseUI!.switchLevel(levelBase.speed, levelBase.totalTime, this._levelIndex);\r\n            this._levelSpeed = levelBase.speed;\r\n            this._levelDistance = 0;\r\n            this._levelDuration = 0;\r\n\r\n            this._preloadState = PRELOAD_STATE.CHECK;\r\n        }\r\n    }\r\n\r\n    private _tick(deltaTime: number) {\r\n\r\n        if (this._levelIndex >= this._chapterData!.levelCount) {\r\n            console.error('GameMapRun', ' no level to tick');\r\n            return;\r\n        }\r\n\r\n        if (this._levelTotalHeight <= 0) {\r\n            return;\r\n        }\r\n\r\n        this._levelDuration += deltaTime;\r\n        this._levelTotalDuration += deltaTime;\r\n        this._levelDistance += this.MapSpeed * deltaTime;\r\n\r\n        const levelBaseUI = this.node.getComponent<LevelBaseUI>(LevelBaseUI);\r\n        if (levelBaseUI != null) {\r\n            //console.log('GameMapRun',' tick level', levelData.levelID, this._levelDuration);\r\n            levelBaseUI.tick(deltaTime);\r\n            var targetDistance = this._levelTotalHeight;\r\n\r\n            if (this._levelDistance >= targetDistance) {\r\n                const finishedLevel = this._levelUIInfoList.shift();\r\n                console.log('GameMapRun', '关卡完成:', this._levelIndex, '_levelUIInfoList shift length:', this._levelUIInfoList.length);\r\n                this._levelIndex++;\r\n\r\n                if (this._levelIndex >= this._chapterData!.levelCount) {\r\n                    this._levelIndex = 0;\r\n                    console.log('GameMapRun', '章节_', this._chapterData!.id, '完成，开始循环');\r\n                }\r\n\r\n                this._initCurLevelData();\r\n            }\r\n        }\r\n    }\r\n\r\n    private _initLevelInfo(levelID: number, time: number, speed: number): LevelBaseUIInfo {\r\n        return {\r\n            levelID: levelID,\r\n            totalTime: time,\r\n            speed: speed,\r\n        };\r\n    }\r\n}"]}