System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Vec2, instantiate, LubanMgr, LevelEditorUtils, _dec, _dec2, _dec3, _class, _class2, _crd, ccclass, property, executeInEditMode, menu, WavePreview;

  function _reportPossibleCrUseOfWave(extras) {
    _reporterNs.report("Wave", "db://assets/bundles/common/script/game/wave/Wave", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLubanMgr(extras) {
    _reporterNs.report("LubanMgr", "db://assets/bundles/common/script/luban/LubanMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelEditorUtils(extras) {
    _reporterNs.report("LevelEditorUtils", "../utils", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Vec2 = _cc.Vec2;
      instantiate = _cc.instantiate;
    }, function (_unresolved_2) {
      LubanMgr = _unresolved_2.LubanMgr;
    }, function (_unresolved_3) {
      LevelEditorUtils = _unresolved_3.LevelEditorUtils;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "e6727UuHORMv49xH6m7Wz2U", "WavePreview", undefined);

      __checkObsolete__(['_decorator', 'Node', 'Prefab', 'CCBoolean', 'CCFloat', 'CCInteger', 'Component', 'Vec2', 'instantiate']);

      ({
        ccclass,
        property,
        executeInEditMode,
        menu
      } = _decorator);

      /// 用来创建和管理波次的所有飞机对象
      _export("WavePreview", WavePreview = (_dec = ccclass('WavePreview'), _dec2 = menu("怪物/编辑器/波次预览"), _dec3 = executeInEditMode(), _dec(_class = _dec2(_class = _dec3(_class = (_class2 = class WavePreview extends Component {
        constructor() {
          super(...arguments);
          this._luban = null;
          this._bindingMap = new Map();
        }

        static get instance() {
          return this._instance;
        }

        get luban() {
          if (this._luban == null) {
            this._luban = new (_crd && LubanMgr === void 0 ? (_reportPossibleCrUseOfLubanMgr({
              error: Error()
            }), LubanMgr) : LubanMgr)();
          }

          return this._luban;
        }

        onLoad() {
          if (WavePreview._instance == null) {
            WavePreview._instance = this;
          } else {
            console.warn("WavePreview multiple instance");
          }
        }

        reset() {
          this.node.removeAllChildren();

          this._bindingMap.clear();
        }

        update(dt) {
          this._bindingMap.forEach((nodes, wave) => {
            this.updateWave(wave, nodes);
          });
        }

        setupWave(wave) {
          var waveData = wave.waveData;

          if (waveData.spawnGroup && waveData.spawnGroup.length > 0) {
            var _this$luban;

            var planeId = 0;

            for (var i = 0; i < waveData.spawnGroup.length; i++) {
              if (waveData.spawnGroup[i].planeID > 0) {
                planeId = waveData.spawnGroup[i].planeID;
                break;
              }
            }

            if (planeId == 0) {
              console.warn("WavePreview createPlane no valid planeId in spawnGroup");
              return;
            }

            if (((_this$luban = this.luban) == null ? void 0 : _this$luban.table) == null) {
              var _this$luban2;

              (_this$luban2 = this.luban) == null || _this$luban2.initInEditor().then(() => {
                this.createPlane(wave, planeId);
              });
            }
          }
        }

        createPlane(wave, planeId) {
          var _this$luban3;

          var planeData = (_this$luban3 = this.luban) == null ? void 0 : _this$luban3.table.TbResEnemy.get(planeId);

          if (planeData == null) {
            console.warn("WavePreview createPlane no planeData for id:", planeId);
            return;
          }

          var fullPath = "db://assets/resources/" + planeData.prefab + ".prefab";
          (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
            error: Error()
          }), LevelEditorUtils) : LevelEditorUtils).loadByPath(fullPath).then(prefab => {
            if (prefab) {
              var node = instantiate(prefab);

              if (node) {
                this.node.addChild(node);

                var nodes = this._bindingMap.get(wave);

                if (nodes == null) {
                  nodes = [];

                  this._bindingMap.set(wave, nodes);
                }

                nodes.push(node);
              }
            }
          });
        }

        updateWave(wave, nodes) {
          var wavePos = wave.node.worldPosition;
          var waveData = wave.waveData;
          var nodePos = new Vec2(wavePos.x + waveData.spawnPosX.eval(), wavePos.y + waveData.spawnPosY.eval());
          var nodeAngle = waveData.spawnAngle.eval();

          for (var i = 0; i < nodes.length; i++) {
            var node = nodes[i];
            node.setWorldPosition(nodePos.x, nodePos.y, 0);
            node.setRotationFromEuler(0, 0, nodeAngle);
          }
        }

      }, _class2._instance = null, _class2)) || _class) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=41cd73be68eabcc1cac24b8cc6301d64aca50e3d.js.map