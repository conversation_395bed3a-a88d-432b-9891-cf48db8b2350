{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/level/preview/WavePreview.ts"], "names": ["_decorator", "Component", "Vec2", "instantiate", "LubanMgr", "LevelEditorUtils", "ccclass", "property", "executeInEditMode", "menu", "WavePreview", "_luban", "_bindingMap", "Map", "instance", "_instance", "luban", "onLoad", "console", "warn", "reset", "node", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clear", "update", "dt", "for<PERSON>ach", "nodes", "wave", "updateWave", "setupWave", "waveData", "spawnGroup", "length", "planeId", "i", "planeID", "table", "initInEditor", "then", "createPlane", "planeData", "TbResEnemy", "get", "fullPath", "prefab", "loadByPath", "<PERSON><PERSON><PERSON><PERSON>", "set", "push", "wavePos", "worldPosition", "nodePos", "x", "spawnPosX", "eval", "y", "spawnPosY", "nodeAngle", "spawnAngle", "setWorldPosition", "setRotationFromEuler"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAyDC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,W,OAAAA,W;;AAK1EC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,gB,iBAAAA,gB;;;;;;;;;OALH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA,iBAArB;AAAwCC,QAAAA;AAAxC,O,GAAiDT,U;;AAOvD;6BAIaU,W,WAHZJ,OAAO,CAAC,aAAD,C,UACPG,IAAI,CAAC,aAAD,C,UACJD,iBAAiB,E,yDAFlB,MAGaE,WAHb,SAGiCT,SAHjC,CAG2C;AAAA;AAAA;AAAA,eAO/BU,MAP+B,GAOP,IAPO;AAAA,eAe/BC,WAf+B,GAeE,IAAIC,GAAJ,EAfF;AAAA;;AAGb,mBAARC,QAAQ,GAAqB;AAC3C,iBAAO,KAAKC,SAAZ;AACH;;AAGe,YAALC,KAAK,GAAkB;AAC9B,cAAI,KAAKL,MAAL,IAAe,IAAnB,EAAyB;AACrB,iBAAKA,MAAL,GAAc;AAAA;AAAA,uCAAd;AACH;;AACD,iBAAO,KAAKA,MAAZ;AACH;;AAIDM,QAAAA,MAAM,GAAG;AACL,cAAIP,WAAW,CAACK,SAAZ,IAAyB,IAA7B,EAAmC;AAC/BL,YAAAA,WAAW,CAACK,SAAZ,GAAwB,IAAxB;AACH,WAFD,MAEO;AACHG,YAAAA,OAAO,CAACC,IAAR,CAAa,+BAAb;AACH;AACJ;;AAEDC,QAAAA,KAAK,GAAG;AACJ,eAAKC,IAAL,CAAUC,iBAAV;;AACA,eAAKV,WAAL,CAAiBW,KAAjB;AACH;;AAEDC,QAAAA,MAAM,CAACC,EAAD,EAAa;AACf,eAAKb,WAAL,CAAiBc,OAAjB,CAAyB,CAACC,KAAD,EAAQC,IAAR,KAAiB;AACtC,iBAAKC,UAAL,CAAgBD,IAAhB,EAAsBD,KAAtB;AACH,WAFD;AAGH;;AAEDG,QAAAA,SAAS,CAACF,IAAD,EAAa;AAClB,cAAMG,QAAQ,GAAGH,IAAI,CAACG,QAAtB;;AACA,cAAIA,QAAQ,CAACC,UAAT,IAAuBD,QAAQ,CAACC,UAAT,CAAoBC,MAApB,GAA6B,CAAxD,EAA2D;AAAA;;AACvD,gBAAIC,OAAO,GAAG,CAAd;;AACA,iBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGJ,QAAQ,CAACC,UAAT,CAAoBC,MAAxC,EAAgDE,CAAC,EAAjD,EAAqD;AACjD,kBAAIJ,QAAQ,CAACC,UAAT,CAAoBG,CAApB,EAAuBC,OAAvB,GAAiC,CAArC,EAAwC;AACpCF,gBAAAA,OAAO,GAAGH,QAAQ,CAACC,UAAT,CAAoBG,CAApB,EAAuBC,OAAjC;AACA;AACH;AACJ;;AAED,gBAAIF,OAAO,IAAI,CAAf,EAAkB;AACdhB,cAAAA,OAAO,CAACC,IAAR,CAAa,wDAAb;AACA;AACH;;AAED,gBAAI,qBAAKH,KAAL,iCAAYqB,KAAZ,KAAqB,IAAzB,EAA+B;AAAA;;AAC3B,mCAAKrB,KAAL,0BAAYsB,YAAZ,GAA2BC,IAA3B,CAAgC,MAAM;AAClC,qBAAKC,WAAL,CAAiBZ,IAAjB,EAAuBM,OAAvB;AACH,eAFD;AAGH;AACJ;AACJ;;AAEOM,QAAAA,WAAW,CAACZ,IAAD,EAAaM,OAAb,EAA8B;AAAA;;AAC7C,cAAMO,SAAS,mBAAG,KAAKzB,KAAR,qBAAG,aAAYqB,KAAZ,CAAkBK,UAAlB,CAA6BC,GAA7B,CAAiCT,OAAjC,CAAlB;;AACA,cAAIO,SAAS,IAAI,IAAjB,EAAuB;AACnBvB,YAAAA,OAAO,CAACC,IAAR,CAAa,8CAAb,EAA6De,OAA7D;AACA;AACH;;AAED,cAAMU,QAAQ,GAAG,2BAA2BH,SAAS,CAACI,MAArC,GAA8C,SAA/D;AACA;AAAA;AAAA,oDAAiBC,UAAjB,CAAoCF,QAApC,EAA8CL,IAA9C,CAAoDM,MAAD,IAAY;AAC3D,gBAAIA,MAAJ,EAAY;AACR,kBAAMxB,IAAI,GAAGlB,WAAW,CAAC0C,MAAD,CAAxB;;AACA,kBAAIxB,IAAJ,EAAU;AACN,qBAAKA,IAAL,CAAU0B,QAAV,CAAmB1B,IAAnB;;AACA,oBAAIM,KAAK,GAAG,KAAKf,WAAL,CAAiB+B,GAAjB,CAAqBf,IAArB,CAAZ;;AACA,oBAAID,KAAK,IAAI,IAAb,EAAmB;AACfA,kBAAAA,KAAK,GAAG,EAAR;;AACA,uBAAKf,WAAL,CAAiBoC,GAAjB,CAAqBpB,IAArB,EAA2BD,KAA3B;AACH;;AACDA,gBAAAA,KAAK,CAACsB,IAAN,CAAW5B,IAAX;AACH;AACJ;AACJ,WAbD;AAcH;;AAEDQ,QAAAA,UAAU,CAACD,IAAD,EAAaD,KAAb,EAA4B;AAClC,cAAMuB,OAAO,GAAGtB,IAAI,CAACP,IAAL,CAAU8B,aAA1B;AACA,cAAMpB,QAAQ,GAAGH,IAAI,CAACG,QAAtB;AACA,cAAMqB,OAAO,GAAG,IAAIlD,IAAJ,CAASgD,OAAO,CAACG,CAAR,GAAYtB,QAAQ,CAACuB,SAAT,CAAmBC,IAAnB,EAArB,EAAgDL,OAAO,CAACM,CAAR,GAAYzB,QAAQ,CAAC0B,SAAT,CAAmBF,IAAnB,EAA5D,CAAhB;AACA,cAAMG,SAAS,GAAG3B,QAAQ,CAAC4B,UAAT,CAAoBJ,IAApB,EAAlB;;AAEA,eAAK,IAAIpB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGR,KAAK,CAACM,MAA1B,EAAkCE,CAAC,EAAnC,EAAuC;AACnC,gBAAMd,IAAI,GAAGM,KAAK,CAACQ,CAAD,CAAlB;AACAd,YAAAA,IAAI,CAACuC,gBAAL,CAAsBR,OAAO,CAACC,CAA9B,EAAiCD,OAAO,CAACI,CAAzC,EAA4C,CAA5C;AACAnC,YAAAA,IAAI,CAACwC,oBAAL,CAA0B,CAA1B,EAA6B,CAA7B,EAAgCH,SAAhC;AACH;AACJ;;AA/FsC,O,UAExB3C,S,GAA8B,I", "sourcesContent": ["import { _decorator, Node, Prefab, CCBoolean, CCFloat, CCInteger, Component, Vec2, instantiate } from 'cc';\r\nconst { ccclass, property, executeInEditMode, menu } = _decorator;\r\n\r\nimport { WaveData, eSpawnOrder, eWaveCompletion } from 'db://assets/bundles/common/script/game/data/WaveData';\r\nimport { Wave } from 'db://assets/bundles/common/script/game/wave/Wave';\r\nimport { LubanMgr } from 'db://assets/bundles/common/script/luban/LubanMgr';\r\nimport { LevelEditorUtils } from '../utils';\r\n\r\n/// 用来创建和管理波次的所有飞机对象\r\n@ccclass('WavePreview')\r\n@menu(\"怪物/编辑器/波次预览\")\r\n@executeInEditMode()\r\nexport class WavePreview extends Component {\r\n\r\n    private static _instance: WavePreview|null = null;\r\n    public static get instance(): WavePreview|null {\r\n        return this._instance;\r\n    }\r\n\r\n    private _luban: LubanMgr|null = null;\r\n    public get luban(): LubanMgr|null {\r\n        if (this._luban == null) {\r\n            this._luban = new LubanMgr();\r\n        }\r\n        return this._luban;\r\n    }\r\n\r\n    private _bindingMap: Map<Wave, Node[]> = new Map();\r\n\r\n    onLoad() {\r\n        if (WavePreview._instance == null) {\r\n            WavePreview._instance = this;\r\n        } else {\r\n            console.warn(\"WavePreview multiple instance\");\r\n        }\r\n    }\r\n\r\n    reset() {\r\n        this.node.removeAllChildren();\r\n        this._bindingMap.clear();\r\n    }\r\n\r\n    update(dt: number) {\r\n        this._bindingMap.forEach((nodes, wave) => {\r\n            this.updateWave(wave, nodes);\r\n        });\r\n    }\r\n\r\n    setupWave(wave: Wave) {\r\n        const waveData = wave.waveData;\r\n        if (waveData.spawnGroup && waveData.spawnGroup.length > 0) {\r\n            let planeId = 0;\r\n            for (let i = 0; i < waveData.spawnGroup.length; i++) {\r\n                if (waveData.spawnGroup[i].planeID > 0) {\r\n                    planeId = waveData.spawnGroup[i].planeID;\r\n                    break;\r\n                }\r\n            }\r\n\r\n            if (planeId == 0) {\r\n                console.warn(\"WavePreview createPlane no valid planeId in spawnGroup\");\r\n                return;\r\n            }\r\n\r\n            if (this.luban?.table == null) {\r\n                this.luban?.initInEditor().then(() => {\r\n                    this.createPlane(wave, planeId);\r\n                });\r\n            }\r\n        }\r\n    }\r\n\r\n    private createPlane(wave: Wave, planeId: number) {\r\n        const planeData = this.luban?.table.TbResEnemy.get(planeId);\r\n        if (planeData == null) {\r\n            console.warn(\"WavePreview createPlane no planeData for id:\", planeId);\r\n            return;\r\n        }\r\n\r\n        const fullPath = \"db://assets/resources/\" + planeData.prefab + \".prefab\";\r\n        LevelEditorUtils.loadByPath<Prefab>(fullPath).then((prefab) => {\r\n            if (prefab) {\r\n                const node = instantiate(prefab);\r\n                if (node) {\r\n                    this.node.addChild(node);\r\n                    let nodes = this._bindingMap.get(wave);\r\n                    if (nodes == null) {\r\n                        nodes = [];\r\n                        this._bindingMap.set(wave, nodes);\r\n                    }\r\n                    nodes.push(node);\r\n                }\r\n            }\r\n        });\r\n    }\r\n\r\n    updateWave(wave: Wave, nodes: Node[]) {\r\n        const wavePos = wave.node.worldPosition;\r\n        const waveData = wave.waveData;\r\n        const nodePos = new Vec2(wavePos.x + waveData.spawnPosX.eval(), wavePos.y + waveData.spawnPosY.eval());\r\n        const nodeAngle = waveData.spawnAngle.eval();\r\n\r\n        for (let i = 0; i < nodes.length; i++) {\r\n            const node = nodes[i];\r\n            node.setWorldPosition(nodePos.x, nodePos.y, 0);\r\n            node.setRotationFromEuler(0, 0, nodeAngle);\r\n        }\r\n    }\r\n}"]}