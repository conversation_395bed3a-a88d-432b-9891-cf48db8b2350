System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9", "__unresolved_10", "__unresolved_11", "__unresolved_12"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, SingletonBase, FColliderManager, BattleManager, BossManager, EnemyManager, GameDataManager, GamePlaneManager, GameRuleManager, HurtEffectManager, MainPlaneManager, StageManager, WaveManager, _GameIns, _crd, GameIns;

  function _reportPossibleCrUseOfSingletonBase(extras) {
    _reporterNs.report("SingletonBase", "../../../../scripts/core/base/SingletonBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFColliderManager(extras) {
    _reporterNs.report("FColliderManager", "./collider-system/FColliderManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBattleManager(extras) {
    _reporterNs.report("BattleManager", "./manager/BattleManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBossManager(extras) {
    _reporterNs.report("BossManager", "./manager/BossManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyManager(extras) {
    _reporterNs.report("EnemyManager", "./manager/EnemyManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameDataManager(extras) {
    _reporterNs.report("GameDataManager", "./manager/GameDataManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGamePlaneManager(extras) {
    _reporterNs.report("GamePlaneManager", "./manager/GamePlaneManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameRuleManager(extras) {
    _reporterNs.report("GameRuleManager", "./manager/GameRuleManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfHurtEffectManager(extras) {
    _reporterNs.report("HurtEffectManager", "./manager/HurtEffectManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMainPlaneManager(extras) {
    _reporterNs.report("MainPlaneManager", "./manager/MainPlaneManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfStageManager(extras) {
    _reporterNs.report("StageManager", "./manager/StageManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfWaveManager(extras) {
    _reporterNs.report("WaveManager", "./manager/WaveManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameMain(extras) {
    _reporterNs.report("GameMain", "./scenes/GameMain", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      SingletonBase = _unresolved_2.SingletonBase;
    }, function (_unresolved_3) {
      FColliderManager = _unresolved_3.default;
    }, function (_unresolved_4) {
      BattleManager = _unresolved_4.BattleManager;
    }, function (_unresolved_5) {
      BossManager = _unresolved_5.BossManager;
    }, function (_unresolved_6) {
      EnemyManager = _unresolved_6.EnemyManager;
    }, function (_unresolved_7) {
      GameDataManager = _unresolved_7.GameDataManager;
    }, function (_unresolved_8) {
      GamePlaneManager = _unresolved_8.GamePlaneManager;
    }, function (_unresolved_9) {
      GameRuleManager = _unresolved_9.GameRuleManager;
    }, function (_unresolved_10) {
      HurtEffectManager = _unresolved_10.HurtEffectManager;
    }, function (_unresolved_11) {
      MainPlaneManager = _unresolved_11.MainPlaneManager;
    }, function (_unresolved_12) {
      StageManager = _unresolved_12.StageManager;
    }, function (_unresolved_13) {
      WaveManager = _unresolved_13.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "28f96SlzaZCLJsFVJcKZyjG", "GameIns", undefined);

      ;
      _GameIns = class _GameIns extends (_crd && SingletonBase === void 0 ? (_reportPossibleCrUseOfSingletonBase({
        error: Error()
      }), SingletonBase) : SingletonBase) {
        constructor() {
          super(...arguments);
          this.gameMainUI = null;
        }

        get battleManager() {
          return (_crd && BattleManager === void 0 ? (_reportPossibleCrUseOfBattleManager({
            error: Error()
          }), BattleManager) : BattleManager).getInstance(_crd && BattleManager === void 0 ? (_reportPossibleCrUseOfBattleManager({
            error: Error()
          }), BattleManager) : BattleManager);
        }

        get bossManager() {
          return (_crd && BossManager === void 0 ? (_reportPossibleCrUseOfBossManager({
            error: Error()
          }), BossManager) : BossManager).getInstance(_crd && BossManager === void 0 ? (_reportPossibleCrUseOfBossManager({
            error: Error()
          }), BossManager) : BossManager);
        }

        get enemyManager() {
          return (_crd && EnemyManager === void 0 ? (_reportPossibleCrUseOfEnemyManager({
            error: Error()
          }), EnemyManager) : EnemyManager).getInstance(_crd && EnemyManager === void 0 ? (_reportPossibleCrUseOfEnemyManager({
            error: Error()
          }), EnemyManager) : EnemyManager);
        }

        get gameDataManager() {
          return (_crd && GameDataManager === void 0 ? (_reportPossibleCrUseOfGameDataManager({
            error: Error()
          }), GameDataManager) : GameDataManager).getInstance(_crd && GameDataManager === void 0 ? (_reportPossibleCrUseOfGameDataManager({
            error: Error()
          }), GameDataManager) : GameDataManager);
        }

        get gameRuleManager() {
          return (_crd && GameRuleManager === void 0 ? (_reportPossibleCrUseOfGameRuleManager({
            error: Error()
          }), GameRuleManager) : GameRuleManager).getInstance(_crd && GameRuleManager === void 0 ? (_reportPossibleCrUseOfGameRuleManager({
            error: Error()
          }), GameRuleManager) : GameRuleManager);
        }

        get hurtEffectManager() {
          return (_crd && HurtEffectManager === void 0 ? (_reportPossibleCrUseOfHurtEffectManager({
            error: Error()
          }), HurtEffectManager) : HurtEffectManager).getInstance(_crd && HurtEffectManager === void 0 ? (_reportPossibleCrUseOfHurtEffectManager({
            error: Error()
          }), HurtEffectManager) : HurtEffectManager);
        }

        get mainPlaneManager() {
          return (_crd && MainPlaneManager === void 0 ? (_reportPossibleCrUseOfMainPlaneManager({
            error: Error()
          }), MainPlaneManager) : MainPlaneManager).getInstance(_crd && MainPlaneManager === void 0 ? (_reportPossibleCrUseOfMainPlaneManager({
            error: Error()
          }), MainPlaneManager) : MainPlaneManager);
        }

        get gamePlaneManager() {
          return (_crd && GamePlaneManager === void 0 ? (_reportPossibleCrUseOfGamePlaneManager({
            error: Error()
          }), GamePlaneManager) : GamePlaneManager).getInstance(_crd && GamePlaneManager === void 0 ? (_reportPossibleCrUseOfGamePlaneManager({
            error: Error()
          }), GamePlaneManager) : GamePlaneManager);
        }

        get stageManager() {
          return (_crd && StageManager === void 0 ? (_reportPossibleCrUseOfStageManager({
            error: Error()
          }), StageManager) : StageManager).getInstance(_crd && StageManager === void 0 ? (_reportPossibleCrUseOfStageManager({
            error: Error()
          }), StageManager) : StageManager);
        }

        get waveManager() {
          return (_crd && WaveManager === void 0 ? (_reportPossibleCrUseOfWaveManager({
            error: Error()
          }), WaveManager) : WaveManager).getInstance(_crd && WaveManager === void 0 ? (_reportPossibleCrUseOfWaveManager({
            error: Error()
          }), WaveManager) : WaveManager);
        }

        get fColliderManager() {
          return (_crd && FColliderManager === void 0 ? (_reportPossibleCrUseOfFColliderManager({
            error: Error()
          }), FColliderManager) : FColliderManager).instance;
        }

      };

      _export("GameIns", GameIns = _GameIns.getInstance(_GameIns));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=41d00f9be52f7272750998107d15febd88063eca.js.map