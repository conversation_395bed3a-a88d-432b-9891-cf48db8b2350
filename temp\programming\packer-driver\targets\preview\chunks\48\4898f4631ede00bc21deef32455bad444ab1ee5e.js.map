{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/core/base/MessageBox.ts"], "names": ["MessageBox", "PopupUI", "ToastUI", "UIMgr", "confirm", "content", "onConfirm", "onCancel", "cancelHandler", "show", "openUI", "toast"], "mappings": ";;;uDAOaA,U;;;;;;;;;;;;;;;;;;;;;;AAPJC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,K,iBAAAA,K;;;;;;;AAET;4BAGaH,U,GAAN,MAAMA,UAAN,CAAiB;AACtB;AACqB,eAAPI,OAAO,CAACC,OAAD,EAAkBC,SAAlB,EAAuCC,QAAvC,EAA4D;AAC/E,cAAMC,aAAa,GAAGD,QAAQ,KAAK,MAAM,CAAG,CAAd,CAA9B;;AACA,eAAKE,IAAL,CAAUJ,OAAV,EAAmBC,SAAnB,EAA8BE,aAA9B;AACD,SALqB,CAOtB;;;AACkB,eAAJC,IAAI,CAACJ,OAAD,EAAkBC,SAAlB,EAAwCC,QAAxC,EAAmE;AACnF;AACA;AAAA;AAAA,8BAAMG,MAAN;AAAA;AAAA,kCAAsBL,OAAtB,EAA+BC,SAA/B,EAA0CC,QAA1C;AACD;;AAEkB,eAALI,KAAK,CAACN,OAAD,EAAkB;AACnC;AAAA;AAAA,8BAAMK,MAAN;AAAA;AAAA,kCAAsBL,OAAtB;AACD;;AAfqB,O", "sourcesContent": ["import { PopupUI } from \"db://assets/bundles/common/script/ui/common/PopupUI\";\nimport { ToastUI } from \"db://assets/bundles/common/script/ui/common/ToastUI\";\nimport { UIMgr } from \"./UIMgr\";\n\n// 定义回调函数类型\ntype Callback = () => void;\n\nexport class MessageBox {\n  // 强制显示确认+取消按钮，不需要取消函数回调，不传onCancel\n  public static confirm(content: string, onConfirm: Callback, onCancel?: Callback) {\n    const cancelHandler = onCancel || (() => { });\n    this.show(content, onConfirm, cancelHandler);\n  }\n\n  // 只显示确认按钮，调用这个\n  public static show(content: string, onConfirm?: Callback, onCancel?: Callback): void {\n    // 统一调用 UIMgr\n    UIMgr.openUI(PopupUI, content, onConfirm, onCancel);\n  }\n\n  public static toast(content: string) {\n    UIMgr.openUI(ToastUI, content);\n  }\n\n}\n"]}