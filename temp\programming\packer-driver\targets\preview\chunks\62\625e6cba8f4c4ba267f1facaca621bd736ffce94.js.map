{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/RewardUI.ts"], "names": ["_decorator", "Node", "tween", "Vec3", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "BundleName", "ButtonPlus", "ccclass", "property", "RewardUI", "activeTweens", "activeTimeouts", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "Top", "getBundleName", "Home", "getUIOption", "isClickBgCloseUI", "onLoad", "btnGet", "addClick", "onGetClick", "closeUI", "onShow", "rewardID", "showNodesSequentiallyWithScale", "children", "nodeReward", "length", "for<PERSON>ach", "child", "active", "showNodeWithHalfScale", "index", "setScale", "halfScaleTween", "to", "scale", "easing", "call", "start", "fullScaleTween", "push", "onHide", "onClose", "stopAllEffects", "stop", "timeoutId", "clearTimeout"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAAaC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;;AAChCC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBV,U;;0BAGjBW,Q,WADZF,OAAO,CAAC,UAAD,C,UAEHC,QAAQ,CAACT,IAAD,C,UAGRS,QAAQ;AAAA;AAAA,mC,2BALb,MACaC,QADb;AAAA;AAAA,4BACqC;AAAA;AAAA;;AAAA;;AAAA;;AAAA,eAOzBC,YAPyB,GAOK,EAPL;AAAA,eAQzBC,cARyB,GAQyB,EARzB;AAAA;;AAUb,eAANC,MAAM,GAAW;AAAE,iBAAO,oBAAP;AAA8B;;AACzC,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,GAAf;AAAoB;;AAC7B,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,IAAlB;AAAwB;;AACvC,eAAXC,WAAW,GAAU;AAC/B,iBAAO;AAAEC,YAAAA,gBAAgB,EAAE;AAApB,WAAP;AACH;;AAESC,QAAAA,MAAM,GAAS;AACrB,eAAKC,MAAL,CAAaC,QAAb,CAAsB,KAAKC,UAA3B,EAAuC,IAAvC;AACH;;AACKA,QAAAA,UAAU,GAAG;AAAA;AACf;AAAA;AAAA,gCAAMC,OAAN,CAAcd,QAAd;AADe;AAElB;;AAEKe,QAAAA,MAAM,CAACC,QAAD,EAAkC;AAAA;;AAAA;AAC1C,YAAA,KAAI,CAACC,8BAAL;AAD0C;AAE7C;;AAEOA,QAAAA,8BAA8B,GAAS;AAC3C,cAAMC,QAAQ,GAAG,KAAKC,UAAL,CAAiBD,QAAlC;AACA,cAAI,CAACA,QAAD,IAAaA,QAAQ,CAACE,MAAT,KAAoB,CAArC,EAAwC,OAFG,CAI3C;;AACAF,UAAAA,QAAQ,CAACG,OAAT,CAAkBC,KAAD,IAAW;AACxBA,YAAAA,KAAK,CAACC,MAAN,GAAe,KAAf;AACH,WAFD,EAL2C,CAS3C;;AACA,eAAKC,qBAAL,CAA2BN,QAA3B,EAAqC,CAArC;AACH;;AACOM,QAAAA,qBAAqB,CAACN,QAAD,EAAmBO,KAAnB,EAAwC;AACjE,cAAIA,KAAK,IAAIP,QAAQ,CAACE,MAAtB,EAA8B;AAE9B,cAAME,KAAK,GAAGJ,QAAQ,CAACO,KAAD,CAAtB;AACAH,UAAAA,KAAK,CAACC,MAAN,GAAe,IAAf;AACAD,UAAAA,KAAK,CAACI,QAAN,CAAe,IAAIlC,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf,CAAf,EALiE,CAOjE;;AACA,cAAMmC,cAAc,GAAGpC,KAAK,CAAC+B,KAAD,CAAL,CAClBM,EADkB,CACf,IADe,EACT;AAAEC,YAAAA,KAAK,EAAE,IAAIrC,IAAJ,CAAS,GAAT,EAAc,GAAd,EAAmB,CAAnB;AAAT,WADS,EACyB;AAAEsC,YAAAA,MAAM,EAAE;AAAV,WADzB,EAElBC,IAFkB,CAEb,MAAM;AACR;AACA,iBAAKP,qBAAL,CAA2BN,QAA3B,EAAqCO,KAAK,GAAG,CAA7C;AACH,WALkB,EAMlBO,KANkB,EAAvB,CARiE,CAgBjE;;AACA,cAAMC,cAAc,GAAG1C,KAAK,CAAC+B,KAAD,CAAL,CAClBM,EADkB,CACf,IADe,EACT;AAAEC,YAAAA,KAAK,EAAE,IAAIrC,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf;AAAT,WADS,EACqB;AAAEsC,YAAAA,MAAM,EAAE;AAAV,WADrB,EAElBE,KAFkB,EAAvB;AAIA,eAAK/B,YAAL,CAAkBiC,IAAlB,CAAuBP,cAAvB,EAAuCM,cAAvC;AACH;;AAEKE,QAAAA,MAAM,GAAkB;AAAA;AAC7B;;AACKC,QAAAA,OAAO,GAAkB;AAAA;;AAAA;AAC3B,YAAA,MAAI,CAACC,cAAL,GAD2B,CACJ;AACvB;;;AACA,gBAAMnB,QAAQ,GAAG,MAAI,CAACC,UAAL,CAAiBD,QAAlC;;AACA,gBAAIA,QAAJ,EAAc;AACVA,cAAAA,QAAQ,CAACG,OAAT,CAAkBC,KAAD,IAAW;AACxBA,gBAAAA,KAAK,CAACC,MAAN,GAAe,KAAf;AACAD,gBAAAA,KAAK,CAACI,QAAN,CAAe,IAAIlC,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf,CAAf,EAFwB,CAEW;AACtC,eAHD;AAIH;AAT0B;AAU9B;;AAGO6C,QAAAA,cAAc,GAAS;AAC3B;AACA,eAAKpC,YAAL,CAAkBoB,OAAlB,CAA2B9B,KAAD,IAAW;AACjCA,YAAAA,KAAK,CAAC+C,IAAN;AACH,WAFD;AAGA,eAAKrC,YAAL,GAAoB,EAApB,CAL2B,CAO3B;;AACA,eAAKC,cAAL,CAAoBmB,OAApB,CAA6BkB,SAAD,IAAe;AACvCC,YAAAA,YAAY,CAACD,SAAD,CAAZ;AACH,WAFD;AAGA,eAAKrC,cAAL,GAAsB,EAAtB;AACH;;AA3FgC,O;;;;;iBAEP,I;;;;;;;iBAGE,I", "sourcesContent": ["import { _decorator, Node, Tween, tween, Vec3 } from 'cc';\r\nimport { BaseUI, UILayer, UIMgr, UIOpt } from \"db://assets/scripts/core/base/UIMgr\";\r\nimport { BundleName } from '../../const/BundleConst';\r\nimport { ButtonPlus } from './components/button/ButtonPlus';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('RewardUI')\r\nexport class RewardUI extends BaseUI {\r\n    @property(Node)\r\n    nodeReward: Node | null = null;\r\n\r\n    @property(ButtonPlus)\r\n    btnGet: ButtonPlus | null = null;\r\n\r\n    private activeTweens: Tween<Node>[] = [];\r\n    private activeTimeouts: ReturnType<typeof setTimeout>[] = [];\r\n\r\n    public static getUrl(): string { return \"prefab/ui/RewardUI\"; }\r\n    public static getLayer(): UILayer { return UILayer.Top }\r\n    public static getBundleName(): string { return BundleName.Home }\r\n    public static getUIOption(): UIOpt {\r\n        return { isClickBgCloseUI: false }\r\n    }\r\n\r\n    protected onLoad(): void {\r\n        this.btnGet!.addClick(this.onGetClick, this);\r\n    }\r\n    async onGetClick() {\r\n        UIMgr.closeUI(RewardUI);\r\n    }\r\n\r\n    async onShow(rewardID: number): Promise<void> {\r\n        this.showNodesSequentiallyWithScale();\r\n    }\r\n\r\n    private showNodesSequentiallyWithScale(): void {\r\n        const children = this.nodeReward!.children;\r\n        if (!children || children.length === 0) return;\r\n\r\n        // 初始隐藏所有子节点\r\n        children.forEach((child) => {\r\n            child.active = false;\r\n        });\r\n\r\n        // 显示第一个节点并启动动画\r\n        this.showNodeWithHalfScale(children, 0);\r\n    }\r\n    private showNodeWithHalfScale(children: Node[], index: number): void {\r\n        if (index >= children.length) return;\r\n\r\n        const child = children[index];\r\n        child.active = true;\r\n        child.setScale(new Vec3(0, 0, 1));\r\n\r\n        // 前半部分动画：缩放到一半\r\n        const halfScaleTween = tween(child)\r\n            .to(0.03, { scale: new Vec3(0.5, 0.5, 1) }, { easing: 'quadOut' })\r\n            .call(() => {\r\n                // 缩放到一半时，触发下一个节点\r\n                this.showNodeWithHalfScale(children, index + 1);\r\n            })\r\n            .start();\r\n\r\n        // 后半部分动画：从一半缩放到完整\r\n        const fullScaleTween = tween(child)\r\n            .to(0.03, { scale: new Vec3(1, 1, 1) }, { easing: 'quadOut' })\r\n            .start();\r\n\r\n        this.activeTweens.push(halfScaleTween, fullScaleTween);\r\n    }\r\n\r\n    async onHide(): Promise<void> {\r\n    }\r\n    async onClose(): Promise<void> {\r\n        this.stopAllEffects(); // 停止所有动画\r\n        // 可选：重置节点状态\r\n        const children = this.nodeReward!.children;\r\n        if (children) {\r\n            children.forEach((child) => {\r\n                child.active = false;\r\n                child.setScale(new Vec3(1, 1, 1)); // 恢复默认缩放\r\n            });\r\n        }\r\n    }\r\n\r\n\r\n    private stopAllEffects(): void {\r\n        // 停止所有 tween 动画\r\n        this.activeTweens.forEach((tween) => {\r\n            tween.stop();\r\n        });\r\n        this.activeTweens = [];\r\n\r\n        // 清除所有 setTimeout\r\n        this.activeTimeouts.forEach((timeoutId) => {\r\n            clearTimeout(timeoutId as unknown as number);\r\n        });\r\n        this.activeTimeouts = [];\r\n    }\r\n}\r\n"]}