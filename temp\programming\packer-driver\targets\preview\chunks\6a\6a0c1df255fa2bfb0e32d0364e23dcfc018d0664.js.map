{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/level/LevelEditorEventUI.ts"], "names": ["_decorator", "Enum", "CCString", "assetManager", "instantiate", "AudioClip", "LevelDataEventTriggerType", "LevelDataEventTriggerLog", "LevelDataEventWaveGroup", "newTrigger", "LevelDataEventCondtionType", "Wave", "WavePreview", "LevelEditorElemUI", "LevelEditorWaveGroup", "LevelEditorCondition", "ccclass", "property", "executeInEditMode", "LevelEditorEventTrigger", "type", "visible", "Log", "Audio", "_index", "data", "_audio", "_waveGroup", "_type", "value", "message", "audio", "audioUUID", "uuid", "waveGroup", "for<PERSON>ach", "levelDataWaveGroup", "wavePrefab", "length", "prefab", "waveUUID", "push", "weight", "LevelEditorEventUI", "update", "dt", "i", "conditions", "cond", "targetElemID", "_targetElem", "elems", "node", "scene", "getComponentsInChildren", "elem", "elemID", "initByLevelData", "condition", "triggers", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trigger", "loadAny", "err", "console", "error", "waveTrigger", "editorWaveGroup", "waveNode", "<PERSON><PERSON><PERSON><PERSON>", "setupWave", "getComponent", "fillLevelData", "wave", "instance"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAAuFC,MAAAA,Q,OAAAA,Q;AAA4BC,MAAAA,Y,OAAAA,Y;AAAoEC,MAAAA,W,OAAAA,W;AAA8BC,MAAAA,S,OAAAA,S;;AAI1MC,MAAAA,yB,iBAAAA,yB;;AACvBC,MAAAA,wB,iBAAAA,wB;;AAE2BC,MAAAA,uB,iBAAAA,uB;;AAC3BC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,0B,iBAAAA,0B;;AAEAC,MAAAA,I,iBAAAA,I;;AACAC,MAAAA,W,iBAAAA,W;;AAEAC,MAAAA,iB,iBAAAA,iB;;AACAC,MAAAA,oB,kBAAAA,oB;;AACAC,MAAAA,oB,kBAAAA,oB;;;;;;;;;OAfH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2ClB,U;;yCAkBpCmB,uB,WADZH,OAAO,CAAC,yBAAD,C,UAKHC,QAAQ,CAAC;AACNG,QAAAA,IAAI,EAACnB,IAAI;AAAA;AAAA;AADH,OAAD,C,UAYRgB,QAAQ,CAAC;AACNG,QAAAA,IAAI,EAAElB,QADA;;AAENmB,QAAAA,OAAO,GAAI;AACP,iBAAO,KAAKD,IAAL,IAAa;AAAA;AAAA,sEAA0BE,GAA9C;AACH;;AAJK,OAAD,C,UAcRL,QAAQ,CAAC;AACNG,QAAAA,IAAI,EAAEf,SADA;;AAENgB,QAAAA,OAAO,GAAI;AACP,iBAAO,KAAKD,IAAL,IAAa;AAAA;AAAA,sEAA0BG,KAA9C;AACH;;AAJK,OAAD,C,UAmBRN,QAAQ,CAAC;AACNG,QAAAA,IAAI,EAAE;AAAA;AAAA,yDADA;;AAENC,QAAAA,OAAO,GAAI;AACP,iBAAO,KAAKD,IAAL,IAAa;AAAA;AAAA,sEAA0BT,IAA9C;AACH;;AAJK,OAAD,C,2BAlDb,MACaQ,uBADb,CACoC;AAAA;AAAA,eACzBK,MADyB,GAChB,CADgB;AAAA,eAEzBC,IAFyB,GAEM;AAAA;AAAA,qEAFN;AAAA,eA6BzBC,MA7ByB,GA6BA,IA7BA;AAAA,eAgDzBC,UAhDyB,GAgDY,EAhDZ;AAAA;;AAOjB,YAAJP,IAAI,GAA6B;AACxC,iBAAO,KAAKK,IAAL,CAAUG,KAAjB;AACH;;AACc,YAAJR,IAAI,CAACS,KAAD,EAAmC;AAC9C,cAAI,KAAKJ,IAAL,CAAUG,KAAV,IAAmBC,KAAvB,EAA8B;AAC1B,iBAAKJ,IAAL,GAAY;AAAA;AAAA,0CAAW;AAACG,cAAAA,KAAK,EAAEC;AAAR,aAAX,CAAZ;AACH;AACJ;;AAQiB,YAAPC,OAAO,GAAW;AACzB,iBAAQ,KAAKL,IAAN,CAAwCK,OAA/C;AACH;;AACiB,YAAPA,OAAO,CAACD,KAAD,EAAgB;AAC7B,eAAKJ,IAAN,CAAwCK,OAAxC,GAAkDD,KAAlD;AACH;;AASe,YAALE,KAAK,GAAmB;AAC/B,iBAAO,KAAKL,MAAZ;AACH;;AACe,YAALK,KAAK,CAACF,KAAD,EAAwB;AACpC,eAAKH,MAAL,GAAcG,KAAd;;AACA,cAAIA,KAAJ,EAAW;AACN,iBAAKJ,IAAN,CAA0CO,SAA1C,GAAsDH,KAAK,CAACI,IAA5D;AACH,WAFD,MAEO;AACF,iBAAKR,IAAN,CAA0CO,SAA1C,GAAsD,EAAtD;AACH;AACJ;;AASmB,YAATE,SAAS,GAA2B;AAC3C,iBAAO,KAAKP,UAAZ;AACH;;AACmB,YAATO,SAAS,CAACL,KAAD,EAAgC;AAChD,eAAKF,UAAL,GAAkBE,KAAlB;;AACA,cAAIA,KAAJ,EAAW;AACN,iBAAKJ,IAAN,CAAyCS,SAAzC,GAAqD,EAArD;AACAL,YAAAA,KAAK,CAACM,OAAN,CAAeD,SAAD,IAAe;AACzB,kBAAIE,kBAAkB,GAAG;AAAA;AAAA,uEAAzB;;AACA,kBAAIF,SAAS,CAACG,UAAV,IAAwBH,SAAS,CAACG,UAAV,CAAqBC,MAArB,GAA8B,CAA1D,EAA6D;AACzDJ,gBAAAA,SAAS,CAACG,UAAV,CAAqBF,OAArB,CAA8BI,MAAD,IAAY;AACrCH,kBAAAA,kBAAkB,CAACI,QAAnB,CAA4BC,IAA5B,CAAiCF,MAAM,GAAGA,MAAM,CAACN,IAAV,GAAiB,EAAxD;AACH,iBAFD;AAGH;;AACDG,cAAAA,kBAAkB,CAACM,MAAnB,GAA4BR,SAAS,CAACQ,MAAtC;AACC,mBAAKjB,IAAN,CAAyCS,SAAzC,CAAmDO,IAAnD,CAAwDL,kBAAxD;AACH,aATD;AAUH,WAZD,MAYO;AACF,iBAAKX,IAAN,CAAyCS,SAAzC,GAAqD,EAArD;AACH;AACJ;;AA3E+B,O;;oCAgFvBS,kB,YAFZ3B,OAAO,CAAC,oBAAD,C,UACPE,iBAAiB,E,UAEbD,QAAQ,CAAC;AAAA;AAAA,uDAAD,C,UAERA,QAAQ,CAAC,CAACE,uBAAD,CAAD,C,6CALb,MAEawB,kBAFb;AAAA;AAAA,kDAE0D;AAAA;AAAA;;AAAA;;AAAA;AAAA;;AAM/CC,QAAAA,MAAM,CAACC,EAAD,EAAmB;AAC5B,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,UAAL,CAAgBT,MAApC,EAA4CQ,CAAC,EAA7C,EAAiD;AAC7C,gBAAME,IAAI,GAAG,KAAKD,UAAL,CAAgBD,CAAhB,CAAb;AACAE,YAAAA,IAAI,CAACxB,MAAL,GAAcsB,CAAd;;AACA,gBAAIE,IAAI,CAAC5B,IAAL,IAAa;AAAA;AAAA,0EAA2BT,IAAxC,IACIqC,IAAI,CAACvB,IAAN,CAA0CwB,YAA1C,IAA0D,EAD7D,IAEGD,IAAI,CAACE,WAAL,IAAoB,IAF3B,EAEiC;AAC7B,kBAAMC,KAAK,GAAG,KAAKC,IAAL,CAAUC,KAAV,CAAgBC,uBAAhB;AAAA;AAAA,yDAAd;;AACA,mBAAK,IAAIC,IAAT,IAAiBJ,KAAjB,EAAwB;AACpB,oBAAII,IAAI,CAACC,MAAL,IAAgBR,IAAI,CAACvB,IAAN,CAA0CwB,YAA7D,EAA2E;AACvED,kBAAAA,IAAI,CAACE,WAAL,GAAmBK,IAAnB;AACA;AACH;AACJ;AACJ;AACJ;AACJ;;AAEME,QAAAA,eAAe,CAAChC,IAAD,EAAuB;AAAA;;AACzC,gBAAMgC,eAAN,CAAsBhC,IAAtB;;AACA,cAAIA,IAAI,CAACsB,UAAT,EAAqB;AACjB,iBAAK,IAAID,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGrB,IAAI,CAACsB,UAAL,CAAgBT,MAApC,EAA4CQ,CAAC,EAA7C,EAAiD;AAC7C,kBAAMY,SAAS,GAAG;AAAA;AAAA,iEAAlB;AACAA,cAAAA,SAAS,CAAClC,MAAV,GAAmBsB,CAAnB;AACAY,cAAAA,SAAS,CAACjC,IAAV,GAAiBA,IAAI,CAACsB,UAAL,CAAgBD,CAAhB,CAAjB;AACA,mBAAKC,UAAL,CAAgBN,IAAhB,CAAqBiB,SAArB;AACH;AACJ;;AACD,cAAIjC,IAAI,CAACkC,QAAT,EAAmB;AACf;AACA,iBAAKP,IAAL,CAAUQ,iBAAV;;AAFe,yCAIgC;AAC3C,kBAAMC,OAAO,GAAG,IAAI1C,uBAAJ,EAAhB;AACA0C,cAAAA,OAAO,CAACrC,MAAR,GAAiBsB,EAAjB;AACAe,cAAAA,OAAO,CAACpC,IAAR,GAAeA,IAAI,CAACkC,QAAL,CAAcb,EAAd,CAAf;;AACA,kBAAIe,OAAO,CAACpC,IAAR,CAAaG,KAAb,IAAsB;AAAA;AAAA,0EAA0BL,KAApD,EAA2D;AACvD,oBAAIU,IAAI,GAAI4B,OAAO,CAACpC,IAAT,CAA6CO,SAAxD;;AACA,oBAAIC,IAAI,IAAI,EAAZ,EAAgB;AACZ9B,kBAAAA,YAAY,CAAC2D,OAAb,CAAqB;AAAC7B,oBAAAA,IAAI,EAACA;AAAN,mBAArB,EAAkC,CAAC8B,GAAD,EAAMhC,KAAN,KAA0B;AACxD,wBAAIgC,GAAJ,EAAS;AACLC,sBAAAA,OAAO,CAACC,KAAR,CAAc,mDAAd,EAAmEF,GAAnE;AACA;AACH;;AACDF,oBAAAA,OAAO,CAACnC,MAAR,GAAiBK,KAAjB;AACH,mBAND;AAOH;AACJ;;AACD,kBAAI8B,OAAO,CAACpC,IAAR,CAAaG,KAAb,IAAsB;AAAA;AAAA,0EAA0BjB,IAApD,EAA0D;AACtD,oBAAIuD,WAAW,GAAGL,OAAO,CAACpC,IAA1B;AACAyC,gBAAAA,WAAW,CAAChC,SAAZ,CAAsBC,OAAtB,CAA+BD,SAAD,IAAe;AACzC,sBAAIiC,eAAe,GAAG;AAAA;AAAA,qEAAtB;AACAjC,kBAAAA,SAAS,CAACM,QAAV,CAAmBL,OAAnB,CAA4BF,IAAD,IAAU;AACjC9B,oBAAAA,YAAY,CAAC2D,OAAb,CAAqB;AAAC7B,sBAAAA,IAAI,EAACA;AAAN,qBAArB,EAAkC,CAAC8B,GAAD,EAAMxB,MAAN,KAAwB;AACtD,0BAAIwB,GAAJ,EAAS;AACLC,wBAAAA,OAAO,CAACC,KAAR,CAAc,yDAAd,EAAyEF,GAAzE;AACA;AACH;;AACDI,sBAAAA,eAAe,CAAC9B,UAAhB,CAA2BI,IAA3B,CAAgCF,MAAhC,EALsD,CAOtD;;AACA,0BAAM6B,QAAQ,GAAGhE,WAAW,CAACmC,MAAD,CAA5B;;AACA,sBAAA,KAAI,CAACa,IAAL,CAAUiB,QAAV,CAAmBD,QAAnB;;AACA,sBAAA,KAAI,CAACE,SAAL,CAAeF,QAAQ,CAACG,YAAT;AAAA;AAAA,uCAAf;AACH,qBAXD;AAYH,mBAbD;AAcAJ,kBAAAA,eAAe,CAACzB,MAAhB,GAAyBR,SAAS,CAACQ,MAAnC;AACAmB,kBAAAA,OAAO,CAAC3B,SAAR,CAAkBO,IAAlB,CAAuB0B,eAAvB;AACH,iBAlBD;AAmBH;;AACD,cAAA,KAAI,CAACR,QAAL,CAAclB,IAAd,CAAmBoB,OAAnB;AACH,aA3Cc;;AAIf,iBAAK,IAAIf,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAGrB,IAAI,CAACkC,QAAL,CAAcrB,MAAlC,EAA0CQ,EAAC,EAA3C;AAAA;AAAA;AAwCH;AACJ;;AAEM0B,QAAAA,aAAa,CAAC/C,IAAD,EAAuB;AACvC,gBAAM+C,aAAN,CAAoB/C,IAApB;AACAA,UAAAA,IAAI,CAACsB,UAAL,GAAkB,EAAlB;AACA,eAAKA,UAAL,CAAgBZ,OAAhB,CAAyBa,IAAD,IAAU;AAC9B,gBAAIA,IAAI,IAAI,IAAZ,EAAkB;AACdvB,cAAAA,IAAI,CAACsB,UAAL,CAAgBN,IAAhB,CAAqBO,IAAI,CAACvB,IAA1B;AACH;AACJ,WAJD;AAKA,eAAKkC,QAAL,CAAcxB,OAAd,CAAuB0B,OAAD,IAAa;AAC/B,gBAAIA,OAAO,IAAI,IAAf,EAAqB;AACjBpC,cAAAA,IAAI,CAACkC,QAAL,CAAclB,IAAd,CAAmBoB,OAAO,CAACpC,IAA3B;AACH;AACJ,WAJD;AAKH;;AAEO6C,QAAAA,SAAS,CAACG,IAAD,EAAwB;AAAA;;AACrC,cAAIA,IAAI,IAAI,IAAZ,EACI;AAEJ;AAAA;AAAA,0CAAYC,QAAZ,uBAAsBJ,SAAtB,CAAgCG,IAAhC;AACH;;AArGqD,O;;;;;iBAEV,E;;;;;;;iBAEC,E", "sourcesContent": ["import { _decorator, Enum, CCFloat, Component, JsonAsset, Node, Prefab, Slider, Vec3, ValueType, CCBoolean, CCString, Asset, resources, assetManager, AssetManager, Sprite, SpriteFrame, SpriteAtlas, math, instantiate, Vec2, CCInteger, AudioClip } from 'cc';\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\n\r\nimport { LevelDataEvent } from 'db://assets/bundles/common/script/leveldata/leveldata';\r\nimport { LevelDataEventTrigger, LevelDataEventTriggerType } from 'db://assets/bundles/common/script/leveldata/trigger/LevelDataEventTrigger';\r\nimport { LevelDataEventTriggerLog } from 'db://assets/bundles/common/script/leveldata/trigger/LevelDataEventTriggerLog';\r\nimport { LevelDataEventTriggerAudio } from 'db://assets/bundles/common/script/leveldata/trigger/LevelDataEventTriggerAudio';\r\nimport { LevelDataEventTriggerWave, LevelDataEventWaveGroup } from 'db://assets/bundles/common/script/leveldata/trigger/LevelDataEventTriggerWave';\r\nimport { newTrigger } from 'db://assets/bundles/common/script/leveldata/trigger/newTrigger';\r\nimport { LevelDataEventCondtionType } from 'db://assets/bundles/common/script/leveldata/condition/LevelDataEventCondtion';\r\nimport { LevelDataEventCondtionWave } from 'db://assets/bundles/common/script/leveldata/condition/LevelDataEventCondtionWave';\r\nimport { Wave } from 'db://assets/bundles/common/script/game/wave/Wave';\r\nimport { WavePreview } from './preview/WavePreview';\r\n\r\nimport { LevelEditorElemUI } from './LevelEditorElemUI';\r\nimport { LevelEditorWaveGroup } from './LevelEditorWaveParam';\r\nimport { LevelEditorCondition } from './LevelEditorCondition';\r\n\r\n@ccclass('LevelEditorEventTrigger')\r\nexport class LevelEditorEventTrigger{\r\n    public _index = 0;\r\n    public data : LevelDataEventTrigger = new LevelDataEventTriggerLog();\r\n\r\n    @property({\r\n        type:Enum(LevelDataEventTriggerType),\r\n    })\r\n    public get type(): LevelDataEventTriggerType{\r\n        return this.data._type;\r\n    }\r\n    public set type(value: LevelDataEventTriggerType) {\r\n        if (this.data._type != value) {\r\n            this.data = newTrigger({_type: value});\r\n        }\r\n    }\r\n\r\n    @property({\r\n        type :CCString,\r\n        visible () {\r\n            return this.type == LevelDataEventTriggerType.Log ;\r\n        }\r\n    })\r\n    public get message(): string {\r\n        return (this.data as LevelDataEventTriggerLog).message;\r\n    }\r\n    public set message(value: string) {\r\n        (this.data as LevelDataEventTriggerLog).message = value;\r\n    }\r\n\r\n    public _audio: AudioClip|null = null;\r\n    @property({\r\n        type :AudioClip,\r\n        visible () {\r\n            return this.type == LevelDataEventTriggerType.Audio;\r\n        }\r\n    })\r\n    public get audio(): AudioClip|null {\r\n        return this._audio;\r\n    }\r\n    public set audio(value: AudioClip|null) {\r\n        this._audio = value;\r\n        if (value) {\r\n            (this.data as LevelDataEventTriggerAudio).audioUUID = value.uuid;\r\n        } else {\r\n            (this.data as LevelDataEventTriggerAudio).audioUUID = \"\";\r\n        }\r\n    }\r\n\r\n    public _waveGroup: LevelEditorWaveGroup[] = []; \r\n    @property({\r\n        type: [LevelEditorWaveGroup],\r\n        visible () {\r\n            return this.type == LevelDataEventTriggerType.Wave;\r\n        }\r\n    })\r\n    public get waveGroup(): LevelEditorWaveGroup[] {\r\n        return this._waveGroup;\r\n    }\r\n    public set waveGroup(value: LevelEditorWaveGroup[]) {\r\n        this._waveGroup = value;\r\n        if (value) {\r\n            (this.data as LevelDataEventTriggerWave).waveGroup = [];\r\n            value.forEach((waveGroup) => {\r\n                let levelDataWaveGroup = new LevelDataEventWaveGroup();\r\n                if (waveGroup.wavePrefab && waveGroup.wavePrefab.length > 0) {\r\n                    waveGroup.wavePrefab.forEach((prefab) => {\r\n                        levelDataWaveGroup.waveUUID.push(prefab ? prefab.uuid : \"\");\r\n                    });\r\n                }\r\n                levelDataWaveGroup.weight = waveGroup.weight;\r\n                (this.data as LevelDataEventTriggerWave).waveGroup.push(levelDataWaveGroup);\r\n            });\r\n        } else {\r\n            (this.data as LevelDataEventTriggerWave).waveGroup = [];\r\n        }\r\n    }\r\n}\r\n\r\n@ccclass('LevelEditorEventUI')\r\n@executeInEditMode()\r\nexport class LevelEditorEventUI extends LevelEditorElemUI {\r\n    @property([LevelEditorCondition])\r\n    public conditions: LevelEditorCondition[] = [];\r\n    @property([LevelEditorEventTrigger])\r\n    public triggers: LevelEditorEventTrigger[] = [];\r\n\r\n    public update(dt: number): void {\r\n        for (let i = 0; i < this.conditions.length; i++) {\r\n            const cond = this.conditions[i];\r\n            cond._index = i;\r\n            if (cond.type == LevelDataEventCondtionType.Wave \r\n                && (cond.data as LevelDataEventCondtionWave).targetElemID != \"\" \r\n                && cond._targetElem == null) {\r\n                const elems = this.node.scene.getComponentsInChildren(LevelEditorElemUI);\r\n                for (let elem of elems) {\r\n                    if (elem.elemID == (cond.data as LevelDataEventCondtionWave).targetElemID) {\r\n                        cond._targetElem = elem;\r\n                        break;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    public initByLevelData(data: LevelDataEvent) {\r\n        super.initByLevelData(data)\r\n        if (data.conditions) {\r\n            for (let i = 0; i < data.conditions.length; i++) {\r\n                const condition = new LevelEditorCondition();\r\n                condition._index = i;\r\n                condition.data = data.conditions[i];\r\n                this.conditions.push(condition);\r\n            }\r\n        }\r\n        if (data.triggers) {\r\n            // clear wave childrens \r\n            this.node.removeAllChildren();\r\n\r\n            for (let i = 0; i < data.triggers.length; i++) {\r\n                const trigger = new LevelEditorEventTrigger();\r\n                trigger._index = i;\r\n                trigger.data = data.triggers[i];\r\n                if (trigger.data._type == LevelDataEventTriggerType.Audio) {\r\n                    let uuid = (trigger.data as LevelDataEventTriggerAudio).audioUUID;\r\n                    if (uuid != \"\") {\r\n                        assetManager.loadAny({uuid:uuid}, (err, audio:AudioClip) => {\r\n                            if (err) {\r\n                                console.error(\"LevelEditorEventUI initByLevelData load audio err\", err);\r\n                                return;\r\n                            }\r\n                            trigger._audio = audio;\r\n                        });\r\n                    }\r\n                }\r\n                if (trigger.data._type == LevelDataEventTriggerType.Wave) {\r\n                    let waveTrigger = trigger.data as LevelDataEventTriggerWave;\r\n                    waveTrigger.waveGroup.forEach((waveGroup) => {\r\n                        let editorWaveGroup = new LevelEditorWaveGroup();\r\n                        waveGroup.waveUUID.forEach((uuid) => {\r\n                            assetManager.loadAny({uuid:uuid}, (err, prefab:Prefab) => {\r\n                                if (err) {\r\n                                    console.error(\"LevelEditorEventUI initByLevelData load wave prefab err\", err);\r\n                                    return;\r\n                                }\r\n                                editorWaveGroup.wavePrefab.push(prefab);\r\n\r\n                                // create wave\r\n                                const waveNode = instantiate(prefab);\r\n                                this.node.addChild(waveNode);\r\n                                this.setupWave(waveNode.getComponent(Wave));\r\n                            });\r\n                        });\r\n                        editorWaveGroup.weight = waveGroup.weight;\r\n                        trigger.waveGroup.push(editorWaveGroup);\r\n                    });\r\n                }\r\n                this.triggers.push(trigger);\r\n            }\r\n        }\r\n    }\r\n\r\n    public fillLevelData(data: LevelDataEvent) {\r\n        super.fillLevelData(data)\r\n        data.conditions = []\r\n        this.conditions.forEach((cond) => {\r\n            if (cond != null) {\r\n                data.conditions.push(cond.data);\r\n            }\r\n        })\r\n        this.triggers.forEach((trigger) => {\r\n            if (trigger != null) {\r\n                data.triggers.push(trigger.data);\r\n            }\r\n        })\r\n    }\r\n\r\n    private setupWave(wave: Wave|null): void {\r\n        if (wave == null) \r\n            return;\r\n\r\n        WavePreview.instance?.setupWave(wave);\r\n    }\r\n}"]}