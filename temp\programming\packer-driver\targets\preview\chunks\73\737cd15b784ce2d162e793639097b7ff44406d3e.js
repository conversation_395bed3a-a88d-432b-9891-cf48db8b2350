System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, _crd, GameResourceList;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "61f499mcnhBILxTYD34p53c", "GameResourceList", undefined);

      GameResourceList = {
        MainPlane: "prefabs/mainPlane/MainPlane",
        PrefabBoss: "prefabs/boss/BossPlane",
        Bullet: "prefabs/Bullet",
        EnemyPlane: "prefabs/enemy/EnemyPlane",
        HurtNum: "prefabs/effect/HurtNum",
        Hurt0: "prefabs/effect/Hurt",
        EmitterPrefabPath: "prefabs/emitter/",
        font_hurtNum: "font/hurtNum"
      }; // Add "game/" prefix to all values

      (() => {
        for (var key in GameResourceList) {
          GameResourceList[key] = "game/" + GameResourceList[key];
        }
      })();

      _export("default", GameResourceList);

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=737cd15b784ce2d162e793639097b7ff44406d3e.js.map