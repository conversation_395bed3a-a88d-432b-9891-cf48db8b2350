{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/const/GameResourceList.ts"], "names": ["GameResourceList", "MainPlane", "PrefabBoss", "Bullet", "EnemyPlane", "HurtNum", "Hurt0", "EmitterPrefabPath", "font_hurtNum", "key"], "mappings": ";;;;;;;;;;;;;;AAAIA,MAAAA,gB,GAAmB;AAEnBC,QAAAA,SAAS,EAAE,6BAFQ;AAGnBC,QAAAA,UAAU,EAAE,wBAHO;AAInBC,QAAAA,MAAM,EAAE,gBAJW;AAKnBC,QAAAA,UAAU,EAAE,0BALO;AAMnBC,QAAAA,OAAO,EAAE,wBANU;AAOnBC,QAAAA,KAAK,EAAE,qBAPY;AAQnBC,QAAAA,iBAAiB,EAAE,kBARA;AAUnBC,QAAAA,YAAY,EAAE;AAVK,O,EAavB;;AACA,OAAC,MAAM;AACC,aAAK,IAAMC,GAAX,IAAkBT,gBAAlB,EAAoC;AACpCA,UAAAA,gBAAgB,CAACS,GAAD,CAAhB,aAAiET,gBAAgB,CAACS,GAAD,CAAjF;AACH;AACJ,OAJD;;yBAMeT,gB", "sourcesContent": ["let GameResourceList = {\r\n\r\n    MainPlane: \"prefabs/mainPlane/MainPlane\",\r\n    PrefabBoss: \"prefabs/boss/BossPlane\",\r\n    Bullet: \"prefabs/Bullet\",\r\n    EnemyPlane: \"prefabs/enemy/EnemyPlane\",\r\n    HurtNum: \"prefabs/effect/HurtNum\",\r\n    Hurt0: \"prefabs/effect/Hurt\",\r\n    EmitterPrefabPath: \"prefabs/emitter/\",\r\n\r\n    font_hurtNum: \"font/hurtNum\",\r\n};\r\n\r\n// Add \"game/\" prefix to all values\r\n(() => {\r\n        for (const key in GameResourceList) {\r\n        GameResourceList[key as keyof typeof GameResourceList] = `game/${GameResourceList[key as keyof typeof GameResourceList]}`;\r\n    }\r\n})();\r\n\r\nexport default GameResourceList;"]}