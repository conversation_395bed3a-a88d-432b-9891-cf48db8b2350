System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, CCFloat, Component, Node, CCString, assetManager, instantiate, UITransform, view, Graphics, Color, Rect, LayerOffset, LayerSplicingMode, LayerType, LevelDataBackgroundLayer, LevelDataLayer, LevelDataRandTerrains, LevelDataScroll, LevelEditorLayerUI, LevelBackgroundLayer, LevelEditorUtils, LevelLayer, LevelRandTerrainsLayersUI, LevelRandTerrainsLayerUI, LevelRandTerrainUI, RandTerrain, WavePreview, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _crd, ccclass, property, executeInEditMode, BackgroundsNodeName, LevelEditorBaseUI;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfLayerOffset(extras) {
    _reporterNs.report("LayerOffset", "db://assets/bundles/common/script/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLayerSplicingMode(extras) {
    _reporterNs.report("LayerSplicingMode", "db://assets/bundles/common/script/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLayerType(extras) {
    _reporterNs.report("LayerType", "db://assets/bundles/common/script/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelData(extras) {
    _reporterNs.report("LevelData", "db://assets/bundles/common/script/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataBackgroundLayer(extras) {
    _reporterNs.report("LevelDataBackgroundLayer", "db://assets/bundles/common/script/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataLayer(extras) {
    _reporterNs.report("LevelDataLayer", "db://assets/bundles/common/script/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataRandTerrain(extras) {
    _reporterNs.report("LevelDataRandTerrain", "db://assets/bundles/common/script/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataRandTerrains(extras) {
    _reporterNs.report("LevelDataRandTerrains", "db://assets/bundles/common/script/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataScroll(extras) {
    _reporterNs.report("LevelDataScroll", "db://assets/bundles/common/script/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelEditorLayerUI(extras) {
    _reporterNs.report("LevelEditorLayerUI", "./LevelEditorLayerUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelBackgroundLayer(extras) {
    _reporterNs.report("LevelBackgroundLayer", "./utils", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelEditorUtils(extras) {
    _reporterNs.report("LevelEditorUtils", "./utils", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelLayer(extras) {
    _reporterNs.report("LevelLayer", "./utils", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelRandTerrainsLayersUI(extras) {
    _reporterNs.report("LevelRandTerrainsLayersUI", "./utils", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelRandTerrainsLayerUI(extras) {
    _reporterNs.report("LevelRandTerrainsLayerUI", "./utils", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelRandTerrainUI(extras) {
    _reporterNs.report("LevelRandTerrainUI", "./utils", _context.meta, extras);
  }

  function _reportPossibleCrUseOfRandTerrain(extras) {
    _reporterNs.report("RandTerrain", "db://assets/bundles/common/script/game/dyncTerrain/RandTerrain", _context.meta, extras);
  }

  function _reportPossibleCrUseOfWavePreview(extras) {
    _reporterNs.report("WavePreview", "./preview/WavePreview", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      CCFloat = _cc.CCFloat;
      Component = _cc.Component;
      Node = _cc.Node;
      CCString = _cc.CCString;
      assetManager = _cc.assetManager;
      instantiate = _cc.instantiate;
      UITransform = _cc.UITransform;
      view = _cc.view;
      Graphics = _cc.Graphics;
      Color = _cc.Color;
      Rect = _cc.Rect;
    }, function (_unresolved_2) {
      LayerOffset = _unresolved_2.LayerOffset;
      LayerSplicingMode = _unresolved_2.LayerSplicingMode;
      LayerType = _unresolved_2.LayerType;
      LevelDataBackgroundLayer = _unresolved_2.LevelDataBackgroundLayer;
      LevelDataLayer = _unresolved_2.LevelDataLayer;
      LevelDataRandTerrains = _unresolved_2.LevelDataRandTerrains;
      LevelDataScroll = _unresolved_2.LevelDataScroll;
    }, function (_unresolved_3) {
      LevelEditorLayerUI = _unresolved_3.LevelEditorLayerUI;
    }, function (_unresolved_4) {
      LevelBackgroundLayer = _unresolved_4.LevelBackgroundLayer;
      LevelEditorUtils = _unresolved_4.LevelEditorUtils;
      LevelLayer = _unresolved_4.LevelLayer;
      LevelRandTerrainsLayersUI = _unresolved_4.LevelRandTerrainsLayersUI;
      LevelRandTerrainsLayerUI = _unresolved_4.LevelRandTerrainsLayerUI;
      LevelRandTerrainUI = _unresolved_4.LevelRandTerrainUI;
    }, function (_unresolved_5) {
      RandTerrain = _unresolved_5.RandTerrain;
    }, function (_unresolved_6) {
      WavePreview = _unresolved_6.WavePreview;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "a4bf2J2KGJJV7RbX1jwoQWJ", "LevelEditorBaseUI", undefined);

      __checkObsolete__(['_decorator', 'CCFloat', 'Component', 'Node', 'CCString', 'Prefab', 'assetManager', 'instantiate', 'UITransform', 'view', 'Graphics', 'Color', 'Rect']);

      ({
        ccclass,
        property,
        executeInEditMode
      } = _decorator);
      BackgroundsNodeName = "backgrounds";

      _export("LevelEditorBaseUI", LevelEditorBaseUI = (_dec = ccclass('LevelEditorBaseUI'), _dec2 = executeInEditMode(), _dec3 = property(CCString), _dec4 = property({
        type: CCFloat,
        displayName: "关卡时长"
      }), _dec5 = property({
        type: _crd && LevelBackgroundLayer === void 0 ? (_reportPossibleCrUseOfLevelBackgroundLayer({
          error: Error()
        }), LevelBackgroundLayer) : LevelBackgroundLayer,
        displayName: "背景"
      }), _dec6 = property({
        type: [_crd && LevelLayer === void 0 ? (_reportPossibleCrUseOfLevelLayer({
          error: Error()
        }), LevelLayer) : LevelLayer],
        displayName: "地面"
      }), _dec7 = property({
        type: [_crd && LevelLayer === void 0 ? (_reportPossibleCrUseOfLevelLayer({
          error: Error()
        }), LevelLayer) : LevelLayer],
        displayName: "天空"
      }), _dec(_class = _dec2(_class = (_class2 = class LevelEditorBaseUI extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "levelname", _descriptor, this);

          _initializerDefineProperty(this, "totalTime", _descriptor2, this);

          this._totalHeight = 0;

          _initializerDefineProperty(this, "backgroundLayer", _descriptor3, this);

          _initializerDefineProperty(this, "floorLayers", _descriptor4, this);

          _initializerDefineProperty(this, "skyLayers", _descriptor5, this);

          this.backgroundLayerNode = null;
          this.floorLayersNode = null;
          this.skyLayersNode = null;
          this._isLoadingScrollNodes = false;
          this._play = false;
          this._drawNode = null;
        }

        onLoad() {
          var _this$floorLayersNode;

          console.log("LevelEditorBaseUI start.");
          this.backgroundLayerNode = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
            error: Error()
          }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(this.node, "BackgroundLayer");
          this.floorLayersNode = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
            error: Error()
          }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(this.node, "FloorLayers");
          this.skyLayersNode = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
            error: Error()
          }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(this.node, "SkyLayers");
          this._drawNode = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
            error: Error()
          }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(this.node, "DrawNode");
          console.log("LevelEditorBaseUI start " + ((_this$floorLayersNode = this.floorLayersNode) == null ? void 0 : _this$floorLayersNode.uuid));
        }

        update(dt) {
          this.checkLayerNode(this.floorLayersNode, this.floorLayers);
          this.checkLayerNode(this.skyLayersNode, this.skyLayers);
        }

        setBackgroundNodePosition(node, yOff) {
          var height = node.getComponent(UITransform).contentSize.height;
          node.setPosition(0, yOff - view.getVisibleSize().height / 2 + height / 2);
          return height;
        }

        tick(progress) {
          var yOff = 0;

          for (var i = 0; i < this.backgroundLayer.backgroundsNode.children.length; i++) {
            var bg = this.backgroundLayer.backgroundsNode.children[i];
            yOff += this.setBackgroundNodePosition(bg, yOff);
          }

          while (this.backgroundLayer.backgrounds.length > 0 && yOff < this._totalHeight) {
            var _bg = null;
            var bgIndex = this.backgroundLayer.backgroundsNode.children.length % this.backgroundLayer.backgrounds.length;
            var prefab = this.backgroundLayer.backgrounds[bgIndex];

            if (prefab != null) {
              _bg = instantiate(prefab);
            }

            if (_bg == null) {
              _bg = new Node("empty");
              _bg.addComponent(UITransform).height = 1024;
            }

            this.backgroundLayer.backgroundsNode.addChild(_bg);
            yOff += this.setBackgroundNodePosition(_bg, yOff);
          }

          for (var _i = this.backgroundLayer.backgroundsNode.children.length - 1; _i >= 0; _i--) {
            var _bg2 = this.backgroundLayer.backgroundsNode.children[_i];

            if (_bg2.position.y - _bg2.getComponent(UITransform).height / 2 > this._totalHeight) {
              _bg2.removeFromParent();
            } else {
              break;
            }
          }

          this.backgroundLayer.node.getComponent(_crd && LevelEditorLayerUI === void 0 ? (_reportPossibleCrUseOfLevelEditorLayerUI({
            error: Error()
          }), LevelEditorLayerUI) : LevelEditorLayerUI).tick(progress, this.totalTime, this.backgroundLayer.speed);
          this.floorLayers.forEach(layer => {
            layer.node.getComponent(_crd && LevelEditorLayerUI === void 0 ? (_reportPossibleCrUseOfLevelEditorLayerUI({
              error: Error()
            }), LevelEditorLayerUI) : LevelEditorLayerUI).tick(progress, this.totalTime, layer.speed);
          });
          this.skyLayers.forEach(layer => {
            layer.node.getComponent(_crd && LevelEditorLayerUI === void 0 ? (_reportPossibleCrUseOfLevelEditorLayerUI({
              error: Error()
            }), LevelEditorLayerUI) : LevelEditorLayerUI).tick(progress, this.totalTime, layer.speed);
          });
        }

        static addLayer(parentNode, name) {
          var layerNode = new Node(name);
          var layerCom = layerNode.addComponent(_crd && LevelEditorLayerUI === void 0 ? (_reportPossibleCrUseOfLevelEditorLayerUI({
            error: Error()
          }), LevelEditorLayerUI) : LevelEditorLayerUI);
          parentNode.addChild(layerNode);
          return layerCom;
        }

        checkLayerNode(parentNode, layers) {
          var removeLayerNodes = [];
          parentNode.children.forEach(node => {
            var layerCom = node.getComponent(_crd && LevelEditorLayerUI === void 0 ? (_reportPossibleCrUseOfLevelEditorLayerUI({
              error: Error()
            }), LevelEditorLayerUI) : LevelEditorLayerUI);

            if (layerCom == null) {
              console.log("Level checkLayerNode remove " + node.name + " because layerCom == null\"");
              removeLayerNodes.push(node);
              return;
            }

            if (layers.find(layer => layer.node == node) == null) {
              console.log("Level checkLayerNode remove " + node.name + " because not in layers\"");
              removeLayerNodes.push(node);
              return;
            }
          });
          removeLayerNodes.forEach(element => {
            element.removeFromParent();
          });
          layers.forEach((layer, i) => {
            if (layer.node == null || layer.node.isValid == false) {
              console.log("Level checkLayerNode add because layer == null");
              layer.node = LevelEditorBaseUI.addLayer(parentNode, "layer_" + i).node;
            }

            if (layer.type === (_crd && LayerType === void 0 ? (_reportPossibleCrUseOfLayerType({
              error: Error()
            }), LayerType) : LayerType).Scroll) {//this._checkScrollNode(layer, layer!.node);
            } else if (layer.type === (_crd && LayerType === void 0 ? (_reportPossibleCrUseOfLayerType({
              error: Error()
            }), LayerType) : LayerType).Random) {
              this._checkRandTerrainNode(layer, layer.node);
            }
          });
        }

        _checkScrollNode(data, parentNode) {
          var _this = this;

          return _asyncToGenerator(function* () {
            var scrollsNode = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
              error: Error()
            }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(parentNode, "scrolls");

            if (data.type != (_crd && LayerType === void 0 ? (_reportPossibleCrUseOfLayerType({
              error: Error()
            }), LayerType) : LayerType).Scroll) {
              scrollsNode.removeAllChildren();
              return;
            }

            if (_this._isLoadingScrollNodes) {
              console.log("LevelEditorBaseUI _checkScrollNode _isLoadingScrollNodes " + _this._isLoadingScrollNodes + " scrollsNode.children.length " + scrollsNode.children.length);
              return;
            }

            var isCountMatch = scrollsNode.children.length === data.scrollLayers.length;
            console.log("LevelEditorBaseUI _checkScrollNode children.length " + scrollsNode.children.length + " data.scrollLayers.length " + data.scrollLayers.length);

            if (!isCountMatch) {
              var loadPromises = [];
              scrollsNode.removeAllChildren();
              _this._isLoadingScrollNodes = true; // 标记为加载中

              data.scrollLayers.forEach((scroll, index) => {
                if (scroll.scrollPrefabs.length <= 0) return;
                scroll.scrollPrefabs.forEach(prefab => {
                  var loadPromise = new Promise(resolve => {
                    assetManager.loadAny({
                      uuid: prefab.uuid
                    }, (err, loadedPrefab) => {
                      if (err) {
                        console.error("Failed to load prefab:", err);
                        resolve();
                      } else {
                        resolve();
                      }
                    });
                  });
                  loadPromises.push(loadPromise);
                });
              });
              yield Promise.all(loadPromises);
              console.log("LevelEditorBaseUI _checkScrollNode data.scrollLayers " + data.scrollLayers.length);
              data.scrollLayers.forEach((scroll, index) => {
                var scrollNode = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
                  error: Error()
                }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(scrollsNode, "scroll_" + index);
                var totalHeight = data.speed * _this.totalTime;
                var posOffsetY = 0;
                var height = 0;
                var prefabIndex = 0;

                while (height < totalHeight) {
                  var curPrefab = scroll.scrollPrefabs[prefabIndex];
                  var child = instantiate(curPrefab);
                  var randomOffsetX = Math.random() * (scroll.splicingOffsetX.max - scroll.splicingOffsetX.min) + scroll.splicingOffsetX.min;
                  child.setPosition(randomOffsetX, posOffsetY, 0);
                  var offY = 0;

                  if (scroll.splicingMode === (_crd && LayerSplicingMode === void 0 ? (_reportPossibleCrUseOfLayerSplicingMode({
                    error: Error()
                  }), LayerSplicingMode) : LayerSplicingMode).node_height) {
                    offY = child.getComponent(UITransform).contentSize.height;
                  } else if (scroll.splicingMode === (_crd && LayerSplicingMode === void 0 ? (_reportPossibleCrUseOfLayerSplicingMode({
                    error: Error()
                  }), LayerSplicingMode) : LayerSplicingMode).fix_height) {
                    offY = 1334;
                  } else if (scroll.splicingMode === (_crd && LayerSplicingMode === void 0 ? (_reportPossibleCrUseOfLayerSplicingMode({
                    error: Error()
                  }), LayerSplicingMode) : LayerSplicingMode).random_height) {
                    offY = Math.max(scroll.splicingOffsetY.min, scroll.splicingOffsetY.max) + child.getComponent(UITransform).contentSize.height;
                  }

                  scrollNode.addChild(child);
                  posOffsetY += offY;
                  height += offY;
                  prefabIndex = (prefabIndex + 1) % scroll.scrollPrefabs.length;
                }
              });
              _this._isLoadingScrollNodes = false;
            }
          })();
        }

        _checkRandTerrainNode(data, parentNode) {
          var dynamicNode = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
            error: Error()
          }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(parentNode, "dynamic"); // 删除所有多余的dyna节点

          var currentDynaNodes = dynamicNode.children;

          for (var i = currentDynaNodes.length - 1; i >= 0; i--) {
            var node = currentDynaNodes[i];
            var match = node.name.match(/^dyna_(\d+)$/);

            if (match) {
              var index = parseInt(match[1]);

              if (index >= data.randomLayers.length) {
                node.removeFromParent();
              }
            }
          }

          if (data.type != (_crd && LayerType === void 0 ? (_reportPossibleCrUseOfLayerType({
            error: Error()
          }), LayerType) : LayerType).Random || data.randomLayers.length === 0) {
            dynamicNode.removeAllChildren();
            return;
          }

          var needRebuild = false;
          var rebuildList = [];

          for (var _i2 = 0; _i2 < data.randomLayers.length; _i2++) {
            var randTerrains = data.randomLayers[_i2];
            var dynaNode = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
              error: Error()
            }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(dynamicNode, "dyna_" + _i2); // 计算该dyna节点应有的总地形元素数量

            var expectedChildCount = 0;

            for (var terrains of randTerrains.dynamicTerrains) {
              expectedChildCount += terrains.dynamicTerrain.length;
            } // 检查子节点数量是否匹配


            if (dynaNode.children.length !== expectedChildCount) {
              needRebuild = true;
              rebuildList.push(_i2);
              continue;
            } // 检查每个子节点对应的预制体UUID是否匹配


            var childIndex = 0;
            var isUUIDMatch = true;

            for (var _terrains of randTerrains.dynamicTerrains) {
              for (var terrain of _terrains.dynamicTerrain) {
                var _childNode$_prefab, _terrain$terrainEleme;

                var childNode = dynaNode.children[childIndex]; // @ts-ignore

                var childPrefabUUID = (_childNode$_prefab = childNode._prefab) == null || (_childNode$_prefab = _childNode$_prefab.asset) == null ? void 0 : _childNode$_prefab._uuid;
                var terrainUUID = terrain == null || (_terrain$terrainEleme = terrain.terrainElement) == null ? void 0 : _terrain$terrainEleme.uuid;

                if (childPrefabUUID !== terrainUUID) {
                  isUUIDMatch = false;
                  break;
                }

                childIndex++;
              }

              if (!isUUIDMatch) break;
            }

            if (!isUUIDMatch) {
              needRebuild = true;
              rebuildList.push(_i2);
            }
          }

          if (needRebuild) {
            var _loop = function _loop() {
              var dynaNode = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
                error: Error()
              }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(dynamicNode, "dyna_" + _index);
              dynaNode.removeAllChildren();
              var randTerrains = data.randomLayers[_index]; // 遍历所有地形组

              for (var j = 0; j < randTerrains.dynamicTerrains.length; j++) {
                var _terrains2 = randTerrains.dynamicTerrains[j]; // 遍历地形组中的每个地形元素

                for (var k = 0; k < _terrains2.dynamicTerrain.length; k++) {
                  var _terrain$terrainEleme2;

                  var _terrain = _terrains2.dynamicTerrain[k];
                  assetManager.loadAny({
                    uuid: _terrain == null || (_terrain$terrainEleme2 = _terrain.terrainElement) == null ? void 0 : _terrain$terrainEleme2.uuid
                  }, (err, prefab) => {
                    if (err) {
                      //console.error(`加载地形元素失败: ${terrain?.terrainElements?.uuid}`, err);
                      return;
                    }

                    var node = instantiate(prefab);
                    dynaNode.addChild(node);
                  });
                }
              }
            };

            //console.log("LevelEditorBaseUI _checkRandTerrainNode need rebuild");
            for (var _index of rebuildList) {
              _loop();
            }
          }
        }

        initByLevelData(data) {
          var _data$backgroundLayer, _data$backgroundLayer2, _data$backgroundLayer3, _instance;

          this.levelname = data.name;
          this.totalTime = data.totalTime;
          this.backgroundLayerNode.removeAllChildren();
          this.backgroundLayer = new (_crd && LevelBackgroundLayer === void 0 ? (_reportPossibleCrUseOfLevelBackgroundLayer({
            error: Error()
          }), LevelBackgroundLayer) : LevelBackgroundLayer)();
          this.backgroundLayer.backgrounds = [];
          (_data$backgroundLayer = data.backgroundLayer) == null || (_data$backgroundLayer = _data$backgroundLayer.backgrounds) == null || _data$backgroundLayer.forEach(background => {
            assetManager.loadAny({
              uuid: background
            }, (err, prefab) => {
              if (err) {
                console.error("LevelEditorBaseUI initByLevelData load background prefab err", err);
                return;
              }

              this.backgroundLayer.backgrounds.push(prefab);
            });
          });
          this.backgroundLayer.speed = (_data$backgroundLayer2 = data.backgroundLayer) == null ? void 0 : _data$backgroundLayer2.speed;
          this.backgroundLayer.remark = (_data$backgroundLayer3 = data.backgroundLayer) == null ? void 0 : _data$backgroundLayer3.remark;
          this._totalHeight = this.backgroundLayer.speed * this.totalTime;
          this.backgroundLayer.node = LevelEditorBaseUI.addLayer(this.backgroundLayerNode, "layer").node;
          this.backgroundLayer.backgroundsNode = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
            error: Error()
          }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(this.backgroundLayer.node, BackgroundsNodeName);
          this.backgroundLayer.backgroundsNode.setSiblingIndex(0);
          this.backgroundLayer.node.getComponent(_crd && LevelEditorLayerUI === void 0 ? (_reportPossibleCrUseOfLevelEditorLayerUI({
            error: Error()
          }), LevelEditorLayerUI) : LevelEditorLayerUI).initByLevelData(data.backgroundLayer);
          this.floorLayers = [];
          this.skyLayers = [];
          LevelEditorBaseUI.initLayers(this.floorLayersNode, this.floorLayers, data.floorLayers);
          LevelEditorBaseUI.initLayers(this.skyLayersNode, this.skyLayers, data.skyLayers);
          (_instance = (_crd && WavePreview === void 0 ? (_reportPossibleCrUseOfWavePreview({
            error: Error()
          }), WavePreview) : WavePreview).instance) == null || _instance.reset();

          this._drawNodeGraphics();
        }

        static initLayers(parentNode, layers, dataLayers) {
          parentNode.removeAllChildren();
          dataLayers.forEach((layer, i) => {
            var levelLayer = new (_crd && LevelLayer === void 0 ? (_reportPossibleCrUseOfLevelLayer({
              error: Error()
            }), LevelLayer) : LevelLayer)();
            levelLayer.speed = layer.speed;
            levelLayer.type = layer.type;
            levelLayer.remark = layer.remark;
            levelLayer.zIndex = layer.zIndex;
            levelLayer.node = LevelEditorBaseUI.addLayer(parentNode, "layer_" + i).node;
            levelLayer.node.setSiblingIndex(layer.zIndex);
            var levelEditorLayerUI = levelLayer.node.getComponent(_crd && LevelEditorLayerUI === void 0 ? (_reportPossibleCrUseOfLevelEditorLayerUI({
              error: Error()
            }), LevelEditorLayerUI) : LevelEditorLayerUI);

            if (layer.type === (_crd && LayerType === void 0 ? (_reportPossibleCrUseOfLayerType({
              error: Error()
            }), LayerType) : LayerType).Scroll) {
              console.log("initScorllsByLevelData levelLayer.length -------------", levelLayer.scrollLayers.length);
              levelEditorLayerUI.initScorllsByLevelData(levelLayer, layer);
            } else if (layer.type === (_crd && LayerType === void 0 ? (_reportPossibleCrUseOfLayerType({
              error: Error()
            }), LayerType) : LayerType).Random) {
              levelLayer.randomLayers = [];
              var randomLayers = new (_crd && LevelRandTerrainsLayersUI === void 0 ? (_reportPossibleCrUseOfLevelRandTerrainsLayersUI({
                error: Error()
              }), LevelRandTerrainsLayersUI) : LevelRandTerrainsLayersUI)();
              var randomLayer = new (_crd && LevelRandTerrainsLayerUI === void 0 ? (_reportPossibleCrUseOfLevelRandTerrainsLayerUI({
                error: Error()
              }), LevelRandTerrainsLayerUI) : LevelRandTerrainsLayerUI)();
              randomLayer.dynamicTerrain = [];
              layer.dynamics.forEach(dynamic => {
                randomLayer.weight = dynamic.weight;
                dynamic.terrains.forEach(terrain => {
                  var dynamicTerrain = new (_crd && LevelRandTerrainUI === void 0 ? (_reportPossibleCrUseOfLevelRandTerrainUI({
                    error: Error()
                  }), LevelRandTerrainUI) : LevelRandTerrainUI)();
                  dynamicTerrain.weight = terrain.weight;
                  assetManager.loadAny({
                    uuid: terrain.uuid
                  }, (err, prefab) => {
                    if (err) {
                      return;
                    }

                    dynamicTerrain.terrainElement = prefab;
                    randomLayer.dynamicTerrain.push(dynamicTerrain);
                  });
                });
              });
              randomLayers.dynamicTerrains.push(randomLayer);
              levelLayer.randomLayers.push(randomLayers);
            }

            levelEditorLayerUI.initByLevelData(layer);
            layers.push(levelLayer);
          });
        }

        static fillLevelLayerData(layer, dataLayer) {
          dataLayer.speed = layer.speed;
          dataLayer.type = layer.type;
          dataLayer.remark = layer.remark;
          dataLayer.zIndex = layer.zIndex;

          if (layer.type === (_crd && LayerType === void 0 ? (_reportPossibleCrUseOfLayerType({
            error: Error()
          }), LayerType) : LayerType).Scroll) {
            dataLayer.scrolls = [];
            layer.scrollLayers.forEach(scrollLayer => {
              var dataScroll = new (_crd && LevelDataScroll === void 0 ? (_reportPossibleCrUseOfLevelDataScroll({
                error: Error()
              }), LevelDataScroll) : LevelDataScroll)();
              scrollLayer.scrollPrefabs.forEach(scrollPrefab => {
                dataScroll.uuids.push(scrollPrefab == null ? void 0 : scrollPrefab.uuid);
              });
              dataScroll.weight = scrollLayer.weight;
              dataScroll.splicingMode = scrollLayer.splicingMode;
              dataScroll.offSetY = new (_crd && LayerOffset === void 0 ? (_reportPossibleCrUseOfLayerOffset({
                error: Error()
              }), LayerOffset) : LayerOffset)();
              dataScroll.offSetY.min = scrollLayer.splicingOffsetY.min;
              dataScroll.offSetY.max = scrollLayer.splicingOffsetY.max;
              dataScroll.offSetX = new (_crd && LayerOffset === void 0 ? (_reportPossibleCrUseOfLayerOffset({
                error: Error()
              }), LayerOffset) : LayerOffset)();
              dataScroll.offSetX.min = scrollLayer.splicingOffsetX.min;
              dataScroll.offSetX.max = scrollLayer.splicingOffsetX.max;
              dataLayer.scrolls.push(dataScroll);
              console.log("LevelEditorBaseUI fill scrollLayersData", dataLayer);
            });
          } else if (layer.type === (_crd && LayerType === void 0 ? (_reportPossibleCrUseOfLayerType({
            error: Error()
          }), LayerType) : LayerType).Random) {
            dataLayer.dynamics = [];
            layer.randomLayers.forEach(randomLayer => {
              randomLayer.dynamicTerrains.forEach(terrains => {
                var data = new (_crd && LevelDataRandTerrains === void 0 ? (_reportPossibleCrUseOfLevelDataRandTerrains({
                  error: Error()
                }), LevelDataRandTerrains) : LevelDataRandTerrains)();
                data.terrains = [];
                data.weight = terrains.weight;
                terrains.dynamicTerrain.forEach(terrainElement => {
                  var _terrainElement$terra;

                  var terrainData = {
                    weight: terrainElement.weight,
                    uuid: (_terrainElement$terra = terrainElement.terrainElement) == null ? void 0 : _terrainElement$terra.uuid
                  };
                  data.terrains.push(terrainData);
                });
                dataLayer.dynamics.push(data);
              });
            });
          }

          layer.node.getComponent(_crd && LevelEditorLayerUI === void 0 ? (_reportPossibleCrUseOfLevelEditorLayerUI({
            error: Error()
          }), LevelEditorLayerUI) : LevelEditorLayerUI).fillLevelData(dataLayer);
        }

        static fillLevelLayersData(layers, dataLayers) {
          layers.sort((a, b) => a.zIndex - b.zIndex);
          layers.forEach(layer => {
            var levelLayer = new (_crd && LevelDataLayer === void 0 ? (_reportPossibleCrUseOfLevelDataLayer({
              error: Error()
            }), LevelDataLayer) : LevelDataLayer)();
            LevelEditorBaseUI.fillLevelLayerData(layer, levelLayer);
            dataLayers.push(levelLayer);
          });
        }

        fillLevelData(data) {
          data.name = this.levelname;
          data.totalTime = this.totalTime;
          data.backgroundLayer = new (_crd && LevelDataBackgroundLayer === void 0 ? (_reportPossibleCrUseOfLevelDataBackgroundLayer({
            error: Error()
          }), LevelDataBackgroundLayer) : LevelDataBackgroundLayer)();

          for (var i = 0; i < this.backgroundLayer.backgrounds.length; i++) {
            var prefab = this.backgroundLayer.backgrounds[i];

            if (prefab == null) {
              continue;
            }

            data.backgroundLayer.backgrounds.push(prefab.uuid);
          }

          LevelEditorBaseUI.fillLevelLayerData(this.backgroundLayer, data.backgroundLayer);
          data.floorLayers = [];
          data.skyLayers = [];
          LevelEditorBaseUI.fillLevelLayersData(this.floorLayers, data.floorLayers);
          LevelEditorBaseUI.fillLevelLayersData(this.skyLayers, data.skyLayers);
        }

        set play(bPlay) {
          if (this._play === bPlay) {
            return;
          }

          this._play = bPlay;
          console.log("LevelEditorBaseUI set play", bPlay);

          if (bPlay) {
            this._drawMask();
          } else {
            this._drawMaskClear();
          }

          this._randLayerActive(this.floorLayers, this.floorLayersNode, this._play);

          this._randLayerActive(this.skyLayers, this.skyLayersNode, this._play);
        }

        _randLayerActive(layers, parentNode, bPlay) {
          layers.forEach(layer => {
            if (layer.type === (_crd && LayerType === void 0 ? (_reportPossibleCrUseOfLayerType({
              error: Error()
            }), LayerType) : LayerType).Random) {
              parentNode.children.forEach(layerNode => {
                var dynamicNode = layerNode.getChildByName("dynamic");

                if (bPlay) {
                  // 先激活所有dyna_x节点
                  dynamicNode.children.forEach(dynaNode => dynaNode.active = true); // 遍历所有dyna_x节点（每个对应一个地形策略组）

                  dynamicNode.children.forEach((dynaNode, groupIndex) => {
                    var _layer$randomLayers$g;

                    // 获取对应的地形策略组配置
                    var terrainGroup = (_layer$randomLayers$g = layer.randomLayers[groupIndex]) == null ? void 0 : _layer$randomLayers$g.dynamicTerrains[0];
                    if (!terrainGroup) return; // 计算该组的总权重

                    var totalWeight = terrainGroup.dynamicTerrain.reduce((sum, terrain) => sum + terrain.weight, 0); // 随机选择地形

                    var randomWeight = Math.random() * totalWeight;
                    var accumulatedWeight = 0;
                    var selectedIndex = -1;

                    for (var i = 0; i < terrainGroup.dynamicTerrain.length; i++) {
                      accumulatedWeight += terrainGroup.dynamicTerrain[i].weight;

                      if (randomWeight <= accumulatedWeight) {
                        selectedIndex = i;
                        break;
                      }
                    } // 显示选中的地形，隐藏其他


                    dynaNode.children.forEach((terrainNode, terrainIndex) => {
                      var active = terrainIndex === selectedIndex;
                      terrainNode.active = active;
                      var terrain = terrainNode.getComponent(_crd && RandTerrain === void 0 ? (_reportPossibleCrUseOfRandTerrain({
                        error: Error()
                      }), RandTerrain) : RandTerrain);
                      terrain.play(active);
                    });
                  });
                } else {
                  dynamicNode.children.forEach(dynaNode => {
                    dynaNode.active = true;
                    dynaNode.children.forEach(terrainNode => {
                      terrainNode.active = true;
                      var terrain = terrainNode.getComponent(_crd && RandTerrain === void 0 ? (_reportPossibleCrUseOfRandTerrain({
                        error: Error()
                      }), RandTerrain) : RandTerrain);
                      terrain.play(false);
                    });
                  });
                }
              });
            } else if (layer.type === (_crd && LayerType === void 0 ? (_reportPossibleCrUseOfLayerType({
              error: Error()
            }), LayerType) : LayerType).Scroll) {
              parentNode.children.forEach(layerNode => {
                // 滚动层逻辑
                var scrollsNode = layerNode.getChildByName("scrolls");
                scrollsNode.children.forEach(layerNode => layerNode.active = true);

                if (bPlay) {
                  // 计算总权重
                  var totalWeight = 0;

                  for (var scrollLayer of layer.scrollLayers) {
                    totalWeight += scrollLayer.weight;
                  } // 随机选择要显示的滚动体


                  var randomWeight = Math.random() * totalWeight;
                  var selectedIndex = -1;

                  for (var i = 0; i < layer.scrollLayers.length; i++) {
                    randomWeight -= layer.scrollLayers[i].weight;

                    if (randomWeight <= 0) {
                      selectedIndex = i;
                      console.log("LevelEditorBase selectedIndex", selectedIndex);
                      break;
                    }
                  }

                  scrollsNode.children.forEach((child, index) => {
                    child.active = index === selectedIndex;
                  });
                } else {
                  scrollsNode.children.forEach(child => {
                    child.active = true;
                  });
                }
              });
            }
          });
        }

        _drawNodeGraphics() {
          var graphics = this._drawNode.getComponent(Graphics);

          if (!graphics) return;

          var drawTransform = this._drawNode.getComponent(UITransform);

          var drawport = new Rect(this._drawNode.getPosition().x - drawTransform.contentSize.width / 2, this._drawNode.getPosition().y - drawTransform.contentSize.height / 2, drawTransform.contentSize.width, this._totalHeight); // Draw drawport rectangle

          graphics.strokeColor = Color.BLUE;
          graphics.lineWidth = 10;
          graphics.rect(drawport.x, drawport.y, drawport.width, drawport.height);
          graphics.stroke();

          var graphicsView = this._drawNode.getChildByName("drawView").getComponent(Graphics);

          if (!graphicsView) return;
          var drawview = new Rect(-750 / 2, -1334 / 2, 750, this._totalHeight);
          graphicsView.strokeColor = Color.RED;
          graphicsView.lineWidth = 10;
          graphicsView.rect(drawview.x, drawview.y, drawview.width, drawview.height);
          graphicsView.stroke();
        }

        _drawMask() {
          if (!this._play) return;

          var maskGraphics = this._drawNode.getChildByName("drawMask").getComponent(Graphics);

          if (!maskGraphics) return; // 绘制4个填充矩形表示视口边界

          var maskWidth = 10000;
          var maskHeight = 1334;
          maskGraphics.fillColor = Color.BLACK; // 顶部矩形

          maskGraphics.fillRect(-maskWidth / 2, this._drawNode.getPosition().y + maskHeight - maskHeight / 2, maskWidth, maskHeight); // 底部矩形

          maskGraphics.fillRect(-maskWidth / 2, this._drawNode.getPosition().y - maskHeight - maskHeight / 2, maskWidth, maskHeight); // 左侧矩形

          maskGraphics.fillRect(-maskWidth - 750 / 2, this._drawNode.getPosition().y - maskHeight / 2, maskWidth, maskHeight); // 右侧矩形

          maskGraphics.fillRect(750 / 2, this._drawNode.getPosition().y - maskHeight / 2, maskWidth, maskHeight);
        }

        _drawMaskClear() {
          var maskGraphics = this._drawNode.getChildByName("drawMask").getComponent(Graphics);

          if (!maskGraphics) return;
          maskGraphics.clear();
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "levelname", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return "";
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "totalTime", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 10;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "backgroundLayer", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return new (_crd && LevelBackgroundLayer === void 0 ? (_reportPossibleCrUseOfLevelBackgroundLayer({
            error: Error()
          }), LevelBackgroundLayer) : LevelBackgroundLayer)();
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "floorLayers", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return [];
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "skyLayers", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return [];
        }
      })), _class2)) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=7faf2e43a321c3c1a125f57fa1812daa6a9e334f.js.map