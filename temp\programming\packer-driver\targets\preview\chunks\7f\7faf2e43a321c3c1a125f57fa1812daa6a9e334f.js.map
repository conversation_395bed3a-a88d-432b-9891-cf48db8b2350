{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/level/LevelEditorBaseUI.ts"], "names": ["_decorator", "CCFloat", "Component", "Node", "CCString", "assetManager", "instantiate", "UITransform", "view", "Graphics", "Color", "Rect", "LayerOffset", "LayerSplicingMode", "LayerType", "LevelDataBackgroundLayer", "LevelDataLayer", "LevelDataRandTerrains", "LevelDataScroll", "LevelEditorLayerUI", "LevelBackgroundLayer", "LevelEditorUtils", "<PERSON><PERSON><PERSON><PERSON>", "LevelRandTerrainsLayersUI", "LevelRandTerrainsLayerUI", "LevelRandTerrainUI", "RandTerrain", "WavePreview", "ccclass", "property", "executeInEditMode", "BackgroundsNodeName", "LevelEditorBaseUI", "type", "displayName", "_totalHeight", "backgroundLayerNode", "floorLayersNode", "skyLayersNode", "_isLoadingScrollNodes", "_play", "_drawNode", "onLoad", "console", "log", "getOrAddNode", "node", "uuid", "update", "dt", "checkLayerNode", "floorLayers", "skyLayers", "setBackgroundNodePosition", "yOff", "height", "getComponent", "contentSize", "setPosition", "getVisibleSize", "tick", "progress", "i", "background<PERSON>ayer", "backgroundsNode", "children", "length", "bg", "backgrounds", "bgIndex", "prefab", "addComponent", "<PERSON><PERSON><PERSON><PERSON>", "position", "y", "removeFromParent", "totalTime", "speed", "for<PERSON>ach", "layer", "add<PERSON><PERSON>er", "parentNode", "name", "layerNode", "layerCom", "layers", "removeLayerNodes", "push", "find", "element", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Random", "_checkRandTerrainNode", "_checkScrollNode", "data", "scrollsNode", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isCountMatch", "scrollLayers", "loadPromises", "scroll", "index", "scrollPrefabs", "loadPromise", "Promise", "resolve", "loadAny", "err", "loadedPrefab", "error", "all", "scrollNode", "totalHeight", "posOffsetY", "prefabIndex", "curPrefab", "child", "randomOffsetX", "Math", "random", "splicingOffsetX", "max", "min", "offY", "splicingMode", "node_height", "fix_height", "random_height", "splicingOffsetY", "dynamicNode", "currentDynaNodes", "match", "parseInt", "randomLayers", "needRebuild", "rebuildList", "randTerrains", "dynaNode", "expected<PERSON><PERSON>dCount", "terrains", "dynamicTerrains", "dynamicTerrain", "childIndex", "isUUIDMatch", "terrain", "childNode", "childPrefabUUID", "_prefab", "asset", "_uuid", "terrainUUID", "terrainElement", "j", "k", "initByLevelData", "levelname", "background", "remark", "setSiblingIndex", "initLayers", "instance", "reset", "_drawNodeGraphics", "dataLayers", "<PERSON><PERSON>ayer", "zIndex", "levelEditorLayerUI", "initScorllsByLevelData", "random<PERSON>ayer", "dynamics", "dynamic", "weight", "fillLevelLayerData", "dataLayer", "scrolls", "scroll<PERSON>ayer", "dataScroll", "scrollPrefab", "uuids", "offSetY", "offSetX", "terrainData", "fillLevelData", "fillLevelLayersData", "sort", "a", "b", "play", "bPlay", "_drawMask", "_drawMaskClear", "_randLayerActive", "getChildByName", "active", "groupIndex", "terrainGroup", "totalWeight", "reduce", "sum", "randomWeight", "accumulatedWeight", "selectedIndex", "terrainNode", "terrainIndex", "graphics", "drawTransform", "drawport", "getPosition", "x", "width", "strokeColor", "BLUE", "lineWidth", "rect", "stroke", "graphicsView", "drawview", "RED", "maskGraphics", "<PERSON><PERSON><PERSON><PERSON>", "maskHeight", "fillColor", "BLACK", "fillRect", "clear"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,O,OAAAA,O;AAASC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,Q,OAAAA,Q;AAAkBC,MAAAA,Y,OAAAA,Y;AAAcC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;;AAEvHC,MAAAA,W,iBAAAA,W;AAAaC,MAAAA,iB,iBAAAA,iB;AAAmBC,MAAAA,S,iBAAAA,S;AAAsBC,MAAAA,wB,iBAAAA,wB;AAA0BC,MAAAA,c,iBAAAA,c;AAAsCC,MAAAA,qB,iBAAAA,qB;AAAuBC,MAAAA,e,iBAAAA,e;;AAC7IC,MAAAA,kB,iBAAAA,kB;;AACAC,MAAAA,oB,iBAAAA,oB;AAAsBC,MAAAA,gB,iBAAAA,gB;AAAkBC,MAAAA,U,iBAAAA,U;AAAYC,MAAAA,yB,iBAAAA,yB;AAA2BC,MAAAA,wB,iBAAAA,wB;AAA0BC,MAAAA,kB,iBAAAA,kB;;AACzGC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,W,iBAAAA,W;;;;;;;;;OALH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2C9B,U;AAO3C+B,MAAAA,mB,GAAsB,a;;mCAIfC,iB,WAFZJ,OAAO,CAAC,mBAAD,C,UACPE,iBAAiB,E,UAEbD,QAAQ,CAACzB,QAAD,C,UAERyB,QAAQ,CAAC;AAACI,QAAAA,IAAI,EAAChC,OAAN;AAAeiC,QAAAA,WAAW,EAAC;AAA3B,OAAD,C,UAIRL,QAAQ,CAAC;AAACI,QAAAA,IAAI;AAAA;AAAA,wDAAL;AAA4BC,QAAAA,WAAW,EAAC;AAAxC,OAAD,C,UAERL,QAAQ,CAAC;AAACI,QAAAA,IAAI,EAAC;AAAA;AAAA,qCAAN;AAAoBC,QAAAA,WAAW,EAAC;AAAhC,OAAD,C,UAERL,QAAQ,CAAC;AAACI,QAAAA,IAAI,EAAC;AAAA;AAAA,qCAAN;AAAoBC,QAAAA,WAAW,EAAC;AAAhC,OAAD,C,0CAbb,MAEaF,iBAFb,SAEuC9B,SAFvC,CAEiD;AAAA;AAAA;;AAAA;;AAAA;;AAAA,eAKrCiC,YALqC,GAKd,CALc;;AAAA;;AAAA;;AAAA;;AAAA,eAcrCC,mBAdqC,GAcL,IAdK;AAAA,eAerCC,eAfqC,GAeT,IAfS;AAAA,eAgBrCC,aAhBqC,GAgBX,IAhBW;AAAA,eAkBrCC,qBAlBqC,GAkBJ,KAlBI;AAAA,eAmBrCC,KAnBqC,GAmBpB,KAnBoB;AAAA,eAoBrCC,SApBqC,GAoBZ,IApBY;AAAA;;AAsB7CC,QAAAA,MAAM,GAAQ;AAAA;;AACVC,UAAAA,OAAO,CAACC,GAAR;AACA,eAAKR,mBAAL,GAA2B;AAAA;AAAA,oDAAiBS,YAAjB,CAA8B,KAAKC,IAAnC,EAAyC,iBAAzC,CAA3B;AACA,eAAKT,eAAL,GAAuB;AAAA;AAAA,oDAAiBQ,YAAjB,CAA8B,KAAKC,IAAnC,EAAyC,aAAzC,CAAvB;AACA,eAAKR,aAAL,GAAqB;AAAA;AAAA,oDAAiBO,YAAjB,CAA8B,KAAKC,IAAnC,EAAyC,WAAzC,CAArB;AAEA,eAAKL,SAAL,GAAiB;AAAA;AAAA,oDAAiBI,YAAjB,CAA8B,KAAKC,IAAnC,EAAyC,UAAzC,CAAjB;AAEAH,UAAAA,OAAO,CAACC,GAAR,wDAAuC,KAAKP,eAA5C,qBAAuC,sBAAsBU,IAA7D;AACH;;AACDC,QAAAA,MAAM,CAACC,EAAD,EAAiB;AACnB,eAAKC,cAAL,CAAoB,KAAKb,eAAzB,EAA2C,KAAKc,WAAhD;AACA,eAAKD,cAAL,CAAoB,KAAKZ,aAAzB,EAAyC,KAAKc,SAA9C;AACH;;AACOC,QAAAA,yBAAyB,CAACP,IAAD,EAAYQ,IAAZ,EAAgC;AAC7D,cAAMC,MAAM,GAAGT,IAAI,CAACU,YAAL,CAAkBjD,WAAlB,EAAgCkD,WAAhC,CAA4CF,MAA3D;AACAT,UAAAA,IAAI,CAACY,WAAL,CAAiB,CAAjB,EAAoBJ,IAAI,GAAC9C,IAAI,CAACmD,cAAL,GAAsBJ,MAAtB,GAA6B,CAAlC,GAAoCA,MAAM,GAAC,CAA/D;AACA,iBAAOA,MAAP;AAEH;;AACMK,QAAAA,IAAI,CAACC,QAAD,EAAwB;AAC/B,cAAIP,IAAI,GAAG,CAAX;;AACA,eAAK,IAAIQ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,eAAL,CAAqBC,eAArB,CAAsCC,QAAtC,CAA+CC,MAAnE,EAA2EJ,CAAC,EAA5E,EAAgF;AAC5E,gBAAIK,EAAE,GAAG,KAAKJ,eAAL,CAAqBC,eAArB,CAAsCC,QAAtC,CAA+CH,CAA/C,CAAT;AACAR,YAAAA,IAAI,IAAI,KAAKD,yBAAL,CAA+Bc,EAA/B,EAAmCb,IAAnC,CAAR;AACH;;AACD,iBAAM,KAAKS,eAAL,CAAqBK,WAArB,CAAiCF,MAAjC,GAA0C,CAA1C,IAA+CZ,IAAI,GAAG,KAAKnB,YAAjE,EAA+E;AAC3E,gBAAIgC,GAAY,GAAG,IAAnB;AACA,gBAAIE,OAAO,GAAG,KAAKN,eAAL,CAAqBC,eAArB,CAAsCC,QAAtC,CAA+CC,MAA/C,GAAwD,KAAKH,eAAL,CAAqBK,WAArB,CAAiCF,MAAvG;AACA,gBAAMI,MAAM,GAAG,KAAKP,eAAL,CAAqBK,WAArB,CAAiCC,OAAjC,CAAf;;AACA,gBAAIC,MAAM,IAAI,IAAd,EAAoB;AAChBH,cAAAA,GAAE,GAAG7D,WAAW,CAACgE,MAAD,CAAhB;AACH;;AACD,gBAAIH,GAAE,IAAI,IAAV,EAAgB;AACZA,cAAAA,GAAE,GAAG,IAAIhE,IAAJ,CAAS,OAAT,CAAL;AACAgE,cAAAA,GAAE,CAACI,YAAH,CAAgBhE,WAAhB,EAA6BgD,MAA7B,GAAsC,IAAtC;AACH;;AACD,iBAAKQ,eAAL,CAAqBC,eAArB,CAAsCQ,QAAtC,CAA+CL,GAA/C;AACAb,YAAAA,IAAI,IAAI,KAAKD,yBAAL,CAA+Bc,GAA/B,EAAmCb,IAAnC,CAAR;AACH;;AACD,eAAK,IAAIQ,EAAC,GAAG,KAAKC,eAAL,CAAqBC,eAArB,CAAsCC,QAAtC,CAA+CC,MAA/C,GAAwD,CAArE,EAAwEJ,EAAC,IAAI,CAA7E,EAAgFA,EAAC,EAAjF,EAAqF;AACjF,gBAAMK,IAAE,GAAG,KAAKJ,eAAL,CAAqBC,eAArB,CAAsCC,QAAtC,CAA+CH,EAA/C,CAAX;;AACA,gBAAIK,IAAE,CAACM,QAAH,CAAYC,CAAZ,GAAgBP,IAAE,CAACX,YAAH,CAAgBjD,WAAhB,EAA8BgD,MAA9B,GAAqC,CAArD,GAAyD,KAAKpB,YAAlE,EAAgF;AAC5EgC,cAAAA,IAAE,CAACQ,gBAAH;AACH,aAFD,MAEO;AACH;AACH;AACJ;;AAED,eAAKZ,eAAL,CAAsBjB,IAAtB,CAA4BU,YAA5B;AAAA;AAAA,wDAAkFI,IAAlF,CACIC,QADJ,EACc,KAAKe,SADnB,EAC8B,KAAKb,eAAL,CAAqBc,KADnD;AAEA,eAAK1B,WAAL,CAAiB2B,OAAjB,CAA0BC,KAAD,IAAW;AAChCA,YAAAA,KAAK,CAACjC,IAAN,CAAYU,YAAZ;AAAA;AAAA,0DAAkEI,IAAlE,CAAuEC,QAAvE,EAAiF,KAAKe,SAAtF,EAAiGG,KAAK,CAACF,KAAvG;AACH,WAFD;AAGA,eAAKzB,SAAL,CAAe0B,OAAf,CAAwBC,KAAD,IAAW;AAC9BA,YAAAA,KAAK,CAACjC,IAAN,CAAYU,YAAZ;AAAA;AAAA,0DAAkEI,IAAlE,CAAuEC,QAAvE,EAAiF,KAAKe,SAAtF,EAAiGG,KAAK,CAACF,KAAvG;AACH,WAFD;AAGH;;AAEsB,eAARG,QAAQ,CAACC,UAAD,EAAmBC,IAAnB,EAAqD;AACxE,cAAIC,SAAS,GAAG,IAAIhF,IAAJ,CAAS+E,IAAT,CAAhB;AACA,cAAIE,QAAQ,GAAGD,SAAS,CAACZ,YAAV;AAAA;AAAA,uDAAf;AACAU,UAAAA,UAAU,CAACT,QAAX,CAAoBW,SAApB;AACA,iBAAOC,QAAP;AACH;;AAEOlC,QAAAA,cAAc,CAAC+B,UAAD,EAAmBI,MAAnB,EAA8C;AAChE,cAAIC,gBAAwB,GAAG,EAA/B;AACAL,UAAAA,UAAU,CAAChB,QAAX,CAAoBa,OAApB,CAA4BhC,IAAI,IAAI;AAChC,gBAAIsC,QAAQ,GAAGtC,IAAI,CAACU,YAAL;AAAA;AAAA,yDAAf;;AACA,gBAAI4B,QAAQ,IAAI,IAAhB,EAAsB;AAClBzC,cAAAA,OAAO,CAACC,GAAR,kCAA2CE,IAAI,CAACoC,IAAhD;AACAI,cAAAA,gBAAgB,CAACC,IAAjB,CAAsBzC,IAAtB;AACA;AACH;;AACD,gBAAIuC,MAAM,CAACG,IAAP,CAAaT,KAAD,IAAWA,KAAK,CAACjC,IAAN,IAAcA,IAArC,KAA8C,IAAlD,EAAwD;AACpDH,cAAAA,OAAO,CAACC,GAAR,kCAA2CE,IAAI,CAACoC,IAAhD;AACAI,cAAAA,gBAAgB,CAACC,IAAjB,CAAsBzC,IAAtB;AACA;AACH;AACJ,WAZD;AAaAwC,UAAAA,gBAAgB,CAACR,OAAjB,CAAyBW,OAAO,IAAI;AAChCA,YAAAA,OAAO,CAACd,gBAAR;AACH,WAFD;AAGAU,UAAAA,MAAM,CAACP,OAAP,CAAe,CAACC,KAAD,EAAQjB,CAAR,KAAc;AACzB,gBAAIiB,KAAK,CAACjC,IAAN,IAAc,IAAd,IAAsBiC,KAAK,CAACjC,IAAN,CAAW4C,OAAX,IAAsB,KAAhD,EAAuD;AACnD/C,cAAAA,OAAO,CAACC,GAAR;AACAmC,cAAAA,KAAK,CAACjC,IAAN,GAAad,iBAAiB,CAACgD,QAAlB,CAA2BC,UAA3B,aAAgDnB,CAAhD,EAAqDhB,IAAlE;AACH;;AAED,gBAAIiC,KAAK,CAAC9C,IAAN,KAAe;AAAA;AAAA,wCAAU0D,MAA7B,EAAqC,CACjC;AACH,aAFD,MAEO,IAAIZ,KAAK,CAAC9C,IAAN,KAAe;AAAA;AAAA,wCAAU2D,MAA7B,EAAqC;AACxC,mBAAKC,qBAAL,CAA2Bd,KAA3B,EAAkCA,KAAK,CAAEjC,IAAzC;AACH;AACJ,WAXD;AAYH;;AAEagD,QAAAA,gBAAgB,CAACC,IAAD,EAAmBd,UAAnB,EAAmD;AAAA;;AAAA;AAC7E,gBAAMe,WAAW,GAAG;AAAA;AAAA,sDAAiBnD,YAAjB,CAA8BoC,UAA9B,EAA0C,SAA1C,CAApB;;AACA,gBAAIc,IAAI,CAAC9D,IAAL,IAAa;AAAA;AAAA,wCAAU0D,MAA3B,EAAmC;AAC/BK,cAAAA,WAAW,CAACC,iBAAZ;AACA;AACH;;AAED,gBAAI,KAAI,CAAC1D,qBAAT,EAAgC;AAC5BI,cAAAA,OAAO,CAACC,GAAR,+DAAwE,KAAI,CAACL,qBAA7E,qCAAkIyD,WAAW,CAAC/B,QAAZ,CAAqBC,MAAvJ;AACA;AACH;;AAED,gBAAMgC,YAAY,GAAGF,WAAW,CAAC/B,QAAZ,CAAqBC,MAArB,KAAgC6B,IAAI,CAACI,YAAL,CAAkBjC,MAAvE;AACAvB,YAAAA,OAAO,CAACC,GAAR,yDAAkEoD,WAAW,CAAC/B,QAAZ,CAAqBC,MAAvF,kCAA0H6B,IAAI,CAACI,YAAL,CAAkBjC,MAA5I;;AACA,gBAAI,CAACgC,YAAL,EAAmB;AACf,kBAAME,YAA6B,GAAG,EAAtC;AACAJ,cAAAA,WAAW,CAACC,iBAAZ;AACA,cAAA,KAAI,CAAC1D,qBAAL,GAA6B,IAA7B,CAHe,CAGoB;;AAEnCwD,cAAAA,IAAI,CAACI,YAAL,CAAkBrB,OAAlB,CAA0B,CAACuB,MAAD,EAASC,KAAT,KAAmB;AACzC,oBAAID,MAAM,CAACE,aAAP,CAAqBrC,MAArB,IAA+B,CAAnC,EAAsC;AAElCmC,gBAAAA,MAAM,CAACE,aAAP,CAAqBzB,OAArB,CAA6BR,MAAM,IAAI;AACnC,sBAAMkC,WAAW,GAAG,IAAIC,OAAJ,CAAmBC,OAAD,IAAa;AAC/CrG,oBAAAA,YAAY,CAACsG,OAAb,CAAqB;AAAE5D,sBAAAA,IAAI,EAAEuB,MAAM,CAAEvB;AAAhB,qBAArB,EAA6C,CAAC6D,GAAD,EAAMC,YAAN,KAA+B;AACxE,0BAAID,GAAJ,EAAS;AACLjE,wBAAAA,OAAO,CAACmE,KAAR,CAAc,wBAAd,EAAwCF,GAAxC;AACAF,wBAAAA,OAAO;AACV,uBAHD,MAGO;AACHA,wBAAAA,OAAO;AACV;AACJ,qBAPD;AAQP,mBATuB,CAApB;AAWJN,kBAAAA,YAAY,CAACb,IAAb,CAAkBiB,WAAlB;AACH,iBAbG;AAcP,eAjBD;AAmBA,oBAAMC,OAAO,CAACM,GAAR,CAAYX,YAAZ,CAAN;AACAzD,cAAAA,OAAO,CAACC,GAAR,2DAAoEmD,IAAI,CAACI,YAAL,CAAkBjC,MAAtF;AACA6B,cAAAA,IAAI,CAACI,YAAL,CAAkBrB,OAAlB,CAA0B,CAACuB,MAAD,EAASC,KAAT,KAAmB;AACzC,oBAAMU,UAAU,GAAG;AAAA;AAAA,0DAAiBnE,YAAjB,CAA8BmD,WAA9B,cAAqDM,KAArD,CAAnB;AACA,oBAAMW,WAAW,GAAGlB,IAAI,CAAClB,KAAL,GAAa,KAAI,CAACD,SAAtC;AACA,oBAAIsC,UAAU,GAAG,CAAjB;AACA,oBAAI3D,MAAM,GAAG,CAAb;AACA,oBAAI4D,WAAW,GAAG,CAAlB;;AAEA,uBAAO5D,MAAM,GAAG0D,WAAhB,EAA6B;AACzB,sBAAMG,SAAS,GAAGf,MAAM,CAACE,aAAP,CAAqBY,WAArB,CAAlB;AACA,sBAAME,KAAK,GAAG/G,WAAW,CAAC8G,SAAD,CAAzB;AACA,sBAAME,aAAa,GAAGC,IAAI,CAACC,MAAL,MAAiBnB,MAAM,CAACoB,eAAP,CAAuBC,GAAvB,GAA6BrB,MAAM,CAACoB,eAAP,CAAuBE,GAArE,IAA4EtB,MAAM,CAACoB,eAAP,CAAuBE,GAAzH;AACAN,kBAAAA,KAAK,CAAC3D,WAAN,CAAkB4D,aAAlB,EAAiCJ,UAAjC,EAA6C,CAA7C;AAEA,sBAAIU,IAAI,GAAG,CAAX;;AACA,sBAAIvB,MAAM,CAACwB,YAAP,KAAwB;AAAA;AAAA,8DAAkBC,WAA9C,EAA2D;AACvDF,oBAAAA,IAAI,GAAGP,KAAK,CAAC7D,YAAN,CAAmBjD,WAAnB,EAAiCkD,WAAjC,CAA6CF,MAApD;AACH,mBAFD,MAEO,IAAI8C,MAAM,CAACwB,YAAP,KAAwB;AAAA;AAAA,8DAAkBE,UAA9C,EAA0D;AAC7DH,oBAAAA,IAAI,GAAG,IAAP;AACH,mBAFM,MAEA,IAAIvB,MAAM,CAACwB,YAAP,KAAwB;AAAA;AAAA,8DAAkBG,aAA9C,EAA6D;AAChEJ,oBAAAA,IAAI,GAAGL,IAAI,CAACG,GAAL,CAASrB,MAAM,CAAC4B,eAAP,CAAuBN,GAAhC,EAAqCtB,MAAM,CAAC4B,eAAP,CAAuBP,GAA5D,IAAmEL,KAAK,CAAC7D,YAAN,CAAmBjD,WAAnB,EAAiCkD,WAAjC,CAA6CF,MAAvH;AACH;;AAEDyD,kBAAAA,UAAU,CAACxC,QAAX,CAAoB6C,KAApB;AACAH,kBAAAA,UAAU,IAAIU,IAAd;AACArE,kBAAAA,MAAM,IAAIqE,IAAV;AACAT,kBAAAA,WAAW,GAAG,CAACA,WAAW,GAAG,CAAf,IAAoBd,MAAM,CAACE,aAAP,CAAqBrC,MAAvD;AACH;AACJ,eA3BD;AA4BA,cAAA,KAAI,CAAC3B,qBAAL,GAA6B,KAA7B;AACH;AArE4E;AAsEhF;;AAEOsD,QAAAA,qBAAqB,CAACE,IAAD,EAAmBd,UAAnB,EAA0C;AACnE,cAAMiD,WAAW,GAAG;AAAA;AAAA,oDAAiBrF,YAAjB,CAA8BoC,UAA9B,EAA0C,SAA1C,CAApB,CADmE,CAGnE;;AACA,cAAMkD,gBAAgB,GAAGD,WAAW,CAACjE,QAArC;;AACA,eAAK,IAAIH,CAAC,GAAGqE,gBAAgB,CAACjE,MAAjB,GAA0B,CAAvC,EAA0CJ,CAAC,IAAI,CAA/C,EAAkDA,CAAC,EAAnD,EAAuD;AACnD,gBAAMhB,IAAI,GAAGqF,gBAAgB,CAACrE,CAAD,CAA7B;AACA,gBAAMsE,KAAK,GAAGtF,IAAI,CAACoC,IAAL,CAAUkD,KAAV,CAAgB,cAAhB,CAAd;;AACA,gBAAIA,KAAJ,EAAW;AACP,kBAAM9B,KAAK,GAAG+B,QAAQ,CAACD,KAAK,CAAC,CAAD,CAAN,CAAtB;;AACA,kBAAI9B,KAAK,IAAIP,IAAI,CAACuC,YAAL,CAAkBpE,MAA/B,EAAuC;AACnCpB,gBAAAA,IAAI,CAAC6B,gBAAL;AACH;AACJ;AACJ;;AAED,cAAIoB,IAAI,CAAC9D,IAAL,IAAa;AAAA;AAAA,sCAAU2D,MAAvB,IAAiCG,IAAI,CAACuC,YAAL,CAAkBpE,MAAlB,KAA6B,CAAlE,EAAqE;AACjEgE,YAAAA,WAAW,CAACjC,iBAAZ;AACA;AACH;;AAED,cAAIsC,WAAW,GAAG,KAAlB;AACA,cAAMC,WAAqB,GAAG,EAA9B;;AAEA,eAAK,IAAI1E,GAAC,GAAG,CAAb,EAAgBA,GAAC,GAAGiC,IAAI,CAACuC,YAAL,CAAkBpE,MAAtC,EAA8CJ,GAAC,EAA/C,EAAmD;AAC/C,gBAAM2E,YAAY,GAAG1C,IAAI,CAACuC,YAAL,CAAkBxE,GAAlB,CAArB;AACA,gBAAM4E,QAAQ,GAAG;AAAA;AAAA,sDAAiB7F,YAAjB,CAA8BqF,WAA9B,YAAmDpE,GAAnD,CAAjB,CAF+C,CAI/C;;AACA,gBAAI6E,kBAAkB,GAAG,CAAzB;;AACA,iBAAK,IAAMC,QAAX,IAAuBH,YAAY,CAACI,eAApC,EAAqD;AACjDF,cAAAA,kBAAkB,IAAIC,QAAQ,CAACE,cAAT,CAAwB5E,MAA9C;AACH,aAR8C,CAU/C;;;AACA,gBAAIwE,QAAQ,CAACzE,QAAT,CAAkBC,MAAlB,KAA6ByE,kBAAjC,EAAqD;AACjDJ,cAAAA,WAAW,GAAG,IAAd;AACAC,cAAAA,WAAW,CAACjD,IAAZ,CAAiBzB,GAAjB;AACA;AACH,aAf8C,CAiB/C;;;AACA,gBAAIiF,UAAU,GAAG,CAAjB;AACA,gBAAIC,WAAW,GAAG,IAAlB;;AAEA,iBAAK,IAAMJ,SAAX,IAAuBH,YAAY,CAACI,eAApC,EAAqD;AACjD,mBAAK,IAAMI,OAAX,IAAsBL,SAAQ,CAACE,cAA/B,EAA+C;AAAA;;AAC3C,oBAAMI,SAAS,GAAGR,QAAQ,CAACzE,QAAT,CAAkB8E,UAAlB,CAAlB,CAD2C,CAE3C;;AACA,oBAAMI,eAAe,yBAAGD,SAAS,CAACE,OAAb,mCAAG,mBAAmBC,KAAtB,qBAAG,mBAA0BC,KAAlD;AACA,oBAAMC,WAAW,GAAGN,OAAH,qCAAGA,OAAO,CAAEO,cAAZ,qBAAG,sBAAyBzG,IAA7C;;AAEA,oBAAIoG,eAAe,KAAKI,WAAxB,EAAqC;AACjCP,kBAAAA,WAAW,GAAG,KAAd;AACA;AACH;;AACDD,gBAAAA,UAAU;AACb;;AACD,kBAAI,CAACC,WAAL,EAAkB;AACrB;;AAED,gBAAI,CAACA,WAAL,EAAkB;AACdT,cAAAA,WAAW,GAAG,IAAd;AACAC,cAAAA,WAAW,CAACjD,IAAZ,CAAiBzB,GAAjB;AACH;AACJ;;AAED,cAAIyE,WAAJ,EAAiB;AAAA,yCAGoB;AAC7B,kBAAMG,QAAQ,GAAG;AAAA;AAAA,wDAAiB7F,YAAjB,CAA8BqF,WAA9B,YAAmD5B,MAAnD,CAAjB;AACAoC,cAAAA,QAAQ,CAACzC,iBAAT;AACA,kBAAMwC,YAAY,GAAG1C,IAAI,CAACuC,YAAL,CAAkBhC,MAAlB,CAArB,CAH6B,CAI7B;;AACA,mBAAK,IAAImD,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGhB,YAAY,CAACI,eAAb,CAA6B3E,MAAjD,EAAyDuF,CAAC,EAA1D,EAA8D;AAC1D,oBAAMb,UAAQ,GAAGH,YAAY,CAACI,eAAb,CAA6BY,CAA7B,CAAjB,CAD0D,CAG1D;;AACA,qBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGd,UAAQ,CAACE,cAAT,CAAwB5E,MAA5C,EAAoDwF,CAAC,EAArD,EAAyD;AAAA;;AACrD,sBAAMT,QAAO,GAAGL,UAAQ,CAACE,cAAT,CAAwBY,CAAxB,CAAhB;AACArJ,kBAAAA,YAAY,CAACsG,OAAb,CAAqB;AAAE5D,oBAAAA,IAAI,EAAEkG,QAAF,sCAAEA,QAAO,CAAEO,cAAX,qBAAE,uBAAyBzG;AAAjC,mBAArB,EAA8D,CAAC6D,GAAD,EAAMtC,MAAN,KAAyB;AACnF,wBAAIsC,GAAJ,EAAS;AACL;AACA;AACH;;AAED,wBAAM9D,IAAI,GAAGxC,WAAW,CAACgE,MAAD,CAAxB;AACAoE,oBAAAA,QAAQ,CAAClE,QAAT,CAAkB1B,IAAlB;AACH,mBARD;AASH;AACJ;AACJ,aAzBY;;AACb;AAEA,iBAAK,IAAMwD,MAAX,IAAoBkC,WAApB;AAAA;AAAA;AAuBH;AACJ;;AAEMmB,QAAAA,eAAe,CAAC5D,IAAD,EAAuB;AAAA;;AACzC,eAAK6D,SAAL,GAAiB7D,IAAI,CAACb,IAAtB;AACA,eAAKN,SAAL,GAAiBmB,IAAI,CAACnB,SAAtB;AAEA,eAAKxC,mBAAL,CAA0B6D,iBAA1B;AACA,eAAKlC,eAAL,GAAuB;AAAA;AAAA,6DAAvB;AACA,eAAKA,eAAL,CAAqBK,WAArB,GAAmC,EAAnC;AACA,mCAAA2B,IAAI,CAAChC,eAAL,4DAAsBK,WAAtB,mCAAmCU,OAAnC,CAA4C+E,UAAD,IAAgB;AACvDxJ,YAAAA,YAAY,CAACsG,OAAb,CAAqB;AAAC5D,cAAAA,IAAI,EAAC8G;AAAN,aAArB,EAAwC,CAACjD,GAAD,EAAatC,MAAb,KAA+B;AACnE,kBAAIsC,GAAJ,EAAS;AACLjE,gBAAAA,OAAO,CAACmE,KAAR,CAAc,8DAAd,EAA8EF,GAA9E;AACA;AACH;;AACD,mBAAK7C,eAAL,CAAqBK,WAArB,CAAiCmB,IAAjC,CAAsCjB,MAAtC;AACH,aAND;AAOH,WARD;AASA,eAAKP,eAAL,CAAqBc,KAArB,6BAA6BkB,IAAI,CAAChC,eAAlC,qBAA6B,uBAAsBc,KAAnD;AACA,eAAKd,eAAL,CAAqB+F,MAArB,6BAA8B/D,IAAI,CAAChC,eAAnC,qBAA8B,uBAAsB+F,MAApD;AACA,eAAK3H,YAAL,GAAoB,KAAK4B,eAAL,CAAqBc,KAArB,GAA6B,KAAKD,SAAtD;AACA,eAAKb,eAAL,CAAqBjB,IAArB,GAA4Bd,iBAAiB,CAACgD,QAAlB,CAA2B,KAAK5C,mBAAhC,EAAsD,OAAtD,EAA+DU,IAA3F;AACA,eAAKiB,eAAL,CAAqBC,eAArB,GAAuC;AAAA;AAAA,oDAAiBnB,YAAjB,CAA8B,KAAKkB,eAAL,CAAqBjB,IAAnD,EAAyDf,mBAAzD,CAAvC;AACA,eAAKgC,eAAL,CAAqBC,eAArB,CAAqC+F,eAArC,CAAqD,CAArD;AACA,eAAKhG,eAAL,CAAqBjB,IAArB,CAA0BU,YAA1B;AAAA;AAAA,wDAAgFmG,eAAhF,CAAgG5D,IAAI,CAAChC,eAArG;AAEA,eAAKZ,WAAL,GAAmB,EAAnB;AACA,eAAKC,SAAL,GAAiB,EAAjB;AACApB,UAAAA,iBAAiB,CAACgI,UAAlB,CAA6B,KAAK3H,eAAlC,EAAoD,KAAKc,WAAzD,EAAsE4C,IAAI,CAAC5C,WAA3E;AACAnB,UAAAA,iBAAiB,CAACgI,UAAlB,CAA6B,KAAK1H,aAAlC,EAAkD,KAAKc,SAAvD,EAAkE2C,IAAI,CAAC3C,SAAvE;AAEA;AAAA;AAAA,0CAAY6G,QAAZ,uBAAsBC,KAAtB;;AAEA,eAAKC,iBAAL;AACH;;AAEwB,eAAVH,UAAU,CAAC/E,UAAD,EAAmBI,MAAnB,EAAyC+E,UAAzC,EAA4E;AACjGnF,UAAAA,UAAU,CAACgB,iBAAX;AACAmE,UAAAA,UAAU,CAACtF,OAAX,CAAmB,CAACC,KAAD,EAAQjB,CAAR,KAAc;AAC7B,gBAAIuG,UAAU,GAAG;AAAA;AAAA,2CAAjB;AACAA,YAAAA,UAAU,CAACxF,KAAX,GAAmBE,KAAK,CAACF,KAAzB;AACAwF,YAAAA,UAAU,CAACpI,IAAX,GAAkB8C,KAAK,CAAC9C,IAAxB;AACAoI,YAAAA,UAAU,CAACP,MAAX,GAAoB/E,KAAK,CAAC+E,MAA1B;AACAO,YAAAA,UAAU,CAACC,MAAX,GAAoBvF,KAAK,CAACuF,MAA1B;AACAD,YAAAA,UAAU,CAACvH,IAAX,GAAkBd,iBAAiB,CAACgD,QAAlB,CAA2BC,UAA3B,aAAgDnB,CAAhD,EAAqDhB,IAAvE;AACAuH,YAAAA,UAAU,CAACvH,IAAX,CAAgBiH,eAAhB,CAAgChF,KAAK,CAACuF,MAAtC;AACA,gBAAMC,kBAAkB,GAAGF,UAAU,CAACvH,IAAX,CAAgBU,YAAhB;AAAA;AAAA,yDAA3B;;AACA,gBAAIuB,KAAK,CAAC9C,IAAN,KAAe;AAAA;AAAA,wCAAU0D,MAA7B,EAAqC;AACjChD,cAAAA,OAAO,CAACC,GAAR,CAAY,wDAAZ,EAAsEyH,UAAU,CAAClE,YAAX,CAAwBjC,MAA9F;AACAqG,cAAAA,kBAAkB,CAACC,sBAAnB,CAA0CH,UAA1C,EAAsDtF,KAAtD;AACH,aAHD,MAGO,IAAIA,KAAK,CAAC9C,IAAN,KAAe;AAAA;AAAA,wCAAU2D,MAA7B,EAAqC;AACxCyE,cAAAA,UAAU,CAAC/B,YAAX,GAA0B,EAA1B;AACA,kBAAIA,YAAY,GAAG;AAAA;AAAA,2EAAnB;AACA,kBAAImC,WAAW,GAAG;AAAA;AAAA,yEAAlB;AACAA,cAAAA,WAAW,CAAC3B,cAAZ,GAA6B,EAA7B;AACA/D,cAAAA,KAAK,CAAC2F,QAAN,CAAe5F,OAAf,CAAwB6F,OAAD,IAAa;AAChCF,gBAAAA,WAAW,CAACG,MAAZ,GAAqBD,OAAO,CAACC,MAA7B;AACAD,gBAAAA,OAAO,CAAC/B,QAAR,CAAiB9D,OAAjB,CAA0BmE,OAAD,IAAa;AAClC,sBAAIH,cAAc,GAAG;AAAA;AAAA,iEAArB;AACAA,kBAAAA,cAAc,CAAC8B,MAAf,GAAwB3B,OAAO,CAAC2B,MAAhC;AACAvK,kBAAAA,YAAY,CAACsG,OAAb,CAAqB;AAAC5D,oBAAAA,IAAI,EAAEkG,OAAO,CAAClG;AAAf,mBAArB,EAA2C,CAAC6D,GAAD,EAAatC,MAAb,KAA+B;AACtE,wBAAIsC,GAAJ,EAAS;AACL;AACH;;AACDkC,oBAAAA,cAAc,CAACU,cAAf,GAAgClF,MAAhC;AACAmG,oBAAAA,WAAW,CAAC3B,cAAZ,CAA2BvD,IAA3B,CAAgCuD,cAAhC;AACH,mBAND;AAQH,iBAXD;AAYH,eAdD;AAeAR,cAAAA,YAAY,CAACO,eAAb,CAA6BtD,IAA7B,CAAkCkF,WAAlC;AACAJ,cAAAA,UAAU,CAAC/B,YAAX,CAAwB/C,IAAxB,CAA6B+C,YAA7B;AACH;;AACDiC,YAAAA,kBAAkB,CAACZ,eAAnB,CAAmC5E,KAAnC;AACAM,YAAAA,MAAM,CAACE,IAAP,CAAY8E,UAAZ;AACH,WArCD;AAsCH;;AAEgC,eAAlBQ,kBAAkB,CAAC9F,KAAD,EAAoB+F,SAApB,EAAoD;AACjFA,UAAAA,SAAS,CAACjG,KAAV,GAAkBE,KAAK,CAACF,KAAxB;AACAiG,UAAAA,SAAS,CAAC7I,IAAV,GAAiB8C,KAAK,CAAC9C,IAAvB;AACA6I,UAAAA,SAAS,CAAChB,MAAV,GAAmB/E,KAAK,CAAC+E,MAAzB;AACAgB,UAAAA,SAAS,CAACR,MAAV,GAAmBvF,KAAK,CAACuF,MAAzB;;AAEA,cAAIvF,KAAK,CAAC9C,IAAN,KAAe;AAAA;AAAA,sCAAU0D,MAA7B,EAAqC;AACjCmF,YAAAA,SAAS,CAACC,OAAV,GAAoB,EAApB;AACAhG,YAAAA,KAAK,CAACoB,YAAN,CAAmBrB,OAAnB,CAA4BkG,WAAD,IAAiB;AACxC,kBAAIC,UAAU,GAAG;AAAA;AAAA,uDAAjB;AACAD,cAAAA,WAAW,CAACzE,aAAZ,CAA0BzB,OAA1B,CAAmCoG,YAAD,IAAkB;AAChDD,gBAAAA,UAAU,CAACE,KAAX,CAAiB5F,IAAjB,CAAsB2F,YAAtB,oBAAsBA,YAAY,CAAEnI,IAApC;AACH,eAFD;AAGAkI,cAAAA,UAAU,CAACL,MAAX,GAAoBI,WAAW,CAACJ,MAAhC;AAEAK,cAAAA,UAAU,CAACpD,YAAX,GAA0BmD,WAAW,CAACnD,YAAtC;AACAoD,cAAAA,UAAU,CAACG,OAAX,GAAqB;AAAA;AAAA,+CAArB;AACAH,cAAAA,UAAU,CAACG,OAAX,CAAmBzD,GAAnB,GAAyBqD,WAAW,CAAC/C,eAAZ,CAA4BN,GAArD;AACAsD,cAAAA,UAAU,CAACG,OAAX,CAAmB1D,GAAnB,GAAyBsD,WAAW,CAAC/C,eAAZ,CAA4BP,GAArD;AAEAuD,cAAAA,UAAU,CAACI,OAAX,GAAqB;AAAA;AAAA,+CAArB;AACAJ,cAAAA,UAAU,CAACI,OAAX,CAAmB1D,GAAnB,GAAyBqD,WAAW,CAACvD,eAAZ,CAA4BE,GAArD;AACAsD,cAAAA,UAAU,CAACI,OAAX,CAAmB3D,GAAnB,GAAyBsD,WAAW,CAACvD,eAAZ,CAA4BC,GAArD;AACAoD,cAAAA,SAAS,CAACC,OAAV,CAAkBxF,IAAlB,CAAuB0F,UAAvB;AACAtI,cAAAA,OAAO,CAACC,GAAR,CAAY,yCAAZ,EAAuDkI,SAAvD;AACH,aAjBD;AAkBH,WApBD,MAoBO,IAAI/F,KAAK,CAAC9C,IAAN,KAAe;AAAA;AAAA,sCAAU2D,MAA7B,EAAqC;AACxCkF,YAAAA,SAAS,CAACJ,QAAV,GAAqB,EAArB;AACA3F,YAAAA,KAAK,CAACuD,YAAN,CAAmBxD,OAAnB,CAA4B2F,WAAD,IAAiB;AACxCA,cAAAA,WAAW,CAAC5B,eAAZ,CAA4B/D,OAA5B,CAAqC8D,QAAD,IAAc;AAC9C,oBAAI7C,IAAI,GAAG;AAAA;AAAA,qEAAX;AACAA,gBAAAA,IAAI,CAAC6C,QAAL,GAAgB,EAAhB;AACA7C,gBAAAA,IAAI,CAAC6E,MAAL,GAAchC,QAAQ,CAACgC,MAAvB;AACAhC,gBAAAA,QAAQ,CAACE,cAAT,CAAwBhE,OAAxB,CAAiC0E,cAAD,IAAoB;AAAA;;AAChD,sBAAI8B,WAAiC,GAAG;AACpCV,oBAAAA,MAAM,EAAEpB,cAAc,CAACoB,MADa;AAEpC7H,oBAAAA,IAAI,2BAAEyG,cAAc,CAACA,cAAjB,qBAAE,sBAA+BzG;AAFD,mBAAxC;AAIAgD,kBAAAA,IAAI,CAAC6C,QAAL,CAAcrD,IAAd,CAAmB+F,WAAnB;AACH,iBAND;AAOAR,gBAAAA,SAAS,CAACJ,QAAV,CAAmBnF,IAAnB,CAAwBQ,IAAxB;AACH,eAZD;AAaH,aAdD;AAeH;;AACDhB,UAAAA,KAAK,CAACjC,IAAN,CAAYU,YAAZ;AAAA;AAAA,wDAAkE+H,aAAlE,CAAgFT,SAAhF;AACH;;AAEiC,eAAnBU,mBAAmB,CAACnG,MAAD,EAAuB+E,UAAvB,EAA0D;AACxF/E,UAAAA,MAAM,CAACoG,IAAP,CAAY,CAACC,CAAD,EAAIC,CAAJ,KAAUD,CAAC,CAACpB,MAAF,GAAWqB,CAAC,CAACrB,MAAnC;AACAjF,UAAAA,MAAM,CAACP,OAAP,CAAgBC,KAAD,IAAW;AACtB,gBAAIsF,UAAU,GAAG;AAAA;AAAA,mDAAjB;AACArI,YAAAA,iBAAiB,CAAC6I,kBAAlB,CAAqC9F,KAArC,EAA4CsF,UAA5C;AACAD,YAAAA,UAAU,CAAC7E,IAAX,CAAgB8E,UAAhB;AACH,WAJD;AAKH;;AACMkB,QAAAA,aAAa,CAACxF,IAAD,EAAuB;AACvCA,UAAAA,IAAI,CAACb,IAAL,GAAY,KAAK0E,SAAjB;AACA7D,UAAAA,IAAI,CAACnB,SAAL,GAAiB,KAAKA,SAAtB;AAEAmB,UAAAA,IAAI,CAAChC,eAAL,GAAuB;AAAA;AAAA,qEAAvB;;AACA,eAAK,IAAID,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,eAAL,CAAqBK,WAArB,CAAiCF,MAArD,EAA6DJ,CAAC,EAA9D,EAAkE;AAC9D,gBAAMQ,MAAM,GAAG,KAAKP,eAAL,CAAqBK,WAArB,CAAiCN,CAAjC,CAAf;;AACA,gBAAIQ,MAAM,IAAI,IAAd,EAAoB;AAChB;AACH;;AACDyB,YAAAA,IAAI,CAAChC,eAAL,CAAqBK,WAArB,CAAiCmB,IAAjC,CAAsCjB,MAAM,CAACvB,IAA7C;AACH;;AACDf,UAAAA,iBAAiB,CAAC6I,kBAAlB,CAAqC,KAAK9G,eAA1C,EAA2DgC,IAAI,CAAChC,eAAhE;AAEAgC,UAAAA,IAAI,CAAC5C,WAAL,GAAmB,EAAnB;AACA4C,UAAAA,IAAI,CAAC3C,SAAL,GAAiB,EAAjB;AACApB,UAAAA,iBAAiB,CAACwJ,mBAAlB,CAAsC,KAAKrI,WAA3C,EAAwD4C,IAAI,CAAC5C,WAA7D;AACAnB,UAAAA,iBAAiB,CAACwJ,mBAAlB,CAAsC,KAAKpI,SAA3C,EAAsD2C,IAAI,CAAC3C,SAA3D;AACH;;AAEc,YAAJwI,IAAI,CAACC,KAAD,EAAiB;AAC5B,cAAI,KAAKrJ,KAAL,KAAeqJ,KAAnB,EAA0B;AACtB;AACH;;AAED,eAAKrJ,KAAL,GAAaqJ,KAAb;AACAlJ,UAAAA,OAAO,CAACC,GAAR,CAAY,4BAAZ,EAA0CiJ,KAA1C;;AACA,cAAIA,KAAJ,EAAW;AACP,iBAAKC,SAAL;AACH,WAFD,MAEO;AACH,iBAAKC,cAAL;AACH;;AACD,eAAKC,gBAAL,CAAsB,KAAK7I,WAA3B,EAAwC,KAAKd,eAA7C,EAA+D,KAAKG,KAApE;;AACA,eAAKwJ,gBAAL,CAAsB,KAAK5I,SAA3B,EAAsC,KAAKd,aAA3C,EAA2D,KAAKE,KAAhE;AACH;;AAEOwJ,QAAAA,gBAAgB,CAAC3G,MAAD,EAAuBJ,UAAvB,EAAyC4G,KAAzC,EAA8D;AAClFxG,UAAAA,MAAM,CAACP,OAAP,CAAgBC,KAAD,IAAW;AACtB,gBAAIA,KAAK,CAAC9C,IAAN,KAAe;AAAA;AAAA,wCAAU2D,MAA7B,EAAqC;AACjCX,cAAAA,UAAU,CAAChB,QAAX,CAAoBa,OAApB,CAA4BK,SAAS,IAAI;AACrC,oBAAM+C,WAAW,GAAG/C,SAAS,CAAC8G,cAAV,CAAyB,SAAzB,CAApB;;AAEA,oBAAIJ,KAAJ,EAAW;AACP;AACA3D,kBAAAA,WAAW,CAACjE,QAAZ,CAAqBa,OAArB,CAA6B4D,QAAQ,IAAIA,QAAQ,CAACwD,MAAT,GAAkB,IAA3D,EAFO,CAIP;;AACAhE,kBAAAA,WAAW,CAACjE,QAAZ,CAAqBa,OAArB,CAA6B,CAAC4D,QAAD,EAAWyD,UAAX,KAA0B;AAAA;;AACnD;AACA,wBAAMC,YAAY,4BAAGrH,KAAK,CAACuD,YAAN,CAAmB6D,UAAnB,CAAH,qBAAG,sBAAgCtD,eAAhC,CAAgD,CAAhD,CAArB;AACA,wBAAI,CAACuD,YAAL,EAAmB,OAHgC,CAKnD;;AACA,wBAAMC,WAAW,GAAGD,YAAY,CAACtD,cAAb,CAA4BwD,MAA5B,CAChB,CAACC,GAAD,EAAMtD,OAAN,KAAkBsD,GAAG,GAAGtD,OAAO,CAAC2B,MADhB,EACwB,CADxB,CAApB,CANmD,CAUnD;;AACA,wBAAI4B,YAAY,GAAGjF,IAAI,CAACC,MAAL,KAAgB6E,WAAnC;AACA,wBAAII,iBAAiB,GAAG,CAAxB;AACA,wBAAIC,aAAa,GAAG,CAAC,CAArB;;AAEA,yBAAK,IAAI5I,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGsI,YAAY,CAACtD,cAAb,CAA4B5E,MAAhD,EAAwDJ,CAAC,EAAzD,EAA6D;AACzD2I,sBAAAA,iBAAiB,IAAIL,YAAY,CAACtD,cAAb,CAA4BhF,CAA5B,EAA+B8G,MAApD;;AACA,0BAAI4B,YAAY,IAAIC,iBAApB,EAAuC;AACnCC,wBAAAA,aAAa,GAAG5I,CAAhB;AACA;AACH;AACJ,qBArBkD,CAuBnD;;;AACA4E,oBAAAA,QAAQ,CAACzE,QAAT,CAAkBa,OAAlB,CAA0B,CAAC6H,WAAD,EAAcC,YAAd,KAA+B;AACrD,0BAAMV,MAAM,GAAIU,YAAY,KAAKF,aAAjC;AACAC,sBAAAA,WAAW,CAACT,MAAZ,GAAqBA,MAArB;AACA,0BAAMjD,OAAO,GAAG0D,WAAW,CAACnJ,YAAZ;AAAA;AAAA,qDAAhB;AACAyF,sBAAAA,OAAO,CAAC2C,IAAR,CAAaM,MAAb;AACH,qBALD;AAMH,mBA9BD;AA+BH,iBApCD,MAoCO;AACHhE,kBAAAA,WAAW,CAACjE,QAAZ,CAAqBa,OAArB,CAA6B4D,QAAQ,IAAI;AACrCA,oBAAAA,QAAQ,CAACwD,MAAT,GAAkB,IAAlB;AACAxD,oBAAAA,QAAQ,CAACzE,QAAT,CAAkBa,OAAlB,CAA0B6H,WAAW,IAAI;AACrCA,sBAAAA,WAAW,CAACT,MAAZ,GAAqB,IAArB;AACA,0BAAMjD,OAAO,GAAG0D,WAAW,CAACnJ,YAAZ;AAAA;AAAA,qDAAhB;AACAyF,sBAAAA,OAAO,CAAC2C,IAAR,CAAa,KAAb;AACH,qBAJD;AAKH,mBAPD;AAQH;AACJ,eAjDD;AAkDH,aAnDD,MAmDO,IAAI7G,KAAK,CAAC9C,IAAN,KAAe;AAAA;AAAA,wCAAU0D,MAA7B,EAAqC;AACxCV,cAAAA,UAAU,CAAChB,QAAX,CAAoBa,OAApB,CAA4BK,SAAS,IAAI;AACrC;AACA,oBAAMa,WAAW,GAAGb,SAAS,CAAC8G,cAAV,CAAyB,SAAzB,CAApB;AACAjG,gBAAAA,WAAW,CAAC/B,QAAZ,CAAqBa,OAArB,CAA6BK,SAAS,IAAIA,SAAS,CAAC+G,MAAV,GAAmB,IAA7D;;AAEA,oBAAIL,KAAJ,EAAW;AACP;AACA,sBAAIQ,WAAW,GAAG,CAAlB;;AACA,uBAAK,IAAMrB,WAAX,IAA0BjG,KAAK,CAACoB,YAAhC,EAA8C;AAC1CkG,oBAAAA,WAAW,IAAIrB,WAAW,CAACJ,MAA3B;AACH,mBALM,CAOP;;;AACA,sBAAI4B,YAAY,GAAGjF,IAAI,CAACC,MAAL,KAAgB6E,WAAnC;AACA,sBAAIK,aAAa,GAAG,CAAC,CAArB;;AACA,uBAAK,IAAI5I,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGiB,KAAK,CAACoB,YAAN,CAAmBjC,MAAvC,EAA+CJ,CAAC,EAAhD,EAAoD;AAChD0I,oBAAAA,YAAY,IAAIzH,KAAK,CAACoB,YAAN,CAAmBrC,CAAnB,EAAsB8G,MAAtC;;AACA,wBAAI4B,YAAY,IAAI,CAApB,EAAuB;AACnBE,sBAAAA,aAAa,GAAG5I,CAAhB;AACAnB,sBAAAA,OAAO,CAACC,GAAR,CAAY,+BAAZ,EAA6C8J,aAA7C;AACA;AACH;AACJ;;AAED1G,kBAAAA,WAAW,CAAC/B,QAAZ,CAAqBa,OAArB,CAA6B,CAACuC,KAAD,EAAQf,KAAR,KAAkB;AAC3Ce,oBAAAA,KAAK,CAAC6E,MAAN,GAAgB5F,KAAK,KAAKoG,aAA1B;AACH,mBAFD;AAGH,iBAtBD,MAsBO;AACH1G,kBAAAA,WAAW,CAAC/B,QAAZ,CAAqBa,OAArB,CAA6BuC,KAAK,IAAI;AAClCA,oBAAAA,KAAK,CAAC6E,MAAN,GAAe,IAAf;AACH,mBAFD;AAGH;AACJ,eAhCD;AAiCH;AACJ,WAvFD;AAwFH;;AAEO/B,QAAAA,iBAAiB,GAAS;AAC9B,cAAM0C,QAAQ,GAAG,KAAKpK,SAAL,CAAgBe,YAAhB,CAA6B/C,QAA7B,CAAjB;;AACA,cAAI,CAACoM,QAAL,EAAe;;AAEf,cAAMC,aAAa,GAAG,KAAKrK,SAAL,CAAgBe,YAAhB,CAA6BjD,WAA7B,CAAtB;;AAEA,cAAMwM,QAAQ,GAAG,IAAIpM,IAAJ,CACb,KAAK8B,SAAL,CAAgBuK,WAAhB,GAA8BC,CAA9B,GAAkCH,aAAa,CAACrJ,WAAd,CAA0ByJ,KAA1B,GAAkC,CADvD,EAEb,KAAKzK,SAAL,CAAgBuK,WAAhB,GAA8BtI,CAA9B,GAAkCoI,aAAa,CAACrJ,WAAd,CAA0BF,MAA1B,GAAmC,CAFxD,EAGbuJ,aAAa,CAACrJ,WAAd,CAA0ByJ,KAHb,EAIb,KAAK/K,YAJQ,CAAjB,CAN8B,CAa9B;;AACA0K,UAAAA,QAAQ,CAACM,WAAT,GAAuBzM,KAAK,CAAC0M,IAA7B;AACAP,UAAAA,QAAQ,CAACQ,SAAT,GAAqB,EAArB;AACAR,UAAAA,QAAQ,CAACS,IAAT,CAAcP,QAAQ,CAACE,CAAvB,EAA0BF,QAAQ,CAACrI,CAAnC,EAAsCqI,QAAQ,CAACG,KAA/C,EAAsDH,QAAQ,CAACxJ,MAA/D;AACAsJ,UAAAA,QAAQ,CAACU,MAAT;;AAEA,cAAMC,YAAY,GAAG,KAAK/K,SAAL,CAAgBwJ,cAAhB,CAA+B,UAA/B,EAA4CzI,YAA5C,CAAyD/C,QAAzD,CAArB;;AACA,cAAI,CAAC+M,YAAL,EAAmB;AAEnB,cAAMC,QAAQ,GAAG,IAAI9M,IAAJ,CACb,CAAC,GAAD,GAAO,CADM,EAEb,CAAC,IAAD,GAAQ,CAFK,EAGb,GAHa,EAIb,KAAKwB,YAJQ,CAAjB;AAOAqL,UAAAA,YAAY,CAACL,WAAb,GAA2BzM,KAAK,CAACgN,GAAjC;AACAF,UAAAA,YAAY,CAACH,SAAb,GAAyB,EAAzB;AACAG,UAAAA,YAAY,CAACF,IAAb,CAAkBG,QAAQ,CAACR,CAA3B,EAA8BQ,QAAQ,CAAC/I,CAAvC,EAA0C+I,QAAQ,CAACP,KAAnD,EAA0DO,QAAQ,CAAClK,MAAnE;AACAiK,UAAAA,YAAY,CAACD,MAAb;AACH;;AAEOzB,QAAAA,SAAS,GAAS;AACtB,cAAI,CAAC,KAAKtJ,KAAV,EAAiB;;AAEjB,cAAMmL,YAAY,GAAG,KAAKlL,SAAL,CAAgBwJ,cAAhB,CAA+B,UAA/B,EAA4CzI,YAA5C,CAAyD/C,QAAzD,CAArB;;AACA,cAAI,CAACkN,YAAL,EAAmB,OAJG,CAMtB;;AACA,cAAMC,SAAS,GAAG,KAAlB;AACA,cAAMC,UAAU,GAAG,IAAnB;AACAF,UAAAA,YAAY,CAACG,SAAb,GAAyBpN,KAAK,CAACqN,KAA/B,CATsB,CAWtB;;AACAJ,UAAAA,YAAY,CAACK,QAAb,CACI,CAACJ,SAAD,GAAa,CADjB,EAEI,KAAKnL,SAAL,CAAgBuK,WAAhB,GAA8BtI,CAA9B,GAAkCmJ,UAAlC,GAA+CA,UAAU,GAAG,CAFhE,EAGID,SAHJ,EAIIC,UAJJ,EAZsB,CAmBtB;;AACAF,UAAAA,YAAY,CAACK,QAAb,CACI,CAACJ,SAAD,GAAa,CADjB,EAEI,KAAKnL,SAAL,CAAgBuK,WAAhB,GAA8BtI,CAA9B,GAAkCmJ,UAAlC,GAA+CA,UAAU,GAAG,CAFhE,EAGID,SAHJ,EAIIC,UAJJ,EApBsB,CA2BtB;;AACAF,UAAAA,YAAY,CAACK,QAAb,CACI,CAACJ,SAAD,GAAa,MAAM,CADvB,EAEI,KAAKnL,SAAL,CAAgBuK,WAAhB,GAA8BtI,CAA9B,GAAkCmJ,UAAU,GAAG,CAFnD,EAGID,SAHJ,EAIIC,UAJJ,EA5BsB,CAmCtB;;AACAF,UAAAA,YAAY,CAACK,QAAb,CACI,MAAM,CADV,EAEI,KAAKvL,SAAL,CAAgBuK,WAAhB,GAA8BtI,CAA9B,GAAkCmJ,UAAU,GAAG,CAFnD,EAGID,SAHJ,EAIIC,UAJJ;AAMH;;AAGO9B,QAAAA,cAAc,GAAS;AAC3B,cAAM4B,YAAY,GAAG,KAAKlL,SAAL,CAAgBwJ,cAAhB,CAA+B,UAA/B,EAA4CzI,YAA5C,CAAyD/C,QAAzD,CAArB;;AACA,cAAI,CAACkN,YAAL,EAAmB;AAEnBA,UAAAA,YAAY,CAACM,KAAb;AACH;;AAvnB4C,O;;;;;iBAElB,E;;;;;;;iBAEA,E;;;;;;;iBAIoB;AAAA;AAAA,6D;;;;;;;iBAEZ,E;;;;;;;iBAEF,E", "sourcesContent": ["import { _decorator, CCFloat, Component, Node, CCString, Prefab, assetManager, instantiate, UITransform, view, Graphics, Color, Rect } from 'cc';\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\nimport { LayerOffset, LayerSplicingMode, LayerType, LevelData, LevelDataBackgroundLayer, LevelDataLayer, LevelDataRandTerrain, LevelDataRandTerrains, LevelDataScroll } from 'db://assets/bundles/common/script/leveldata/leveldata';\r\nimport { LevelEditorLayerUI } from './LevelEditorLayerUI';\r\nimport { LevelBackgroundLayer, LevelEditorUtils, LevelLayer, LevelRandTerrainsLayersUI, LevelRandTerrainsLayerUI, LevelRandTerrainUI } from './utils';\r\nimport { RandTerrain } from 'db://assets/bundles/common/script/game/dyncTerrain/RandTerrain';\r\nimport { WavePreview } from './preview/WavePreview';\r\n\r\nconst BackgroundsNodeName = \"backgrounds\";\r\n\r\n@ccclass('LevelEditorBaseUI')\r\n@executeInEditMode()\r\nexport class LevelEditorBaseUI extends Component {\r\n    @property(CCString)\r\n    public levelname: string = \"\";\r\n    @property({type:CCFloat, displayName:\"关卡时长\"})\r\n    public totalTime: number = 10;\r\n    private _totalHeight: number = 0;\r\n\r\n    @property({type:LevelBackgroundLayer, displayName:\"背景\"})\r\n    public backgroundLayer: LevelBackgroundLayer = new LevelBackgroundLayer();\r\n    @property({type:[LevelLayer], displayName:\"地面\"})\r\n    public floorLayers: LevelLayer[] = [];\r\n    @property({type:[LevelLayer], displayName:\"天空\"})\r\n    public skyLayers: LevelLayer[] = [];\r\n\r\n    private backgroundLayerNode:Node|null = null;\r\n    private floorLayersNode:Node|null = null;\r\n    private skyLayersNode:Node|null = null;\r\n\r\n    private _isLoadingScrollNodes: boolean = false;\r\n    private _play: boolean = false;\r\n    private _drawNode: Node | null = null;\r\n\r\n    onLoad():void {\r\n        console.log(`LevelEditorBaseUI start.`);\r\n        this.backgroundLayerNode = LevelEditorUtils.getOrAddNode(this.node, \"BackgroundLayer\");\r\n        this.floorLayersNode = LevelEditorUtils.getOrAddNode(this.node, \"FloorLayers\");\r\n        this.skyLayersNode = LevelEditorUtils.getOrAddNode(this.node, \"SkyLayers\");\r\n\r\n        this._drawNode = LevelEditorUtils.getOrAddNode(this.node, \"DrawNode\");\r\n        \r\n        console.log(`LevelEditorBaseUI start ${this.floorLayersNode?.uuid}`);\r\n    }\r\n    update(dt:number):void {\r\n        this.checkLayerNode(this.floorLayersNode!, this.floorLayers);\r\n        this.checkLayerNode(this.skyLayersNode!, this.skyLayers);\r\n    }\r\n    private setBackgroundNodePosition(node:Node, yOff:number):number {\r\n        const height = node.getComponent(UITransform)!.contentSize.height;\r\n        node.setPosition(0, yOff-view.getVisibleSize().height/2+height/2);\r\n        return height\r\n\r\n    }\r\n    public tick(progress: number):void {\r\n        let yOff = 0\r\n        for (let i = 0; i < this.backgroundLayer.backgroundsNode!.children.length; i++) {\r\n            var bg = this.backgroundLayer.backgroundsNode!.children[i]\r\n            yOff += this.setBackgroundNodePosition(bg, yOff)\r\n        }\r\n        while(this.backgroundLayer.backgrounds.length > 0 && yOff < this._totalHeight) {\r\n            let bg:Node|null = null;\r\n            let bgIndex = this.backgroundLayer.backgroundsNode!.children.length % this.backgroundLayer.backgrounds.length;\r\n            const prefab = this.backgroundLayer.backgrounds[bgIndex]\r\n            if (prefab != null) {\r\n                bg = instantiate(prefab)\r\n            } \r\n            if (bg == null) {\r\n                bg = new Node(\"empty\");\r\n                bg.addComponent(UITransform).height = 1024;\r\n            }\r\n            this.backgroundLayer.backgroundsNode!.addChild(bg);\r\n            yOff += this.setBackgroundNodePosition(bg, yOff)\r\n        }\r\n        for (let i = this.backgroundLayer.backgroundsNode!.children.length - 1; i >= 0; i--) {\r\n            const bg = this.backgroundLayer.backgroundsNode!.children[i]\r\n            if (bg.position.y - bg.getComponent(UITransform)!.height/2 > this._totalHeight) {\r\n                bg.removeFromParent()\r\n            } else {\r\n                break;\r\n            }\r\n        }\r\n\r\n        this.backgroundLayer!.node!.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI)!.tick(\r\n            progress, this.totalTime, this.backgroundLayer.speed);\r\n        this.floorLayers.forEach((layer) => {\r\n            layer.node!.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI)!.tick(progress, this.totalTime, layer.speed);\r\n        });\r\n        this.skyLayers.forEach((layer) => {\r\n            layer.node!.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI)!.tick(progress, this.totalTime, layer.speed);\r\n        });\r\n    }\r\n\r\n    private static addLayer(parentNode: Node, name: string): LevelEditorLayerUI {\r\n        var layerNode = new Node(name);\r\n        var layerCom = layerNode.addComponent<LevelEditorLayerUI>(LevelEditorLayerUI);\r\n        parentNode.addChild(layerNode);\r\n        return layerCom;\r\n    }\r\n\r\n    private checkLayerNode(parentNode: Node, layers: LevelLayer[]):void {\r\n        var removeLayerNodes: Node[] = []\r\n        parentNode.children.forEach(node => {\r\n            var layerCom = node.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI);\r\n            if (layerCom == null) {\r\n                console.log(`Level checkLayerNode remove ${node.name} because layerCom == null\"`);\r\n                removeLayerNodes.push(node)\r\n                return;\r\n            }\r\n            if (layers.find((layer) => layer.node == node) == null) {\r\n                console.log(`Level checkLayerNode remove ${node.name} because not in layers\"`);\r\n                removeLayerNodes.push(node)\r\n                return;\r\n            }\r\n        });\r\n        removeLayerNodes.forEach(element => {\r\n            element.removeFromParent();    \r\n        });\r\n        layers.forEach((layer, i) => {\r\n            if (layer.node == null || layer.node.isValid == false) {\r\n                console.log(`Level checkLayerNode add because layer == null`);\r\n                layer.node = LevelEditorBaseUI.addLayer(parentNode, `layer_${i}`).node; \r\n            }\r\n\r\n            if (layer.type === LayerType.Scroll) {\r\n                //this._checkScrollNode(layer, layer!.node);\r\n            } else if (layer.type === LayerType.Random) {\r\n                this._checkRandTerrainNode(layer, layer!.node);\r\n            }\r\n        });\r\n    }\r\n\r\n    private async _checkScrollNode(data: LevelLayer, parentNode: Node):Promise<void> {\r\n        const scrollsNode = LevelEditorUtils.getOrAddNode(parentNode, \"scrolls\");\r\n        if (data.type != LayerType.Scroll) {\r\n            scrollsNode.removeAllChildren();\r\n            return;\r\n        }\r\n\r\n        if (this._isLoadingScrollNodes) {\r\n            console.log(`LevelEditorBaseUI _checkScrollNode _isLoadingScrollNodes ${this._isLoadingScrollNodes} scrollsNode.children.length ${scrollsNode.children.length}`);\r\n            return;\r\n        }\r\n\r\n        const isCountMatch = scrollsNode.children.length === data.scrollLayers.length;\r\n        console.log(`LevelEditorBaseUI _checkScrollNode children.length ${scrollsNode.children.length} data.scrollLayers.length ${data.scrollLayers.length}`); \r\n        if (!isCountMatch) {\r\n            const loadPromises: Promise<void>[] = [];\r\n            scrollsNode.removeAllChildren();\r\n            this._isLoadingScrollNodes = true; // 标记为加载中\r\n\r\n            data.scrollLayers.forEach((scroll, index) => {\r\n                if (scroll.scrollPrefabs.length <= 0) return;\r\n\r\n                    scroll.scrollPrefabs.forEach(prefab => {\r\n                        const loadPromise = new Promise<void>((resolve) => {\r\n                            assetManager.loadAny({ uuid: prefab!.uuid }, (err, loadedPrefab: Prefab) => {\r\n                                if (err) {\r\n                                    console.error(\"Failed to load prefab:\", err);\r\n                                    resolve();\r\n                                } else {\r\n                                    resolve();\r\n                                }\r\n                            });\r\n                    });\r\n                    \r\n                    loadPromises.push(loadPromise);\r\n                });\r\n            });\r\n\r\n            await Promise.all(loadPromises);\r\n            console.log(`LevelEditorBaseUI _checkScrollNode data.scrollLayers ${data.scrollLayers.length}`);\r\n            data.scrollLayers.forEach((scroll, index) => {\r\n                const scrollNode = LevelEditorUtils.getOrAddNode(scrollsNode, `scroll_${index}`);\r\n                const totalHeight = data.speed * this.totalTime; \r\n                let posOffsetY = 0;\r\n                let height = 0;\r\n                let prefabIndex = 0;\r\n\r\n                while (height < totalHeight) {\r\n                    const curPrefab = scroll.scrollPrefabs[prefabIndex];\r\n                    const child = instantiate(curPrefab);\r\n                    const randomOffsetX = Math.random() * (scroll.splicingOffsetX.max - scroll.splicingOffsetX.min) + scroll.splicingOffsetX.min;\r\n                    child.setPosition(randomOffsetX, posOffsetY, 0);\r\n\r\n                    let offY = 0;\r\n                    if (scroll.splicingMode === LayerSplicingMode.node_height) {\r\n                        offY = child.getComponent(UITransform)!.contentSize.height;\r\n                    } else if (scroll.splicingMode === LayerSplicingMode.fix_height) {\r\n                        offY = 1334;\r\n                    } else if (scroll.splicingMode === LayerSplicingMode.random_height) {\r\n                        offY = Math.max(scroll.splicingOffsetY.min, scroll.splicingOffsetY.max) + child.getComponent(UITransform)!.contentSize.height;\r\n                    }\r\n\r\n                    scrollNode.addChild(child);\r\n                    posOffsetY += offY;\r\n                    height += offY;\r\n                    prefabIndex = (prefabIndex + 1) % scroll.scrollPrefabs.length;\r\n                }\r\n            });\r\n            this._isLoadingScrollNodes = false; \r\n        }\r\n    }\r\n\r\n    private _checkRandTerrainNode(data: LevelLayer, parentNode: Node):void {\r\n        const dynamicNode = LevelEditorUtils.getOrAddNode(parentNode, \"dynamic\");\r\n\r\n        // 删除所有多余的dyna节点\r\n        const currentDynaNodes = dynamicNode.children;\r\n        for (let i = currentDynaNodes.length - 1; i >= 0; i--) {\r\n            const node = currentDynaNodes[i];\r\n            const match = node.name.match(/^dyna_(\\d+)$/);\r\n            if (match) {\r\n                const index = parseInt(match[1]);\r\n                if (index >= data.randomLayers.length) {\r\n                    node.removeFromParent();\r\n                }\r\n            }\r\n        }\r\n\r\n        if (data.type != LayerType.Random || data.randomLayers.length === 0) {\r\n            dynamicNode.removeAllChildren();\r\n            return;\r\n        }\r\n\r\n        let needRebuild = false;\r\n        const rebuildList: number[] = [];\r\n        \r\n        for (let i = 0; i < data.randomLayers.length; i++) {\r\n            const randTerrains = data.randomLayers[i];\r\n            const dynaNode = LevelEditorUtils.getOrAddNode(dynamicNode, `dyna_${i}`);\r\n            \r\n            // 计算该dyna节点应有的总地形元素数量\r\n            let expectedChildCount = 0;\r\n            for (const terrains of randTerrains.dynamicTerrains) {\r\n                expectedChildCount += terrains.dynamicTerrain.length;\r\n            }\r\n            \r\n            // 检查子节点数量是否匹配\r\n            if (dynaNode.children.length !== expectedChildCount) {\r\n                needRebuild = true;\r\n                rebuildList.push(i);\r\n                continue;\r\n            }\r\n            \r\n            // 检查每个子节点对应的预制体UUID是否匹配\r\n            let childIndex = 0;\r\n            let isUUIDMatch = true;\r\n            \r\n            for (const terrains of randTerrains.dynamicTerrains) {\r\n                for (const terrain of terrains.dynamicTerrain) {\r\n                    const childNode = dynaNode.children[childIndex];\r\n                    // @ts-ignore\r\n                    const childPrefabUUID = childNode._prefab?.asset?._uuid;\r\n                    const terrainUUID = terrain?.terrainElement?.uuid;\r\n                    \r\n                    if (childPrefabUUID !== terrainUUID) {\r\n                        isUUIDMatch = false;\r\n                        break;\r\n                    }\r\n                    childIndex++;\r\n                }\r\n                if (!isUUIDMatch) break;\r\n            }\r\n            \r\n            if (!isUUIDMatch) {\r\n                needRebuild = true;\r\n                rebuildList.push(i);\r\n            }\r\n        }\r\n\r\n        if (needRebuild) {\r\n            //console.log(\"LevelEditorBaseUI _checkRandTerrainNode need rebuild\");\r\n\r\n            for (const index of rebuildList) {\r\n                const dynaNode = LevelEditorUtils.getOrAddNode(dynamicNode, `dyna_${index}`);\r\n                dynaNode.removeAllChildren();\r\n                const randTerrains = data.randomLayers[index];\r\n                // 遍历所有地形组\r\n                for (let j = 0; j < randTerrains.dynamicTerrains.length; j++) {\r\n                    const terrains = randTerrains.dynamicTerrains[j];\r\n                    \r\n                    // 遍历地形组中的每个地形元素\r\n                    for (let k = 0; k < terrains.dynamicTerrain.length; k++) {\r\n                        const terrain = terrains.dynamicTerrain[k];\r\n                        assetManager.loadAny({ uuid: terrain?.terrainElement?.uuid }, (err, prefab: Prefab) => { \r\n                            if (err) {\r\n                                //console.error(`加载地形元素失败: ${terrain?.terrainElements?.uuid}`, err);\r\n                                return;\r\n                            }\r\n\r\n                            const node = instantiate(prefab);\r\n                            dynaNode.addChild(node);\r\n                        });\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    public initByLevelData(data: LevelData):void {\r\n        this.levelname = data.name;\r\n        this.totalTime = data.totalTime\r\n\r\n        this.backgroundLayerNode!.removeAllChildren()\r\n        this.backgroundLayer = new LevelBackgroundLayer();\r\n        this.backgroundLayer.backgrounds = [];\r\n        data.backgroundLayer?.backgrounds?.forEach((background) => {\r\n            assetManager.loadAny({uuid:background}, (err: Error, prefab:Prefab) => {\r\n                if (err) {\r\n                    console.error(\"LevelEditorBaseUI initByLevelData load background prefab err\", err);\r\n                    return\r\n                } \r\n                this.backgroundLayer.backgrounds.push(prefab);\r\n            });\r\n        });\r\n        this.backgroundLayer.speed = data.backgroundLayer?.speed;\r\n        this.backgroundLayer.remark = data.backgroundLayer?.remark;\r\n        this._totalHeight = this.backgroundLayer.speed * this.totalTime;\r\n        this.backgroundLayer.node = LevelEditorBaseUI.addLayer(this.backgroundLayerNode!, \"layer\").node;\r\n        this.backgroundLayer.backgroundsNode = LevelEditorUtils.getOrAddNode(this.backgroundLayer.node, BackgroundsNodeName);\r\n        this.backgroundLayer.backgroundsNode.setSiblingIndex(0); \r\n        this.backgroundLayer.node.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI)!.initByLevelData(data.backgroundLayer);\r\n    \r\n        this.floorLayers = []\r\n        this.skyLayers = []\r\n        LevelEditorBaseUI.initLayers(this.floorLayersNode!, this.floorLayers, data.floorLayers);\r\n        LevelEditorBaseUI.initLayers(this.skyLayersNode!, this.skyLayers, data.skyLayers);\r\n\r\n        WavePreview.instance?.reset();\r\n\r\n        this._drawNodeGraphics();\r\n    }\r\n\r\n    private static initLayers(parentNode: Node, layers: LevelLayer[], dataLayers: LevelDataLayer[]):void {\r\n        parentNode.removeAllChildren()\r\n        dataLayers.forEach((layer, i) => {\r\n            var levelLayer = new LevelLayer();\r\n            levelLayer.speed = layer.speed;\r\n            levelLayer.type = layer.type;\r\n            levelLayer.remark = layer.remark;\r\n            levelLayer.zIndex = layer.zIndex;\r\n            levelLayer.node = LevelEditorBaseUI.addLayer(parentNode, `layer_${i}`).node;\r\n            levelLayer.node.setSiblingIndex(layer.zIndex);\r\n            const levelEditorLayerUI = levelLayer.node.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI)!;\r\n            if (layer.type === LayerType.Scroll) {\r\n                console.log(\"initScorllsByLevelData levelLayer.length -------------\", levelLayer.scrollLayers.length);\r\n                levelEditorLayerUI.initScorllsByLevelData(levelLayer, layer);\r\n            } else if (layer.type === LayerType.Random) {\r\n                levelLayer.randomLayers = [];\r\n                var randomLayers = new LevelRandTerrainsLayersUI();\r\n                var randomLayer = new LevelRandTerrainsLayerUI();\r\n                randomLayer.dynamicTerrain = [];\r\n                layer.dynamics.forEach((dynamic) => {\r\n                    randomLayer.weight = dynamic.weight;\r\n                    dynamic.terrains.forEach((terrain) => {\r\n                        var dynamicTerrain = new LevelRandTerrainUI();\r\n                        dynamicTerrain.weight = terrain.weight;\r\n                        assetManager.loadAny({uuid: terrain.uuid}, (err: Error, prefab:Prefab) => {\r\n                            if (err) {\r\n                                return;\r\n                            } \r\n                            dynamicTerrain.terrainElement = prefab;\r\n                            randomLayer.dynamicTerrain.push(dynamicTerrain);\r\n                        });\r\n\r\n                    });\r\n                });\r\n                randomLayers.dynamicTerrains.push(randomLayer);\r\n                levelLayer.randomLayers.push(randomLayers);\r\n            }\r\n            levelEditorLayerUI.initByLevelData(layer);\r\n            layers.push(levelLayer);\r\n        });\r\n    }\r\n\r\n    private static fillLevelLayerData(layer: LevelLayer, dataLayer: LevelDataLayer):void {\r\n        dataLayer.speed = layer.speed;\r\n        dataLayer.type = layer.type;\r\n        dataLayer.remark = layer.remark;\r\n        dataLayer.zIndex = layer.zIndex;\r\n\r\n        if (layer.type === LayerType.Scroll) {   \r\n            dataLayer.scrolls = [];\r\n            layer.scrollLayers.forEach((scrollLayer) => {  \r\n                var dataScroll = new LevelDataScroll();     \r\n                scrollLayer.scrollPrefabs.forEach((scrollPrefab) => {\r\n                    dataScroll.uuids.push(scrollPrefab?.uuid!);\r\n                });\r\n                dataScroll.weight = scrollLayer.weight;\r\n                \r\n                dataScroll.splicingMode = scrollLayer.splicingMode;\r\n                dataScroll.offSetY = new LayerOffset();\r\n                dataScroll.offSetY.min = scrollLayer.splicingOffsetY.min;\r\n                dataScroll.offSetY.max = scrollLayer.splicingOffsetY.max;\r\n\r\n                dataScroll.offSetX = new LayerOffset();\r\n                dataScroll.offSetX.min = scrollLayer.splicingOffsetX.min;\r\n                dataScroll.offSetX.max = scrollLayer.splicingOffsetX.max;\r\n                dataLayer.scrolls.push(dataScroll);\r\n                console.log(\"LevelEditorBaseUI fill scrollLayersData\", dataLayer);\r\n            });\r\n        } else if (layer.type === LayerType.Random) {\r\n            dataLayer.dynamics = [];\r\n            layer.randomLayers.forEach((randomLayer) => {    \r\n                randomLayer.dynamicTerrains.forEach((terrains) => {\r\n                    var data = new LevelDataRandTerrains();\r\n                    data.terrains = [];\r\n                    data.weight = terrains.weight;\r\n                    terrains.dynamicTerrain.forEach((terrainElement) => {\r\n                        var terrainData: LevelDataRandTerrain = {\r\n                            weight: terrainElement.weight,\r\n                            uuid: terrainElement.terrainElement?.uuid!\r\n                        };\r\n                        data.terrains.push(terrainData);\r\n                    });\r\n                    dataLayer.dynamics.push(data);\r\n                }); \r\n            });\r\n        }\r\n        layer.node!.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI)!.fillLevelData(dataLayer);\r\n    }\r\n\r\n    private static fillLevelLayersData(layers: LevelLayer[], dataLayers: LevelDataLayer[]):void {\r\n        layers.sort((a, b) => a.zIndex - b.zIndex);\r\n        layers.forEach((layer) => {\r\n            var levelLayer = new LevelDataLayer();\r\n            LevelEditorBaseUI.fillLevelLayerData(layer, levelLayer);\r\n            dataLayers.push(levelLayer);\r\n        });\r\n    }\r\n    public fillLevelData(data: LevelData):void {\r\n        data.name = this.levelname;\r\n        data.totalTime = this.totalTime;\r\n\r\n        data.backgroundLayer = new LevelDataBackgroundLayer();\r\n        for (let i = 0; i < this.backgroundLayer.backgrounds.length; i++) {\r\n            const prefab = this.backgroundLayer.backgrounds[i];\r\n            if (prefab == null) {\r\n                continue;\r\n            }\r\n            data.backgroundLayer.backgrounds.push(prefab.uuid);\r\n        }\r\n        LevelEditorBaseUI.fillLevelLayerData(this.backgroundLayer, data.backgroundLayer);\r\n\r\n        data.floorLayers = []\r\n        data.skyLayers = []\r\n        LevelEditorBaseUI.fillLevelLayersData(this.floorLayers, data.floorLayers);\r\n        LevelEditorBaseUI.fillLevelLayersData(this.skyLayers, data.skyLayers);\r\n    }\r\n\r\n    public set play(bPlay: boolean) {\r\n        if (this._play === bPlay) {\r\n            return;\r\n        }\r\n        \r\n        this._play = bPlay;\r\n        console.log(\"LevelEditorBaseUI set play\", bPlay);\r\n        if (bPlay) {\r\n            this._drawMask();\r\n        } else {\r\n            this._drawMaskClear();\r\n        }\r\n        this._randLayerActive(this.floorLayers, this.floorLayersNode!, this._play);\r\n        this._randLayerActive(this.skyLayers, this.skyLayersNode!, this._play);\r\n    }\r\n\r\n    private _randLayerActive(layers: LevelLayer[], parentNode: Node, bPlay: boolean):void {\r\n        layers.forEach((layer) => {\r\n            if (layer.type === LayerType.Random) {\r\n                parentNode.children.forEach(layerNode => {\r\n                    const dynamicNode = layerNode.getChildByName(\"dynamic\")!;\r\n                \r\n                    if (bPlay) {\r\n                        // 先激活所有dyna_x节点\r\n                        dynamicNode.children.forEach(dynaNode => dynaNode.active = true);\r\n                        \r\n                        // 遍历所有dyna_x节点（每个对应一个地形策略组）\r\n                        dynamicNode.children.forEach((dynaNode, groupIndex) => {\r\n                            // 获取对应的地形策略组配置\r\n                            const terrainGroup = layer.randomLayers[groupIndex]?.dynamicTerrains[0];\r\n                            if (!terrainGroup) return;\r\n                            \r\n                            // 计算该组的总权重\r\n                            const totalWeight = terrainGroup.dynamicTerrain.reduce(\r\n                                (sum, terrain) => sum + terrain.weight, 0\r\n                            );\r\n                            \r\n                            // 随机选择地形\r\n                            let randomWeight = Math.random() * totalWeight;\r\n                            let accumulatedWeight = 0;\r\n                            let selectedIndex = -1;\r\n                            \r\n                            for (let i = 0; i < terrainGroup.dynamicTerrain.length; i++) {\r\n                                accumulatedWeight += terrainGroup.dynamicTerrain[i].weight;\r\n                                if (randomWeight <= accumulatedWeight) {\r\n                                    selectedIndex = i;\r\n                                    break;\r\n                                }\r\n                            }\r\n                            \r\n                            // 显示选中的地形，隐藏其他\r\n                            dynaNode.children.forEach((terrainNode, terrainIndex) => {\r\n                                const active = (terrainIndex === selectedIndex);\r\n                                terrainNode.active = active;\r\n                                const terrain = terrainNode.getComponent(RandTerrain)!;\r\n                                terrain.play(active);\r\n                            });\r\n                        });\r\n                    } else {\r\n                        dynamicNode.children.forEach(dynaNode => {\r\n                            dynaNode.active = true;\r\n                            dynaNode.children.forEach(terrainNode => {\r\n                                terrainNode.active = true;\r\n                                const terrain = terrainNode.getComponent(RandTerrain)!;\r\n                                terrain.play(false);\r\n                            });\r\n                        });\r\n                    }  \r\n                });\r\n            } else if (layer.type === LayerType.Scroll) {\r\n                parentNode.children.forEach(layerNode => {\r\n                    // 滚动层逻辑\r\n                    const scrollsNode = layerNode.getChildByName(\"scrolls\")!;\r\n                    scrollsNode.children.forEach(layerNode => layerNode.active = true);\r\n                    \r\n                    if (bPlay) {\r\n                        // 计算总权重\r\n                        let totalWeight = 0;\r\n                        for (const scrollLayer of layer.scrollLayers) {\r\n                            totalWeight += scrollLayer.weight;\r\n                        }\r\n                        \r\n                        // 随机选择要显示的滚动体\r\n                        let randomWeight = Math.random() * totalWeight;\r\n                        let selectedIndex = -1;\r\n                        for (let i = 0; i < layer.scrollLayers.length; i++) {\r\n                            randomWeight -= layer.scrollLayers[i].weight;\r\n                            if (randomWeight <= 0) {\r\n                                selectedIndex = i;\r\n                                console.log(\"LevelEditorBase selectedIndex\", selectedIndex);\r\n                                break;\r\n                            }\r\n                        }\r\n                        \r\n                        scrollsNode.children.forEach((child, index) => {\r\n                            child.active = (index === selectedIndex);\r\n                        });\r\n                    } else {\r\n                        scrollsNode.children.forEach(child => {\r\n                            child.active = true;\r\n                        });\r\n                    }\r\n                })\r\n            }\r\n        });\r\n    }\r\n\r\n    private _drawNodeGraphics(): void {\r\n        const graphics = this._drawNode!.getComponent(Graphics);\r\n        if (!graphics) return; \r\n\r\n        const drawTransform = this._drawNode!.getComponent(UITransform)!;\r\n\r\n        const drawport = new Rect(\r\n            this._drawNode!.getPosition().x - drawTransform.contentSize.width / 2,\r\n            this._drawNode!.getPosition().y - drawTransform.contentSize.height / 2,\r\n            drawTransform.contentSize.width,\r\n            this._totalHeight\r\n        );\r\n        \r\n        // Draw drawport rectangle\r\n        graphics.strokeColor = Color.BLUE;\r\n        graphics.lineWidth = 10;\r\n        graphics.rect(drawport.x, drawport.y, drawport.width, drawport.height);\r\n        graphics.stroke()\r\n\r\n        const graphicsView = this._drawNode!.getChildByName(\"drawView\")!.getComponent(Graphics);\r\n        if (!graphicsView) return;\r\n\r\n        const drawview = new Rect(\r\n            -750 / 2,\r\n            -1334 / 2,\r\n            750,\r\n            this._totalHeight\r\n        );\r\n\r\n        graphicsView.strokeColor = Color.RED;\r\n        graphicsView.lineWidth = 10;\r\n        graphicsView.rect(drawview.x, drawview.y, drawview.width, drawview.height);\r\n        graphicsView.stroke()\r\n    }\r\n\r\n    private _drawMask(): void {\r\n        if (!this._play) return;\r\n\r\n        const maskGraphics = this._drawNode!.getChildByName(\"drawMask\")!.getComponent(Graphics);\r\n        if (!maskGraphics) return;\r\n\r\n        // 绘制4个填充矩形表示视口边界\r\n        const maskWidth = 10000;\r\n        const maskHeight = 1334;\r\n        maskGraphics.fillColor = Color.BLACK;\r\n        \r\n        // 顶部矩形\r\n        maskGraphics.fillRect(\r\n            -maskWidth / 2,\r\n            this._drawNode!.getPosition().y + maskHeight - maskHeight / 2, \r\n            maskWidth, \r\n            maskHeight\r\n        );\r\n        \r\n        // 底部矩形\r\n        maskGraphics.fillRect(\r\n            -maskWidth / 2,\r\n            this._drawNode!.getPosition().y - maskHeight - maskHeight / 2,  \r\n            maskWidth, \r\n            maskHeight\r\n        );\r\n        \r\n        // 左侧矩形\r\n        maskGraphics.fillRect(\r\n            -maskWidth - 750 / 2, \r\n            this._drawNode!.getPosition().y - maskHeight / 2, \r\n            maskWidth, \r\n            maskHeight\r\n        );\r\n        \r\n        // 右侧矩形\r\n        maskGraphics.fillRect(\r\n            750 / 2, \r\n            this._drawNode!.getPosition().y - maskHeight / 2, \r\n            maskWidth, \r\n            maskHeight\r\n        );\r\n    }\r\n\r\n\r\n    private _drawMaskClear(): void {\r\n        const maskGraphics = this._drawNode!.getChildByName(\"drawMask\")!.getComponent(Graphics);\r\n        if (!maskGraphics) return;\r\n\r\n        maskGraphics.clear();\r\n    }\r\n}"]}