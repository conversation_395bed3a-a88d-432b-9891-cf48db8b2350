{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/PopupUI.ts"], "names": ["_decorator", "Label", "Vec3", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "BundleName", "ButtonPlus", "ccclass", "property", "PopupUI", "onConfirm", "undefined", "onCancel", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "Top", "getBundleName", "Home", "getUIOption", "isClickBgCloseUI", "onLoad", "btnOK", "addClick", "onConfirmClick", "btnCancel", "onCancelClick", "closeUI", "onShow", "content", "info", "string", "node", "active", "position", "y", "onHide", "onClose"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;;AACnBC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBT,U;;yBAGjBU,O,WADZF,OAAO,CAAC,SAAD,C,UAEHC,QAAQ,CAACR,KAAD,C,UAGRQ,QAAQ;AAAA;AAAA,mC,UAGRA,QAAQ;AAAA;AAAA,mC,2BARb,MACaC,OADb;AAAA;AAAA,4BACoC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAUhCC,SAVgC,GAUEC,SAVF;AAAA,eAWhCC,QAXgC,GAWCD,SAXD;AAAA;;AAaZ,eAANE,MAAM,GAAW;AAAE,iBAAO,mBAAP;AAA6B;;AACxC,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,GAAf;AAAoB;;AAC7B,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,IAAlB;AAAwB;;AACvC,eAAXC,WAAW,GAAU;AAC/B,iBAAO;AAAEC,YAAAA,gBAAgB,EAAE;AAApB,WAAP;AACH;;AAESC,QAAAA,MAAM,GAAS;AACrB,eAAKC,KAAL,CAAYC,QAAZ,CAAqB,KAAKC,cAA1B,EAA0C,IAA1C;AACA,eAAKC,SAAL,CAAgBF,QAAhB,CAAyB,KAAKG,aAA9B,EAA6C,IAA7C;AACH;;AACKF,QAAAA,cAAc,GAAG;AAAA;;AAAA;AACnB;AAAA;AAAA,gCAAMG,OAAN,CAAcjB,OAAd;;AACA,gBAAI,KAAI,CAACC,SAAT,EAAoB;AAChB,cAAA,KAAI,CAACA,SAAL;AACH;AAJkB;AAKtB;;AACKe,QAAAA,aAAa,GAAG;AAAA;;AAAA;AAClB;AAAA;AAAA,gCAAMC,OAAN,CAAcjB,OAAd;;AACA,gBAAI,MAAI,CAACG,QAAT,EAAmB;AACf,cAAA,MAAI,CAACA,QAAL;AACH;AAJiB;AAKrB;;AACKe,QAAAA,MAAM,CAACC,OAAD,EACRlB,SADQ,EAERE,QAFQ,EAE2B;AAAA;;AAAA;AACnC,YAAA,MAAI,CAACiB,IAAL,CAAWC,MAAX,GAAoBF,OAApB;AACA,YAAA,MAAI,CAAClB,SAAL,GAAiBA,SAAjB;AACA,YAAA,MAAI,CAACE,QAAL,GAAgBA,QAAhB;;AACA,gBAAI,MAAI,CAACA,QAAT,EAAmB,CAElB,CAFD,MAEO;AACH,cAAA,MAAI,CAACY,SAAL,CAAgBO,IAAhB,CAAqBC,MAArB,GAA8B,KAA9B;AACA,cAAA,MAAI,CAACX,KAAL,CAAYU,IAAZ,CAAiBE,QAAjB,GAA4B,IAAIhC,IAAJ,CAAS,CAAT,EAAY,MAAI,CAACoB,KAAL,CAAYU,IAAZ,CAAiBE,QAAjB,CAA0BC,CAAtC,EAAyC,CAAzC,CAA5B;AACH;AATkC;AAUtC;;AACKC,QAAAA,MAAM,GAAkB;AAAA;AAC7B;;AACKC,QAAAA,OAAO,GAAkB;AAAA;AAC9B;;AApD+B,O;;;;;iBAEX,I;;;;;;;iBAGM,I;;;;;;;iBAGI,I", "sourcesContent": ["import { _decorator, Label, Vec3 } from 'cc';\nimport { BaseUI, UILayer, UIMgr, UIOpt } from \"db://assets/scripts/core/base/UIMgr\";\nimport { BundleName } from '../../const/BundleConst';\nimport { ButtonPlus } from './components/button/ButtonPlus';\n\nconst { ccclass, property } = _decorator;\n\n@ccclass('PopupUI')\nexport class PopupUI extends BaseUI {\n    @property(Label)\n    info: Label | null = null;\n\n    @property(ButtonPlus)\n    btnOK: ButtonPlus | null = null;\n\n    @property(ButtonPlus)\n    btnCancel: ButtonPlus | null = null;\n\n    onConfirm: Function | undefined = undefined;\n    onCancel: Function | undefined = undefined;\n\n    public static getUrl(): string { return \"prefab/ui/PopupUI\"; }\n    public static getLayer(): UILayer { return UILayer.Top }\n    public static getBundleName(): string { return BundleName.Home }\n    public static getUIOption(): UIOpt {\n        return { isClickBgCloseUI: true }\n    }\n\n    protected onLoad(): void {\n        this.btnOK!.addClick(this.onConfirmClick, this);\n        this.btnCancel!.addClick(this.onCancelClick, this);\n    }\n    async onConfirmClick() {\n        UIMgr.closeUI(PopupUI);\n        if (this.onConfirm) {\n            this.onConfirm();\n        }\n    }\n    async onCancelClick() {\n        UIMgr.closeUI(PopupUI);\n        if (this.onCancel) {\n            this.onCancel();\n        }\n    }\n    async onShow(content: string,\n        onConfirm: Function,\n        onCancel: Function): Promise<void> {\n        this.info!.string = content;\n        this.onConfirm = onConfirm;\n        this.onCancel = onCancel;\n        if (this.onCancel) {\n\n        } else {\n            this.btnCancel!.node.active = false;\n            this.btnOK!.node.position = new Vec3(0, this.btnOK!.node.position.y, 0);\n        }\n    }\n    async onHide(): Promise<void> {\n    }\n    async onClose(): Promise<void> {\n    }\n\n}\n"]}