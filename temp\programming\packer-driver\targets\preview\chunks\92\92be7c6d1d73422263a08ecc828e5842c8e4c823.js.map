{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/boss/BossPlane.ts"], "names": ["_decorator", "<PERSON><PERSON><PERSON><PERSON>", "Material", "size", "sp", "v2", "TrackComponent", "GameEnum", "Tools", "GameIns", "TrackGroup", "FBoxCollider", "ColliderGroupType", "EnemyPlaneBase", "MyApp", "ccclass", "property", "RAND_POINT", "BossPlane", "Skeleton", "_idleName", "_formIndex", "_posX", "_posY", "_moveToX", "_moveToY", "_moveSpeed", "_bArriveDes", "_nextWayPointTime", "_nextWayPointX", "_nextWayPointY", "_nextWayPointInterval", "_nextWaySpeed", "_action", "_bFirstWayPoint", "tip", "_hurtActDuration", "onLoad", "_trackCom", "addScript", "node", "update", "dt", "battleManager", "animSpeed", "resetRoleEffect", "initPlane", "data", "bDamageable", "_initCollide", "_initTrack", "setFormIndex", "setTrackGroupOverCall", "BossAction", "Appear", "setTrackAble", "setAction", "Transform", "collide<PERSON>omp", "getComponent", "addComponent", "init", "groupType", "ENEMY_NORMAL", "colliderEnabled", "index", "action", "Normal", "_playSkel", "_startAppearTrack", "transformEnd", "AttackPrepare", "scheduleOnce", "AttackIng", "AttackOver", "Blast", "anim<PERSON><PERSON>", "loop", "callback", "spinePlane", "setCompleteListener", "setAnimation", "setTip", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bossFightStart", "startBattle", "_startNormalTrack", "updateGameLogic", "deltaTime", "isDead", "updateAction", "_processNextWayPoint", "_updateMove", "_processNextAttack", "_udpateShoot", "trackGroup", "loopNum", "trackIDs", "speeds", "trackIntervals", "startTrack", "moveToPos", "x", "y", "speed", "setPos", "setPosition", "random_int", "length", "getRandomInArray", "deltaX", "deltaY", "distance", "Math", "sqrt", "moveX", "moveY", "abs", "playHurtAnim", "playFlashAnim", "material", "resMgr", "loadAsync", "customMaterial", "setAnimSpeed", "timeScale"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAWC,MAAAA,O,OAAAA,O;AAAQC,MAAAA,Q,OAAAA,Q;AAASC,MAAAA,I,OAAAA,I;AAAKC,MAAAA,E,OAAAA,E;AAAGC,MAAAA,E,OAAAA,E;;AACtCC,MAAAA,c;;AACEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,U,iBAAAA,U;;AACFC,MAAAA,Y;;AACEC,MAAAA,iB,iBAAAA,iB;;AACFC,MAAAA,c;;AAEEC,MAAAA,K,kBAAAA,K;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBhB,U;AAExBiB,MAAAA,U,GAAa,CACfZ,EAAE,CAAC,CAAC,GAAF,EAAM,CAAC,GAAP,CADa,EAEfA,EAAE,CAAC,CAAC,GAAF,EAAM,CAAC,GAAP,CAFa,EAGfA,EAAE,CAAC,GAAD,EAAK,CAAC,GAAN,CAHa,EAIfA,EAAE,CAAC,GAAD,EAAK,CAAC,GAAN,CAJa,C;;yBAQEa,S,WADpBH,OAAO,CAAC,WAAD,C,UAGHC,QAAQ,CAACZ,EAAE,CAACe,QAAJ,C,2BAHb,MACqBD,SADrB;AAAA;AAAA,4CACsD;AAAA;AAAA;;AAAA;;AAAA,eAKlDE,SALkD,GAK9B,OAL8B;AAAA,eAMlDC,UANkD,GAM7B,CAAC,CAN4B;AAM1B;AAExB;AARkD,eASlDC,KATkD,GASlC,CATkC;AAAA,eAUlDC,KAVkD,GAUlC,CAVkC;AAAA,eAWlDC,QAXkD,GAW/B,CAX+B;AAAA,eAYlDC,QAZkD,GAY/B,CAZ+B;AAAA,eAalDC,UAbkD,GAa7B,CAb6B;AAAA,eAclDC,WAdkD,GAc3B,KAd2B;AAcrB;AAC7B;AAfkD,eAgBlDC,iBAhBkD,GAgBtB,CAhBsB;AAAA,eAiBlDC,cAjBkD,GAiBzB,CAjByB;AAAA,eAkBlDC,cAlBkD,GAkBzB,CAlByB;AAAA,eAmBlDC,qBAnBkD,GAmBlB,CAnBkB;AAAA,eAoBlDC,aApBkD,GAoB1B,CApB0B;AAAA,eAsBlDC,OAtBkD,GAsBhC,CAAC,CAtB+B;AAAA,eAuBlDC,eAvBkD,GAuBvB,KAvBuB;AAAA,eAwBlDC,GAxBkD,GAwBpC,EAxBoC;AAwBjC;AAxBiC,eAyBlDC,gBAzBkD,GAyB/B,GAzB+B;AAAA;;AAyB1B;AAEdC,QAAAA,MAAM,GAAS;AACrB,eAAKC,SAAL,GAAiB;AAAA;AAAA,8BAAMC,SAAN,CAAgB,KAAKC,IAArB;AAAA;AAAA,+CAAjB;AACH;;AAESC,QAAAA,MAAM,CAACC,EAAD,EAAmB;AAC/BA,UAAAA,EAAE,GAAGA,EAAE,GAAG;AAAA;AAAA,kCAAQC,aAAR,CAAsBC,SAAhC;;AACA,cAAI,KAAKR,gBAAL,GAAwB,CAA5B,EAA+B;AAC3B,iBAAKA,gBAAL,IAAyBM,EAAzB;;AACA,gBAAI,KAAKN,gBAAL,IAAyB,CAA7B,EAAgC;AAC5B,mBAAKS,eAAL;AACH;AACJ;AACJ;;AAEDC,QAAAA,SAAS,CAACC,IAAD,EAAkB;AACvB,gBAAMD,SAAN,CAAgBC,IAAhB;AACA,eAAKb,eAAL,GAAuB,IAAvB;AACA,eAAKc,WAAL,GAAmB,KAAnB;;AACA,eAAKC,YAAL;;AACA,eAAKC,UAAL;;AACA,eAAKC,YAAL,CAAkB,CAAlB;AACH;;AAGDD,QAAAA,UAAU,GAAG;AACT,eAAKZ,SAAL,CAAgBc,qBAAhB,CAAsC,MAAM;AACxC,gBAAI,KAAKnB,OAAL,KAAiB;AAAA;AAAA,sCAASoB,UAAT,CAAoBC,MAAzC,EAAiD;AAC7C,mBAAKhB,SAAL,CAAgBiB,YAAhB,CAA6B,KAA7B;;AACA,mBAAKC,SAAL,CAAe;AAAA;AAAA,wCAASH,UAAT,CAAoBI,SAAnC;AACH;AACJ,WALD;AAMH;;AAEDR,QAAAA,YAAY,GAAS;AACjB,eAAKS,WAAL,GAAmB,KAAKC,YAAL;AAAA;AAAA,+CAAmC,KAAKC,YAAL;AAAA;AAAA,2CAAtD;AACA,eAAKF,WAAL,CAAkBG,IAAlB,CAAuB,IAAvB,EAA6B1D,IAAI,CAAC,GAAD,EAAM,GAAN,CAAjC,EAFiB,CAE6B;;AAC9C,eAAKuD,WAAL,CAAkBI,SAAlB,GAA8B;AAAA;AAAA,sDAAkBC,YAAhD;AACA,eAAKC,eAAL,GAAuB,KAAvB;AACH;AAEG;AACR;AACA;AACA;;;AACIb,QAAAA,YAAY,CAACc,KAAD,EAAgB;AACxB,eAAK5C,UAAL,GAAkB4C,KAAlB;AACA,eAAK7C,SAAL;AACA,eAAKoC,SAAL,CAAe;AAAA;AAAA,oCAASH,UAAT,CAAoBC,MAAnC;AACH;;AAEDE,QAAAA,SAAS,CAACU,MAAD,EAAiB;AACtB,cAAI,KAAKjC,OAAL,KAAiBiC,MAArB,EAA6B;AACzB,iBAAKjC,OAAL,GAAeiC,MAAf;AAEA,gBAAIb,UAAU,GAAG;AAAA;AAAA,sCAASA,UAA1B;;AACA,oBAAQ,KAAKpB,OAAb;AACI,mBAAKoB,UAAU,CAACc,MAAhB;AACI,qBAAKC,SAAL,CAAe,KAAKhD,SAApB,EAA+B,IAA/B,EAAqC,MAAM,CAAG,CAA9C;;AACA,qBAAK4B,WAAL,GAAmB,IAAnB;AACA;;AAEJ,mBAAKK,UAAU,CAACC,MAAhB;AACI,qBAAKc,SAAL,YAAuB,KAAK/C,UAAL,GAAkB,CAAzC,GAA8C,IAA9C,EAAoD,MAAM,CAAG,CAA7D;;AACA,qBAAKgD,iBAAL;;AACA;;AAEJ,mBAAKhB,UAAU,CAACI,SAAhB;AACI,qBAAKW,SAAL,YAAuB,KAAK/C,UAAL,GAAkB,CAAzC,GAA8C,KAA9C,EAAqD,MAAM;AACvD,uBAAKiD,YAAL;AACH,iBAFD;;AAGA;;AAEJ,mBAAKjB,UAAU,CAACkB,aAAhB;AACI,qBAAKC,YAAL,CAAkB,MAAM;AACpB,uBAAKhB,SAAL,CAAeH,UAAU,CAACoB,SAA1B;AACH,iBAFD;AAGA;;AAEJ,mBAAKpB,UAAU,CAACoB,SAAhB;AACA,mBAAKpB,UAAU,CAACqB,UAAhB;AACI;;AACJ,mBAAKrB,UAAU,CAACsB,KAAhB;AACI;;AACJ;AA5BJ;AA8BH;AACJ;;AAEDP,QAAAA,SAAS,CAACQ,QAAD,EAAmBC,IAAnB,EAAkCC,QAAlC,EAAsD;AAC3D,eAAKC,UAAL,CAAiBC,mBAAjB,CAAqC,MAAM;AACvCF,YAAAA,QAAQ,QAAR,IAAAA,QAAQ;AACX,WAFD;AAGA,eAAKC,UAAL,CAAiBE,YAAjB,CAA8B,CAA9B,EAAiCL,QAAjC,EAA2CC,IAA3C;AACH;AAED;AACJ;AACA;AACA;;;AACIK,QAAAA,MAAM,CAAC/C,GAAD,EAAc;AAChB,eAAKA,GAAL,GAAWA,GAAX;AACH;AACD;AACJ;AACA;;;AACImC,QAAAA,YAAY,GAAG;AACX,cAAI,KAAKnC,GAAL,KAAa,EAAjB,EAAqB;AACjB;AAAA;AAAA,oCAAQQ,aAAR,CAAsBwC,gBAAtB,CAAuC,KAAKhD,GAA5C;AACH,WAFD,MAEO;AACH;AAAA;AAAA,oCAAQQ,aAAR,CAAsByC,cAAtB;AACH;AACJ;AAED;AACJ;AACA;;;AACIC,QAAAA,WAAW,GAAG;AACV,eAAKC,iBAAL;;AACA,eAAK9B,SAAL,CAAe;AAAA;AAAA,oCAASH,UAAT,CAAoBc,MAAnC;AACA,eAAKH,eAAL,GAAuB,IAAvB;AACH;AAED;AACJ;AACA;AACA;;;AACIuB,QAAAA,eAAe,CAACC,SAAD,EAAoB;AAC/B,cAAI,CAAC,KAAKC,MAAV,EAAkB;AACd,iBAAKC,YAAL,CAAkBF,SAAlB;AACH;AACJ;;AAEDE,QAAAA,YAAY,CAACF,SAAD,EAAoB;AAC5B,cAAInC,UAAU,GAAG;AAAA;AAAA,oCAASA,UAA1B;;AACA,kBAAQ,KAAKpB,OAAb;AACI,iBAAKoB,UAAU,CAACc,MAAhB;AACI,mBAAKwB,oBAAL,CAA0BH,SAA1B;;AACA,mBAAKI,WAAL,CAAiBJ,SAAjB;;AACA,mBAAKK,kBAAL,CAAwBL,SAAxB;;AACA;;AAEJ,iBAAKnC,UAAU,CAACC,MAAhB;AACI,mBAAKsC,WAAL,CAAiBJ,SAAjB;;AACA,kBAAI,KAAK7D,WAAT,EAAsB;AAClB,qBAAK6B,SAAL,CAAeH,UAAU,CAACI,SAA1B;AACH;;AACD;;AAEJ,iBAAKJ,UAAU,CAACI,SAAhB;AACI;;AAEJ,iBAAKJ,UAAU,CAACkB,aAAhB;AACI,mBAAKoB,oBAAL,CAA0BH,SAA1B;;AACA;;AAEJ,iBAAKnC,UAAU,CAACoB,SAAhB;AACI,mBAAKkB,oBAAL,CAA0BH,SAA1B;;AACA,mBAAKM,YAAL,CAAkBN,SAAlB;;AACA;;AAEJ,iBAAKnC,UAAU,CAACqB,UAAhB;AACI,mBAAKiB,oBAAL,CAA0BH,SAA1B;;AACA,mBAAKhC,SAAL,CAAeH,UAAU,CAACc,MAA1B;AACA;;AAEJ,iBAAKd,UAAU,CAACsB,KAAhB;AACI;AAhCR;AAkCH;AACD;AACJ;AACA;;;AACIN,QAAAA,iBAAiB,GAAG;AAChB;AAEA;AACA,cAAM0B,UAAU,GAAG;AAAA;AAAA,yCAAnB;AACAA,UAAAA,UAAU,CAACC,OAAX,GAAqB,CAArB;AACAD,UAAAA,UAAU,CAACE,QAAX,GAAsB,CAAC,EAAD,CAAtB;AACAF,UAAAA,UAAU,CAACG,MAAX,GAAoB,CAAC,GAAD,CAApB;AACAH,UAAAA,UAAU,CAACI,cAAX,GAA4B,CAAC,CAAD,CAA5B;;AAEA,eAAK7D,SAAL,CAAgBuB,IAAhB,CAAqB,IAArB,EAA2B,CAACkC,UAAD,CAA3B,EAAyC,EAAzC,EAA6C1F,EAAE,CAAC,CAAD,EAAI,GAAJ,CAA/C;;AACA,eAAKiC,SAAL,CAAgBiB,YAAhB,CAA6B,IAA7B;;AACA,eAAKjB,SAAL,CAAgB8D,UAAhB;AACH;AAED;AACJ;AACA;;;AACId,QAAAA,iBAAiB,GAAG;AAChB,eAAKhD,SAAL,CAAgBiB,YAAhB,CAA6B,KAA7B;;AACA,eAAKC,SAAL,CAAe;AAAA;AAAA,oCAASH,UAAT,CAAoBc,MAAnC;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACIkC,QAAAA,SAAS,CAACC,CAAD,EAAYC,CAAZ,EAAuBC,KAAvB,EAAsC;AAC3C,eAAKhF,QAAL,GAAgB8E,CAAhB;AACA,eAAK7E,QAAL,GAAgB8E,CAAhB;AACA,eAAK7E,UAAL,GAAkB8E,KAAlB;AACA,eAAK7E,WAAL,GAAmB,KAAnB;AACH;;AAGD8E,QAAAA,MAAM,CAACH,CAAD,EAAYC,CAAZ,EAAuB9D,MAAvB,EAA+C;AAAA,cAAxBA,MAAwB;AAAxBA,YAAAA,MAAwB,GAAN,IAAM;AAAA;;AACjD,eAAKD,IAAL,CAAUkE,WAAV,CAAsBJ,CAAtB,EAAyBC,CAAzB;AACA,eAAKjF,KAAL,GAAagF,CAAb;AACA,eAAK/E,KAAL,GAAagF,CAAb;AACH;AAED;AACJ;AACA;AACA;;;AACIZ,QAAAA,oBAAoB,CAACH,SAAD,EAAoB;AACpC,cAAI,KAAK7D,WAAT,EAAsB;AAClB,iBAAKC,iBAAL,IAA0B4D,SAA1B;;AACA,gBAAI,KAAK5D,iBAAL,GAAyB,KAAKG,qBAAlC,EAAyD;AACrD,mBAAKA,qBAAL,GAA6B,IAA7B;AACA,mBAAKH,iBAAL,GAAyB,CAAzB;;AAEA,kBAAI,KAAKM,eAAT,EAA0B;AACtB,qBAAKA,eAAL,GAAuB,KAAvB;AACH,eAFD,MAEO;AACH,oBAAM+B,KAAK,GAAG;AAAA;AAAA,oCAAM0C,UAAN,CAAiB,CAAjB,EAAoB1F,UAAU,CAAC2F,MAAX,GAAoB,CAAxC,CAAd;AACA,qBAAK/E,cAAL,GAAsBZ,UAAU,CAACgD,KAAD,CAAV,CAAkBqC,CAAxC;AACA,qBAAKxE,cAAL,GAAsBb,UAAU,CAACgD,KAAD,CAAV,CAAkBsC,CAAxC;AACA,qBAAKvE,aAAL,GAAqB;AAAA;AAAA,oCAAM6E,gBAAN,CAAuB,CAAC,CAAD,EAAG,GAAH,CAAvB,CAArB;AACA,qBAAKR,SAAL,CAAe,KAAKxE,cAApB,EAAoC,KAAKC,cAAzC,EAAyD,KAAKE,aAA9D;AACH;AACJ;AACJ;AACJ;;AAGD4D,QAAAA,WAAW,CAACJ,SAAD,EAAoB;AAC3B,cAAI,KAAKvD,OAAL,KAAiB;AAAA;AAAA,oCAASoB,UAAT,CAAoBC,MAAzC,EAAiD;AAC7C;AACA,iBAAKhB,SAAL,CAAgBiD,eAAhB,CAAgCC,SAAhC;AACH,WAHD,MAGO,IAAI,CAAC,KAAK7D,WAAV,EAAuB;AAC1B,gBAAMmF,MAAM,GAAG,KAAKtF,QAAL,GAAgB,KAAKF,KAApC;AACA,gBAAMyF,MAAM,GAAG,KAAKtF,QAAL,GAAgB,KAAKF,KAApC;AACA,gBAAMyF,QAAQ,GAAGC,IAAI,CAACC,IAAL,CAAUJ,MAAM,GAAGA,MAAT,GAAkBC,MAAM,GAAGA,MAArC,CAAjB;AAEA,gBAAII,KAAK,GAAG,CAAZ;AACA,gBAAIC,KAAK,GAAG,CAAZ,CAN0B,CAQ1B;;AACA,gBAAIJ,QAAQ,IAAI,KAAKtF,UAArB,EAAiC;AAC7ByF,cAAAA,KAAK,GAAGL,MAAR;AACAM,cAAAA,KAAK,GAAGL,MAAR;AACH,aAHD,CAIA;AAJA,iBAKK;AACDI,cAAAA,KAAK,GAAG,KAAKzF,UAAL,GAAkBoF,MAAlB,GAA2BE,QAAnC;AACAI,cAAAA,KAAK,GAAG,KAAK1F,UAAL,GAAkBqF,MAAlB,GAA2BC,QAAnC;AACH,aAjByB,CAmB1B;;;AACA,iBAAK1F,KAAL,IAAc6F,KAAd;AACA,iBAAK5F,KAAL,IAAc6F,KAAd;AACA,iBAAKX,MAAL,CAAY,KAAKnF,KAAjB,EAAwB,KAAKC,KAA7B,EAtB0B,CAwB1B;;AACA,iBAAKI,WAAL,GAAoBsF,IAAI,CAACI,GAAL,CAASF,KAAT,IAAkB,GAAlB,IAAyBF,IAAI,CAACI,GAAL,CAASD,KAAT,IAAkB,GAA/D;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIvB,QAAAA,kBAAkB,CAACL,SAAD,EAAoB,CAClC;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACH;AAED;AACJ;AACA;AACA;;;AACUM,QAAAA,YAAY,CAACN,SAAD,EAAoB,CAClC;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;AAdkC;AAerC,SApXiD,CAuXlD;;;AAEA8B,QAAAA,YAAY,GAAE;AACV,eAAKC,aAAL;AACH,SA3XiD,CA4XlD;;;AACMA,QAAAA,aAAa,GAAG;AAAA;;AAAA;AAClB,gBAAIC,QAAQ,SAAS;AAAA;AAAA,gCAAMC,MAAN,CAAaC,SAAb,CAAuB,oBAAvB,EAA6CxH,QAA7C,CAArB;;AACA,gBAAIsH,QAAQ,IAAIvH,OAAO,CAAC,KAAI,CAAC8E,UAAN,CAAvB,EAAyC;AACrC,cAAA,KAAI,CAACA,UAAL,CAAiB4C,cAAjB,GAAkCH,QAAlC;AACH;;AACD,YAAA,KAAI,CAACpF,gBAAL,GAAwB,GAAxB;AALkB;AAMrB;;AAEDS,QAAAA,eAAe,GAAE;AACb,eAAKT,gBAAL,GAAwB,CAAxB;AACA,eAAK2C,UAAL,CAAiB4C,cAAjB,GAAkC,IAAlC;AACH;;AAEDC,QAAAA,YAAY,CAACpB,KAAD,EAAgB;AACxB,cAAI,KAAKzB,UAAL,IAAmB9E,OAAO,CAAC,KAAK8E,UAAN,CAA9B,EAAgD;AAC5C,iBAAKA,UAAL,CAAgB8C,SAAhB,GAA4BrB,KAA5B;AACH;AACJ;;AA9YiD,O;;;;;iBAGjB,I", "sourcesContent": ["import { _decorator,isValid,Material,size,sp,v2,} from 'cc';\r\nimport TrackComponent from '../../base/TrackComponent';\r\nimport { GameEnum } from '../../../const/GameEnum';\r\nimport { Tools } from '../../../utils/Tools';\r\nimport { GameIns } from '../../../GameIns';\r\nimport { TrackGroup } from '../../../data/EnemyWave';\r\nimport FBoxCollider from '../../../collider-system/FBoxCollider';\r\nimport { ColliderGroupType } from '../../../collider-system/FCollider';\r\nimport EnemyPlaneBase from '../enemy/EnemyPlaneBase';\r\nimport { EnemyData } from '../../../data/EnemyData';\r\nimport { MyApp } from '../../../../app/MyApp';\r\nconst { ccclass, property } = _decorator;\r\n\r\nconst RAND_POINT = [\r\n    v2(-100,-320),\r\n    v2(-100,-250),\r\n    v2(100,-320),\r\n    v2(100,-250),\r\n]\r\n\r\n@ccclass(\"BossPlane\")\r\nexport default class BossPlane extends EnemyPlaneBase {\r\n\r\n    @property(sp.Skeleton)//boss显示组件\r\n    spinePlane: sp.Skeleton | null = null;\r\n\r\n    _idleName: string = \"idle1\";\r\n    _formIndex: number = -1;//形态索引\r\n\r\n    //随机移动参数\r\n    _posX: number = 0;\r\n    _posY: number = 0;\r\n    _moveToX: number = 0;\r\n    _moveToY: number = 0;\r\n    _moveSpeed: number = 0;\r\n    _bArriveDes: boolean = false;//是否达到目标点\r\n    // //下一个航点\r\n    _nextWayPointTime: number = 0;\r\n    _nextWayPointX: number = 0;\r\n    _nextWayPointY: number = 0;\r\n    _nextWayPointInterval: number = 0;\r\n    _nextWaySpeed: number = 0;\r\n\r\n    _action: number = -1;\r\n    _bFirstWayPoint: boolean = false;\r\n    tip: string = \"\";//boss警告提示文本\r\n    _hurtActDuration = 0.2; // 受伤动画持续时间\r\n\r\n    protected onLoad(): void {\r\n        this._trackCom = Tools.addScript(this.node, TrackComponent);\r\n    }\r\n\r\n    protected update(dt: number): void {\r\n        dt = dt * GameIns.battleManager.animSpeed;\r\n        if (this._hurtActDuration > 0) {\r\n            this._hurtActDuration -= dt;\r\n            if (this._hurtActDuration <= 0) {\r\n                this.resetRoleEffect();\r\n            }\r\n        }\r\n    }\r\n\r\n    initPlane(data: EnemyData) {\r\n        super.initPlane(data);\r\n        this._bFirstWayPoint = true;\r\n        this.bDamageable = false;\r\n        this._initCollide();\r\n        this._initTrack();\r\n        this.setFormIndex(0);\r\n    }\r\n\r\n\r\n    _initTrack() {\r\n        this._trackCom!.setTrackGroupOverCall(() => {\r\n            if (this._action === GameEnum.BossAction.Appear) {\r\n                this._trackCom!.setTrackAble(false);\r\n                this.setAction(GameEnum.BossAction.Transform);\r\n            }\r\n        });\r\n    }\r\n\r\n    _initCollide(): void {\r\n        this.collideComp = this.getComponent(FBoxCollider) || this.addComponent(FBoxCollider);\r\n        this.collideComp!.init(this, size(250, 250)); // 初始化碰撞组件\r\n        this.collideComp!.groupType = ColliderGroupType.ENEMY_NORMAL;\r\n        this.colliderEnabled = false;\r\n    }\r\n\r\n        /**\r\n     * 设置形态索引（一般boss会有好几段变形，要等策划设计）\r\n     * @param index 形态索引\r\n     */\r\n    setFormIndex(index: number) {\r\n        this._formIndex = index;\r\n        this._idleName = `idle1`;\r\n        this.setAction(GameEnum.BossAction.Appear);\r\n    }\r\n\r\n    setAction(action: number) {\r\n        if (this._action !== action) {\r\n            this._action = action;\r\n\r\n            let BossAction = GameEnum.BossAction;\r\n            switch (this._action) {\r\n                case BossAction.Normal:\r\n                    this._playSkel(this._idleName, true, () => { });\r\n                    this.bDamageable = true;\r\n                    break;\r\n\r\n                case BossAction.Appear:\r\n                    this._playSkel(`enter${this._formIndex + 1}`, true, () => { });\r\n                    this._startAppearTrack();\r\n                    break;\r\n\r\n                case BossAction.Transform:\r\n                    this._playSkel(`ready${this._formIndex + 1}`, false, () => {\r\n                        this.transformEnd();\r\n                    });\r\n                    break;\r\n\r\n                case BossAction.AttackPrepare:\r\n                    this.scheduleOnce(() => {\r\n                        this.setAction(BossAction.AttackIng);\r\n                    });\r\n                    break;\r\n\r\n                case BossAction.AttackIng:\r\n                case BossAction.AttackOver:\r\n                    break;\r\n                case BossAction.Blast:\r\n                    break;\r\n                default:\r\n            }\r\n        }\r\n    }\r\n\r\n    _playSkel(animName: string, loop: boolean, callback: Function) {\r\n        this.spinePlane!.setCompleteListener(() => {\r\n            callback?.()\r\n        })\r\n        this.spinePlane!.setAnimation(0, animName, loop);\r\n    }\r\n\r\n    /**\r\n     * 设置提示信息\r\n     * @param tip 提示信息\r\n     */\r\n    setTip(tip: string) {\r\n        this.tip = tip;\r\n    }\r\n    /**\r\n    * 变形结束\r\n    */\r\n    transformEnd() {\r\n        if (this.tip !== \"\") {\r\n            GameIns.battleManager.bossChangeFinish(this.tip);\r\n        } else {\r\n            GameIns.battleManager.bossFightStart();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 开始战斗\r\n     */\r\n    startBattle() {\r\n        this._startNormalTrack();\r\n        this.setAction(GameEnum.BossAction.Normal);\r\n        this.colliderEnabled = true;\r\n    }\r\n\r\n    /**\r\n     * 更新游戏逻辑\r\n     * @param deltaTime 每帧时间\r\n     */\r\n    updateGameLogic(deltaTime: number) {\r\n        if (!this.isDead) {\r\n            this.updateAction(deltaTime);\r\n        }\r\n    }\r\n\r\n    updateAction(deltaTime: number) {\r\n        let BossAction = GameEnum.BossAction;\r\n        switch (this._action) {\r\n            case BossAction.Normal:\r\n                this._processNextWayPoint(deltaTime);\r\n                this._updateMove(deltaTime);\r\n                this._processNextAttack(deltaTime);\r\n                break;\r\n\r\n            case BossAction.Appear:\r\n                this._updateMove(deltaTime);\r\n                if (this._bArriveDes) {\r\n                    this.setAction(BossAction.Transform);\r\n                }\r\n                break;\r\n\r\n            case BossAction.Transform:\r\n                break;\r\n\r\n            case BossAction.AttackPrepare:\r\n                this._processNextWayPoint(deltaTime);\r\n                break;\r\n\r\n            case BossAction.AttackIng:\r\n                this._processNextWayPoint(deltaTime);\r\n                this._udpateShoot(deltaTime);\r\n                break;\r\n\r\n            case BossAction.AttackOver:\r\n                this._processNextWayPoint(deltaTime);\r\n                this.setAction(BossAction.Normal);\r\n                break;\r\n\r\n            case BossAction.Blast:\r\n                break;\r\n        }\r\n    }\r\n    /**\r\n     * 开始出现轨迹\r\n     */\r\n    _startAppearTrack() {\r\n        // 0,150,16,150\r\n\r\n        //出现的轨迹，暂时写死，等波次管理器做完，再接入新的轨迹系统\r\n        const trackGroup = new TrackGroup();\r\n        trackGroup.loopNum = 1;\r\n        trackGroup.trackIDs = [16];\r\n        trackGroup.speeds = [150];\r\n        trackGroup.trackIntervals = [0];\r\n\r\n        this._trackCom!.init(this, [trackGroup], [], v2(0, 150));\r\n        this._trackCom!.setTrackAble(true);\r\n        this._trackCom!.startTrack();\r\n    }\r\n\r\n    /**\r\n     * 开始正常轨迹,boss随机移动位置\r\n     */\r\n    _startNormalTrack() {\r\n        this._trackCom!.setTrackAble(false);\r\n        this.setAction(GameEnum.BossAction.Normal);\r\n    }\r\n\r\n    /**\r\n     * 移动到指定位置\r\n     * @param x X 坐标\r\n     * @param y Y 坐标\r\n     * @param speed 移动速度\r\n     * @param transformMove 是否为变形移动\r\n     */\r\n    moveToPos(x: number, y: number, speed: number) {\r\n        this._moveToX = x;\r\n        this._moveToY = y;\r\n        this._moveSpeed = speed;\r\n        this._bArriveDes = false;\r\n    }\r\n\r\n\r\n    setPos(x: number, y: number, update: boolean = true) {\r\n        this.node.setPosition(x, y);\r\n        this._posX = x;\r\n        this._posY = y;\r\n    }\r\n\r\n    /**\r\n     * 处理下一个路径点\r\n     * @param deltaTime 每帧时间\r\n     */\r\n    _processNextWayPoint(deltaTime: number) {\r\n        if (this._bArriveDes) {\r\n            this._nextWayPointTime += deltaTime;\r\n            if (this._nextWayPointTime > this._nextWayPointInterval) {\r\n                this._nextWayPointInterval = 0.15;\r\n                this._nextWayPointTime = 0;\r\n\r\n                if (this._bFirstWayPoint) {\r\n                    this._bFirstWayPoint = false;\r\n                } else {\r\n                    const index = Tools.random_int(0, RAND_POINT.length - 1);\r\n                    this._nextWayPointX = RAND_POINT[index].x;\r\n                    this._nextWayPointY = RAND_POINT[index].y;\r\n                    this._nextWaySpeed = Tools.getRandomInArray([2,2.2])!;\r\n                    this.moveToPos(this._nextWayPointX, this._nextWayPointY, this._nextWaySpeed);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n\r\n    _updateMove(deltaTime: number) {\r\n        if (this._action === GameEnum.BossAction.Appear) {\r\n            // 如果 Boss 在出现阶段或有轨迹组，则更新轨迹逻辑\r\n            this._trackCom!.updateGameLogic(deltaTime);\r\n        } else if (!this._bArriveDes) {\r\n            const deltaX = this._moveToX - this._posX;\r\n            const deltaY = this._moveToY - this._posY;\r\n            const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);\r\n\r\n            let moveX = 0;\r\n            let moveY = 0;\r\n\r\n            // 如果距离小于等于移动速度，则直接到达目标点\r\n            if (distance <= this._moveSpeed) {\r\n                moveX = deltaX;\r\n                moveY = deltaY;\r\n            }\r\n            // 否则按比例移动\r\n            else {\r\n                moveX = this._moveSpeed * deltaX / distance;\r\n                moveY = this._moveSpeed * deltaY / distance;\r\n            }\r\n\r\n            // 更新位置\r\n            this._posX += moveX;\r\n            this._posY += moveY;\r\n            this.setPos(this._posX, this._posY);\r\n\r\n            // 检查是否到达目的地（当移动量很小时认为已到达）\r\n            this._bArriveDes = (Math.abs(moveX) < 0.5 && Math.abs(moveY) < 0.5);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 处理下一次攻击\r\n     * @param deltaTime 每帧时间\r\n     */\r\n    _processNextAttack(deltaTime: number) {\r\n        // if (this._shootAble && this._action === GameEnum.BossAction.Normal) {\r\n        //     this._nextAttackTime += deltaTime;\r\n        //     if (this._nextAttackTime > this._nextAttackInterval) {\r\n        //         this._nextAttackInterval = Tools.getRandomInArray(this._data!.attackIntervals)!;\r\n        //         this._nextAttackTime = 0;\r\n\r\n        //         let attackAction = null;\r\n        //         if (this._bOrderAttack) {\r\n        //             const randomIndex = Tools.getRandomInArray(this._orderAtkArr)!;\r\n        //             Tools.arrRemove(this._orderAtkArr, randomIndex);\r\n        //             attackAction = this._atkActions[randomIndex];\r\n        //             this._orderIndex++;\r\n        //             if (this._orderIndex > this._atkActions.length - 1) {\r\n        //                 this._bOrderAttack = false;\r\n        //             }\r\n        //         } else {\r\n        //             attackAction = Tools.getRandomInArray(this._atkActions);\r\n        //         }\r\n\r\n        //         if (attackAction) {\r\n        //             this._bAttackMove = attackAction.bAtkMove;\r\n        //             this._attackID = attackAction.atkActId;\r\n        //             this._attackPoints.splice(0);\r\n\r\n        //             for (const pointId of attackAction.atkPointId) {\r\n        //                 const pointData = this._atkPointDatas[pointId];\r\n        //                 if (pointData[0]) {\r\n        //                     let attackPoint = this._atkPointsPool[pointId]\r\n        //                     if (!attackPoint) {\r\n        //                         const pointNode = new Node();\r\n        //                         this.node.addChild(pointNode);\r\n        //                         attackPoint = pointNode.addComponent(AttackPoint);\r\n        //                         this._atkPointsPool.push(attackPoint);\r\n        //                     }\r\n        //                     attackPoint.initForBoss(pointData[1], this);\r\n        //                     this._attackPoints.push(attackPoint);\r\n        //                 }\r\n        //             }\r\n\r\n        //             if (this._attackPoints.length > 0) {\r\n        //                 this.setAction(GameEnum.BossAction.AttackPrepare);\r\n        //             }\r\n        //         }\r\n        //     }\r\n        // }\r\n    }\r\n\r\n    /**\r\n     * 更新射击逻辑\r\n     * @param deltaTime 每帧时间\r\n     */\r\n    async _udpateShoot(deltaTime: number) {\r\n        // if (this._shootAble) {\r\n        //     let allAttacksOver = true;\r\n\r\n        //     for (const attackPoint of this._attackPoints) {\r\n        //         await attackPoint.updateGameLogic(deltaTime);\r\n        //         if (!attackPoint.isAttackOver()) {\r\n        //             allAttacksOver = false;\r\n        //         }\r\n        //     }\r\n\r\n        //     if (allAttacksOver) {\r\n        //         this.setAction(GameEnum.BossAction.AttackOver);\r\n        //     }\r\n        // }\r\n    }\r\n\r\n\r\n    //由于boss资源不是新的，暂时把闪白效果，写在这里\r\n    \r\n    playHurtAnim(){\r\n        this.playFlashAnim();\r\n    }\r\n    // 播放闪白动画\r\n    async playFlashAnim() {\r\n        let material = await MyApp.resMgr.loadAsync(\"effect/flash/flash\", Material);\r\n        if (material && isValid(this.spinePlane)){\r\n            this.spinePlane!.customMaterial = material;\r\n        }\r\n        this._hurtActDuration = 0.2;\r\n    }\r\n\r\n    resetRoleEffect(){\r\n        this._hurtActDuration = 0;\r\n        this.spinePlane!.customMaterial = null;\r\n    }\r\n\r\n    setAnimSpeed(speed: number) {\r\n        if (this.spinePlane && isValid(this.spinePlane)){\r\n            this.spinePlane.timeScale = speed;\r\n        }\r\n    }\r\n}"]}