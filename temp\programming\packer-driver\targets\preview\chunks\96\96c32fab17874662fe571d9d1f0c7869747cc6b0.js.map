{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/dyncTerrain/EmittierTerrain.ts"], "names": ["_decorator", "Component", "EDITOR", "ccclass", "property", "executeInEditMode", "EmittierTerrain", "onLoad", "node", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_loadElems", "update", "play", "bPlay", "onDestroy"], "mappings": ";;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAqCC,MAAAA,S,OAAAA,S;;AACrCC,MAAAA,M,UAAAA,M;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CL,U;;iCAIpCM,e,WAFZH,OAAO,CAAC,iBAAD,C,UACPE,iBAAiB,E,+BADlB,MAEaC,eAFb,SAEqCL,SAFrC,CAE+C;AAEjCM,QAAAA,MAAM,GAAS;AACrB,cAAIL,MAAJ,EAAY;AACR,iBAAKM,IAAL,CAAUC,iBAAV;;AACA,iBAAKC,UAAL;AACH;AACJ;;AAESC,QAAAA,MAAM,GAAS,CAExB;;AAEMC,QAAAA,IAAI,CAACC,KAAD,EAAuB;AAC9B,cAAIX,MAAJ,EAAY;AACR,gBAAIW,KAAJ,EAAW,CACV;AACJ;AACJ;;AAESC,QAAAA,SAAS,GAAS;AACxB,eAAKN,IAAL,CAAUC,iBAAV;AACH;;AAEOC,QAAAA,UAAU,GAAS,CAE1B;;AA1B0C,O", "sourcesContent": ["import { _decorator, assetManager, CCInteger, Component, instantiate, Prefab, v2, Vec2 } from 'cc';\r\nimport { EDITOR } from 'cc/env';\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\n\r\n@ccclass('EmittierTerrain')\r\n@executeInEditMode()\r\nexport class EmittierTerrain extends Component {\r\n\r\n    protected onLoad(): void {\r\n        if (EDITOR) {\r\n            this.node.removeAllChildren();\r\n            this._loadElems();\r\n        }\r\n    }\r\n\r\n    protected update(): void {\r\n        \r\n    }\r\n\r\n    public play(bPlay: boolean): void {\r\n        if (EDITOR) {\r\n            if (bPlay) {\r\n            }\r\n        }\r\n    }\r\n\r\n    protected onDestroy(): void {\r\n        this.node.removeAllChildren();\r\n    }\r\n\r\n    private _loadElems(): void {\r\n        \r\n    }\r\n}\r\n\r\n"]}