System.register(["cc", "cc/env"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, assetManager, CCInteger, Component, instantiate, Prefab, v2, Vec2, EDITOR, _dec, _dec2, _dec3, _dec4, _class, _class2, _descriptor, _descriptor2, _descriptor3, _dec5, _dec6, _dec7, _class4, _class5, _descriptor4, _crd, ccclass, property, executeInEditMode, TerrainElem, RandTerrain;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      assetManager = _cc.assetManager;
      CCInteger = _cc.CCInteger;
      Component = _cc.Component;
      instantiate = _cc.instantiate;
      Prefab = _cc.Prefab;
      v2 = _cc.v2;
      Vec2 = _cc.Vec2;
    }, function (_ccEnv) {
      EDITOR = _ccEnv.EDITOR;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "ddd20hR0cNEFo84SUOIhIer", "RandTerrain", undefined);

      __checkObsolete__(['_decorator', 'assetManager', 'CCInteger', 'Component', 'instantiate', 'Prefab', 'v2', 'Vec2']);

      ({
        ccclass,
        property,
        executeInEditMode
      } = _decorator);

      _export("TerrainElem", TerrainElem = (_dec = ccclass('TerrainElem'), _dec2 = property(CCInteger), _dec3 = property(Prefab), _dec4 = property({
        displayName: "坐标偏移"
      }), _dec(_class = (_class2 = class TerrainElem {
        constructor() {
          _initializerDefineProperty(this, "weight", _descriptor, this);

          _initializerDefineProperty(this, "elem", _descriptor2, this);

          _initializerDefineProperty(this, "offSet", _descriptor3, this);
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "weight", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "elem", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "offSet", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return new Vec2(0, 0);
        }
      })), _class2)) || _class));

      _export("RandTerrain", RandTerrain = (_dec5 = ccclass('RandTerrain'), _dec6 = executeInEditMode(), _dec7 = property({
        type: [TerrainElem]
      }), _dec5(_class4 = _dec6(_class4 = (_class5 = class RandTerrain extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "terrain", _descriptor4, this);
        }

        onLoad() {
          if (EDITOR) {
            this.node.removeAllChildren();

            this._loadElems();
          }
        }

        update() {
          if (EDITOR) {
            if (this.terrain === null || this.terrain.length == 0) {
              return;
            }

            var isCountMatch = this.node.children.length === this.terrain.length;
            var isUUIDMatch = true;

            for (var i = 0; i < Math.min(this.node.children.length, this.terrain.length); i++) {
              var _node$_prefab;

              var terrainElem = this.terrain[i]; // 增加空值检查

              if (!terrainElem || !terrainElem.elem) {
                console.warn("TerrainElem at index " + i + " is invalid");
                continue; // 跳过无效元素
              }

              var node = this.node.children[i]; // @ts-ignore

              var nodeUUID = (_node$_prefab = node._prefab) == null || (_node$_prefab = _node$_prefab.asset) == null ? void 0 : _node$_prefab._uuid; // 使用可选链

              var elemUUID = terrainElem.elem.uuid;

              if (nodeUUID !== elemUUID) {
                isUUIDMatch = false;
                break;
              }
            }

            if (!isCountMatch || !isUUIDMatch) {
              this._loadElems();
            } else {
              this.node.children.forEach((child, index) => {
                this.terrain[index].offSet = v2(child.position.x, child.position.y);
              });
            }
          }
        }

        play(bPlay) {
          if (EDITOR) {
            if (bPlay) {
              var totalWeight = 0;

              for (var i = 0; i < this.terrain.length; i++) {
                totalWeight += this.terrain[i].weight;
              }

              var randomValue = Math.random() * totalWeight;
              var accumulatedWeight = 0;
              var selectedIndex = 0;

              for (var _i = 0; _i < this.terrain.length; _i++) {
                accumulatedWeight += this.terrain[_i].weight;

                if (randomValue < accumulatedWeight) {
                  selectedIndex = _i;
                  break;
                }
              } // 设置所有节点的active状态


              for (var _i2 = 0; _i2 < this.node.children.length; _i2++) {
                this.node.children[_i2].active = _i2 === selectedIndex;
              }
            } else {
              for (var _i3 = 0; _i3 < this.node.children.length; _i3++) {
                this.node.children[_i3].active = true;
              }
            }
          }
        }

        onDestroy() {
          this.node.removeAllChildren();
        }

        _loadElems() {
          this.terrain.forEach(elem => {
            var _elem$elem;

            if (!elem || elem.elem == null) {
              return;
            }

            this.node.removeAllChildren();
            assetManager.loadAny({
              uuid: (_elem$elem = elem.elem) == null ? void 0 : _elem$elem.uuid
            }, (err, prefab) => {
              if (err) {
                console.error("RandTerrain load TerrainElem prefab err", err);
                return;
              }

              var node = instantiate(prefab);
              node.setPosition(elem.offSet.x, elem.offSet.y, 0);
              this.node.addChild(node);
            });
          });
        }

      }, (_descriptor4 = _applyDecoratedDescriptor(_class5.prototype, "terrain", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return [];
        }
      })), _class5)) || _class4) || _class4));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=97ad45c924900ac4799a7df7974bf339c0924b9e.js.map