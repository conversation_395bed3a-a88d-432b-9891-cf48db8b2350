{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/dyncTerrain/RandTerrain.ts"], "names": ["_decorator", "assetManager", "CCInteger", "Component", "instantiate", "Prefab", "v2", "Vec2", "EDITOR", "ccclass", "property", "executeInEditMode", "TerrainElem", "displayName", "RandTerrain", "type", "onLoad", "node", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_loadElems", "update", "terrain", "length", "isCountMatch", "children", "isUUIDMatch", "i", "Math", "min", "terrainElem", "elem", "console", "warn", "nodeUUID", "_prefab", "asset", "_uuid", "elemUUID", "uuid", "for<PERSON>ach", "child", "index", "offSet", "position", "x", "y", "play", "bPlay", "totalWeight", "weight", "randomValue", "random", "accumulatedWeight", "selectedIndex", "active", "onDestroy", "loadAny", "err", "prefab", "error", "setPosition", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,Y,OAAAA,Y;AAAcC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,E,OAAAA,E;AAAIC,MAAAA,I,OAAAA,I;;AACzEC,MAAAA,M,UAAAA,M;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CX,U;;6BAGpCY,W,WADZH,OAAO,CAAC,aAAD,C,UAEHC,QAAQ,CAACR,SAAD,C,UAERQ,QAAQ,CAACL,MAAD,C,UAERK,QAAQ,CAAC;AAACG,QAAAA,WAAW,EAAE;AAAd,OAAD,C,2BANb,MACaD,WADb,CACyB;AAAA;AAAA;;AAAA;;AAAA;AAAA;;AAAA,O;;;;;iBAEG,C;;;;;;;iBAEK,I;;;;;;;iBAEP,IAAIL,IAAJ,CAAS,CAAT,EAAY,CAAZ,C;;;;6BAKbO,W,YAFZL,OAAO,CAAC,aAAD,C,UACPE,iBAAiB,E,UAEbD,QAAQ,CAAC;AAACK,QAAAA,IAAI,EAAE,CAACH,WAAD;AAAP,OAAD,C,6CAHb,MAEaE,WAFb,SAEiCX,SAFjC,CAE2C;AAAA;AAAA;;AAAA;AAAA;;AAI7Ba,QAAAA,MAAM,GAAS;AACrB,cAAIR,MAAJ,EAAY;AACR,iBAAKS,IAAL,CAAUC,iBAAV;;AACA,iBAAKC,UAAL;AACH;AACJ;;AAESC,QAAAA,MAAM,GAAS;AACrB,cAAIZ,MAAJ,EAAY;AACR,gBAAI,KAAKa,OAAL,KAAiB,IAAjB,IAAyB,KAAKA,OAAL,CAAaC,MAAb,IAAuB,CAApD,EAAuD;AACnD;AACH;;AAED,gBAAMC,YAAY,GAAG,KAAKN,IAAL,CAAUO,QAAV,CAAmBF,MAAnB,KAA8B,KAAKD,OAAL,CAAaC,MAAhE;AACA,gBAAIG,WAAW,GAAG,IAAlB;;AAEA,iBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGC,IAAI,CAACC,GAAL,CAAS,KAAKX,IAAL,CAAUO,QAAV,CAAmBF,MAA5B,EAAoC,KAAKD,OAAL,CAAaC,MAAjD,CAApB,EAA8EI,CAAC,EAA/E,EAAmF;AAAA;;AAC/E,kBAAMG,WAAW,GAAG,KAAKR,OAAL,CAAaK,CAAb,CAApB,CAD+E,CAG/E;;AACA,kBAAI,CAACG,WAAD,IAAgB,CAACA,WAAW,CAACC,IAAjC,EAAuC;AACnCC,gBAAAA,OAAO,CAACC,IAAR,2BAAqCN,CAArC;AACA,yBAFmC,CAEzB;AACb;;AAED,kBAAMT,IAAI,GAAG,KAAKA,IAAL,CAAUO,QAAV,CAAmBE,CAAnB,CAAb,CAT+E,CAU/E;;AACA,kBAAMO,QAAQ,oBAAGhB,IAAI,CAACiB,OAAR,8BAAG,cAAcC,KAAjB,qBAAG,cAAqBC,KAAtC,CAX+E,CAWlC;;AAC7C,kBAAMC,QAAQ,GAAGR,WAAW,CAACC,IAAZ,CAAiBQ,IAAlC;;AAEA,kBAAIL,QAAQ,KAAKI,QAAjB,EAA2B;AACvBZ,gBAAAA,WAAW,GAAG,KAAd;AACA;AACH;AACJ;;AAED,gBAAI,CAACF,YAAD,IAAiB,CAACE,WAAtB,EAAmC;AAC/B,mBAAKN,UAAL;AACH,aAFD,MAEO;AACH,mBAAKF,IAAL,CAAUO,QAAV,CAAmBe,OAAnB,CAA2B,CAACC,KAAD,EAAQC,KAAR,KAAkB;AACzC,qBAAKpB,OAAL,CAAaoB,KAAb,EAAoBC,MAApB,GAA6BpC,EAAE,CAACkC,KAAK,CAACG,QAAN,CAAeC,CAAhB,EAAmBJ,KAAK,CAACG,QAAN,CAAeE,CAAlC,CAA/B;AACH,eAFD;AAGH;AACJ;AACJ;;AAEMC,QAAAA,IAAI,CAACC,KAAD,EAAuB;AAC9B,cAAIvC,MAAJ,EAAY;AACR,gBAAIuC,KAAJ,EAAW;AACP,kBAAIC,WAAW,GAAG,CAAlB;;AACA,mBAAK,IAAItB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKL,OAAL,CAAaC,MAAjC,EAAyCI,CAAC,EAA1C,EAA8C;AAC1CsB,gBAAAA,WAAW,IAAI,KAAK3B,OAAL,CAAaK,CAAb,EAAgBuB,MAA/B;AACH;;AAED,kBAAMC,WAAW,GAAGvB,IAAI,CAACwB,MAAL,KAAgBH,WAApC;AACA,kBAAII,iBAAiB,GAAG,CAAxB;AACA,kBAAIC,aAAa,GAAG,CAApB;;AAEA,mBAAK,IAAI3B,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAG,KAAKL,OAAL,CAAaC,MAAjC,EAAyCI,EAAC,EAA1C,EAA8C;AAC1C0B,gBAAAA,iBAAiB,IAAI,KAAK/B,OAAL,CAAaK,EAAb,EAAgBuB,MAArC;;AACA,oBAAIC,WAAW,GAAGE,iBAAlB,EAAqC;AACjCC,kBAAAA,aAAa,GAAG3B,EAAhB;AACA;AACH;AACJ,eAhBM,CAkBP;;;AACA,mBAAK,IAAIA,GAAC,GAAG,CAAb,EAAgBA,GAAC,GAAG,KAAKT,IAAL,CAAUO,QAAV,CAAmBF,MAAvC,EAA+CI,GAAC,EAAhD,EAAoD;AAChD,qBAAKT,IAAL,CAAUO,QAAV,CAAmBE,GAAnB,EAAsB4B,MAAtB,GAAgC5B,GAAC,KAAK2B,aAAtC;AACH;AACJ,aAtBD,MAsBO;AACH,mBAAK,IAAI3B,GAAC,GAAG,CAAb,EAAgBA,GAAC,GAAG,KAAKT,IAAL,CAAUO,QAAV,CAAmBF,MAAvC,EAA+CI,GAAC,EAAhD,EAAoD;AAChD,qBAAKT,IAAL,CAAUO,QAAV,CAAmBE,GAAnB,EAAsB4B,MAAtB,GAA+B,IAA/B;AACH;AACJ;AACJ;AACJ;;AAESC,QAAAA,SAAS,GAAS;AACxB,eAAKtC,IAAL,CAAUC,iBAAV;AACH;;AAEOC,QAAAA,UAAU,GAAS;AACvB,eAAKE,OAAL,CAAakB,OAAb,CAAsBT,IAAD,IAAU;AAAA;;AAC3B,gBAAI,CAACA,IAAD,IAASA,IAAI,CAACA,IAAL,IAAa,IAA1B,EAAgC;AAC5B;AACH;;AAED,iBAAKb,IAAL,CAAUC,iBAAV;AACAjB,YAAAA,YAAY,CAACuD,OAAb,CAAqB;AAAClB,cAAAA,IAAI,gBAACR,IAAI,CAACA,IAAN,qBAAC,WAAWQ;AAAjB,aAArB,EAA6C,CAACmB,GAAD,EAAMC,MAAN,KAAwB;AACjE,kBAAID,GAAJ,EAAS;AACL1B,gBAAAA,OAAO,CAAC4B,KAAR,CAAc,yCAAd,EAAyDF,GAAzD;AACA;AACH;;AAED,kBAAIxC,IAAI,GAAGb,WAAW,CAACsD,MAAD,CAAtB;AACAzC,cAAAA,IAAI,CAAC2C,WAAL,CAAiB9B,IAAI,CAACY,MAAL,CAAYE,CAA7B,EAAgCd,IAAI,CAACY,MAAL,CAAYG,CAA5C,EAA+C,CAA/C;AACA,mBAAK5B,IAAL,CAAW4C,QAAX,CAAoB5C,IAApB;AACH,aATD;AAUH,WAhBD;AAiBH;;AAxGsC,O;;;;;iBAEP,E", "sourcesContent": ["import { _decorator, assetManager, CCInteger, Component, instantiate, Prefab, v2, Vec2 } from 'cc';\r\nimport { EDITOR } from 'cc/env';\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\n\r\n@ccclass('TerrainElem')\r\nexport class TerrainElem {\r\n    @property(CCInteger)\r\n    public weight: number = 0;\r\n    @property(Prefab)\r\n    public elem: Prefab | null = null;\r\n    @property({displayName: \"坐标偏移\"})\r\n    public offSet: Vec2 = new Vec2(0, 0);\r\n}\r\n\r\n@ccclass('RandTerrain')\r\n@executeInEditMode()\r\nexport class RandTerrain extends Component {\r\n    @property({type: [TerrainElem]})\r\n    public terrain: TerrainElem[] = [];\r\n\r\n    protected onLoad(): void {\r\n        if (EDITOR) {\r\n            this.node.removeAllChildren();\r\n            this._loadElems();\r\n        }\r\n    }\r\n\r\n    protected update(): void {\r\n        if (EDITOR) {\r\n            if (this.terrain === null || this.terrain.length == 0) {\r\n                return;\r\n            }\r\n\r\n            const isCountMatch = this.node.children.length === this.terrain.length;\r\n            let isUUIDMatch = true;\r\n\r\n            for (let i = 0; i < Math.min(this.node.children.length, this.terrain.length); i++) {\r\n                const terrainElem = this.terrain[i];\r\n                \r\n                // 增加空值检查\r\n                if (!terrainElem || !terrainElem.elem) {\r\n                    console.warn(`TerrainElem at index ${i} is invalid`);\r\n                    continue; // 跳过无效元素\r\n                }\r\n\r\n                const node = this.node.children[i];\r\n                // @ts-ignore\r\n                const nodeUUID = node._prefab?.asset?._uuid; // 使用可选链\r\n                const elemUUID = terrainElem.elem.uuid;\r\n\r\n                if (nodeUUID !== elemUUID) {\r\n                    isUUIDMatch = false;\r\n                    break;\r\n                }\r\n            }\r\n\r\n            if (!isCountMatch || !isUUIDMatch) {\r\n                this._loadElems();\r\n            } else {\r\n                this.node.children.forEach((child, index) => {\r\n                    this.terrain[index].offSet = v2(child.position.x, child.position.y);\r\n                });\r\n            }\r\n        }\r\n    }\r\n\r\n    public play(bPlay: boolean): void {\r\n        if (EDITOR) {\r\n            if (bPlay) {\r\n                let totalWeight = 0;\r\n                for (let i = 0; i < this.terrain.length; i++) {\r\n                    totalWeight += this.terrain[i].weight;\r\n                }\r\n                \r\n                const randomValue = Math.random() * totalWeight;\r\n                let accumulatedWeight = 0;\r\n                let selectedIndex = 0;\r\n                \r\n                for (let i = 0; i < this.terrain.length; i++) {\r\n                    accumulatedWeight += this.terrain[i].weight;\r\n                    if (randomValue < accumulatedWeight) {\r\n                        selectedIndex = i;\r\n                        break;\r\n                    }\r\n                }\r\n\r\n                // 设置所有节点的active状态\r\n                for (let i = 0; i < this.node.children.length; i++) {\r\n                    this.node.children[i].active = (i === selectedIndex);\r\n                }\r\n            } else {\r\n                for (let i = 0; i < this.node.children.length; i++) {\r\n                    this.node.children[i].active = true;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    protected onDestroy(): void {\r\n        this.node.removeAllChildren();\r\n    }\r\n\r\n    private _loadElems(): void {\r\n        this.terrain.forEach((elem) => {\r\n            if (!elem || elem.elem == null) {\r\n                return;\r\n            }\r\n\r\n            this.node.removeAllChildren();\r\n            assetManager.loadAny({uuid:elem.elem?.uuid}, (err, prefab:Prefab) => { \r\n                if (err) {\r\n                    console.error(\"RandTerrain load TerrainElem prefab err\", err);\r\n                    return;\r\n                }\r\n\r\n                var node = instantiate(prefab);\r\n                node.setPosition(elem.offSet.x, elem.offSet.y, 0);\r\n                this.node!.addChild(node);\r\n            });\r\n        })\r\n    }\r\n}\r\n\r\n"]}