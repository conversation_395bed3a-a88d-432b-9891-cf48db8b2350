{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/BossManager.ts"], "names": ["BossManager", "instantiate", "Prefab", "MyApp", "SingletonBase", "GameResourceList", "EnemyData", "GameIns", "BattleLayer", "BossPlane", "Tools", "constructor", "_bossArr", "_pfBoss", "preLoad", "battleManager", "addLoadCount", "resMgr", "load", "PrefabBoss", "error", "prefab", "checkLoadFinish", "mainReset", "subReset", "boss", "node", "parent", "setTimeout", "destroy", "addBoss", "bossId", "planeData", "planeId", "Error", "name", "me", "addEnemy", "getComponent", "initPlane", "push", "remove<PERSON>oss", "arr<PERSON><PERSON><PERSON>", "updateGameLogic", "deltaTime", "i", "length", "removeAble", "bossFightStart", "isDead", "startBattle", "bosses", "isBossOver", "isBossDead", "setAnimSpeed", "speed"], "mappings": ";;;+LAWa<PERSON>,W;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAXJC,MAAAA,W,OAAAA,W;AAAmBC,MAAAA,M,OAAAA,M;;AACnBC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,a,iBAAAA,a;;AACFC,MAAAA,gB;;AACEC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,O,iBAAAA,O;;AACFC,MAAAA,W;;AACAC,MAAAA,S;;AACEC,MAAAA,K,iBAAAA,K;;;;;;;;;6BAGIV,W,GAAN,MAAMA,WAAN;AAAA;AAAA,0CAAqD;AAIxDW,QAAAA,WAAW,GAAG;AACV;AADU,eAHdC,QAGc,GAHU,EAGV;AAAA,eAFdC,OAEc,GAFW,IAEX;AAEb;;AAEKC,QAAAA,OAAO,GAAG;AAAA;;AAAA;AACZ;AAAA;AAAA,oCAAQC,aAAR,CAAsBC,YAAtB,CAAmC,CAAnC;AACA;AAAA;AAAA,gCAAMC,MAAN,CAAaC,IAAb,CAAkB;AAAA;AAAA,sDAAiBC,UAAnC,EAA+CjB,MAA/C,EAAuD,CAACkB,KAAD,EAAaC,MAAb,KAAgC;AACnF,cAAA,KAAI,CAACR,OAAL,GAAeQ,MAAf;AACA;AAAA;AAAA,sCAAQN,aAAR,CAAsBO,eAAtB;AACH,aAHD;AAFY;AAMf;;AAGDC,QAAAA,SAAS,GAAG;AACR,eAAKC,QAAL;AACH;AAED;AACJ;AACA;;;AACIA,QAAAA,QAAQ,GAAG;AAAA,2CAC2B;AAC9BC,YAAAA,IAAI,CAACC,IAAL,CAAUC,MAAV,GAAmB,IAAnB;AACAC,YAAAA,UAAU,CAAC,MAAM;AACbH,cAAAA,IAAI,CAACC,IAAL,CAAUG,OAAV;AACH,aAFS,EAEP,IAFO,CAAV;AAGH,WANM;;AACP,eAAK,IAAMJ,IAAX,IAAmB,KAAKb,QAAxB;AAAA;AAAA;;AAMA,eAAKA,QAAL,GAAgB,EAAhB;AACH;AAGD;AACJ;AACA;AACA;AACA;;;AACIkB,QAAAA,OAAO,CAACC,MAAD,EAAmC;AACtC,cAAMC,SAAS,GAAG;AAAA;AAAA,uCAAlB;AACAA,UAAAA,SAAS,CAACC,OAAV,GAAoBF,MAApB;;AAEA,cAAI,CAAC,KAAKlB,OAAV,EAAmB;AACf,kBAAM,IAAIqB,KAAJ,CAAU,uDAAV,CAAN;AACH;;AACD,cAAMR,IAAU,GAAGzB,WAAW,CAAC,KAAKY,OAAN,CAA9B;AACAa,UAAAA,IAAI,CAACS,IAAL,GAAY,MAAZ;AACA;AAAA;AAAA,0CAAYC,EAAZ,CAAeC,QAAf,CAAwBX,IAAxB;AAEA,cAAMD,IAAI,GAAGC,IAAI,CAACY,YAAL;AAAA;AAAA,qCAAb;AACAb,UAAAA,IAAI,CAACc,SAAL,CAAeP,SAAf;;AACA,eAAKpB,QAAL,CAAc4B,IAAd,CAAmBf,IAAnB;;AAEA,iBAAOA,IAAP;AACH;AAGD;AACJ;AACA;AACA;;;AACIgB,QAAAA,UAAU,CAAChB,IAAD,EAAkB;AACxB;AAAA;AAAA,8BAAMiB,SAAN,CAAgB,KAAK9B,QAArB,EAA+Ba,IAA/B;AACAA,UAAAA,IAAI,CAACC,IAAL,CAAUC,MAAV,GAAmB,IAAnB;AACAF,UAAAA,IAAI,CAACC,IAAL,CAAUG,OAAV;AACH;AAED;AACJ;AACA;AACA;;;AACIc,QAAAA,eAAe,CAACC,SAAD,EAAoB;AAC/B,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKjC,QAAL,CAAckC,MAAlC,EAA0CD,CAAC,EAA3C,EAA+C;AAC3C,gBAAMpB,IAAI,GAAG,KAAKb,QAAL,CAAciC,CAAd,CAAb;;AACA,gBAAIpB,IAAI,CAACsB,UAAT,EAAqB;AACjB,mBAAKN,UAAL,CAAgBhB,IAAhB;AACAoB,cAAAA,CAAC;AACJ,aAHD,MAGO;AACHpB,cAAAA,IAAI,CAACkB,eAAL,CAAqBC,SAArB;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACII,QAAAA,cAAc,GAAG;AACb,eAAK,IAAMvB,IAAX,IAAmB,KAAKb,QAAxB,EAAkC;AAC9B,gBAAI,CAACa,IAAI,CAACwB,MAAV,EAAkB;AACdxB,cAAAA,IAAI,CAACyB,WAAL;AACA;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACc,YAANC,MAAM,GAAgB;AACtB,iBAAO,KAAKvC,QAAZ;AACH;AAED;AACJ;AACA;;;AACIwC,QAAAA,UAAU,GAAY;AAClB,iBAAO,KAAKxC,QAAL,CAAckC,MAAd,KAAyB,CAAhC;AACH;AAED;AACJ;AACA;;;AACIO,QAAAA,UAAU,GAAY;AAClB,eAAK,IAAM5B,IAAX,IAAmB,KAAKb,QAAxB,EAAkC;AAC9B,gBAAI,CAACa,IAAI,CAACwB,MAAV,EAAkB;AACd,qBAAO,KAAP;AACH;AACJ;;AACD,iBAAO,IAAP;AACH;;AAEDK,QAAAA,YAAY,CAACC,KAAD,EAAgB;AACxB,eAAK,IAAM9B,IAAX,IAAmB,KAAKb,QAAxB,EAAkC;AAC9Ba,YAAAA,IAAI,CAAC6B,YAAL,CAAkBC,KAAlB;AACH;AACJ;;AA/HuD,O", "sourcesContent": ["import { instantiate, Node, Prefab } from \"cc\";\r\nimport { MyApp } from 'db://assets/bundles/common/script/app/MyApp';\r\nimport { SingletonBase } from \"../../../../../scripts/core/base/SingletonBase\";\r\nimport GameResourceList from \"../const/GameResourceList\";\r\nimport { EnemyData } from \"../data/EnemyData\";\r\nimport { GameIns } from \"../GameIns\";\r\nimport BattleLayer from \"../ui/layer/BattleLayer\";\r\nimport BossPlane from \"../ui/plane/boss/BossPlane\";\r\nimport { Tools } from \"../utils/Tools\";\r\n\r\n\r\nexport class BossManager extends SingletonBase<BossManager> {\r\n    _bossArr: BossPlane[] = [];\r\n    _pfBoss: Prefab | null = null;\r\n\r\n    constructor() {\r\n        super();\r\n    }\r\n\r\n    async preLoad() {\r\n        GameIns.battleManager.addLoadCount(1);\r\n        MyApp.resMgr.load(GameResourceList.PrefabBoss, Prefab, (error: any, prefab: Prefab) => {\r\n            this._pfBoss = prefab;\r\n            GameIns.battleManager.checkLoadFinish();\r\n        });\r\n    }\r\n\r\n\r\n    mainReset() {\r\n        this.subReset();\r\n    }\r\n\r\n    /**\r\n     * 重置子关卡\r\n     */\r\n    subReset() {\r\n        for (const boss of this._bossArr) {\r\n            boss.node.parent = null;\r\n            setTimeout(() => {\r\n                boss.node.destroy();\r\n            }, 1000);\r\n        }\r\n        this._bossArr = [];\r\n    }\r\n\r\n\r\n    /**\r\n * 添加 Boss\r\n * @param bossType Boss 类型\r\n * @param bossId Boss ID\r\n */\r\n    addBoss(bossId: number): BossPlane | null {\r\n        const planeData = new EnemyData();\r\n        planeData.planeId = bossId;\r\n\r\n        if (!this._pfBoss) {\r\n            throw new Error(\"Boss prefab is not initialized. Call preLoad() first.\");\r\n        }\r\n        const node: Node = instantiate(this._pfBoss);\r\n        node.name = \"boss\";\r\n        BattleLayer.me.addEnemy(node);\r\n\r\n        const boss = node.getComponent(BossPlane)!;\r\n        boss.initPlane(planeData);\r\n        this._bossArr.push(boss);\r\n\r\n        return boss;\r\n    }\r\n\r\n\r\n    /**\r\n     * 移除 Boss\r\n     * @param boss 要移除的 Boss\r\n     */\r\n    removeBoss(boss: BossPlane) {\r\n        Tools.arrRemove(this._bossArr, boss);\r\n        boss.node.parent = null;\r\n        boss.node.destroy();\r\n    }\r\n\r\n    /**\r\n     * 更新游戏逻辑\r\n     * @param deltaTime 每帧时间\r\n     */\r\n    updateGameLogic(deltaTime: number) {\r\n        for (let i = 0; i < this._bossArr.length; i++) {\r\n            const boss = this._bossArr[i];\r\n            if (boss.removeAble) {\r\n                this.removeBoss(boss);\r\n                i--;\r\n            } else {\r\n                boss.updateGameLogic(deltaTime);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 开始 Boss 战斗\r\n     */\r\n    bossFightStart() {\r\n        for (const boss of this._bossArr) {\r\n            if (!boss.isDead) {\r\n                boss.startBattle();\r\n                break;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 获取所有 Boss\r\n     */\r\n    get bosses(): BossPlane[] {\r\n        return this._bossArr;\r\n    }\r\n\r\n    /**\r\n     * 检查是否所有 Boss 已结束\r\n     */\r\n    isBossOver(): boolean {\r\n        return this._bossArr.length === 0;\r\n    }\r\n\r\n    /**\r\n     * 检查是否所有 Boss 已死亡\r\n     */\r\n    isBossDead(): boolean {\r\n        for (const boss of this._bossArr) {\r\n            if (!boss.isDead) {\r\n                return false;\r\n            }\r\n        }\r\n        return true;\r\n    }\r\n\r\n    setAnimSpeed(speed: number) {\r\n        for (const boss of this._bossArr) {\r\n            boss.setAnimSpeed(speed);\r\n        }\r\n    }\r\n}"]}