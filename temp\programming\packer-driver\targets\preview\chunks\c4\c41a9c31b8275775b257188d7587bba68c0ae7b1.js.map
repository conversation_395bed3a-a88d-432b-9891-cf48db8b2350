{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/level/LevelEditorLayerUI.ts"], "names": ["_decorator", "Component", "Node", "assetManager", "instantiate", "Vec2", "UITransform", "v2", "LayerSplicingMode", "LevelDataEvent", "LevelEditorUtils", "LevelScrollLayerUI", "LevelEditorEventUI", "ccclass", "property", "executeInEditMode", "TerrainsNodeName", "ScrollsNodeName", "DynamicNodeName", "WaveNodeName", "EventNodeName", "LevelEditorLayerUI", "terrainsNode", "scrollsNode", "dynamicNode", "wavesNode", "eventsNode", "_loadScrollNode", "onLoad", "getOrAddNode", "node", "initByLevelData", "data", "console", "log", "terrains", "for<PERSON>ach", "terrain", "loadAny", "uuid", "err", "prefab", "error", "terrainNode", "setPosition", "position", "x", "y", "setScale", "scale", "setRotationFromEuler", "rotation", "<PERSON><PERSON><PERSON><PERSON>", "dynamics", "dynamic", "index", "dynaNode", "events", "event", "eventUIComp", "addComponent", "initScorllsByLevelData", "layerData", "loadPromises", "scrollLayers", "scrolls", "scroll", "weight", "uuids", "splicingMode", "splicingOffsetX", "min", "offSetX", "max", "splicingOffsetY", "offSetY", "scrollPrefabs", "loadPromise", "Promise", "resolve", "push", "length", "all", "totalHeight", "speed", "totalTime", "posOffsetY", "height", "prefabIndex", "curPrefab", "child", "randomOffsetX", "Math", "random", "offY", "node_height", "getComponent", "contentSize", "fix_height", "random_height", "fillLevelData", "children", "_prefab", "asset", "_uuid", "z", "eventNode", "tick", "progress"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAcC,MAAAA,Y,OAAAA,Y;AAAcC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,E,OAAAA,E;;AAGnFC,MAAAA,iB,iBAAAA,iB;AAAmBC,MAAAA,c,iBAAAA,c;;AAEnBC,MAAAA,gB,iBAAAA,gB;AAA8BC,MAAAA,kB,iBAAAA,kB;;AAE9BC,MAAAA,kB,iBAAAA,kB;;;;;;;;;OANH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2Cf,U;AAQ3CgB,MAAAA,gB,GAAmB,U;AACnBC,MAAAA,e,GAAkB,S;AAClBC,MAAAA,e,GAAkB,S;AAClBC,MAAAA,Y,GAAe,O;AACfC,MAAAA,a,GAAgB,Q;;oCAITC,kB,WAFZR,OAAO,CAAC,oBAAD,C,UACPE,iBAAiB,E,+BADlB,MAEaM,kBAFb,SAEwCpB,SAFxC,CAEkD;AAAA;AAAA;AAAA,eACvCqB,YADuC,GACb,IADa;AAAA,eAEvCC,WAFuC,GAEd,IAFc;AAAA,eAGvCC,WAHuC,GAGd,IAHc;AAAA,eAIvCC,SAJuC,GAIhB,IAJgB;AAAA,eAKvCC,UALuC,GAKf,IALe;AAAA,eAOtCC,eAPsC,GAOX,KAPW;AAAA;;AAS9CC,QAAAA,MAAM,GAAS;AACX,eAAKN,YAAL,GAAoB;AAAA;AAAA,oDAAiBO,YAAjB,CAA8B,KAAKC,IAAnC,EAAyCd,gBAAzC,CAApB;AACA,eAAKO,WAAL,GAAmB;AAAA;AAAA,oDAAiBM,YAAjB,CAA8B,KAAKC,IAAnC,EAAyCb,eAAzC,CAAnB;AACA,eAAKO,WAAL,GAAmB;AAAA;AAAA,oDAAiBK,YAAjB,CAA8B,KAAKC,IAAnC,EAAyCZ,eAAzC,CAAnB;AACA,eAAKO,SAAL,GAAiB;AAAA;AAAA,oDAAiBI,YAAjB,CAA8B,KAAKC,IAAnC,EAAyCX,YAAzC,CAAjB;AACA,eAAKO,UAAL,GAAkB;AAAA;AAAA,oDAAiBG,YAAjB,CAA8B,KAAKC,IAAnC,EAAyCV,aAAzC,CAAlB;AACH;;AAEMW,QAAAA,eAAe,CAACC,IAAD,EAA4B;AAAA;;AAC9CC,UAAAA,OAAO,CAACC,GAAR,CAAY,oCAAZ;;AACA,cAAI,CAACF,IAAL,EAAW;AACP;AACH;;AAED,cAAI,KAAKV,YAAL,KAAsB,IAA1B,EAAgC;AAC5B;AACH;;AACD,4BAAAU,IAAI,CAACG,QAAL,4BAAeC,OAAf,CAAwBC,OAAD,IAAa;AAChClC,YAAAA,YAAY,CAACmC,OAAb,CAAqB;AAACC,cAAAA,IAAI,EAACF,OAAO,CAACE;AAAd,aAArB,EAA0C,CAACC,GAAD,EAAaC,MAAb,KAA+B;AACrE,kBAAID,GAAJ,EAAS;AACLP,gBAAAA,OAAO,CAACS,KAAR,CAAc,4DAAd,EAA4EF,GAA5E;AACA;AACH;;AACD,kBAAIG,WAAW,GAAGvC,WAAW,CAACqC,MAAD,CAA7B;AACAE,cAAAA,WAAW,CAACC,WAAZ,CAAwBP,OAAO,CAACQ,QAAR,CAAiBC,CAAzC,EAA4CT,OAAO,CAACQ,QAAR,CAAiBE,CAA7D,EAAgE,CAAhE;AACAJ,cAAAA,WAAW,CAACK,QAAZ,CAAqBX,OAAO,CAACY,KAAR,CAAcH,CAAnC,EAAsCT,OAAO,CAACY,KAAR,CAAcF,CAApD,EAAuD,CAAvD;AACAJ,cAAAA,WAAW,CAACO,oBAAZ,CAAiC,CAAjC,EAAoC,CAApC,EAAuCb,OAAO,CAACc,QAA/C;AACA,mBAAK7B,YAAL,CAAmB8B,QAAnB,CAA4BT,WAA5B;AACH,aAVD;AAWH,WAZD;AAcA,4BAAAX,IAAI,CAACqB,QAAL,4BAAejB,OAAf,CAAuB,CAACkB,OAAD,EAAUC,KAAV,KAAoB;AACvC,gBAAIC,QAAQ,GAAG;AAAA;AAAA,sDAAiB3B,YAAjB,CAA8B,KAAKL,WAAnC,EAAiD,UAAQ+B,KAAzD,CAAf;AACAC,YAAAA,QAAQ,CAACZ,WAAT,CAAqBU,OAAO,CAACT,QAAR,CAAiBC,CAAtC,EAAyCQ,OAAO,CAACT,QAAR,CAAiBE,CAA1D,EAA6D,CAA7D;AACAS,YAAAA,QAAQ,CAACR,QAAT,CAAkBM,OAAO,CAACL,KAAR,CAAcH,CAAhC,EAAmCQ,OAAO,CAACL,KAAR,CAAcF,CAAjD,EAAoD,CAApD;AACAS,YAAAA,QAAQ,CAACN,oBAAT,CAA8B,CAA9B,EAAiC,CAAjC,EAAoCI,OAAO,CAACH,QAA5C;AACAG,YAAAA,OAAO,CAACnB,QAAR,CAAiBC,OAAjB,CAA0BC,OAAD,IAAW;AAChClC,cAAAA,YAAY,CAACmC,OAAb,CAAqB;AAACC,gBAAAA,IAAI,EAACF,OAAO,CAACE;AAAd,eAArB,EAA0C,CAACC,GAAD,EAAMC,MAAN,KAAwB;AAC9D,oBAAID,GAAJ,EAAS;AACLP,kBAAAA,OAAO,CAACS,KAAR,CAAc,4DAAd,EAA4EF,GAA5E;AACA;AACH;;AACD,oBAAIhB,WAAW,GAAGpB,WAAW,CAACqC,MAAD,CAA7B;AACAe,gBAAAA,QAAQ,CAAEJ,QAAV,CAAmB5B,WAAnB;AACH,eAPD;AAQH,aATD;AAUH,WAfD,EAvB8C,CAwC9C;AACA;AACA;AACA;AACA;AACA;;AACA,0BAAAQ,IAAI,CAACyB,MAAL,0BAAarB,OAAb,CAAsBsB,KAAD,IAAS;AAC1B,gBAAI5B,IAAI,GAAG,IAAI5B,IAAJ,EAAX;AACA,gBAAIyD,WAAW,GAAG7B,IAAI,CAAC8B,YAAL;AAAA;AAAA,yDAAlB;AACAD,YAAAA,WAAW,CAAC5B,eAAZ,CAA4B2B,KAA5B;AACA,iBAAKhC,UAAL,CAAiB0B,QAAjB,CAA0BtB,IAA1B;AACH,WALD;AAMH;;AAEY+B,QAAAA,sBAAsB,CAACC,SAAD,EAAwB9B,IAAxB,EAA4D;AAAA;;AAAA;AAAA;;AAC3F,gBAAI,CAACA,IAAD,IAAS,KAAI,CAACT,WAAL,KAAqB,IAA9B,IAAsC,KAAI,CAACI,eAA/C,EAAgE;AAC5D;AACH;;AAED,gBAAMoC,YAA6B,GAAG,EAAtC;AACAD,YAAAA,SAAS,CAACE,YAAV,GAAyB,EAAzB;AACA,YAAA,KAAI,CAACrC,eAAL,GAAuB,IAAvB;AACA,6BAAAK,IAAI,CAACiC,OAAL,2BAAc7B,OAAd,CAAuB8B,MAAD,IAAY;AAC9B,kBAAMF,YAAY,GAAG;AAAA;AAAA,6DAArB;AACAA,cAAAA,YAAY,CAACG,MAAb,GAAsBD,MAAM,CAACC,MAA7B;AACA,kBAAMC,KAAK,GAAGF,MAAM,CAACE,KAAP,IAAgB,EAA9B;AACAJ,cAAAA,YAAY,CAACK,YAAb,GAA4BH,MAAM,CAACG,YAAnC;AACAL,cAAAA,YAAY,CAACM,eAAb,CAA6BC,GAA7B,GAAmCL,MAAM,CAACM,OAAP,CAAeD,GAAlD;AACAP,cAAAA,YAAY,CAACM,eAAb,CAA6BG,GAA7B,GAAmCP,MAAM,CAACM,OAAP,CAAeC,GAAlD;AACAT,cAAAA,YAAY,CAACU,eAAb,CAA6BH,GAA7B,GAAmCL,MAAM,CAACS,OAAP,CAAeJ,GAAlD;AACAP,cAAAA,YAAY,CAACU,eAAb,CAA6BD,GAA7B,GAAmCP,MAAM,CAACS,OAAP,CAAeF,GAAlD;AACAT,cAAAA,YAAY,CAACY,aAAb,GAA6B,EAA7B;AACAR,cAAAA,KAAK,CAAChC,OAAN,CAAeG,IAAD,IAAU;AACpB,oBAAMsC,WAAW,GAAG,IAAIC,OAAJ,CAAmBC,OAAD,IAAa;AAC/C5E,kBAAAA,YAAY,CAACmC,OAAb,CAAqB;AAACC,oBAAAA,IAAI,EAAEA;AAAP,mBAArB,EAAmC,CAACC,GAAD,EAAMC,MAAN,KAAwB;AACvD,wBAAID,GAAJ,EAAS;AACLuC,sBAAAA,OAAO;AACP;AACH;;AACDf,oBAAAA,YAAY,CAACY,aAAb,CAA2BI,IAA3B,CAAgCvC,MAAhC;AAEAR,oBAAAA,OAAO,CAACC,GAAR,CAAY,wDAAZ,EAAsE4B,SAAS,CAACE,YAAV,CAAuBiB,MAA7F;AACAF,oBAAAA,OAAO;AACV,mBATD;AAUH,iBAXmB,CAApB;AAYAhB,gBAAAA,YAAY,CAACiB,IAAb,CAAkBH,WAAlB;AACH,eAdD;AAeAf,cAAAA,SAAS,CAACE,YAAV,CAAuBgB,IAAvB,CAA4BhB,YAA5B;AACH,aA1BD;AA4BA,kBAAMc,OAAO,CAACI,GAAR,CAAYnB,YAAZ,CAAN;AACA,YAAA,KAAI,CAACpC,eAAL,GAAuB,KAAvB;AACAM,YAAAA,OAAO,CAACC,GAAR,CAAY,sDAAZ,EAAoE4B,SAAS,CAACE,YAAV,CAAuBiB,MAA3F;AACAnB,YAAAA,SAAS,CAACE,YAAV,CAAuB5B,OAAvB,CAA+B,CAAC8B,MAAD,EAASX,KAAT,KAAmB;AAC9C,kBAAMhC,WAAW,GAAG;AAAA;AAAA,wDAAiBM,YAAjB,CAA8B,KAAI,CAACN,WAAnC,cAA2DgC,KAA3D,CAApB;AACA,kBAAI4B,WAAW,GAAGnD,IAAI,CAACoD,KAAL,GAAapD,IAAI,CAACqD,SAApC;AACA,kBAAIC,UAAU,GAAG,CAAjB;AACA,kBAAIC,MAAM,GAAG,CAAb;AACA,kBAAIC,WAAW,GAAG,CAAlB,CAL8C,CAKzB;;AACrB,qBAAOD,MAAM,GAAGJ,WAAhB,EAA6B;AACzB;AACA,oBAAMM,SAAS,GAAGvB,MAAM,CAACU,aAAP,CAAqBY,WAArB,CAAlB;AACA,oBAAME,KAAK,GAAGtF,WAAW,CAACqF,SAAD,CAAzB;AACA,oBAAME,aAAa,GAAGC,IAAI,CAACC,MAAL,MAAiB3B,MAAM,CAACI,eAAP,CAAuBG,GAAvB,GAA6BP,MAAM,CAACI,eAAP,CAAuBC,GAArE,IAA4EL,MAAM,CAACI,eAAP,CAAuBC,GAAzH;AACAmB,gBAAAA,KAAK,CAAC9C,WAAN,CAAkB+C,aAAlB,EAAiCL,UAAjC,EAA6C,CAA7C;AACA,oBAAIQ,IAAI,GAAG,CAAX;;AACA,oBAAI5B,MAAM,CAACG,YAAP,KAAwB;AAAA;AAAA,4DAAkB0B,WAA9C,EAA2D;AACvDD,kBAAAA,IAAI,GAAGJ,KAAK,CAACM,YAAN,CAAmB1F,WAAnB,EAAiC2F,WAAjC,CAA6CV,MAApD;AACH,iBAFD,MAEO,IAAIrB,MAAM,CAACG,YAAP,KAAwB;AAAA;AAAA,4DAAkB6B,UAA9C,EAA0D;AAC7DJ,kBAAAA,IAAI,GAAG,IAAP;AACH,iBAFM,MAEA,IAAI5B,MAAM,CAACG,YAAP,KAAwB;AAAA;AAAA,4DAAkB8B,aAA9C,EAA6D;AAChEL,kBAAAA,IAAI,GAAGF,IAAI,CAACnB,GAAL,CAASP,MAAM,CAACQ,eAAP,CAAuBH,GAAhC,EAAoCL,MAAM,CAACQ,eAAP,CAAuBD,GAA3D,IAAkEiB,KAAK,CAACM,YAAN,CAAmB1F,WAAnB,EAAiC2F,WAAjC,CAA6CV,MAAtH;AACH;;AACDhE,gBAAAA,WAAW,CAAC6B,QAAZ,CAAqBsC,KAArB;AACAJ,gBAAAA,UAAU,IAAIQ,IAAd;AACAP,gBAAAA,MAAM,IAAIO,IAAV;AACAN,gBAAAA,WAAW,GAAG,CAACA,WAAW,GAAG,CAAf,IAAoBtB,MAAM,CAACU,aAAP,CAAqBK,MAAvD;AACH;AACJ,aAzBD;AAvC2F;AAiE9F;;AAEMmB,QAAAA,aAAa,CAACpE,IAAD,EAA4B;AAC5CA,UAAAA,IAAI,CAACG,QAAL,GAAgB,EAAhB;AACA,eAAKb,YAAL,CAAmB+E,QAAnB,CAA4BjE,OAA5B,CAAqCO,WAAD,IAAiB;AACjDX,YAAAA,IAAI,CAACG,QAAL,CAAc6C,IAAd,CAAmB;AACf;AACAzC,cAAAA,IAAI,EAAEI,WAAW,CAAC2D,OAAZ,CAAoBC,KAApB,CAA0BC,KAFjB;AAGf3D,cAAAA,QAAQ,EAAE,IAAIxC,IAAJ,CAASsC,WAAW,CAACE,QAAZ,CAAqBC,CAA9B,EAAiCH,WAAW,CAACE,QAAZ,CAAqBE,CAAtD,CAHK;AAIfE,cAAAA,KAAK,EAAE,IAAI5C,IAAJ,CAASsC,WAAW,CAACM,KAAZ,CAAkBH,CAA3B,EAA8BH,WAAW,CAACM,KAAZ,CAAkBF,CAAhD,CAJQ;AAKfI,cAAAA,QAAQ,EAAER,WAAW,CAACQ,QAAZ,CAAqBsD;AALhB,aAAnB;AAOH,WARD,EAF4C,CAY5C;;AACA,eAAKjF,WAAL,CAAkB6E,QAAlB,CAA2BjE,OAA3B,CAAmC,CAACZ,WAAD,EAAc+B,KAAd,KAAwB;AACvDvB,YAAAA,IAAI,CAACqB,QAAL,CAAcE,KAAd,EAAqBV,QAArB,GAAgCtC,EAAE,CAACiB,WAAW,CAACqB,QAAZ,CAAqBC,CAAtB,EAAyBtB,WAAW,CAACqB,QAAZ,CAAqBE,CAA9C,CAAlC;AACAf,YAAAA,IAAI,CAACqB,QAAL,CAAcE,KAAd,EAAqBN,KAArB,GAA6B1C,EAAE,CAACiB,WAAW,CAACyB,KAAZ,CAAkBH,CAAnB,EAAsBtB,WAAW,CAACyB,KAAZ,CAAkBF,CAAxC,CAA/B;AACAf,YAAAA,IAAI,CAACqB,QAAL,CAAcE,KAAd,EAAqBJ,QAArB,GAAgC3B,WAAW,CAAC2B,QAAZ,CAAqBsD,CAArD;AACH,WAJD,EAb4C,CAmB5C;AACA;AACA;AACA;AACA;AACA;AACA;;AACAzE,UAAAA,IAAI,CAACyB,MAAL,GAAc,EAAd;AACA,eAAK/B,UAAL,CAAiB2E,QAAjB,CAA0BjE,OAA1B,CAAmCsE,SAAD,IAAe;AAC7C,gBAAIhD,KAAK,GAAG;AAAA;AAAA,mDAAZ;AACA,gBAAIC,WAAW,GAAG+C,SAAS,CAACV,YAAV;AAAA;AAAA,yDAAlB;AACArC,YAAAA,WAAW,CAAEyC,aAAb,CAA2B1C,KAA3B;AACA1B,YAAAA,IAAI,CAACyB,MAAL,CAAYuB,IAAZ,CAAiBtB,KAAjB;AACH,WALD;AAMH;;AAEMiD,QAAAA,IAAI,CAACC,QAAD,EAAmBvB,SAAnB,EAAqCD,KAArC,EAAwD;AAC/D,eAAKtD,IAAL,CAAUc,WAAV,CAAsB,CAAtB,EAAyB,CAAEgE,QAAF,GAAavB,SAAb,GAAyBD,KAAlD,EAAyD,CAAzD;AACH;;AA/K6C,O", "sourcesContent": ["import { _decorator, Component, Node, Prefab, assetManager, instantiate, Vec2, UITransform, v2 } from 'cc';\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\n\r\nimport { LayerSplicingMode, LevelDataEvent, LevelDataLayer, LevelDataWave } from 'db://assets/bundles/common/script/leveldata/leveldata';\r\n\r\nimport { LevelEditorUtils, LevelLayer, LevelScrollLayerUI } from './utils';\r\nimport { LevelEditorWaveUI } from './LevelEditorWaveUI';\r\nimport { LevelEditorEventUI } from './LevelEditorEventUI';\r\n\r\nconst TerrainsNodeName = \"terrains\";\r\nconst ScrollsNodeName = \"scrolls\";\r\nconst DynamicNodeName = \"dynamic\";\r\nconst WaveNodeName = \"waves\";\r\nconst EventNodeName = \"events\"\r\n\r\n@ccclass('LevelEditorLayerUI')\r\n@executeInEditMode()\r\nexport class LevelEditorLayerUI extends Component {\r\n    public terrainsNode: Node|null = null;\r\n    public scrollsNode: Node|null = null;\r\n    public dynamicNode: Node|null = null;\r\n    public wavesNode: Node|null = null;\r\n    public eventsNode: Node|null = null;\r\n\r\n    private _loadScrollNode: boolean = false;\r\n\r\n    onLoad(): void {\r\n        this.terrainsNode = LevelEditorUtils.getOrAddNode(this.node, TerrainsNodeName);\r\n        this.scrollsNode = LevelEditorUtils.getOrAddNode(this.node, ScrollsNodeName);\r\n        this.dynamicNode = LevelEditorUtils.getOrAddNode(this.node, DynamicNodeName);\r\n        this.wavesNode = LevelEditorUtils.getOrAddNode(this.node, WaveNodeName);\r\n        this.eventsNode = LevelEditorUtils.getOrAddNode(this.node, EventNodeName);\r\n    }\r\n\r\n    public initByLevelData(data: LevelDataLayer):void {\r\n        console.log(\"LevelEditorLayerUI initByLevelData\");\r\n        if (!data) {\r\n            return;\r\n        }\r\n\r\n        if (this.terrainsNode === null) {\r\n            return;\r\n        }\r\n        data.terrains?.forEach((terrain) => {\r\n            assetManager.loadAny({uuid:terrain.uuid}, (err: Error, prefab:Prefab) => {\r\n                if (err) {\r\n                    console.error(\"LevelEditorLayerUI initByLevelData load terrain prefab err\", err);\r\n                    return\r\n                } \r\n                var terrainNode = instantiate(prefab);\r\n                terrainNode.setPosition(terrain.position.x, terrain.position.y, 0);\r\n                terrainNode.setScale(terrain.scale.x, terrain.scale.y, 1);\r\n                terrainNode.setRotationFromEuler(0, 0, terrain.rotation);\r\n                this.terrainsNode!.addChild(terrainNode);                \r\n            });\r\n        });\r\n\r\n        data.dynamics?.forEach((dynamic, index) => {\r\n            var dynaNode = LevelEditorUtils.getOrAddNode(this.dynamicNode!, 'dyna_'+index);\r\n            dynaNode.setPosition(dynamic.position.x, dynamic.position.y, 0);\r\n            dynaNode.setScale(dynamic.scale.x, dynamic.scale.y, 1);\r\n            dynaNode.setRotationFromEuler(0, 0, dynamic.rotation);\r\n            dynamic.terrains.forEach((terrain)=>{\r\n                assetManager.loadAny({uuid:terrain.uuid}, (err, prefab:Prefab) => {\r\n                    if (err) {\r\n                        console.error(\"LevelEditorLayerUI initByLevelData load dynamic prefab err\", err);\r\n                        return\r\n                    } \r\n                    var dynamicNode = instantiate(prefab);\r\n                    dynaNode!.addChild(dynamicNode);                \r\n                });\r\n            });\r\n        });\r\n\r\n        // data.waves?.forEach((wave)=>{\r\n        //     var node = new Node();\r\n        //     var waveUIComp = node.addComponent(LevelEditorWaveUI);\r\n        //     waveUIComp.initByLevelData(wave);\r\n        //     this.wavesNode!.addChild(node);\r\n        // })\r\n        data.events?.forEach((event)=>{\r\n            var node = new Node();\r\n            var eventUIComp = node.addComponent(LevelEditorEventUI);\r\n            eventUIComp.initByLevelData(event);\r\n            this.eventsNode!.addChild(node);\r\n        })\r\n    }\r\n\r\n    public async initScorllsByLevelData(layerData: LevelLayer, data: LevelDataLayer):Promise<void> {\r\n        if (!data || this.scrollsNode === null || this._loadScrollNode) {\r\n            return;\r\n        } \r\n\r\n        const loadPromises: Promise<void>[] = [];\r\n        layerData.scrollLayers = [];\r\n        this._loadScrollNode = true;\r\n        data.scrolls?.forEach((scroll) => {\r\n            const scrollLayers = new LevelScrollLayerUI(); \r\n            scrollLayers.weight = scroll.weight;\r\n            const uuids = scroll.uuids || []; \r\n            scrollLayers.splicingMode = scroll.splicingMode;\r\n            scrollLayers.splicingOffsetX.min = scroll.offSetX.min;\r\n            scrollLayers.splicingOffsetX.max = scroll.offSetX.max;\r\n            scrollLayers.splicingOffsetY.min = scroll.offSetY.min;\r\n            scrollLayers.splicingOffsetY.max = scroll.offSetY.max;\r\n            scrollLayers.scrollPrefabs = [];\r\n            uuids.forEach((uuid) => {\r\n                const loadPromise = new Promise<void>((resolve) => {\r\n                    assetManager.loadAny({uuid: uuid}, (err, prefab:Prefab) => {\r\n                        if (err) {\r\n                            resolve();\r\n                            return\r\n                        }           \r\n                        scrollLayers.scrollPrefabs.push(prefab);  \r\n                        \r\n                        console.log(\"LevelEditorLayerUI initScorllsByLevelData 1--------- 1\", layerData.scrollLayers.length);\r\n                        resolve();\r\n                    });\r\n                });\r\n                loadPromises.push(loadPromise);\r\n            });\r\n            layerData.scrollLayers.push(scrollLayers);\r\n        });\r\n\r\n        await Promise.all(loadPromises);  \r\n        this._loadScrollNode = false;\r\n        console.log(\"LevelEditorLayerUI initScorllsByLevelData 1---------\", layerData.scrollLayers.length);\r\n        layerData.scrollLayers.forEach((scroll, index) => { \r\n            const scrollsNode = LevelEditorUtils.getOrAddNode(this.scrollsNode!, `scroll_${index}`);\r\n            var totalHeight = data.speed * data.totalTime;\r\n            var posOffsetY = 0;\r\n            var height = 0;\r\n            let prefabIndex = 0; // 当前使用的 prefab 索引\r\n            while (height < totalHeight) {\r\n                // 循环使用 prefab\r\n                const curPrefab = scroll.scrollPrefabs[prefabIndex];\r\n                const child = instantiate(curPrefab);\r\n                const randomOffsetX = Math.random() * (scroll.splicingOffsetX.max - scroll.splicingOffsetX.min) + scroll.splicingOffsetX.min;\r\n                child.setPosition(randomOffsetX, posOffsetY, 0);\r\n                var offY = 0;\r\n                if (scroll.splicingMode === LayerSplicingMode.node_height) {    \r\n                    offY = child.getComponent(UITransform)!.contentSize.height;\r\n                } else if (scroll.splicingMode === LayerSplicingMode.fix_height) {\r\n                    offY = 1334;\r\n                } else if (scroll.splicingMode === LayerSplicingMode.random_height) {\r\n                    offY = Math.max(scroll.splicingOffsetY.min,scroll.splicingOffsetY.max) + child.getComponent(UITransform)!.contentSize.height;\r\n                }\r\n                scrollsNode.addChild(child);\r\n                posOffsetY += offY;\r\n                height += offY;\r\n                prefabIndex = (prefabIndex + 1) % scroll.scrollPrefabs.length;\r\n            }\r\n        });\r\n    }\r\n\r\n    public fillLevelData(data: LevelDataLayer):void {\r\n        data.terrains = []\r\n        this.terrainsNode!.children.forEach((terrainNode) => {\r\n            data.terrains.push({\r\n                // @ts-ignore\r\n                uuid: terrainNode._prefab.asset._uuid,\r\n                position: new Vec2(terrainNode.position.x, terrainNode.position.y),\r\n                scale: new Vec2(terrainNode.scale.x, terrainNode.scale.y),\r\n                rotation: terrainNode.rotation.z\r\n            })\r\n        })\r\n\r\n        //data.dynamics = [] 在上层有保存其它信息，所以这里不清空\r\n        this.dynamicNode!.children.forEach((dynamicNode, index) => {\r\n            data.dynamics[index].position = v2(dynamicNode.position.x, dynamicNode.position.y);\r\n            data.dynamics[index].scale = v2(dynamicNode.scale.x, dynamicNode.scale.y);\r\n            data.dynamics[index].rotation = dynamicNode.rotation.z;\r\n        });\r\n\r\n        // data.waves = []\r\n        // this.wavesNode!.children.forEach((waveNode) => {\r\n        //     var wave = new LevelDataWave()\r\n        //     var waveUIComp = waveNode.getComponent(LevelEditorWaveUI);\r\n        //     waveUIComp!.fillLevelData(wave)\r\n        //     data.waves.push(wave)\r\n        // })\r\n        data.events = []\r\n        this.eventsNode!.children.forEach((eventNode) => {\r\n            var event = new LevelDataEvent()\r\n            var eventUIComp = eventNode.getComponent(LevelEditorEventUI);\r\n            eventUIComp!.fillLevelData(event)\r\n            data.events.push(event)\r\n        })\r\n    }\r\n\r\n    public tick(progress: number, totalTime:number, speed:number):void {\r\n        this.node.setPosition(0, - progress * totalTime * speed, 0);\r\n    }\r\n}"]}