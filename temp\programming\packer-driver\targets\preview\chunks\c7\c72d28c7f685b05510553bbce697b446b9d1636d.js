System.register(["__unresolved_0", "cc", "cc/env", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, assetManager, JsonAsset, EDITOR, IMgr, cfg, BundleName, _dec, _class, _class2, _crd, ccclass, property, LubanMgr;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _reportPossibleCrUseOfIMgr(extras) {
    _reporterNs.report("IMgr", "../../../../scripts/core/base/IMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBundleName(extras) {
    _reporterNs.report("BundleName", "../const/BundleConst", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      assetManager = _cc.assetManager;
      JsonAsset = _cc.JsonAsset;
    }, function (_ccEnv) {
      EDITOR = _ccEnv.EDITOR;
    }, function (_unresolved_2) {
      IMgr = _unresolved_2.IMgr;
    }, function (_unresolved_3) {
      cfg = _unresolved_3;
    }, function (_unresolved_4) {
      BundleName = _unresolved_4.BundleName;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "a3a62bIqipLwrj7c4gC3qgj", "LubanMgr", undefined);

      __checkObsolete__(['_decorator', 'assetManager', 'JsonAsset']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("LubanMgr", LubanMgr = (_dec = ccclass("LubanMgr"), _dec(_class = (_class2 = class LubanMgr extends (_crd && IMgr === void 0 ? (_reportPossibleCrUseOfIMgr({
        error: Error()
      }), IMgr) : IMgr) {
        constructor() {
          super(...arguments);
          this._table = null;
        }

        init() {
          super.init();
        }

        load() {
          return new Promise((resolve, reject) => {
            var bundle = assetManager.bundles.get((_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
              error: Error()
            }), BundleName) : BundleName).Luban);
            bundle.loadDir("./", JsonAsset, (err, assets) => {
              if (err) {
                reject(err);
                return;
              }

              var dataMap = new Map();

              for (var asset of assets) {
                dataMap.set(asset.name, asset);
              }

              this._table = new cfg.Tables(file => {
                if (dataMap.has(file)) {
                  return dataMap.get(file).json;
                }

                console.warn("LubanMgr: File " + file + " not found in loaded assets.");
                return null;
              });
              resolve();
            });
          });
        } // 仅在编辑器环境下使用


        initInEditor() {
          var _this = this;

          return _asyncToGenerator(function* () {
            if (_this._table != null || !EDITOR) {
              return;
            } // 遍历assets/bundles/luban/ 目录下的json文件


            var root_dir = 'db://assets/bundles/luban/';
            var pattern = root_dir + '**/*.json';

            try {
              // @ts-ignore
              var res = yield Editor.Message.request('asset-db', 'query-assets', {
                pattern
              });
              var arr = Array.isArray(res) ? res : Array.isArray(res[0]) ? res[0] : [];
              var files = arr.map(a => a.path).filter(a => a.startsWith(root_dir)); // console.log('LubanMgr: Found files:', files);

              var LoadedCount = 0;
              var dataMap = new Map(); // Load each JSON file's content

              var _loop = function* _loop(filePath) {
                try {
                  // @ts-ignore
                  var uuid = yield Editor.Message.request('asset-db', 'query-uuid', filePath + '.json');
                  assetManager.loadAny(uuid, (err, jsonAsset) => {
                    if (err) {
                      console.warn("LubanMgr: Failed to load asset " + filePath + ":", err);
                      return;
                    }

                    dataMap.set(jsonAsset.name, jsonAsset);
                    LoadedCount++;
                  });
                } catch (fileError) {
                  console.warn("LubanMgr: Failed to load file " + filePath + ":", fileError);
                }
              };

              for (var filePath of files) {
                yield* _loop(filePath);
              } // Wait until all files are loaded


              while (LoadedCount < files.length) {
                yield new Promise(res => setTimeout(res, 100)); // wait 100ms
              }

              _this._table = new cfg.Tables(file => {
                if (dataMap.has(file)) {
                  return dataMap.get(file).json;
                }

                return null;
              });
            } catch (error) {
              console.error("LubanMgr: Failed to initialize in editor:", error);
              throw error;
            }
          })();
        }

        get table() {
          return this._table;
        }

      }, _class2.LUBAN_PATH = 'luban/', _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=c72d28c7f685b05510553bbce697b446b9d1636d.js.map