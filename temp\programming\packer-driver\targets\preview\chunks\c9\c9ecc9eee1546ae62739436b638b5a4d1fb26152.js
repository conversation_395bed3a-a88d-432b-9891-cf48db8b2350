System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Label, Node, ProgressBar, Sprite, comm, logError, MyApp, ButtonPlus, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _descriptor7, _crd, ccclass, property, TaskItem;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfcsproto(extras) {
    _reporterNs.report("csproto", "db://assets/bundles/common/script/autogen/pb/cs_proto.js", _context.meta, extras);
  }

  function _reportPossibleCrUseOfcomm(extras) {
    _reporterNs.report("comm", "db://assets/bundles/common/script/autogen/pb/cs_proto.js", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogError(extras) {
    _reporterNs.report("logError", "db://assets/scripts/utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "../../../app/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfButtonPlus(extras) {
    _reporterNs.report("ButtonPlus", "../../common/components/button/ButtonPlus", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Label = _cc.Label;
      Node = _cc.Node;
      ProgressBar = _cc.ProgressBar;
      Sprite = _cc.Sprite;
    }, function (_unresolved_2) {
      comm = _unresolved_2.comm;
    }, function (_unresolved_3) {
      logError = _unresolved_3.logError;
    }, function (_unresolved_4) {
      MyApp = _unresolved_4.MyApp;
    }, function (_unresolved_5) {
      ButtonPlus = _unresolved_5.ButtonPlus;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "153deKhLWZKnIZ8n/IsbPii", "TaskItem", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Label', 'Node', 'ProgressBar', 'Sprite']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("TaskItem", TaskItem = (_dec = ccclass('TaskItem'), _dec2 = property(Label), _dec3 = property(ProgressBar), _dec4 = property(Label), _dec5 = property(Sprite), _dec6 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec7 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec8 = property(Node), _dec(_class = (_class2 = class TaskItem extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "taskDesc", _descriptor, this);

          _initializerDefineProperty(this, "progressBar", _descriptor2, this);

          _initializerDefineProperty(this, "progressLabel", _descriptor3, this);

          _initializerDefineProperty(this, "taskIcon", _descriptor4, this);

          _initializerDefineProperty(this, "taskJumpBtn", _descriptor5, this);

          _initializerDefineProperty(this, "taskGetRewardBtn", _descriptor6, this);

          _initializerDefineProperty(this, "taskDoneNode", _descriptor7, this);
        }

        onLoad() {
          this.taskJumpBtn.addClick(this.onClickTaskJumpBtn, this);
          this.taskGetRewardBtn.addClick(this.onClickTaskGetRewardBtn, this);
        }

        onRender(taskInfo) {
          var taskCfg = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).lubanTables.TbResTask.get(taskInfo.task_id);

          if (!taskCfg) {
            (_crd && logError === void 0 ? (_reportPossibleCrUseOflogError({
              error: Error()
            }), logError) : logError)("TaskItem", "task id " + taskInfo.task_id + " not found");
            return;
          }

          this.taskDesc.string = taskCfg.taskGoal.desc;
          this.progressLabel.string = taskInfo.progress + "/" + taskCfg.taskGoal.params[1];
          this.progressBar.progress = taskInfo.progress / taskCfg.taskGoal.params[1];

          switch (taskInfo.status) {
            case (_crd && comm === void 0 ? (_reportPossibleCrUseOfcomm({
              error: Error()
            }), comm) : comm).TASK_STATUS.TASK_STATUS_COMPLETE:
              this.taskGetRewardBtn.node.active = true;
              this.taskJumpBtn.node.active = false;
              this.taskDoneNode.active = false;
              break;

            case (_crd && comm === void 0 ? (_reportPossibleCrUseOfcomm({
              error: Error()
            }), comm) : comm).TASK_STATUS.TASK_STATUS_NORMAL:
              this.taskGetRewardBtn.node.active = true;
              this.taskJumpBtn.node.active = false;
              this.taskDoneNode.active = false;
              break;

            case (_crd && comm === void 0 ? (_reportPossibleCrUseOfcomm({
              error: Error()
            }), comm) : comm).TASK_STATUS.TASK_STATUS_AWARD_DONE:
              this.taskGetRewardBtn.node.active = false;
              this.taskJumpBtn.node.active = false;
              this.taskDoneNode.active = true;
              break;

            default:
              (_crd && logError === void 0 ? (_reportPossibleCrUseOflogError({
                error: Error()
              }), logError) : logError)("TaskItem", "task id " + taskInfo.task_id + " status error " + taskInfo.status);
              break;
          } // this.taskIcon!.spriteFrame = MyApp.resMgr.getSpriteFrame(taskCfg.taskIcon);

        }

        onClickTaskJumpBtn() {}

        onClickTaskGetRewardBtn() {}

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "taskDesc", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "progressBar", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "progressLabel", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "taskIcon", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "taskJumpBtn", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class2.prototype, "taskGetRewardBtn", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor7 = _applyDecoratedDescriptor(_class2.prototype, "taskDoneNode", [_dec8], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=c9ecc9eee1546ae62739436b638b5a4d1fb26152.js.map