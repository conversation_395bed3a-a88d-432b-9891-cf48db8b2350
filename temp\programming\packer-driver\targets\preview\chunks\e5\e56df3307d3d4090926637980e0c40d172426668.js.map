{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/leveldata/leveldata.ts"], "names": ["LevelDataTerrain", "LayerOffset", "LevelDataScroll", "LevelDataRandTerrains", "LevelDataElem", "LevelDataWaveGroup", "LevelDataWave", "LevelDataEvent", "LevelDataLayer", "LevelDataBackgroundLayer", "LevelData", "Vec2", "newCondition", "newTrigger", "LayerType", "LayerSplicingMode", "uuid", "position", "scale", "rotation", "min", "max", "weight", "uuids", "splicingMode", "node_height", "offSetX", "offSetY", "terrains", "elemID", "name", "waveUUID", "waveGroup", "fromJSON", "json", "wave", "Object", "assign", "conditions", "triggers", "event", "map", "condition", "trigger", "remark", "zIndex", "totalTime", "speed", "type", "scrolls", "dynamics", "waves", "events", "terrain", "scroll", "dynamic", "layer", "backgrounds", "background<PERSON>ayer", "floorLayers", "skyLayers", "levelData"], "mappings": ";;;+GA6BaA,gB,EAOAC,W,EAKAC,e,EAaAC,qB,EAKAC,a,EAMAC,kB,EAKAC,a,EAWAC,c,EAoBAC,c,EAqCAC,wB,EAaAC,S;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAvJJC,MAAAA,I,OAAAA,I;;AAGAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;AAJkB;;;2BAMfC,S,0BAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;eAAAA,S;cAOZ;;;mCACYC,iB,0BAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;AAAAA,QAAAA,iB,CAAAA,iB;eAAAA,iB;;;kCAeCf,gB,GAAN,MAAMA,gBAAN,CAAuB;AAAA;AAAA,eACnBgB,IADmB,GACJ,EADI;AAAA,eAEnBC,QAFmB,GAEF,IAAIN,IAAJ,EAFE;AAAA,eAGnBO,KAHmB,GAGL,IAAIP,IAAJ,EAHK;AAAA,eAInBQ,QAJmB,GAIA,CAJA;AAAA;;AAAA,O;;6BAOjBlB,W,GAAN,MAAMA,WAAN,CAAkB;AAAA;AAAA,eACdmB,GADc,GACA,CADA;AAAA,eAEdC,GAFc,GAEA,CAFA;AAAA;;AAAA,O;;iCAKZnB,e,GAAN,MAAMA,eAAN,CAAsB;AAAA;AAAA,eAClBoB,MADkB,GACD,GADC;AAAA,eAElBC,KAFkB,GAEA,EAFA;AAAA,eAGlBC,YAHkB,GAGgBT,iBAAiB,CAACU,WAHlC;AAAA,eAIlBC,OAJkB,GAIK,IAAIzB,WAAJ,EAJL;AAAA,eAKlB0B,OALkB,GAKK,IAAI1B,WAAJ,EALL;AAAA;;AAAA,O;;uCAahBE,qB,GAAN,MAAMA,qBAAN,SAAoCH,gBAApC,CAAqD;AAAA;AAAA;AAAA,eACjDsB,MADiD,GAChC,GADgC;AAAA,eAEjDM,QAFiD,GAEd,EAFc;AAAA;;AAAA,O;;+BAK/CxB,a,GAAN,MAAMA,aAAN,CAAoB;AAAA;AAAA,eAChByB,MADgB,GACC,EADD;AAAA,eAEhBZ,QAFgB,GAEC,IAAIN,IAAJ,EAFD;AAAA,eAGhBmB,IAHgB,GAGD,SAHC;AAAA;;AAAA,O;;oCAMdzB,kB,GAAN,MAAMA,kBAAN,CAAyB;AAAA;AAAA,eACrB0B,QADqB,GACA,EADA;AAAA,eAErBT,MAFqB,GAEJ,EAFI;AAAA;;AAAA,O;;+BAKnBhB,a,GAAN,MAAMA,aAAN,SAA4BF,aAA5B,CAA0C;AAAA;AAAA;AAAA,eACtC4B,SADsC,GACJ,EADI;AAAA;;AAG9B,eAARC,QAAQ,CAACC,IAAD,EAA2B;AACtC,cAAMC,IAAI,GAAG,IAAI7B,aAAJ,EAAb;AACA,cAAI,CAAC4B,IAAL,EAAW,OAAOC,IAAP;AACXC,UAAAA,MAAM,CAACC,MAAP,CAAcF,IAAd,EAAoBD,IAApB;AACA,iBAAOC,IAAP;AACH;;AAR4C,O;;gCAWpC5B,c,GAAN,MAAMA,cAAN,SAA6BH,aAA7B,CAA2C;AAAA;AAAA;AAAA,eACvCkC,UADuC,GACA,EADA;AAAA,eAEvCC,QAFuC,GAEH,EAFG;AAAA;;AAI/B,eAARN,QAAQ,CAACC,IAAD,EAA4B;AAAA;;AACvC,cAAMM,KAAK,GAAG,IAAIjC,cAAJ,EAAd;AACA,cAAI,CAAC2B,IAAL,EAAW,OAAOM,KAAP;AAEXJ,UAAAA,MAAM,CAACC,MAAP,CAAcG,KAAd,EAAqBN,IAArB;AACAM,UAAAA,KAAK,CAACF,UAAN,GAAmB,qBAAAJ,IAAI,CAACI,UAAL,sCAAiBG,GAAjB,CAAsBC,SAAD,IAAoB;AACxD,mBAAO;AAAA;AAAA,8CAAaA,SAAb,CAAP;AACH,WAFkB,MAEb,EAFN;AAGAF,UAAAA,KAAK,CAACD,QAAN,GAAiB,mBAAAL,IAAI,CAACK,QAAL,oCAAeE,GAAf,CAAoBE,OAAD,IAAkB;AAClD,mBAAO;AAAA;AAAA,0CAAWA,OAAX,CAAP;AACH,WAFgB,MAEX,EAFN;AAIA,iBAAOH,KAAP;AACH;;AAjB6C,O;;gCAoBrChC,c,GAAN,MAAMA,cAAN,CAAqB;AAAA;AAAA,eACjBoC,MADiB,GACA,EADA;AAAA,eAEjBC,MAFiB,GAEA,CAFA;AAAA,eAGjBC,SAHiB,GAGG,EAHH;AAAA,eAIjBC,KAJiB,GAID,GAJC;AAAA,eAKjBC,IALiB,GAKF,CALE;AAKC;AALD,eAMjBpB,QANiB,GAMc,EANd;AAAA,eAOjBqB,OAPiB,GAOY,EAPZ;AAAA,eAQjBC,QARiB,GAQmB,EARnB;AAAA,eASjBC,KATiB,GASQ,EATR;AAAA,eAUjBC,MAViB,GAUU,EAVV;AAAA;;AAYdf,QAAAA,MAAM,CAACH,IAAD,EAAiB;AAAA;;AAC7BE,UAAAA,MAAM,CAACC,MAAP,CAAc,IAAd,EAAoBH,IAApB;AAEA,eAAKN,QAAL,GAAgB,mBAAAM,IAAI,CAACN,QAAL,oCAAea,GAAf,CAAoBY,OAAD,IAC/BjB,MAAM,CAACC,MAAP,CAAc,IAAIrC,gBAAJ,EAAd,EAAsCqD,OAAtC,CADY,MACuC,EADvD;AAEA,eAAKJ,OAAL,GAAe,kBAAAf,IAAI,CAACe,OAAL,mCAAcR,GAAd,CAAmBa,MAAD,IAC7BlB,MAAM,CAACC,MAAP,CAAc,IAAInC,eAAJ,EAAd,EAAqCoD,MAArC,CADW,MACsC,EADrD;AAEA,eAAKJ,QAAL,GAAgB,mBAAAhB,IAAI,CAACgB,QAAL,oCAAeT,GAAf,CAAoBc,OAAD,IAC/BnB,MAAM,CAACC,MAAP,CAAc,IAAIlC,qBAAJ,EAAd,EAA2CoD,OAA3C,CADY,MAC4C,EAD5D;AAEA,eAAKJ,KAAL,GAAa,gBAAAjB,IAAI,CAACiB,KAAL,iCAAYV,GAAZ,CAAiBN,IAAD,IACzB7B,aAAa,CAAC2B,QAAd,CAAuBE,IAAvB,CADS,MACwB,EADrC;AAEA,eAAKiB,MAAL,GAAc,iBAAAlB,IAAI,CAACkB,MAAL,kCAAaX,GAAb,CAAkBD,KAAD,IAC3BjC,cAAc,CAAC0B,QAAf,CAAwBO,KAAxB,CADU,MACyB,EADvC;AAEH;;AAEc,eAARP,QAAQ,CAACC,IAAD,EAA4B;AACvC,cAAMsB,KAAK,GAAG,IAAIhD,cAAJ,EAAd;AACA,cAAI,CAAC0B,IAAL,EAAW,OAAOsB,KAAP;AAEXA,UAAAA,KAAK,CAACnB,MAAN,CAAaH,IAAb;AAEA,iBAAOsB,KAAP;AACH;;AAlCuB,O;;0CAqCf/C,wB,GAAN,MAAMA,wBAAN,SAAuCD,cAAvC,CAAsD;AAAA;AAAA;AAAA,eAClDiD,WADkD,GAC1B,EAD0B;AAAA;;AAG1C,eAARxB,QAAQ,CAACC,IAAD,EAAsC;AACjD,cAAMsB,KAAK,GAAG,IAAI/C,wBAAJ,EAAd;AACA,cAAI,CAACyB,IAAL,EAAW,OAAOsB,KAAP;AAEXA,UAAAA,KAAK,CAACnB,MAAN,CAAaH,IAAb;AAEA,iBAAOsB,KAAP;AACH;;AAVwD,O;;2BAahD9C,S,GAAN,MAAMA,SAAN,CAAgB;AAAA;AAAA,eACZoB,IADY,GACG,EADH;AAAA,eAEZgB,SAFY,GAEQ,EAFR;AAAA,eAGZY,eAHY,GAGgC,IAAIjD,wBAAJ,EAHhC;AAAA,eAIZkD,WAJY,GAIoB,EAJpB;AAAA,eAKZC,SALY,GAKkB,EALlB;AAAA;;AAOJ,eAAR3B,QAAQ,CAACC,IAAD,EAAuB;AAAA;;AAClC,cAAM2B,SAAS,GAAG,IAAInD,SAAJ,EAAlB;AACA,cAAI,CAACwB,IAAL,EAAW,OAAO2B,SAAP;AAEXzB,UAAAA,MAAM,CAACC,MAAP,CAAcwB,SAAd,EAAyB3B,IAAzB;AACA2B,UAAAA,SAAS,CAACH,eAAV,GAA4BjD,wBAAwB,CAACwB,QAAzB,CAAkCC,IAAI,CAACwB,eAAvC,CAA5B;AACAG,UAAAA,SAAS,CAACF,WAAV,GAAwB,sBAAAzB,IAAI,CAACyB,WAAL,uCAAkBlB,GAAlB,CAAuBe,KAAD,IAC1ChD,cAAc,CAACyB,QAAf,CAAwBuB,KAAxB,CADoB,MACe,EADvC;AAEAK,UAAAA,SAAS,CAACD,SAAV,GAAsB,oBAAA1B,IAAI,CAAC0B,SAAL,qCAAgBnB,GAAhB,CAAqBe,KAAD,IACtChD,cAAc,CAACyB,QAAf,CAAwBuB,KAAxB,CADkB,MACiB,EADvC;AAGA,iBAAOK,SAAP;AACH;;AAnBkB,O", "sourcesContent": ["import { Vec2 } from \"cc\"; // 注意：这里的分号是必须的，因为下面的代码是压缩过的\r\nimport { LevelDataEventCondtion } from \"./condition/LevelDataEventCondtion\";\r\nimport { LevelDataEventTrigger } from \"./trigger/LevelDataEventTrigger\";\r\nimport { newCondition } from \"./condition/newCondition\";\r\nimport { newTrigger } from \"./trigger/newTrigger\";\r\n\r\nexport enum LayerType {\r\n    Background = 1,\r\n    Random,\r\n    Scroll,\r\n    Emittier\r\n}\r\n\r\n// 层级拼接方式\r\nexport enum LayerSplicingMode {\r\n    /**\r\n     * @zh 节点高度拼接\r\n     */\r\n    node_height = 1,\r\n    /**\r\n     * @zh 屏幕高度拼接\r\n     */\r\n    fix_height,\r\n    /**\r\n     * @zh 随机间隔拼接\r\n     */\r\n    random_height\r\n}\r\n\r\nexport class LevelDataTerrain {\r\n    public uuid: string = \"\";\r\n    public position: Vec2 = new Vec2();\r\n    public scale: Vec2 = new Vec2();\r\n    public rotation: number = 0;\r\n}\r\n\r\nexport class LayerOffset {\r\n    public min: number = 0;\r\n    public max: number = 0;\r\n}\r\n\r\nexport class LevelDataScroll {\r\n    public weight: number = 100;\r\n    public uuids: string[] = [];\r\n    public splicingMode: LayerSplicingMode = LayerSplicingMode.node_height;\r\n    public offSetX: LayerOffset = new LayerOffset();\r\n    public offSetY: LayerOffset = new LayerOffset();\r\n}\r\n\r\nexport interface LevelDataRandTerrain {\r\n    weight: number;\r\n    uuid: string;\r\n}\r\n\r\nexport class LevelDataRandTerrains extends LevelDataTerrain {\r\n    public weight: number = 100;\r\n    public terrains: LevelDataRandTerrain[] = [];\r\n}\r\n\r\nexport class LevelDataElem {\r\n    public elemID: string = \"\";\r\n    public position: Vec2 = new Vec2();\r\n    public name: string = \"default\";\r\n}\r\n\r\nexport class LevelDataWaveGroup {\r\n    public waveUUID: string[] = [];\r\n    public weight: number = 50;\r\n}\r\n\r\nexport class LevelDataWave extends LevelDataElem {\r\n    public waveGroup: LevelDataWaveGroup[] = [];\r\n\r\n    static fromJSON(json: any): LevelDataWave {\r\n        const wave = new LevelDataWave();\r\n        if (!json) return wave;\r\n        Object.assign(wave, json);\r\n        return wave;\r\n    }\r\n}\r\n\r\nexport class LevelDataEvent extends LevelDataElem {\r\n    public conditions: LevelDataEventCondtion[] = [];\r\n    public triggers: LevelDataEventTrigger[] = [];\r\n\r\n    static fromJSON(json: any): LevelDataEvent {\r\n        const event = new LevelDataEvent();\r\n        if (!json) return event;\r\n        \r\n        Object.assign(event, json);\r\n        event.conditions = json.conditions?.map((condition: any) => {\r\n            return newCondition(condition);\r\n        }) || [];\r\n        event.triggers = json.triggers?.map((trigger: any) => {\r\n            return newTrigger(trigger);\r\n        }) || [];\r\n        \r\n        return event;\r\n    }\r\n}\r\n\r\nexport class LevelDataLayer {\r\n    public remark: string = \"\";\r\n    public zIndex: number = 0;\r\n    public totalTime: number = 60;\r\n    public speed: number = 200;\r\n    public type: number = 0; // 对应LayerType\r\n    public terrains: LevelDataTerrain[] = [];\r\n    public scrolls: LevelDataScroll[] = [];\r\n    public dynamics: LevelDataRandTerrains[] = [];\r\n    public waves: LevelDataWave[] = [];\r\n    public events: LevelDataEvent[] = [];\r\n\r\n    protected assign(json: any):void {\r\n        Object.assign(this, json);\r\n\r\n        this.terrains = json.terrains?.map((terrain: any) => \r\n            Object.assign(new LevelDataTerrain(), terrain)) || [];\r\n        this.scrolls = json.scrolls?.map((scroll: any) => \r\n            Object.assign(new LevelDataScroll(), scroll)) || [];\r\n        this.dynamics = json.dynamics?.map((dynamic: any) => \r\n            Object.assign(new LevelDataRandTerrains(), dynamic)) || [];\r\n        this.waves = json.waves?.map((wave: any) => \r\n            LevelDataWave.fromJSON(wave)) || [];\r\n        this.events = json.events?.map((event: any) => \r\n            LevelDataEvent.fromJSON(event)) || [];\r\n    }\r\n\r\n    static fromJSON(json: any): LevelDataLayer {\r\n        const layer = new LevelDataLayer();\r\n        if (!json) return layer;\r\n\r\n        layer.assign(json);\r\n        \r\n        return layer;\r\n    }\r\n}\r\n\r\nexport class LevelDataBackgroundLayer extends LevelDataLayer {\r\n    public backgrounds: string[] = [];\r\n    \r\n    static fromJSON(json: any): LevelDataBackgroundLayer {\r\n        const layer = new LevelDataBackgroundLayer();\r\n        if (!json) return layer;\r\n       \r\n        layer.assign(json);\r\n        \r\n        return layer;\r\n    }\r\n}\r\n\r\nexport class LevelData {\r\n    public name: string = \"\";\r\n    public totalTime: number = 59;\r\n    public backgroundLayer: LevelDataBackgroundLayer = new LevelDataBackgroundLayer();\r\n    public floorLayers: LevelDataLayer[] = [];\r\n    public skyLayers: LevelDataLayer[] = [];\r\n\r\n    static fromJSON(json: any): LevelData {\r\n        const levelData = new LevelData();\r\n        if (!json) return levelData;\r\n        \r\n        Object.assign(levelData, json);\r\n        levelData.backgroundLayer = LevelDataBackgroundLayer.fromJSON(json.backgroundLayer);\r\n        levelData.floorLayers = json.floorLayers?.map((layer: any) =>\r\n            LevelDataLayer.fromJSON(layer)) || [];\r\n        levelData.skyLayers = json.skyLayers?.map((layer: any) => \r\n            LevelDataLayer.fromJSON(layer)) || [];\r\n        \r\n        return levelData;\r\n    }\r\n}\r\n"]}