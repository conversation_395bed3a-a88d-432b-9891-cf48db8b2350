{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/Plane.ts"], "names": ["_decorator", "Component", "<PERSON><PERSON><PERSON><PERSON>", "Material", "sp", "UITransform", "MyApp", "StateEvent", "StateMachine", "ccclass", "property", "Plane", "Skeleton", "_planeData", "_curEvent", "IDLE", "_aniCallFunc", "undefined", "_isInit", "_stateMachine", "_hurtActDuration", "onLoad", "spine", "getComponentInChildren", "initializeStateMachine", "update", "dt", "resetRoleEffect", "initPlane", "planeData", "path", "recourseSpine", "resMgr", "loadAsync", "SkeletonData", "then", "skeletonData", "premultipliedAlpha", "node", "setPosition", "getComponent", "height", "setPlaneState", "playFlashAnim", "material", "customMaterial", "event", "callback", "handleEvent", "onEnter", "ENTER", "onMoveCommand", "isLeft", "MOVE_LEFT_COMMAND", "MOVE_RIGHT_COMMAND", "onMoveEnd", "MOVE_END", "onAttackCommand", "ATTACK_COMMAND", "onGetHit", "GET_HIT", "onDie", "DIE", "onDodgeCommand", "DODGE_COMMAND", "setAnimSpeed", "speed", "timeScale"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,O,OAAAA,O;AAASC,MAAAA,Q,OAAAA,Q;AAAwBC,MAAAA,E,OAAAA,E;AAAIC,MAAAA,W,OAAAA,W;;AAC5DC,MAAAA,K,iBAAAA,K;;AAEeC,MAAAA,U,iBAAAA,U;;AACfC,MAAAA,Y,iBAAAA,Y;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBV,U;;uBAGjBW,K,WADZF,OAAO,CAAC,OAAD,C,UAGHC,QAAQ,CAACN,EAAE,CAACQ,QAAJ,C,2BAHb,MACaD,KADb,SAC2BV,SAD3B,CACqC;AAAA;AAAA;;AAAA;;AAAA,eAKjCY,UALiC,GAKF,IALE;AAKG;AALH,eAMjCC,SANiC,GAMT;AAAA;AAAA,wCAAWC,IANF;AAMO;AANP,eAOjCC,YAPiC,GAOSC,SAPT;AAOmB;AAPnB,eAQjCC,OARiC,GAQd,KARc;AAQR;AARQ,eASjCC,aATiC,GAShB;AAAA;AAAA,6CATgB;AAAA,eAUjCC,gBAViC,GAUd,GAVc;AAAA;;AAUT;AAEdC,QAAAA,MAAM,GAAS;AACrB,cAAI,CAAC,KAAKC,KAAV,EAAiB;AACb,iBAAKA,KAAL,GAAa,KAAKC,sBAAL,CAA4BnB,EAAE,CAACQ,QAA/B,CAAb;AACH;;AACD,eAAKO,aAAL,CAAmBK,sBAAnB,CAA0C,KAAKF,KAA/C;AACH;;AAESG,QAAAA,MAAM,CAACC,EAAD,EAAmB;AAC/B,cAAI,KAAKN,gBAAL,GAAwB,CAA5B,EAA+B;AAC3B,iBAAKA,gBAAL,IAAyBM,EAAzB;;AACA,gBAAI,KAAKN,gBAAL,IAAyB,CAA7B,EAAgC;AAC5B,mBAAKO,eAAL;AACH;AACJ;AACJ;;AAGDC,QAAAA,SAAS,CAACC,SAAD,EAAuB;AAC5B;AACA,eAAKhB,UAAL,GAAkBgB,SAAlB;AACA,eAAKX,OAAL,GAAe,KAAf;AACA,cAAIY,IAAI,GAAG,KAAKjB,UAAL,CAAgBkB,aAA3B;AACA;AAAA;AAAA,8BAAMC,MAAN,CAAaC,SAAb,CAAuBH,IAAvB,EAA6B1B,EAAE,CAAC8B,YAAhC,EAA8CC,IAA9C,CAAoDC,YAAD,IAAkB;AACjE,gBAAI,KAAKd,KAAT,EAAgB;AACZ,mBAAKA,KAAL,CAAWc,YAAX,GAA0BA,YAA1B;AACA,mBAAKd,KAAL,CAAWe,kBAAX,GAAgC,KAAhC;AACA,mBAAKnB,OAAL,GAAe,IAAf;AACA,mBAAKI,KAAL,CAAWgB,IAAX,CAAgBC,WAAhB,CAA4B,CAA5B,EAA+B,CAAC,KAAKjB,KAAL,CAAWgB,IAAX,CAAgBE,YAAhB,CAA6BnC,WAA7B,EAA2CoC,MAA5C,GAAqD,CAApF;AACA,mBAAKC,aAAL,CAAmB,KAAK5B,SAAxB,EAAmC,KAAKE,YAAxC;AACH;AACJ,WARD;AASH,SA3CgC,CA6CjC;;;AACM2B,QAAAA,aAAa,GAAG;AAAA;;AAAA;AAClB,gBAAIC,QAAQ,SAAS;AAAA;AAAA,gCAAMZ,MAAN,CAAaC,SAAb,CAAuB,oBAAvB,EAA6C9B,QAA7C,CAArB;;AACA,gBAAIyC,QAAJ,EAAa;AACT,cAAA,KAAI,CAACtB,KAAL,CAAYuB,cAAZ,GAA6BD,QAA7B;AACH;;AACD,YAAA,KAAI,CAACxB,gBAAL,GAAwB,GAAxB;AALkB;AAMrB;;AAEDO,QAAAA,eAAe,GAAE;AACb,eAAKP,gBAAL,GAAwB,CAAxB;AACA,eAAKE,KAAL,CAAYuB,cAAZ,GAA6B,IAA7B;AACH;;AAGDH,QAAAA,aAAa,CAACI,KAAD,EAAmBC,QAAnB,EAAqD;AAC9D,cAAI,CAAC,KAAK7B,OAAV,EAAmB;AACf,iBAAKJ,SAAL,GAAiBgC,KAAjB;AACA,iBAAK9B,YAAL,GAAoB+B,QAApB;AACA,mBAAO,KAAP;AACH;;AACD,iBAAO,KAAK5B,aAAL,CAAmB6B,WAAnB,CAA+BF,KAA/B,EAAqCC,QAArC,CAAP;AACH,SAnEgC,CAqEjC;;;AACOE,QAAAA,OAAO,CAACF,QAAD,EAA2B;AACrC,iBAAO,KAAKL,aAAL,CAAmB;AAAA;AAAA,wCAAWQ,KAA9B,EAAqCH,QAArC,CAAP;AACH,SAxEgC,CA0EjC;;;AACOI,QAAAA,aAAa,CAACC,MAAD,EAAyBL,QAAzB,EAAmD;AAAA,cAAlDK,MAAkD;AAAlDA,YAAAA,MAAkD,GAAjC,KAAiC;AAAA;;AACnE,cAAIN,KAAK,GAAGM,MAAM,GAAG;AAAA;AAAA,wCAAWC,iBAAd,GAAkC;AAAA;AAAA,wCAAWC,kBAA/D;AACA,iBAAO,KAAKZ,aAAL,CAAmBI,KAAnB,EAA0BC,QAA1B,CAAP;AACH,SA9EgC,CAgFjC;;;AACOQ,QAAAA,SAAS,CAACR,QAAD,EAA2B;AACvC,iBAAO,KAAKL,aAAL,CAAmB;AAAA;AAAA,wCAAWc,QAA9B,EAAwCT,QAAxC,CAAP;AACH,SAnFgC,CAqFjC;;;AACOU,QAAAA,eAAe,CAACV,QAAD,EAA2B;AAC7C,iBAAO,KAAKL,aAAL,CAAmB;AAAA;AAAA,wCAAWgB,cAA9B,EAA8CX,QAA9C,CAAP;AACH,SAxFgC,CA0FjC;;;AACOY,QAAAA,QAAQ,CAACZ,QAAD,EAA2B;AACtC,eAAKJ,aAAL;AACA,iBAAO,KAAKD,aAAL,CAAmB;AAAA;AAAA,wCAAWkB,OAA9B,EAAuCb,QAAvC,CAAP;AACH,SA9FgC,CAgGjC;;;AACOc,QAAAA,KAAK,CAACd,QAAD,EAA2B;AACnC,iBAAO,KAAKL,aAAL,CAAmB;AAAA;AAAA,wCAAWoB,GAA9B,EAAmCf,QAAnC,CAAP;AACH;;AAEMgB,QAAAA,cAAc,CAAChB,QAAD,EAA2B;AAC5C,iBAAO,KAAKL,aAAL,CAAmB;AAAA;AAAA,wCAAWsB,aAA9B,EAA6CjB,QAA7C,CAAP;AACH;;AAEDkB,QAAAA,YAAY,CAACC,KAAD,EAAgB;AACxB,cAAI,KAAK5C,KAAL,IAAcpB,OAAO,CAAC,KAAKoB,KAAN,CAAzB,EAAsC;AAClC,iBAAKA,KAAL,CAAW6C,SAAX,GAAuBD,KAAvB;AACH;AACJ;;AA7GgC,O;;;;;iBAGL,I", "sourcesContent": ["import { _decorator, Component, isValid, Material, MeshRenderer, sp, UITransform } from 'cc';\r\nimport { MyApp } from 'db://assets/bundles/common/script/app/MyApp';\r\nimport { PlaneData } from '../data/plane/PlaneData';\r\nimport { StateCallback, StateEvent } from '../plane/StateDefine';\r\nimport { StateMachine } from '../plane/StateMachine';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('Plane')\r\nexport class Plane extends Component {\r\n\r\n    @property(sp.Skeleton)\r\n    spine: sp.Skeleton | null = null\r\n\r\n    _planeData: PlaneData | null = null;//飞机数据\r\n    _curEvent: StateEvent = StateEvent.IDLE;//当前动画名称\r\n    _aniCallFunc: StateCallback | undefined = undefined;//动画回调\r\n    _isInit: boolean = false;//是否初始化完成\r\n    _stateMachine =  new StateMachine();\r\n    _hurtActDuration = 0.2; // 受伤动画持续时间\r\n\r\n    protected onLoad(): void {\r\n        if (!this.spine) {\r\n            this.spine = this.getComponentInChildren(sp.Skeleton);\r\n        }\r\n        this._stateMachine.initializeStateMachine(this.spine!);\r\n    }\r\n\r\n    protected update(dt: number): void {\r\n        if (this._hurtActDuration > 0) {\r\n            this._hurtActDuration -= dt;\r\n            if (this._hurtActDuration <= 0) {\r\n                this.resetRoleEffect();\r\n            }\r\n        }\r\n    }\r\n\r\n\r\n    initPlane(planeData: PlaneData) {\r\n        // 初始化飞机数据\r\n        this._planeData = planeData;\r\n        this._isInit = false;\r\n        let path = this._planeData.recourseSpine;\r\n        MyApp.resMgr.loadAsync(path, sp.SkeletonData).then((skeletonData) => {\r\n            if (this.spine) {\r\n                this.spine.skeletonData = skeletonData;\r\n                this.spine.premultipliedAlpha = false;\r\n                this._isInit = true;\r\n                this.spine.node.setPosition(0, -this.spine.node.getComponent(UITransform)!.height / 2);\r\n                this.setPlaneState(this._curEvent, this._aniCallFunc);\r\n            }\r\n        });\r\n    }\r\n\r\n    // 播放闪白动画\r\n    async playFlashAnim() {\r\n        let material = await MyApp.resMgr.loadAsync(\"effect/flash/flash\", Material);\r\n        if (material){\r\n            this.spine!.customMaterial = material;\r\n        }\r\n        this._hurtActDuration = 0.2;\r\n    }\r\n\r\n    resetRoleEffect(){\r\n        this._hurtActDuration = 0;\r\n        this.spine!.customMaterial = null;\r\n    }\r\n\r\n\r\n    setPlaneState(event: StateEvent,callback?: StateCallback):boolean {\r\n        if (!this._isInit) {\r\n            this._curEvent = event;\r\n            this._aniCallFunc = callback;\r\n            return false;\r\n        }\r\n        return this._stateMachine.handleEvent(event,callback);\r\n    }\r\n    \r\n    // 移动命令（通用移动）\r\n    public onEnter(callback?: StateCallback) {\r\n        return this.setPlaneState(StateEvent.ENTER, callback);\r\n    }\r\n\r\n    // 移动命令\r\n    public onMoveCommand(isLeft:boolean = false, callback?: StateCallback) {\r\n        let event = isLeft ? StateEvent.MOVE_LEFT_COMMAND : StateEvent.MOVE_RIGHT_COMMAND;\r\n        return this.setPlaneState(event, callback);\r\n    }\r\n\r\n    // 移动结束\r\n    public onMoveEnd(callback?: StateCallback) {\r\n        return this.setPlaneState(StateEvent.MOVE_END, callback);\r\n    }\r\n\r\n    // 攻击命令\r\n    public onAttackCommand(callback?: StateCallback) {\r\n        return this.setPlaneState(StateEvent.ATTACK_COMMAND, callback);\r\n    }\r\n\r\n    // 受击事件\r\n    public onGetHit(callback?: StateCallback) {\r\n        this.playFlashAnim();\r\n        return this.setPlaneState(StateEvent.GET_HIT, callback);\r\n    }\r\n\r\n    // 死亡事件\r\n    public onDie(callback?: StateCallback) {\r\n        return this.setPlaneState(StateEvent.DIE, callback);\r\n    }\r\n\r\n    public onDodgeCommand(callback?: StateCallback) {\r\n        return this.setPlaneState(StateEvent.DODGE_COMMAND, callback);\r\n    }\r\n\r\n    setAnimSpeed(speed: number) {\r\n        if (this.spine && isValid(this.spine)){\r\n            this.spine.timeScale = speed;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n"]}