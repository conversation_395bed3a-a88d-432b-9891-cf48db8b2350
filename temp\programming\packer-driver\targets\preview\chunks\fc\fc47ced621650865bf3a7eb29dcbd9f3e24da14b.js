System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, NodeEventType, Vec3, GameIns, GameEnum, _dec, _class, _crd, ccclass, property, Controller;

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameEnum(extras) {
    _reporterNs.report("GameEnum", "../../const/GameEnum", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMainPlane(extras) {
    _reporterNs.report("MainPlane", "../plane/mainPlane/MainPlane", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      NodeEventType = _cc.NodeEventType;
      Vec3 = _cc.Vec3;
    }, function (_unresolved_2) {
      GameIns = _unresolved_2.GameIns;
    }, function (_unresolved_3) {
      GameEnum = _unresolved_3.GameEnum;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "572fcBk7RlLfpdl/RebWJUY", "Controller", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Animation', 'NodeEventType', 'EventTouch', 'Vec2', 'Vec3']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("Controller", Controller = (_dec = ccclass('Controller'), _dec(_class = class Controller extends Component {
        constructor() {
          super(...arguments);
          this.target = null;
          // 目标对象（主飞机）
          this._targetStartPos = new Vec3(0, 0);
        }

        // 目标起始位置

        /**
         * 加载时初始化
         */
        onLoad() {}
        /**
         * 开始时绑定触摸事件
         */


        start() {
          this.node.on(NodeEventType.TOUCH_START, this.onTouchStart, this);
          this.node.on(NodeEventType.TOUCH_END, this.onTouchEnd, this);
          this.node.on(NodeEventType.TOUCH_CANCEL, this.onTouchEnd, this);
          this.node.on(NodeEventType.TOUCH_MOVE, this.onTouchMove, this);
        }
        /**
         * 触摸开始事件
         * @param {Event.EventTouch} event 触摸事件
         */


        onTouchStart(event) {
          var target = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.mainPlane; // 获取主飞机

          if (!target) {
            return; // 如果主飞机不存在，则不处理
          }

          this._targetStartPos = target.node.getPosition(); // 记录主飞机的起始位置

          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.setTouchState(true);
        }
        /**
         * 触摸移动事件
         * @param {Event.EventTouch} event 触摸事件
         */


        onTouchMove(event) {
          if ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameRuleManager.gameState != (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).GameState.Battle) {
            return; // 游戏未进入战斗状态时不处理
          }

          var target = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.mainPlane; // 获取主飞机

          if (!target) {
            return; // 如果主飞机不存在，则不处理
          }

          var startPos = event.getUIStartLocation();
          var location = event.getUILocation(); //得到手指鼠标位置,得到的是世界坐标

          var posX = location.x - startPos.x + this._targetStartPos.x;
          var posY = location.y - startPos.y + this._targetStartPos.y;
          target.onControl(posX, posY); // 控制主飞机移动
        }
        /**
         * 触摸结束事件
         * @param {Event.EventTouch} event 触摸事件
         */


        onTouchEnd(event) {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.setTouchState(false);
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=fc47ced621650865bf3a7eb29dcbd9f3e24da14b.js.map