System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, ApplyBuff, ConParam, ConsumeItem, ConsumeMoney, EffectParam, EquipProp, GM, PlaneEffect, PlaneMaterial, PlaneProperty, PlanePropertyElem, PropInc, randStrategy, RatingParam, ResAchievement, ResActivity, ResBuffer, ResBullet, ResChapter, ResCondition, ResEffect, ResEnemy, ResEquip, ResEquipUpgrade, ResGameMode, ResGlobalAttr, ResItem, ResLevel, ResLevelGroup, ResLoot, ResLootItem, ResPlane, ResSkill, ResStage, ResTask, ResTask2, ResTaskGoal, ResTaskOrbit, ResTrack, ResUpgrade, ResWave, SkillCondition, TbGlobalAttr, TbEquipUpgrade, TbGM, TbPlane, TbResAchievement, TbResActivity, Tb<PERSON><PERSON><PERSON><PERSON>er, TbResBullet, TbResChapter, TbResEffect, TbResEnemy, TbResEquip, TbResGameMode, TbResItem, TbResLevel, TbResLevelGroup, TbResLoot, TbResSkill, TbResStage, TbResTask, TbResTaskOrbit, TbResTrack, TbResUpgrade, TbResWave, Tables, _crd, BindSocket, BuffType, BulletSourceType, BulletType, DamageType, EffectType, EquipClass, GMTabID, ItemEffectType, ItemUseType, ModeType, MoneyType, PlayCycle, PropName, QualityType, ResCondType, ResGoalType, ResLootType, ResPeriodType, ResTaskClass, SkillConditionType, TargetType, builtin;

  _export({
    ApplyBuff: void 0,
    ConParam: void 0,
    ConsumeItem: void 0,
    ConsumeMoney: void 0,
    EffectParam: void 0,
    EquipProp: void 0,
    GM: void 0,
    PlaneEffect: void 0,
    PlaneMaterial: void 0,
    PlaneProperty: void 0,
    PlanePropertyElem: void 0,
    PropInc: void 0,
    randStrategy: void 0,
    RatingParam: void 0,
    ResAchievement: void 0,
    ResActivity: void 0,
    ResBuffer: void 0,
    ResBullet: void 0,
    ResChapter: void 0,
    ResCondition: void 0,
    ResEffect: void 0,
    ResEnemy: void 0,
    ResEquip: void 0,
    ResEquipUpgrade: void 0,
    ResGameMode: void 0,
    ResGlobalAttr: void 0,
    ResItem: void 0,
    ResLevel: void 0,
    ResLevelGroup: void 0,
    ResLoot: void 0,
    ResLootItem: void 0,
    ResPlane: void 0,
    ResSkill: void 0,
    ResStage: void 0,
    ResTask: void 0,
    ResTask2: void 0,
    ResTaskGoal: void 0,
    ResTaskOrbit: void 0,
    ResTrack: void 0,
    ResUpgrade: void 0,
    ResWave: void 0,
    SkillCondition: void 0,
    TbGlobalAttr: void 0,
    TbEquipUpgrade: void 0,
    TbGM: void 0,
    TbPlane: void 0,
    TbResAchievement: void 0,
    TbResActivity: void 0,
    TbResBuffer: void 0,
    TbResBullet: void 0,
    TbResChapter: void 0,
    TbResEffect: void 0,
    TbResEnemy: void 0,
    TbResEquip: void 0,
    TbResGameMode: void 0,
    TbResItem: void 0,
    TbResLevel: void 0,
    TbResLevelGroup: void 0,
    TbResLoot: void 0,
    TbResSkill: void 0,
    TbResStage: void 0,
    TbResTask: void 0,
    TbResTaskOrbit: void 0,
    TbResTrack: void 0,
    TbResUpgrade: void 0,
    TbResWave: void 0,
    Tables: void 0,
    builtin: void 0
  });

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "0bb14MJuCNIcICZhJJlAcu5", "schema", undefined);

      //------------------------------------------------------------------------------
      // <auto-generated>
      //     This code was generated by a tool.
      //     Changes to this file may cause incorrect behavior and will be lost if
      //     the code is regenerated.
      // </auto-generated>
      //------------------------------------------------------------------------------

      /**
       * 特效绑定点
       */
      _export("BindSocket", BindSocket = /*#__PURE__*/function (BindSocket) {
        BindSocket[BindSocket["Self"] = 0] = "Self";
        BindSocket[BindSocket["Scene"] = 1] = "Scene";
        return BindSocket;
      }({}));
      /**
       * buff类型
       */


      _export("BuffType", BuffType = /*#__PURE__*/function (BuffType) {
        BuffType[BuffType["Positive"] = 0] = "Positive";
        BuffType[BuffType["Neutral"] = 1] = "Neutral";
        BuffType[BuffType["Negative"] = 2] = "Negative";
        return BuffType;
      }({}));
      /**
       * 子弹来源
       */


      _export("BulletSourceType", BulletSourceType = /*#__PURE__*/function (BulletSourceType) {
        BulletSourceType[BulletSourceType["MAINPLANE"] = 0] = "MAINPLANE";
        BulletSourceType[BulletSourceType["WINGPLANE"] = 1] = "WINGPLANE";
        BulletSourceType[BulletSourceType["ENEMYPLANE"] = 2] = "ENEMYPLANE";
        return BulletSourceType;
      }({}));
      /**
       * 子弹类型
       */


      _export("BulletType", BulletType = /*#__PURE__*/function (BulletType) {
        BulletType[BulletType["NORMAL"] = 0] = "NORMAL";
        BulletType[BulletType["NUCLEAR"] = 1] = "NUCLEAR";
        return BulletType;
      }({}));
      /**
       * 伤害类型
       */


      _export("DamageType", DamageType = /*#__PURE__*/function (DamageType) {
        DamageType[DamageType["ALL"] = 0] = "ALL";
        DamageType[DamageType["EXPLOSIVE"] = 1] = "EXPLOSIVE";
        DamageType[DamageType["NORMAL"] = 2] = "NORMAL";
        DamageType[DamageType["ENERGETIC"] = 3] = "ENERGETIC";
        DamageType[DamageType["PHYSICAL"] = 4] = "PHYSICAL";
        return DamageType;
      }({}));
      /**
       * 效果类型
       */


      _export("EffectType", EffectType = /*#__PURE__*/function (EffectType) {
        EffectType[EffectType["AttrMaxHPPer"] = 1] = "AttrMaxHPPer";
        EffectType[EffectType["AttrMaxHPAdd"] = 2] = "AttrMaxHPAdd";
        EffectType[EffectType["AttrHPRecoveryPer"] = 3] = "AttrHPRecoveryPer";
        EffectType[EffectType["AttrHPRecoveryAdd"] = 4] = "AttrHPRecoveryAdd";
        EffectType[EffectType["AttrHPRecoveryMaxHPPerAdd"] = 5] = "AttrHPRecoveryMaxHPPerAdd";
        EffectType[EffectType["HealMaxHPPer"] = 6] = "HealMaxHPPer";
        EffectType[EffectType["HealLoseHPPer"] = 7] = "HealLoseHPPer";
        EffectType[EffectType["HealHP"] = 8] = "HealHP";
        EffectType[EffectType["AttrAttackPer"] = 9] = "AttrAttackPer";
        EffectType[EffectType["AttrAttackAdd"] = 10] = "AttrAttackAdd";
        EffectType[EffectType["AttrAttackBossPer"] = 11] = "AttrAttackBossPer";
        EffectType[EffectType["AttrAttackNormalPer"] = 12] = "AttrAttackNormalPer";
        EffectType[EffectType["AttrFortunatePer"] = 13] = "AttrFortunatePer";
        EffectType[EffectType["AttrFortunateAdd"] = 14] = "AttrFortunateAdd";
        EffectType[EffectType["AttrMissAdd"] = 15] = "AttrMissAdd";
        EffectType[EffectType["AttrBulletHurtResistancePer"] = 16] = "AttrBulletHurtResistancePer";
        EffectType[EffectType["AttrBulletHurtResistanceAdd"] = 17] = "AttrBulletHurtResistanceAdd";
        EffectType[EffectType["AttrCollisionHurtDerateAdd"] = 18] = "AttrCollisionHurtDerateAdd";
        EffectType[EffectType["AttrCollisionHurtResistancePer"] = 19] = "AttrCollisionHurtResistancePer";
        EffectType[EffectType["AttrCollisionHurtResistanceAdd"] = 20] = "AttrCollisionHurtResistanceAdd";
        EffectType[EffectType["AttrBulletHurtDerateAdd"] = 21] = "AttrBulletHurtDerateAdd";
        EffectType[EffectType["AttrFinalScoreAdd"] = 22] = "AttrFinalScoreAdd";
        EffectType[EffectType["AttrKillScoreAdd"] = 23] = "AttrKillScoreAdd";
        EffectType[EffectType["AttrEnergyRecoveryPerAdd"] = 24] = "AttrEnergyRecoveryPerAdd";
        EffectType[EffectType["AttrEnergyRecoveryAdd"] = 25] = "AttrEnergyRecoveryAdd";
        EffectType[EffectType["AttrPickRadiusPer"] = 26] = "AttrPickRadiusPer";
        EffectType[EffectType["AttrPickRadiusAdd"] = 27] = "AttrPickRadiusAdd";
        EffectType[EffectType["ApplyBuff"] = 28] = "ApplyBuff";
        EffectType[EffectType["ImmuneBulletHurt"] = 29] = "ImmuneBulletHurt";
        EffectType[EffectType["ImmuneCollisionHurt"] = 30] = "ImmuneCollisionHurt";
        EffectType[EffectType["IgnoreBullet"] = 31] = "IgnoreBullet";
        EffectType[EffectType["IgnoreCollision"] = 32] = "IgnoreCollision";
        EffectType[EffectType["ImmuneBombHurt"] = 33] = "ImmuneBombHurt";
        EffectType[EffectType["ImmuneActiveSkillHurt"] = 34] = "ImmuneActiveSkillHurt";
        EffectType[EffectType["Invincible"] = 35] = "Invincible";
        EffectType[EffectType["AttrBombMax"] = 36] = "AttrBombMax";
        EffectType[EffectType["BulletAttackAdd"] = 37] = "BulletAttackAdd";
        EffectType[EffectType["BulletAttackPer"] = 38] = "BulletAttackPer";
        EffectType[EffectType["HurtMaxHPPer"] = 39] = "HurtMaxHPPer";
        EffectType[EffectType["HurtCurHPPer"] = 40] = "HurtCurHPPer";
        EffectType[EffectType["AttrBombHurtAdd"] = 41] = "AttrBombHurtAdd";
        EffectType[EffectType["AttrBombHurtPer"] = 42] = "AttrBombHurtPer";
        EffectType[EffectType["Kill"] = 43] = "Kill";
        EffectType[EffectType["Hurt"] = 44] = "Hurt";
        return EffectType;
      }({}));
      /**
       * 装备部位
       */


      _export("EquipClass", EquipClass = /*#__PURE__*/function (EquipClass) {
        EquipClass[EquipClass["NONE"] = 0] = "NONE";
        EquipClass[EquipClass["WEAPON"] = 1] = "WEAPON";
        EquipClass[EquipClass["SUB_WEAPON"] = 2] = "SUB_WEAPON";
        EquipClass[EquipClass["ARMOR"] = 3] = "ARMOR";
        EquipClass[EquipClass["TECHNIC"] = 4] = "TECHNIC";
        return EquipClass;
      }({}));
      /**
       * GM命令页签
       */


      _export("GMTabID", GMTabID = /*#__PURE__*/function (GMTabID) {
        GMTabID[GMTabID["COMMON"] = 0] = "COMMON";
        GMTabID[GMTabID["BATTLE"] = 1] = "BATTLE";
        return GMTabID;
      }({}));
      /**
       * 道具的使用效果
       */


      _export("ItemEffectType", ItemEffectType = /*#__PURE__*/function (ItemEffectType) {
        ItemEffectType[ItemEffectType["NONE"] = 0] = "NONE";
        ItemEffectType[ItemEffectType["DROP"] = 1] = "DROP";
        ItemEffectType[ItemEffectType["GEN_GOLD"] = 2] = "GEN_GOLD";
        ItemEffectType[ItemEffectType["GEN_DIAMOND"] = 3] = "GEN_DIAMOND";
        ItemEffectType[ItemEffectType["GEN_XP"] = 4] = "GEN_XP";
        ItemEffectType[ItemEffectType["GEN_ENERGY"] = 5] = "GEN_ENERGY";
        ItemEffectType[ItemEffectType["GEN_ITEM"] = 6] = "GEN_ITEM";
        return ItemEffectType;
      }({}));
      /**
       * 道具的使用类型
       */


      _export("ItemUseType", ItemUseType = /*#__PURE__*/function (ItemUseType) {
        ItemUseType[ItemUseType["NONE"] = 0] = "NONE";
        ItemUseType[ItemUseType["MANUAL"] = 1] = "MANUAL";
        ItemUseType[ItemUseType["AUTO"] = 2] = "AUTO";
        return ItemUseType;
      }({}));
      /**
       * 模式类型
       */


      _export("ModeType", ModeType = /*#__PURE__*/function (ModeType) {
        ModeType[ModeType["ENDLESS"] = 0] = "ENDLESS";
        ModeType[ModeType["STORY"] = 1] = "STORY";
        ModeType[ModeType["EXPEDITION"] = 2] = "EXPEDITION";
        ModeType[ModeType["ENDLESSPK"] = 3] = "ENDLESSPK";
        ModeType[ModeType["FRIENDPK"] = 4] = "FRIENDPK";
        return ModeType;
      }({}));
      /**
       * 货币类型
       */


      _export("MoneyType", MoneyType = /*#__PURE__*/function (MoneyType) {
        MoneyType[MoneyType["NONE"] = 0] = "NONE";
        MoneyType[MoneyType["GOLD"] = 1] = "GOLD";
        MoneyType[MoneyType["DIAMOND"] = 2] = "DIAMOND";
        MoneyType[MoneyType["POWER"] = 3] = "POWER";
        MoneyType[MoneyType["ITEM"] = 4] = "ITEM";
        return MoneyType;
      }({}));
      /**
       * 模式类型
       */


      _export("PlayCycle", PlayCycle = /*#__PURE__*/function (PlayCycle) {
        PlayCycle[PlayCycle["DAY"] = 0] = "DAY";
        PlayCycle[PlayCycle["WEEK"] = 1] = "WEEK";
        return PlayCycle;
      }({}));
      /**
       * 装备属性名称
       */


      _export("PropName", PropName = /*#__PURE__*/function (PropName) {
        PropName[PropName["NONE"] = 0] = "NONE";
        PropName[PropName["HURT"] = 1] = "HURT";
        PropName[PropName["HP"] = 2] = "HP";
        return PropName;
      }({}));
      /**
       * 普通(Common)、精良(Uncommon)、稀有(Rare)、史诗(Epic)、传说(Legendary)、神话(Mythic)
       */


      _export("QualityType", QualityType = /*#__PURE__*/function (QualityType) {
        QualityType[QualityType["NONE"] = 0] = "NONE";
        QualityType[QualityType["COMMON"] = 1] = "COMMON";
        QualityType[QualityType["UNCOMMON"] = 2] = "UNCOMMON";
        QualityType[QualityType["RACE"] = 3] = "RACE";
        QualityType[QualityType["EPIC"] = 4] = "EPIC";
        QualityType[QualityType["LEGENDARY"] = 5] = "LEGENDARY";
        QualityType[QualityType["MYTHIC"] = 6] = "MYTHIC";
        return QualityType;
      }({}));

      _export("ResCondType", ResCondType = /*#__PURE__*/function (ResCondType) {
        ResCondType[ResCondType["NONE"] = 0] = "NONE";
        ResCondType[ResCondType["LEVEL"] = 1] = "LEVEL";
        ResCondType[ResCondType["STAGE"] = 2] = "STAGE";
        ResCondType[ResCondType["STAR"] = 3] = "STAR";
        ResCondType[ResCondType["FORCE"] = 4] = "FORCE";
        ResCondType[ResCondType["ACTIVITY"] = 5] = "ACTIVITY";
        return ResCondType;
      }({}));

      _export("ResGoalType", ResGoalType = /*#__PURE__*/function (ResGoalType) {
        ResGoalType[ResGoalType["MODE_PASS_TIMES"] = 1] = "MODE_PASS_TIMES";
        ResGoalType[ResGoalType["STAGE_PASS_TIMES"] = 2] = "STAGE_PASS_TIMES";
        ResGoalType[ResGoalType["KILL_MONSTER_CLASS_COUNT"] = 3] = "KILL_MONSTER_CLASS_COUNT";
        ResGoalType[ResGoalType["LOTTERY_TIMES"] = 4] = "LOTTERY_TIMES";
        ResGoalType[ResGoalType["AFK_TIMES"] = 5] = "AFK_TIMES";
        ResGoalType[ResGoalType["CHARGE_AMOUNT"] = 6] = "CHARGE_AMOUNT";
        ResGoalType[ResGoalType["BUY_ITEM_COUNT"] = 7] = "BUY_ITEM_COUNT";
        ResGoalType[ResGoalType["WATCH_AD_TIMES"] = 8] = "WATCH_AD_TIMES";
        ResGoalType[ResGoalType["LOGIN_DAYS"] = 9] = "LOGIN_DAYS";
        ResGoalType[ResGoalType["ROLE_LEVEL"] = 10] = "ROLE_LEVEL";
        ResGoalType[ResGoalType["CONSUME_GOLD"] = 11] = "CONSUME_GOLD";
        ResGoalType[ResGoalType["CONSUME_DIAMOND"] = 12] = "CONSUME_DIAMOND";
        ResGoalType[ResGoalType["CONSUME_ENERGY"] = 13] = "CONSUME_ENERGY";
        ResGoalType[ResGoalType["CONSUME_ITEM"] = 14] = "CONSUME_ITEM";
        ResGoalType[ResGoalType["EQUIP_QUALITY"] = 15] = "EQUIP_QUALITY";
        ResGoalType[ResGoalType["EQUIP_LEVEL"] = 16] = "EQUIP_LEVEL";
        ResGoalType[ResGoalType["EQUIP_COMB_TIMES"] = 17] = "EQUIP_COMB_TIMES";
        ResGoalType[ResGoalType["EQUIP_UPGRADE_TIMES"] = 18] = "EQUIP_UPGRADE_TIMES";
        ResGoalType[ResGoalType["GUILD_DONATE_TIMES"] = 19] = "GUILD_DONATE_TIMES";
        ResGoalType[ResGoalType["FIGHTER_UNLOCK_COUNT"] = 20] = "FIGHTER_UNLOCK_COUNT";
        ResGoalType[ResGoalType["FIGHTER_STAR_TOTAL"] = 21] = "FIGHTER_STAR_TOTAL";
        ResGoalType[ResGoalType["ROLE_FORCE"] = 22] = "ROLE_FORCE";
        ResGoalType[ResGoalType["SPECIFY_BUG_CONSUME"] = 23] = "SPECIFY_BUG_CONSUME";
        ResGoalType[ResGoalType["USE_ITEM_TIMES"] = 24] = "USE_ITEM_TIMES";
        ResGoalType[ResGoalType["DAILY_SIGN_IN"] = 25] = "DAILY_SIGN_IN";
        ResGoalType[ResGoalType["MODE_ENTER_TIMES"] = 26] = "MODE_ENTER_TIMES";
        ResGoalType[ResGoalType["STAGE_ENTER_TIMES"] = 27] = "STAGE_ENTER_TIMES";
        ResGoalType[ResGoalType["ORBIT_VALUE"] = 28] = "ORBIT_VALUE";
        return ResGoalType;
      }({}));

      _export("ResLootType", ResLootType = /*#__PURE__*/function (ResLootType) {
        ResLootType[ResLootType["SINGLE"] = 1] = "SINGLE";
        ResLootType[ResLootType["MULTI"] = 2] = "MULTI";
        return ResLootType;
      }({}));

      _export("ResPeriodType", ResPeriodType = /*#__PURE__*/function (ResPeriodType) {
        ResPeriodType[ResPeriodType["SINGLE"] = 0] = "SINGLE";
        ResPeriodType[ResPeriodType["DAILY"] = 1] = "DAILY";
        ResPeriodType[ResPeriodType["WEEKLY"] = 2] = "WEEKLY";
        ResPeriodType[ResPeriodType["MONTHLY"] = 3] = "MONTHLY";
        return ResPeriodType;
      }({}));

      _export("ResTaskClass", ResTaskClass = /*#__PURE__*/function (ResTaskClass) {
        ResTaskClass[ResTaskClass["NONE"] = 0] = "NONE";
        ResTaskClass[ResTaskClass["DAILY_TASK"] = 1] = "DAILY_TASK";
        ResTaskClass[ResTaskClass["WEEKLY_TASK"] = 2] = "WEEKLY_TASK";
        ResTaskClass[ResTaskClass["MONTHLY_TASK"] = 3] = "MONTHLY_TASK";
        ResTaskClass[ResTaskClass["CHALLENGE"] = 4] = "CHALLENGE";
        ResTaskClass[ResTaskClass["ACTIVITY"] = 5] = "ACTIVITY";
        ResTaskClass[ResTaskClass["TASK_ORBIT"] = 8] = "TASK_ORBIT";
        ResTaskClass[ResTaskClass["ACHIEVEMENT"] = 9] = "ACHIEVEMENT";
        return ResTaskClass;
      }({}));
      /**
       * 技能/buff条件
       */


      _export("SkillConditionType", SkillConditionType = /*#__PURE__*/function (SkillConditionType) {
        SkillConditionType[SkillConditionType["NONE"] = 0] = "NONE";
        return SkillConditionType;
      }({}));
      /**
       * 目标类型
       */


      _export("TargetType", TargetType = /*#__PURE__*/function (TargetType) {
        TargetType[TargetType["Self"] = 0] = "Self";
        TargetType[TargetType["Main"] = 1] = "Main";
        TargetType[TargetType["MainFriendly"] = 2] = "MainFriendly";
        TargetType[TargetType["Enemy"] = 3] = "Enemy";
        TargetType[TargetType["BossEnemy"] = 4] = "BossEnemy";
        TargetType[TargetType["NormalEnemy"] = 5] = "NormalEnemy";
        return TargetType;
      }({}));

      _export("ApplyBuff", ApplyBuff = class ApplyBuff {
        constructor(_json_) {
          this.target = void 0;
          this.buffID = void 0;

          if (_json_.target === undefined) {
            throw new Error();
          }

          this.target = _json_.target;

          if (_json_.buffID === undefined) {
            throw new Error();
          }

          this.buffID = _json_.buffID;
        }

        resolve(tables) {}

      });

      (function (_builtin3) {
        class vector2 {
          constructor(_json_) {
            this.x = void 0;
            this.y = void 0;

            if (_json_.x === undefined) {
              throw new Error();
            }

            this.x = _json_.x;

            if (_json_.y === undefined) {
              throw new Error();
            }

            this.y = _json_.y;
          }

          resolve(tables) {}

        }

        _builtin3.vector2 = vector2;
      })(builtin || _export("builtin", builtin = {}));

      (function (_builtin) {
        class vector3 {
          constructor(_json_) {
            this.x = void 0;
            this.y = void 0;
            this.z = void 0;

            if (_json_.x === undefined) {
              throw new Error();
            }

            this.x = _json_.x;

            if (_json_.y === undefined) {
              throw new Error();
            }

            this.y = _json_.y;

            if (_json_.z === undefined) {
              throw new Error();
            }

            this.z = _json_.z;
          }

          resolve(tables) {}

        }

        _builtin.vector3 = vector3;
      })(builtin || _export("builtin", builtin = {}));

      (function (_builtin2) {
        class vector4 {
          constructor(_json_) {
            this.x = void 0;
            this.y = void 0;
            this.z = void 0;
            this.w = void 0;

            if (_json_.x === undefined) {
              throw new Error();
            }

            this.x = _json_.x;

            if (_json_.y === undefined) {
              throw new Error();
            }

            this.y = _json_.y;

            if (_json_.z === undefined) {
              throw new Error();
            }

            this.z = _json_.z;

            if (_json_.w === undefined) {
              throw new Error();
            }

            this.w = _json_.w;
          }

          resolve(tables) {}

        }

        _builtin2.vector4 = vector4;
      })(builtin || _export("builtin", builtin = {}));

      _export("ConParam", ConParam = class ConParam {
        constructor(_json_) {
          this.con = void 0;
          this.param = void 0;

          if (_json_.con === undefined) {
            throw new Error();
          }

          this.con = _json_.con;

          if (_json_.param === undefined) {
            throw new Error();
          }

          this.param = _json_.param;
        }

        resolve(tables) {}

      });
      /**
       * 消耗的材料
       */


      _export("ConsumeItem", ConsumeItem = class ConsumeItem {
        constructor(_json_) {
          this.id = void 0;
          this.num = void 0;

          if (_json_.id === undefined) {
            throw new Error();
          }

          this.id = _json_.id;

          if (_json_.num === undefined) {
            throw new Error();
          }

          this.num = _json_.num;
        }

        resolve(tables) {}

      });

      _export("ConsumeMoney", ConsumeMoney = class ConsumeMoney {
        constructor(_json_) {
          /**
           * 货币类型
           */
          this.type = void 0;

          /**
           * 货币数量
           */
          this.num = void 0;

          if (_json_.type === undefined) {
            throw new Error();
          }

          this.type = _json_.type;

          if (_json_.num === undefined) {
            throw new Error();
          }

          this.num = _json_.num;
        }

        resolve(tables) {}

      });

      _export("EffectParam", EffectParam = class EffectParam {
        constructor(_json_) {
          this.type = void 0;
          this.target = void 0;
          this.param = void 0;

          if (_json_.type === undefined) {
            throw new Error();
          }

          this.type = _json_.type;

          if (_json_.target === undefined) {
            throw new Error();
          }

          this.target = _json_.target;

          if (_json_.param === undefined) {
            throw new Error();
          }

          {
            this.param = [];

            for (var _ele0 of _json_.param) {
              var _e0 = void 0;

              _e0 = _ele0;
              this.param.push(_e0);
            }
          }
        }

        resolve(tables) {}

      });
      /**
       * 装备属性
       */


      _export("EquipProp", EquipProp = class EquipProp {
        constructor(_json_) {
          this.id = void 0;
          this.value = void 0;

          if (_json_.id === undefined) {
            throw new Error();
          }

          this.id = _json_.id;

          if (_json_.value === undefined) {
            throw new Error();
          }

          this.value = _json_.value;
        }

        resolve(tables) {}

      });

      _export("GM", GM = class GM {
        constructor(_json_) {
          /**
           * 页签ID
           */
          this.tabID = void 0;

          /**
           * 页签名称
           */
          this.tabName = void 0;

          /**
           * 按钮名称
           */
          this.name = void 0;

          /**
           * 命令
           */
          this.cmd = void 0;

          /**
           * 描述
           */
          this.desc = void 0;

          if (_json_.tabID === undefined) {
            throw new Error();
          }

          this.tabID = _json_.tabID;

          if (_json_.tabName === undefined) {
            throw new Error();
          }

          this.tabName = _json_.tabName;

          if (_json_.name === undefined) {
            throw new Error();
          }

          this.name = _json_.name;

          if (_json_.cmd === undefined) {
            throw new Error();
          }

          this.cmd = _json_.cmd;

          if (_json_.desc === undefined) {
            throw new Error();
          }

          this.desc = _json_.desc;
        }

        resolve(tables) {}

      });
      /**
       * 战机效果
       */


      _export("PlaneEffect", PlaneEffect = class PlaneEffect {
        constructor(_json_) {
          this.effectId = void 0;

          if (_json_.effect_id === undefined) {
            throw new Error();
          }

          this.effectId = _json_.effect_id;
        }

        resolve(tables) {}

      });
      /**
       * 升星材料
       */


      _export("PlaneMaterial", PlaneMaterial = class PlaneMaterial {
        constructor(_json_) {
          this.materialId = void 0;
          this.materialCount = void 0;

          if (_json_.material_id === undefined) {
            throw new Error();
          }

          this.materialId = _json_.material_id;

          if (_json_.material_count === undefined) {
            throw new Error();
          }

          this.materialCount = _json_.material_count;
        }

        resolve(tables) {}

      });
      /**
       * 战机属性
       */


      _export("PlaneProperty", PlaneProperty = class PlaneProperty {
        constructor(_json_) {
          this.MaxHP = void 0;
          this.HPRecovery = void 0;
          this.Attack = void 0;
          this.Fortunate = void 0;
          this.Miss = void 0;
          this.BulletHurtResistance = void 0;
          this.CollisionHurtResistance = void 0;
          this.PickRadius = void 0;
          this.FinalScore = void 0;
          this.BombMax = void 0;
          this.MaxEnergy = void 0;
          this.EnergyRecovery = void 0;

          if (_json_.MaxHP === undefined) {
            throw new Error();
          }

          this.MaxHP = _json_.MaxHP;

          if (_json_.HPRecovery === undefined) {
            throw new Error();
          }

          this.HPRecovery = _json_.HPRecovery;

          if (_json_.Attack === undefined) {
            throw new Error();
          }

          this.Attack = _json_.Attack;

          if (_json_.Fortunate === undefined) {
            throw new Error();
          }

          this.Fortunate = _json_.Fortunate;

          if (_json_.Miss === undefined) {
            throw new Error();
          }

          this.Miss = _json_.Miss;

          if (_json_.BulletHurtResistance === undefined) {
            throw new Error();
          }

          this.BulletHurtResistance = _json_.BulletHurtResistance;

          if (_json_.CollisionHurtResistance === undefined) {
            throw new Error();
          }

          this.CollisionHurtResistance = _json_.CollisionHurtResistance;

          if (_json_.PickRadius === undefined) {
            throw new Error();
          }

          this.PickRadius = _json_.PickRadius;

          if (_json_.FinalScore === undefined) {
            throw new Error();
          }

          this.FinalScore = _json_.FinalScore;

          if (_json_.BombMax === undefined) {
            throw new Error();
          }

          this.BombMax = _json_.BombMax;

          if (_json_.MaxEnergy === undefined) {
            throw new Error();
          }

          this.MaxEnergy = _json_.MaxEnergy;

          if (_json_.EnergyRecovery === undefined) {
            throw new Error();
          }

          this.EnergyRecovery = _json_.EnergyRecovery;
        }

        resolve(tables) {}

      });
      /**
       * 战机属性
       */


      _export("PlanePropertyElem", PlanePropertyElem = class PlanePropertyElem {
        constructor(_json_) {
          this.propType = void 0;
          this.propParam = void 0;

          if (_json_.prop_type === undefined) {
            throw new Error();
          }

          this.propType = _json_.prop_type;

          if (_json_.prop_param === undefined) {
            throw new Error();
          }

          {
            this.propParam = [];

            for (var _ele0 of _json_.prop_param) {
              var _e0 = void 0;

              _e0 = _ele0;
              this.propParam.push(_e0);
            }
          }
        }

        resolve(tables) {}

      });
      /**
       * 属性增幅
       */


      _export("PropInc", PropInc = class PropInc {
        constructor(_json_) {
          this.id = void 0;

          /**
           * 万分比
           */
          this.inc = void 0;

          if (_json_.id === undefined) {
            throw new Error();
          }

          this.id = _json_.id;

          if (_json_.inc === undefined) {
            throw new Error();
          }

          this.inc = _json_.inc;
        }

        resolve(tables) {}

      });
      /**
       * 随机策略
       */


      _export("randStrategy", randStrategy = class randStrategy {
        constructor(_json_) {
          /**
           * 随机策略ID
           */
          this.ID = void 0;

          /**
           * ID的权重
           */
          this.Weight = void 0;

          if (_json_.ID === undefined) {
            throw new Error();
          }

          this.ID = _json_.ID;

          if (_json_.Weight === undefined) {
            throw new Error();
          }

          this.Weight = _json_.Weight;
        }

        resolve(tables) {}

      });

      _export("RatingParam", RatingParam = class RatingParam {
        constructor(_json_) {
          this.rating = void 0;
          this.param = void 0;

          if (_json_.rating === undefined) {
            throw new Error();
          }

          this.rating = _json_.rating;

          if (_json_.param === undefined) {
            throw new Error();
          }

          this.param = _json_.param;
        }

        resolve(tables) {}

      });

      _export("ResAchievement", ResAchievement = class ResAchievement {
        constructor(_json_) {
          /**
           * 成就 ID
           */
          this.taskId = void 0;

          /**
           * 成就集 ID
           */
          this.groupId = void 0;

          /**
           * 前置ID
           */
          this.prevId = void 0;

          /**
           * 重置周期
           */
          this.periodType = void 0;
          this.taskCond = void 0;
          this.taskGoal = void 0;

          /**
           * 是否累积
           */
          this.accumulate = void 0;

          /**
           * 奖励ID
           */
          this.rewardId = void 0;

          /**
           * 追踪
           */
          this.linkTo = void 0;

          if (_json_.task_id === undefined) {
            throw new Error();
          }

          this.taskId = _json_.task_id;

          if (_json_.group_id === undefined) {
            throw new Error();
          }

          this.groupId = _json_.group_id;

          if (_json_.prev_id === undefined) {
            throw new Error();
          }

          this.prevId = _json_.prev_id;

          if (_json_.period_type === undefined) {
            throw new Error();
          }

          this.periodType = _json_.period_type;

          if (_json_.task_cond === undefined) {
            throw new Error();
          }

          {
            this.taskCond = [];

            for (var _ele0 of _json_.task_cond) {
              var _e0 = void 0;

              _e0 = new ResCondition(_ele0);
              this.taskCond.push(_e0);
            }
          }

          if (_json_.task_goal === undefined) {
            throw new Error();
          }

          this.taskGoal = new ResTaskGoal(_json_.task_goal);

          if (_json_.accumulate === undefined) {
            throw new Error();
          }

          this.accumulate = _json_.accumulate;

          if (_json_.reward_id === undefined) {
            throw new Error();
          }

          this.rewardId = _json_.reward_id;

          if (_json_.link_to === undefined) {
            throw new Error();
          }

          this.linkTo = _json_.link_to;
        }

        resolve(tables) {
          var _this$taskGoal;

          for (var _e of this.taskCond) {
            _e == null || _e.resolve(tables);
          }

          (_this$taskGoal = this.taskGoal) == null || _this$taskGoal.resolve(tables);
        }

      });

      _export("ResActivity", ResActivity = class ResActivity {
        constructor(_json_) {
          /**
           * 活动ID
           */
          this.taskId = void 0;

          /**
           * 活动集ID
           */
          this.groupId = void 0;

          /**
           * 说明
           */
          this.desc = void 0;

          /**
           * UI资源配置
           */
          this.uiRes = void 0;

          /**
           * 前置ID
           */
          this.precvId = void 0;

          /**
           * 重置周期
           */
          this.periodType = void 0;

          /**
           * 条件类型
           */
          this.taskCond = void 0;
          this.taskGoal = void 0;

          /**
           * 目标是否累积
           */
          this.accumlate = void 0;

          /**
           * 奖励ID
           */
          this.rewardId = void 0;

          /**
           * 开放日期
           */
          this.openDate = void 0;

          /**
           * 开放时间
           */
          this.openTime = void 0;

          /**
           * 结束日期
           */
          this.closeDate = void 0;

          /**
           * 结束时间
           */
          this.closeTime = void 0;

          /**
           * 活动追踪
           */
          this.linkTo = void 0;

          if (_json_.task_id === undefined) {
            throw new Error();
          }

          this.taskId = _json_.task_id;

          if (_json_.group_id === undefined) {
            throw new Error();
          }

          this.groupId = _json_.group_id;

          if (_json_.desc === undefined) {
            throw new Error();
          }

          this.desc = _json_.desc;

          if (_json_.ui_res === undefined) {
            throw new Error();
          }

          this.uiRes = _json_.ui_res;

          if (_json_.precv_id === undefined) {
            throw new Error();
          }

          this.precvId = _json_.precv_id;

          if (_json_.period_type === undefined) {
            throw new Error();
          }

          this.periodType = _json_.period_type;

          if (_json_.task_cond === undefined) {
            throw new Error();
          }

          {
            this.taskCond = [];

            for (var _ele0 of _json_.task_cond) {
              var _e0 = void 0;

              _e0 = new ResCondition(_ele0);
              this.taskCond.push(_e0);
            }
          }

          if (_json_.task_goal === undefined) {
            throw new Error();
          }

          this.taskGoal = new ResTaskGoal(_json_.task_goal);

          if (_json_.accumlate === undefined) {
            throw new Error();
          }

          this.accumlate = _json_.accumlate;

          if (_json_.reward_id === undefined) {
            throw new Error();
          }

          this.rewardId = _json_.reward_id;

          if (_json_.open_date === undefined) {
            throw new Error();
          }

          this.openDate = _json_.open_date;

          if (_json_.open_time === undefined) {
            throw new Error();
          }

          this.openTime = _json_.open_time;

          if (_json_.close_date === undefined) {
            throw new Error();
          }

          this.closeDate = _json_.close_date;

          if (_json_.close_time === undefined) {
            throw new Error();
          }

          this.closeTime = _json_.close_time;

          if (_json_.link_to === undefined) {
            throw new Error();
          }

          this.linkTo = _json_.link_to;
        }

        resolve(tables) {
          var _this$taskGoal2;

          for (var _e of this.taskCond) {
            _e == null || _e.resolve(tables);
          }

          (_this$taskGoal2 = this.taskGoal) == null || _this$taskGoal2.resolve(tables);
        }

      });

      _export("ResBuffer", ResBuffer = class ResBuffer {
        constructor(_json_) {
          /**
           * ID
           */
          this.id = void 0;

          /**
           * 类别
           */
          this.buffType = void 0;

          /**
           * Buff触发条件
           */
          this.conditionID = void 0;

          /**
           * 持续时间
           */
          this.duration = void 0;

          /**
           * 持续时间加成
           */
          this.durationBonus = void 0;

          /**
           * 最大叠加次数
           */
          this.maxStack = void 0;

          /**
           * 叠加刷新策略
           */
          this.refreshType = void 0;

          /**
           * 周期
           */
          this.cycle = void 0;

          /**
           * 周期计数
           */
          this.cycleTimes = void 0;

          /**
           * 特效Prefab
           */
          this.EffectPath = void 0;

          /**
           * 特效挂点
           */
          this.EffectSocket = void 0;

          /**
           * 特效偏移
           */
          this.EffectPos = void 0;
          this.effects = void 0;

          if (_json_.id === undefined) {
            throw new Error();
          }

          this.id = _json_.id;

          if (_json_.buffType === undefined) {
            throw new Error();
          }

          this.buffType = _json_.buffType;

          if (_json_.conditionID === undefined) {
            throw new Error();
          }

          this.conditionID = _json_.conditionID;

          if (_json_.duration === undefined) {
            throw new Error();
          }

          this.duration = _json_.duration;

          if (_json_.durationBonus === undefined) {
            throw new Error();
          }

          this.durationBonus = _json_.durationBonus;

          if (_json_.maxStack === undefined) {
            throw new Error();
          }

          this.maxStack = _json_.maxStack;

          if (_json_.refreshType === undefined) {
            throw new Error();
          }

          this.refreshType = _json_.refreshType;

          if (_json_.cycle === undefined) {
            throw new Error();
          }

          this.cycle = _json_.cycle;

          if (_json_.cycleTimes === undefined) {
            throw new Error();
          }

          this.cycleTimes = _json_.cycleTimes;

          if (_json_.EffectPath === undefined) {
            throw new Error();
          }

          this.EffectPath = _json_.EffectPath;

          if (_json_.EffectSocket === undefined) {
            throw new Error();
          }

          this.EffectSocket = _json_.EffectSocket;

          if (_json_.EffectPos === undefined) {
            throw new Error();
          }

          this.EffectPos = new builtin.vector2(_json_.EffectPos);

          if (_json_.effects === undefined) {
            throw new Error();
          }

          {
            this.effects = [];

            for (var _ele0 of _json_.effects) {
              var _e0 = void 0;

              _e0 = new EffectParam(_ele0);
              this.effects.push(_e0);
            }
          }
        }

        resolve(tables) {}

      });

      _export("ResBullet", ResBullet = class ResBullet {
        constructor(_json_) {
          /**
           * ID
           */
          this.id = void 0;

          /**
           * 名称
           */
          this.name = void 0;

          /**
           * 子弹来源
           */
          this.source = void 0;

          /**
           * 子弹类型
           */
          this.type = void 0;

          /**
           * 子弹伤害类型
           */
          this.damageType = void 0;

          /**
           * 子弹Prefab
           */
          this.prefab = void 0;

          /**
           * 攻击转换系数%
           */
          this.attackCoefficient = void 0;

          /**
           * 穿透次数
           */
          this.penetrationCount = void 0;

          /**
           * 穿透伤害冷却
           */
          this.penetrationCooldown = void 0;

          /**
           * 命中反弹次数
           */
          this.hitBounceCount = void 0;

          /**
           * 屏幕反弹次数
           */
          this.boundaryBounceCount = void 0;

          if (_json_.id === undefined) {
            throw new Error();
          }

          this.id = _json_.id;

          if (_json_.name === undefined) {
            throw new Error();
          }

          this.name = _json_.name;

          if (_json_.source === undefined) {
            throw new Error();
          }

          this.source = _json_.source;

          if (_json_.type === undefined) {
            throw new Error();
          }

          this.type = _json_.type;

          if (_json_.damage_type === undefined) {
            throw new Error();
          }

          this.damageType = _json_.damage_type;

          if (_json_.prefab === undefined) {
            throw new Error();
          }

          this.prefab = _json_.prefab;

          if (_json_.attack_coefficient === undefined) {
            throw new Error();
          }

          this.attackCoefficient = _json_.attack_coefficient;

          if (_json_.penetration_count === undefined) {
            throw new Error();
          }

          this.penetrationCount = _json_.penetration_count;

          if (_json_.penetration_cooldown === undefined) {
            throw new Error();
          }

          this.penetrationCooldown = _json_.penetration_cooldown;

          if (_json_.hit_bounce_count === undefined) {
            throw new Error();
          }

          this.hitBounceCount = _json_.hit_bounce_count;

          if (_json_.boundary_bounce_count === undefined) {
            throw new Error();
          }

          this.boundaryBounceCount = _json_.boundary_bounce_count;
        }

        resolve(tables) {}

      });

      _export("ResChapter", ResChapter = class ResChapter {
        constructor(_json_) {
          /**
           * 章节ID
           */
          this.id = void 0;

          /**
           * 章节关卡数量
           */
          this.levelCount = void 0;

          /**
           * 章节关卡组数量
           */
          this.levelGroupCount = void 0;

          /**
           * 1=随机<br/>2=随机不重复<br/>3=顺序重复
           */
          this.strategy = void 0;

          /**
           * 章节伤害加成
           */
          this.damageBonus = void 0;

          /**
           * 章节生命加成
           */
          this.lifeBounus = void 0;
          this.strategyList = void 0;

          if (_json_.id === undefined) {
            throw new Error();
          }

          this.id = _json_.id;

          if (_json_.levelCount === undefined) {
            throw new Error();
          }

          this.levelCount = _json_.levelCount;

          if (_json_.levelGroupCount === undefined) {
            throw new Error();
          }

          this.levelGroupCount = _json_.levelGroupCount;

          if (_json_.strategy === undefined) {
            throw new Error();
          }

          this.strategy = _json_.strategy;

          if (_json_.damageBonus === undefined) {
            throw new Error();
          }

          this.damageBonus = _json_.damageBonus;

          if (_json_.lifeBounus === undefined) {
            throw new Error();
          }

          this.lifeBounus = _json_.lifeBounus;

          if (_json_.strategyList === undefined) {
            throw new Error();
          }

          {
            this.strategyList = [];

            for (var _ele0 of _json_.strategyList) {
              var _e0 = void 0;

              _e0 = new randStrategy(_ele0);
              this.strategyList.push(_e0);
            }
          }
        }

        resolve(tables) {
          for (var _e of this.strategyList) {
            _e == null || _e.resolve(tables);
          }
        }

      });

      _export("ResCondition", ResCondition = class ResCondition {
        constructor(_json_) {
          this.condType = void 0;
          this.params = void 0;

          if (_json_.cond_type === undefined) {
            throw new Error();
          }

          this.condType = _json_.cond_type;

          if (_json_.params === undefined) {
            throw new Error();
          }

          {
            this.params = [];

            for (var _ele0 of _json_.params) {
              var _e0 = void 0;

              _e0 = _ele0;
              this.params.push(_e0);
            }
          }
        }

        resolve(tables) {}

      });

      _export("ResEffect", ResEffect = class ResEffect {
        constructor(_json_) {
          /**
           * 效果ID
           */
          this.id = void 0;

          /**
           * 效果名称
           */
          this.name = void 0;

          /**
           * 效果描述
           */
          this.description = void 0;

          /**
           * 效果图标
           */
          this.icon = void 0;

          /**
           * 效果类型
           */
          this.effectType = void 0;

          /**
           * 效果数值
           */
          this.effectValue = void 0;

          /**
           * 效果参数
           */
          this.effectParams = void 0;

          if (_json_.id === undefined) {
            throw new Error();
          }

          this.id = _json_.id;

          if (_json_.name === undefined) {
            throw new Error();
          }

          this.name = _json_.name;

          if (_json_.description === undefined) {
            throw new Error();
          }

          this.description = _json_.description;

          if (_json_.icon === undefined) {
            throw new Error();
          }

          this.icon = _json_.icon;

          if (_json_.effect_type === undefined) {
            throw new Error();
          }

          this.effectType = _json_.effect_type;

          if (_json_.effect_value === undefined) {
            throw new Error();
          }

          this.effectValue = _json_.effect_value;

          if (_json_.effect_params === undefined) {
            throw new Error();
          }

          this.effectParams = _json_.effect_params;
        }

        resolve(tables) {}

      });

      _export("ResEnemy", ResEnemy = class ResEnemy {
        constructor(_json_) {
          /**
           * 单位ID
           */
          this.id = void 0;

          /**
           * 单位名称
           */
          this.name = void 0;

          /**
           * 备注
           */
          this.comment = void 0;

          /**
           * 单位Prefab
           */
          this.prefab = void 0;

          /**
           * 单位级别
           */
          this.rank = void 0;

          /**
           * 单位用途
           */
          this.purpose = void 0;

          /**
           * 基础生命值
           */
          this.baseHp = void 0;

          /**
           * 基础攻击力
           */
          this.baseAtk = void 0;

          /**
           * 移动速度
           */
          this.moveSpeed = void 0;

          /**
           * 击杀得分
           */
          this.killScore = void 0;

          /**
           * 是否显示血条
           */
          this.showHpBar = void 0;

          /**
           * 是否开启受击次数
           */
          this.useHitCount = void 0;

          /**
           * 受击次数
           */
          this.hitCountToKill = void 0;

          /**
           * 受击间隔
           */
          this.hitCountInterval = void 0;

          /**
           * 选中级别
           */
          this.targetPriority = void 0;

          /**
           * 免疫子弹伤害
           */
          this.immuneBulletDamage = void 0;

          /**
           * 免疫撞击伤害
           */
          this.immuneCollideDamage = void 0;

          /**
           * 无视子弹
           */
          this.ignoreBullet = void 0;

          /**
           * 无视撞击
           */
          this.ignoreCollide = void 0;

          /**
           * 是否免疫核弹
           */
          this.immuneNuke = void 0;

          /**
           * 是否免疫主动技能
           */
          this.immuneActiveSkill = void 0;

          /**
           * 是否无敌
           */
          this.invincible = void 0;

          /**
           * 相撞等级
           */
          this.collideLevel = void 0;

          /**
           * 相撞伤害
           */
          this.collideDamage = void 0;

          /**
           * 掉落半径
           */
          this.dropRadius = void 0;

          if (_json_.id === undefined) {
            throw new Error();
          }

          this.id = _json_.id;

          if (_json_.name === undefined) {
            throw new Error();
          }

          this.name = _json_.name;

          if (_json_.comment === undefined) {
            throw new Error();
          }

          this.comment = _json_.comment;

          if (_json_.prefab === undefined) {
            throw new Error();
          }

          this.prefab = _json_.prefab;

          if (_json_.rank === undefined) {
            throw new Error();
          }

          this.rank = _json_.rank;

          if (_json_.purpose === undefined) {
            throw new Error();
          }

          this.purpose = _json_.purpose;

          if (_json_.base_hp === undefined) {
            throw new Error();
          }

          this.baseHp = _json_.base_hp;

          if (_json_.base_atk === undefined) {
            throw new Error();
          }

          this.baseAtk = _json_.base_atk;

          if (_json_.move_speed === undefined) {
            throw new Error();
          }

          this.moveSpeed = _json_.move_speed;

          if (_json_.kill_score === undefined) {
            throw new Error();
          }

          this.killScore = _json_.kill_score;

          if (_json_.show_hp_bar === undefined) {
            throw new Error();
          }

          this.showHpBar = _json_.show_hp_bar;

          if (_json_.use_hit_count === undefined) {
            throw new Error();
          }

          this.useHitCount = _json_.use_hit_count;

          if (_json_.hit_count_to_kill === undefined) {
            throw new Error();
          }

          this.hitCountToKill = _json_.hit_count_to_kill;

          if (_json_.hit_count_interval === undefined) {
            throw new Error();
          }

          this.hitCountInterval = _json_.hit_count_interval;

          if (_json_.target_priority === undefined) {
            throw new Error();
          }

          this.targetPriority = _json_.target_priority;

          if (_json_.immune_bullet_damage === undefined) {
            throw new Error();
          }

          this.immuneBulletDamage = _json_.immune_bullet_damage;

          if (_json_.immune_collide_damage === undefined) {
            throw new Error();
          }

          this.immuneCollideDamage = _json_.immune_collide_damage;

          if (_json_.ignore_bullet === undefined) {
            throw new Error();
          }

          this.ignoreBullet = _json_.ignore_bullet;

          if (_json_.ignore_collide === undefined) {
            throw new Error();
          }

          this.ignoreCollide = _json_.ignore_collide;

          if (_json_.immune_nuke === undefined) {
            throw new Error();
          }

          this.immuneNuke = _json_.immune_nuke;

          if (_json_.immune_active_skill === undefined) {
            throw new Error();
          }

          this.immuneActiveSkill = _json_.immune_active_skill;

          if (_json_.invincible === undefined) {
            throw new Error();
          }

          this.invincible = _json_.invincible;

          if (_json_.collide_level === undefined) {
            throw new Error();
          }

          this.collideLevel = _json_.collide_level;

          if (_json_.collide_damage === undefined) {
            throw new Error();
          }

          this.collideDamage = _json_.collide_damage;

          if (_json_.drop_radius === undefined) {
            throw new Error();
          }

          this.dropRadius = _json_.drop_radius;
        }

        resolve(tables) {}

      });

      _export("ResEquip", ResEquip = class ResEquip {
        constructor(_json_) {
          /**
           * ID
           */
          this.id = void 0;

          /**
           * 名称
           */
          this.name = void 0;

          /**
           * 图标
           */
          this.icon = void 0;

          /**
           * 品质
           */
          this.quality = void 0;

          /**
           * 品质子等级
           */
          this.qualitySub = void 0;

          /**
           * 装备部位
           */
          this.equipClass = void 0;

          /**
           * 属性
           */
          this.props = void 0;

          /**
           * 消耗材料
           */
          this.consumeItems = void 0;

          if (_json_.id === undefined) {
            throw new Error();
          }

          this.id = _json_.id;

          if (_json_.name === undefined) {
            throw new Error();
          }

          this.name = _json_.name;

          if (_json_.icon === undefined) {
            throw new Error();
          }

          this.icon = _json_.icon;

          if (_json_.quality === undefined) {
            throw new Error();
          }

          this.quality = _json_.quality;

          if (_json_.quality_sub === undefined) {
            throw new Error();
          }

          this.qualitySub = _json_.quality_sub;

          if (_json_.equip_class === undefined) {
            throw new Error();
          }

          this.equipClass = _json_.equip_class;

          if (_json_.props === undefined) {
            throw new Error();
          }

          {
            this.props = [];

            for (var _ele0 of _json_.props) {
              var _e0 = void 0;

              _e0 = new EquipProp(_ele0);
              this.props.push(_e0);
            }
          }

          if (_json_.consume_items === undefined) {
            throw new Error();
          }

          {
            this.consumeItems = [];

            for (var _ele of _json_.consume_items) {
              var _e2 = void 0;

              _e2 = new ConsumeItem(_ele);
              this.consumeItems.push(_e2);
            }
          }
        }

        resolve(tables) {
          for (var _e of this.props) {
            _e == null || _e.resolve(tables);
          }

          for (var _e3 of this.consumeItems) {
            _e3 == null || _e3.resolve(tables);
          }
        }

      });

      _export("ResEquipUpgrade", ResEquipUpgrade = class ResEquipUpgrade {
        constructor(_json_) {
          /**
           * 部位
           */
          this.equipClass = void 0;

          /**
           * 等级下限
           */
          this.levelFrom = void 0;

          /**
           * 等级上限
           */
          this.levelTo = void 0;

          /**
           * 属性增幅
           */
          this.propInc = void 0;

          /**
           * 消耗货币
           */
          this.consumeMoney = void 0;

          /**
           * 消耗材料
           */
          this.consumeItems = void 0;

          if (_json_.equip_class === undefined) {
            throw new Error();
          }

          this.equipClass = _json_.equip_class;

          if (_json_.level_from === undefined) {
            throw new Error();
          }

          this.levelFrom = _json_.level_from;

          if (_json_.level_to === undefined) {
            throw new Error();
          }

          this.levelTo = _json_.level_to;

          if (_json_.prop_inc === undefined) {
            throw new Error();
          }

          {
            this.propInc = [];

            for (var _ele0 of _json_.prop_inc) {
              var _e0 = void 0;

              _e0 = new PropInc(_ele0);
              this.propInc.push(_e0);
            }
          }

          if (_json_.consume_money === undefined) {
            throw new Error();
          }

          this.consumeMoney = new ConsumeMoney(_json_.consume_money);

          if (_json_.consume_items === undefined) {
            throw new Error();
          }

          {
            this.consumeItems = [];

            for (var _ele2 of _json_.consume_items) {
              var _e4 = void 0;

              _e4 = new ConsumeItem(_ele2);
              this.consumeItems.push(_e4);
            }
          }
        }

        resolve(tables) {
          var _this$consumeMoney;

          for (var _e of this.propInc) {
            _e == null || _e.resolve(tables);
          }

          (_this$consumeMoney = this.consumeMoney) == null || _this$consumeMoney.resolve(tables);

          for (var _e5 of this.consumeItems) {
            _e5 == null || _e5.resolve(tables);
          }
        }

      });

      _export("ResGameMode", ResGameMode = class ResGameMode {
        constructor(_json_) {
          /**
           * ID
           */
          this.ID = void 0;

          /**
           * 模式类型
           */
          this.modeType = void 0;

          /**
           * 章节ID
           */
          this.chapterID = void 0;

          /**
           * 排序
           */
          this.order = void 0;

          /**
           * 入口资源
           */
          this.resourceID = void 0;

          /**
           * 文本介绍
           */
          this.description = void 0;
          this.conList = void 0;

          /**
           * 进入周期
           */
          this.cycle = void 0;

          /**
           * 进入次数
           */
          this.times = void 0;

          /**
           * 消耗类型
           */
          this.monType = void 0;

          /**
           * 消耗参数1
           */
          this.costParam1 = void 0;

          /**
           * 消耗参数2
           */
          this.costParam2 = void 0;

          /**
           * 复活次数
           */
          this.rebirthTimes = void 0;

          /**
           * 复活消耗
           */
          this.rebirthCost = void 0;

          /**
           * 战力评估
           */
          this.power = void 0;

          /**
           * 肉鸽组
           */
          this.rogueID = void 0;

          /**
           * 局内等级上限
           */
          this.LevelLimit = void 0;

          /**
           * 初始肉鸽选择
           */
          this.rogueFirst = void 0;

          /**
           * 扫荡次数
           */
          this.sweepLimit = void 0;

          /**
           * 奖励ID1
           */
          this.rewardID1 = void 0;

          /**
           * 奖励ID2
           */
          this.rewardID2 = void 0;
          this.ratingList = void 0;

          if (_json_.ID === undefined) {
            throw new Error();
          }

          this.ID = _json_.ID;

          if (_json_.modeType === undefined) {
            throw new Error();
          }

          this.modeType = _json_.modeType;

          if (_json_.chapterID === undefined) {
            throw new Error();
          }

          this.chapterID = _json_.chapterID;

          if (_json_.order === undefined) {
            throw new Error();
          }

          this.order = _json_.order;

          if (_json_.resourceID === undefined) {
            throw new Error();
          }

          this.resourceID = _json_.resourceID;

          if (_json_.description === undefined) {
            throw new Error();
          }

          this.description = _json_.description;

          if (_json_.conList === undefined) {
            throw new Error();
          }

          {
            this.conList = [];

            for (var _ele0 of _json_.conList) {
              var _e0 = void 0;

              _e0 = new ConParam(_ele0);
              this.conList.push(_e0);
            }
          }

          if (_json_.cycle === undefined) {
            throw new Error();
          }

          this.cycle = _json_.cycle;

          if (_json_.times === undefined) {
            throw new Error();
          }

          this.times = _json_.times;

          if (_json_.monType === undefined) {
            throw new Error();
          }

          this.monType = _json_.monType;

          if (_json_.costParam1 === undefined) {
            throw new Error();
          }

          this.costParam1 = _json_.costParam1;

          if (_json_.costParam2 === undefined) {
            throw new Error();
          }

          this.costParam2 = _json_.costParam2;

          if (_json_.rebirthTimes === undefined) {
            throw new Error();
          }

          this.rebirthTimes = _json_.rebirthTimes;

          if (_json_.rebirthCost === undefined) {
            throw new Error();
          }

          this.rebirthCost = _json_.rebirthCost;

          if (_json_.power === undefined) {
            throw new Error();
          }

          this.power = _json_.power;

          if (_json_.rogueID === undefined) {
            throw new Error();
          }

          this.rogueID = _json_.rogueID;

          if (_json_.LevelLimit === undefined) {
            throw new Error();
          }

          this.LevelLimit = _json_.LevelLimit;

          if (_json_.rogueFirst === undefined) {
            throw new Error();
          }

          this.rogueFirst = _json_.rogueFirst;

          if (_json_.sweepLimit === undefined) {
            throw new Error();
          }

          this.sweepLimit = _json_.sweepLimit;

          if (_json_.rewardID1 === undefined) {
            throw new Error();
          }

          this.rewardID1 = _json_.rewardID1;

          if (_json_.rewardID2 === undefined) {
            throw new Error();
          }

          this.rewardID2 = _json_.rewardID2;

          if (_json_.ratingList === undefined) {
            throw new Error();
          }

          {
            this.ratingList = [];

            for (var _ele3 of _json_.ratingList) {
              var _e6 = void 0;

              _e6 = new RatingParam(_ele3);
              this.ratingList.push(_e6);
            }
          }
        }

        resolve(tables) {}

      });

      _export("ResGlobalAttr", ResGlobalAttr = class ResGlobalAttr {
        constructor(_json_) {
          /**
           * 每回合发放的金币
           */
          this.GoldProducion = void 0;

          /**
           * 体力上限值
           */
          this.MaxEnergy = void 0;

          /**
           * 体力恢复的间隔时间
           */
          this.EnergyRecoverInterval = void 0;

          /**
           * 体力恢复的值
           */
          this.EnergyRecoverValue = void 0;

          /**
           * 局内道具拾取距离
           */
          this.ItemPickUpRadius = void 0;

          /**
           * 受击保护
           */
          this.PostHitProtection = void 0;

          /**
           * 镜头平移最大跟随速度
           */
          this.CameraTranslationMaxMoveSpeed = void 0;

          /**
           * 镜头平移启动延迟
           */
          this.CameraTranslationMoveDelay = void 0;

          if (_json_.GoldProducion === undefined) {
            throw new Error();
          }

          this.GoldProducion = _json_.GoldProducion;

          if (_json_.MaxEnergy === undefined) {
            throw new Error();
          }

          this.MaxEnergy = _json_.MaxEnergy;

          if (_json_.EnergyRecoverInterval === undefined) {
            throw new Error();
          }

          this.EnergyRecoverInterval = _json_.EnergyRecoverInterval;

          if (_json_.EnergyRecoverValue === undefined) {
            throw new Error();
          }

          this.EnergyRecoverValue = _json_.EnergyRecoverValue;

          if (_json_.ItemPickUpRadius === undefined) {
            throw new Error();
          }

          this.ItemPickUpRadius = _json_.ItemPickUpRadius;

          if (_json_.PostHitProtection === undefined) {
            throw new Error();
          }

          this.PostHitProtection = _json_.PostHitProtection;

          if (_json_.CameraTranslationMaxMoveSpeed === undefined) {
            throw new Error();
          }

          this.CameraTranslationMaxMoveSpeed = _json_.CameraTranslationMaxMoveSpeed;

          if (_json_.CameraTranslationMoveDelay === undefined) {
            throw new Error();
          }

          this.CameraTranslationMoveDelay = _json_.CameraTranslationMoveDelay;
        }

        resolve(tables) {}

      });

      _export("ResItem", ResItem = class ResItem {
        constructor(_json_) {
          /**
           * ID
           */
          this.id = void 0;

          /**
           * 名称
           */
          this.name = void 0;

          /**
           * 图标
           */
          this.icon = void 0;

          /**
           * 品质
           */
          this.quality = void 0;

          /**
           * 品质子等级
           */
          this.qualitySub = void 0;

          /**
           * 使用类型
           */
          this.useType = void 0;

          /**
           * 效果类型
           */
          this.effectId = void 0;

          /**
           * 效果参数1
           */
          this.effectParam1 = void 0;

          /**
           * 效果参数2
           */
          this.effectParam2 = void 0;

          /**
           * 最大叠放数量
           */
          this.maxStackNum = void 0;

          if (_json_.id === undefined) {
            throw new Error();
          }

          this.id = _json_.id;

          if (_json_.name === undefined) {
            throw new Error();
          }

          this.name = _json_.name;

          if (_json_.icon === undefined) {
            throw new Error();
          }

          this.icon = _json_.icon;

          if (_json_.quality === undefined) {
            throw new Error();
          }

          this.quality = _json_.quality;

          if (_json_.quality_sub === undefined) {
            throw new Error();
          }

          this.qualitySub = _json_.quality_sub;

          if (_json_.use_type === undefined) {
            throw new Error();
          }

          this.useType = _json_.use_type;

          if (_json_.effect_id === undefined) {
            throw new Error();
          }

          this.effectId = _json_.effect_id;

          if (_json_.effect_param1 === undefined) {
            throw new Error();
          }

          this.effectParam1 = _json_.effect_param1;

          if (_json_.effect_param2 === undefined) {
            throw new Error();
          }

          this.effectParam2 = _json_.effect_param2;

          if (_json_.max_stack_num === undefined) {
            throw new Error();
          }

          this.maxStackNum = _json_.max_stack_num;
        }

        resolve(tables) {}

      });

      _export("ResLevel", ResLevel = class ResLevel {
        constructor(_json_) {
          /**
           * 关卡id
           */
          this.id = void 0;

          /**
           * 关卡prefab
           */
          this.prefab = void 0;

          /**
           * 是、否（默认值）
           */
          this.forbidFire = void 0;

          /**
           * 是、否（默认值）
           */
          this.forbidNBomb = void 0;

          /**
           * 是、否（默认值）
           */
          this.forbidActSkill = void 0;

          /**
           * 0到1（1表示正常碰撞）
           */
          this.planeCollisionScaling = void 0;

          /**
           * 1=常规关卡<br/>2=机关关卡<br/>3=金币关卡<br/>4=火箭关卡<br/>99=Boss关
           */
          this.levelType = void 0;

          if (_json_.id === undefined) {
            throw new Error();
          }

          this.id = _json_.id;

          if (_json_.prefab === undefined) {
            throw new Error();
          }

          this.prefab = _json_.prefab;

          if (_json_.forbidFire === undefined) {
            throw new Error();
          }

          this.forbidFire = _json_.forbidFire;

          if (_json_.forbidNBomb === undefined) {
            throw new Error();
          }

          this.forbidNBomb = _json_.forbidNBomb;

          if (_json_.forbidActSkill === undefined) {
            throw new Error();
          }

          this.forbidActSkill = _json_.forbidActSkill;

          if (_json_.planeCollisionScaling === undefined) {
            throw new Error();
          }

          this.planeCollisionScaling = _json_.planeCollisionScaling;

          if (_json_.levelType === undefined) {
            throw new Error();
          }

          this.levelType = _json_.levelType;
        }

        resolve(tables) {}

      });

      _export("ResLevelGroup", ResLevelGroup = class ResLevelGroup {
        constructor(_json_) {
          /**
           * 关卡组ID
           */
          this.id = void 0;

          /**
           * 常规关卡数量
           */
          this.normLevelCount = void 0;

          /**
           * 1=随机<br/>2=随机不重复<br/>3=顺序重复
           */
          this.normLevelST = void 0;
          this.normSTList = void 0;

          /**
           * BOSS关卡数量
           */
          this.bossLevelCount = void 0;

          /**
           * 1=随机<br/>2=随机不重复<br/>3=顺序重复
           */
          this.bossLevelST = void 0;
          this.bossSTList = void 0;

          if (_json_.id === undefined) {
            throw new Error();
          }

          this.id = _json_.id;

          if (_json_.normLevelCount === undefined) {
            throw new Error();
          }

          this.normLevelCount = _json_.normLevelCount;

          if (_json_.normLevelST === undefined) {
            throw new Error();
          }

          this.normLevelST = _json_.normLevelST;

          if (_json_.normSTList === undefined) {
            throw new Error();
          }

          {
            this.normSTList = [];

            for (var _ele0 of _json_.normSTList) {
              var _e0 = void 0;

              _e0 = new randStrategy(_ele0);
              this.normSTList.push(_e0);
            }
          }

          if (_json_.bossLevelCount === undefined) {
            throw new Error();
          }

          this.bossLevelCount = _json_.bossLevelCount;

          if (_json_.bossLevelST === undefined) {
            throw new Error();
          }

          this.bossLevelST = _json_.bossLevelST;

          if (_json_.bossSTList === undefined) {
            throw new Error();
          }

          {
            this.bossSTList = [];

            for (var _ele4 of _json_.bossSTList) {
              var _e7 = void 0;

              _e7 = new randStrategy(_ele4);
              this.bossSTList.push(_e7);
            }
          }
        }

        resolve(tables) {
          for (var _e of this.normSTList) {
            _e == null || _e.resolve(tables);
          }

          for (var _e8 of this.bossSTList) {
            _e8 == null || _e8.resolve(tables);
          }
        }

      });

      _export("ResLoot", ResLoot = class ResLoot {
        constructor(_json_) {
          /**
           * 掉落ID
           */
          this.lootId = void 0;

          /**
           * 掉落组
           */
          this.lootGroup = void 0;

          /**
           * 掉落方式
           */
          this.lootType = void 0;
          this.itemList = void 0;

          if (_json_.loot_id === undefined) {
            throw new Error();
          }

          this.lootId = _json_.loot_id;

          if (_json_.loot_group === undefined) {
            throw new Error();
          }

          this.lootGroup = _json_.loot_group;

          if (_json_.loot_type === undefined) {
            throw new Error();
          }

          this.lootType = _json_.loot_type;

          if (_json_.item_list === undefined) {
            throw new Error();
          }

          {
            this.itemList = [];

            for (var _ele0 of _json_.item_list) {
              var _e0 = void 0;

              _e0 = new ResLootItem(_ele0);
              this.itemList.push(_e0);
            }
          }
        }

        resolve(tables) {
          for (var _e of this.itemList) {
            _e == null || _e.resolve(tables);
          }
        }

      });

      _export("ResLootItem", ResLootItem = class ResLootItem {
        constructor(_json_) {
          /**
           * item_id 可以是道具，装备，飞机，或者另一个掉落ID
           */
          this.itemId = void 0;
          this.count = void 0;
          this.rate = void 0;
          this.protectTimes = void 0;

          if (_json_.item_id === undefined) {
            throw new Error();
          }

          this.itemId = _json_.item_id;

          if (_json_.count === undefined) {
            throw new Error();
          }

          this.count = _json_.count;

          if (_json_.rate === undefined) {
            throw new Error();
          }

          this.rate = _json_.rate;

          if (_json_.protect_times === undefined) {
            throw new Error();
          }

          this.protectTimes = _json_.protect_times;
        }

        resolve(tables) {}

      });

      _export("ResPlane", ResPlane = class ResPlane {
        constructor(_json_) {
          /**
           * ID
           */
          this.id = void 0;

          /**
           * 战机星级
           */
          this.starLevel = void 0;

          /**
           * 名称
           */
          this.name = void 0;

          /**
           * 图标
           */
          this.icon = void 0;

          /**
           * 立绘
           */
          this.portrait = void 0;

          /**
           * 描述
           */
          this.description = void 0;

          /**
           * 品质
           */
          this.quality = void 0;
          this.property = void 0;

          /**
           * 效果列表
           */
          this.effects = void 0;

          /**
           * 升星材料
           */
          this.materials = void 0;

          if (_json_.id === undefined) {
            throw new Error();
          }

          this.id = _json_.id;

          if (_json_.star_level === undefined) {
            throw new Error();
          }

          this.starLevel = _json_.star_level;

          if (_json_.name === undefined) {
            throw new Error();
          }

          this.name = _json_.name;

          if (_json_.icon === undefined) {
            throw new Error();
          }

          this.icon = _json_.icon;

          if (_json_.portrait === undefined) {
            throw new Error();
          }

          this.portrait = _json_.portrait;

          if (_json_.description === undefined) {
            throw new Error();
          }

          this.description = _json_.description;

          if (_json_.quality === undefined) {
            throw new Error();
          }

          this.quality = _json_.quality;

          if (_json_.property === undefined) {
            throw new Error();
          }

          this.property = new PlaneProperty(_json_.property);

          if (_json_.effects === undefined) {
            throw new Error();
          }

          {
            this.effects = [];

            for (var _ele0 of _json_.effects) {
              var _e0 = void 0;

              _e0 = new PlaneEffect(_ele0);
              this.effects.push(_e0);
            }
          }

          if (_json_.materials === undefined) {
            throw new Error();
          }

          {
            this.materials = [];

            for (var _ele5 of _json_.materials) {
              var _e9 = void 0;

              _e9 = new PlaneMaterial(_ele5);
              this.materials.push(_e9);
            }
          }
        }

        resolve(tables) {
          var _this$property;

          (_this$property = this.property) == null || _this$property.resolve(tables);

          for (var _e of this.effects) {
            _e == null || _e.resolve(tables);
          }

          for (var _e10 of this.materials) {
            _e10 == null || _e10.resolve(tables);
          }
        }

      });

      _export("ResSkill", ResSkill = class ResSkill {
        constructor(_json_) {
          /**
           * ID
           */
          this.id = void 0;

          /**
           * 技能名称
           */
          this.name = void 0;

          /**
           * 描述
           */
          this.desc = void 0;

          /**
           * 技能图标prefab
           */
          this.icon = void 0;

          /**
           * 冷却时间
           */
          this.cd = void 0;

          /**
           * 费用ID
           */
          this.CostID = void 0;

          /**
           * 费用消耗值
           */
          this.CostNum = void 0;

          /**
           * 条件
           */
          this.conditionID = void 0;
          this.ApplyBuffs = void 0;

          if (_json_.id === undefined) {
            throw new Error();
          }

          this.id = _json_.id;

          if (_json_.name === undefined) {
            throw new Error();
          }

          this.name = _json_.name;

          if (_json_.desc === undefined) {
            throw new Error();
          }

          this.desc = _json_.desc;

          if (_json_.icon === undefined) {
            throw new Error();
          }

          this.icon = _json_.icon;

          if (_json_.cd === undefined) {
            throw new Error();
          }

          this.cd = _json_.cd;

          if (_json_.CostID === undefined) {
            throw new Error();
          }

          this.CostID = _json_.CostID;

          if (_json_.CostNum === undefined) {
            throw new Error();
          }

          this.CostNum = _json_.CostNum;

          if (_json_.conditionID === undefined) {
            throw new Error();
          }

          this.conditionID = _json_.conditionID;

          if (_json_.ApplyBuffs === undefined) {
            throw new Error();
          }

          {
            this.ApplyBuffs = [];

            for (var _ele0 of _json_.ApplyBuffs) {
              var _e0 = void 0;

              _e0 = new ApplyBuff(_ele0);
              this.ApplyBuffs.push(_e0);
            }
          }
        }

        resolve(tables) {}

      });

      _export("ResStage", ResStage = class ResStage {
        constructor(_json_) {
          /**
           * id
           */
          this.id = void 0;

          /**
           * 关卡
           */
          this.mainStage = void 0;

          /**
           * 阶段
           */
          this.subStage = void 0;

          /**
           * 类型0:普通敌机 100：boss
           */
          this.type = void 0;

          /**
           * 波次id
           */
          this.enemyGroupID = void 0;

          /**
           * 延迟时间
           */
          this.delay = void 0;

          /**
           * 属性倍率（血量，攻击，碰撞攻击）
           */
          this.enemyNorRate = void 0;

          if (_json_.id === undefined) {
            throw new Error();
          }

          this.id = _json_.id;

          if (_json_.mainStage === undefined) {
            throw new Error();
          }

          this.mainStage = _json_.mainStage;

          if (_json_.subStage === undefined) {
            throw new Error();
          }

          this.subStage = _json_.subStage;

          if (_json_.type === undefined) {
            throw new Error();
          }

          this.type = _json_.type;

          if (_json_.enemyGroupID === undefined) {
            throw new Error();
          }

          this.enemyGroupID = _json_.enemyGroupID;

          if (_json_.delay === undefined) {
            throw new Error();
          }

          this.delay = _json_.delay;

          if (_json_.enemyNorRate === undefined) {
            throw new Error();
          }

          this.enemyNorRate = _json_.enemyNorRate;
        }

        resolve(tables) {}

      });

      _export("ResTask", ResTask = class ResTask {
        constructor(_json_) {
          /**
           * 任务 ID
           */
          this.taskId = void 0;

          /**
           * 任务集 ID
           */
          this.groupId = void 0;

          /**
           * 任务类型
           */
          this.taskClass = void 0;

          /**
           * 前置任务 ID
           */
          this.prevId = void 0;

          /**
           * 重置周期
           */
          this.periodType = void 0;

          /**
           * 任务接取条件
           */
          this.taskCond = void 0;
          this.taskGoal = void 0;

          /**
           * 目标值累积
           */
          this.accumulate = void 0;

          /**
           * 奖励ID
           */
          this.rewardId = void 0;

          /**
           * 奖励轨道集ID
           */
          this.orbitGroupId = void 0;

          /**
           * 完成奖励值
           */
          this.orbitValue = void 0;

          /**
           * 开放日期(yyyymmdd)
           */
          this.openDate = void 0;

          /**
           * 开放时间(HHMMSS)
           */
          this.openTime = void 0;

          /**
           * 结束日期(yyyymmdd)
           */
          this.closeDate = void 0;

          /**
           * 结束时间(HHMMSS)
           */
          this.closeTime = void 0;

          /**
           * 任务追踪
           */
          this.linkTo = void 0;

          if (_json_.task_id === undefined) {
            throw new Error();
          }

          this.taskId = _json_.task_id;

          if (_json_.group_id === undefined) {
            throw new Error();
          }

          this.groupId = _json_.group_id;

          if (_json_.task_class === undefined) {
            throw new Error();
          }

          this.taskClass = _json_.task_class;

          if (_json_.prev_id === undefined) {
            throw new Error();
          }

          this.prevId = _json_.prev_id;

          if (_json_.period_type === undefined) {
            throw new Error();
          }

          this.periodType = _json_.period_type;

          if (_json_.task_cond === undefined) {
            throw new Error();
          }

          {
            this.taskCond = [];

            for (var _ele0 of _json_.task_cond) {
              var _e0 = void 0;

              _e0 = new ResCondition(_ele0);
              this.taskCond.push(_e0);
            }
          }

          if (_json_.task_goal === undefined) {
            throw new Error();
          }

          this.taskGoal = new ResTaskGoal(_json_.task_goal);

          if (_json_.accumulate === undefined) {
            throw new Error();
          }

          this.accumulate = _json_.accumulate;

          if (_json_.reward_id === undefined) {
            throw new Error();
          }

          this.rewardId = _json_.reward_id;

          if (_json_.orbit_group_id === undefined) {
            throw new Error();
          }

          this.orbitGroupId = _json_.orbit_group_id;

          if (_json_.orbit_value === undefined) {
            throw new Error();
          }

          this.orbitValue = _json_.orbit_value;

          if (_json_.open_date === undefined) {
            throw new Error();
          }

          this.openDate = _json_.open_date;

          if (_json_.open_time === undefined) {
            throw new Error();
          }

          this.openTime = _json_.open_time;

          if (_json_.close_date === undefined) {
            throw new Error();
          }

          this.closeDate = _json_.close_date;

          if (_json_.close_time === undefined) {
            throw new Error();
          }

          this.closeTime = _json_.close_time;

          if (_json_.link_to === undefined) {
            throw new Error();
          }

          this.linkTo = _json_.link_to;
        }

        resolve(tables) {
          var _this$taskGoal3;

          for (var _e of this.taskCond) {
            _e == null || _e.resolve(tables);
          }

          (_this$taskGoal3 = this.taskGoal) == null || _this$taskGoal3.resolve(tables);
        }

      });
      /**
       * 任务配置表项定义
       */


      _export("ResTask2", ResTask2 = class ResTask2 {
        constructor(_json_) {
          this.taskId = void 0;
          this.groupId = void 0;
          this.desc = void 0;

          /**
           * 可不配置
           */
          this.taskClass = void 0;
          this.uiRes = void 0;
          this.prevId = void 0;
          this.periodType = void 0;
          this.taskCond = void 0;
          this.taskGoal = void 0;
          this.accumlate = void 0;

          /**
           * 需要客户端领取
           */
          this.rewardId = void 0;
          this.taskOrbitId = void 0;

          /**
           * 自动累积
           */
          this.taskOrbitValue = void 0;

          /**
           * yyyymmdd
           */
          this.openDate = void 0;

          /**
           * HHMMSS
           */
          this.openTime = void 0;

          /**
           * yyyymmdd
           */
          this.closeDate = void 0;

          /**
           * HHMMSS
           */
          this.closeTime = void 0;

          /**
           * 连接到完成任务相关的 UI
           */
          this.linkTo = void 0;

          if (_json_.task_id === undefined) {
            throw new Error();
          }

          this.taskId = _json_.task_id;

          if (_json_.group_id === undefined) {
            throw new Error();
          }

          this.groupId = _json_.group_id;

          if (_json_.desc === undefined) {
            throw new Error();
          }

          this.desc = _json_.desc;

          if (_json_.task_class === undefined) {
            throw new Error();
          }

          this.taskClass = _json_.task_class;

          if (_json_.ui_res === undefined) {
            throw new Error();
          }

          this.uiRes = _json_.ui_res;

          if (_json_.prev_id === undefined) {
            throw new Error();
          }

          this.prevId = _json_.prev_id;

          if (_json_.period_type === undefined) {
            throw new Error();
          }

          this.periodType = _json_.period_type;

          if (_json_.task_cond === undefined) {
            throw new Error();
          }

          {
            this.taskCond = [];

            for (var _ele0 of _json_.task_cond) {
              var _e0 = void 0;

              _e0 = new ResCondition(_ele0);
              this.taskCond.push(_e0);
            }
          }

          if (_json_.task_goal === undefined) {
            throw new Error();
          }

          this.taskGoal = new ResTaskGoal(_json_.task_goal);

          if (_json_.accumlate === undefined) {
            throw new Error();
          }

          this.accumlate = _json_.accumlate;

          if (_json_.reward_id === undefined) {
            throw new Error();
          }

          this.rewardId = _json_.reward_id;

          if (_json_.task_orbit_id === undefined) {
            throw new Error();
          }

          this.taskOrbitId = _json_.task_orbit_id;

          if (_json_.task_orbit_value === undefined) {
            throw new Error();
          }

          this.taskOrbitValue = _json_.task_orbit_value;

          if (_json_.open_date === undefined) {
            throw new Error();
          }

          this.openDate = _json_.open_date;

          if (_json_.open_time === undefined) {
            throw new Error();
          }

          this.openTime = _json_.open_time;

          if (_json_.close_date === undefined) {
            throw new Error();
          }

          this.closeDate = _json_.close_date;

          if (_json_.close_time === undefined) {
            throw new Error();
          }

          this.closeTime = _json_.close_time;

          if (_json_.link_to === undefined) {
            throw new Error();
          }

          this.linkTo = _json_.link_to;
        }

        resolve(tables) {
          var _this$taskGoal4;

          for (var _e of this.taskCond) {
            _e == null || _e.resolve(tables);
          }

          (_this$taskGoal4 = this.taskGoal) == null || _this$taskGoal4.resolve(tables);
        }

      });

      _export("ResTaskGoal", ResTaskGoal = class ResTaskGoal {
        constructor(_json_) {
          this.goalType = void 0;
          this.params = void 0;
          this.desc = void 0;

          if (_json_.goal_type === undefined) {
            throw new Error();
          }

          this.goalType = _json_.goal_type;

          if (_json_.params === undefined) {
            throw new Error();
          }

          {
            this.params = [];

            for (var _ele0 of _json_.params) {
              var _e0 = void 0;

              _e0 = _ele0;
              this.params.push(_e0);
            }
          }

          if (_json_.desc === undefined) {
            throw new Error();
          }

          this.desc = _json_.desc;
        }

        resolve(tables) {}

      });

      _export("ResTaskOrbit", ResTaskOrbit = class ResTaskOrbit {
        constructor(_json_) {
          /**
           * 轨道ID
           */
          this.taskId = void 0;

          /**
           * 轨道集
           */
          this.groupId = void 0;

          /**
           * 序号
           */
          this.index = void 0;

          /**
           * 循环类型
           */
          this.periodType = void 0;
          this.taskCond = void 0;
          this.taskGoal = void 0;

          /**
           * 是否累积
           */
          this.accumulate = void 0;

          /**
           * 奖励ID
           */
          this.rewardId = void 0;

          if (_json_.task_id === undefined) {
            throw new Error();
          }

          this.taskId = _json_.task_id;

          if (_json_.group_id === undefined) {
            throw new Error();
          }

          this.groupId = _json_.group_id;

          if (_json_.index === undefined) {
            throw new Error();
          }

          this.index = _json_.index;

          if (_json_.period_type === undefined) {
            throw new Error();
          }

          this.periodType = _json_.period_type;

          if (_json_.task_cond === undefined) {
            throw new Error();
          }

          {
            this.taskCond = [];

            for (var _ele0 of _json_.task_cond) {
              var _e0 = void 0;

              _e0 = new ResCondition(_ele0);
              this.taskCond.push(_e0);
            }
          }

          if (_json_.task_goal === undefined) {
            throw new Error();
          }

          this.taskGoal = new ResTaskGoal(_json_.task_goal);

          if (_json_.accumulate === undefined) {
            throw new Error();
          }

          this.accumulate = _json_.accumulate;

          if (_json_.reward_id === undefined) {
            throw new Error();
          }

          this.rewardId = _json_.reward_id;
        }

        resolve(tables) {
          var _this$taskGoal5;

          for (var _e of this.taskCond) {
            _e == null || _e.resolve(tables);
          }

          (_this$taskGoal5 = this.taskGoal) == null || _this$taskGoal5.resolve(tables);
        }

      });

      _export("ResTrack", ResTrack = class ResTrack {
        constructor(_json_) {
          /**
           * id
           */
          this.id = void 0;

          /**
           * 类型
           */
          this.tpe = void 0;

          /**
           * 值(不同的轨迹类型，数据代表的信息不一样)
           */
          this.value = void 0;

          if (_json_.id === undefined) {
            throw new Error();
          }

          this.id = _json_.id;

          if (_json_.tpe === undefined) {
            throw new Error();
          }

          this.tpe = _json_.tpe;

          if (_json_.value === undefined) {
            throw new Error();
          }

          this.value = _json_.value;
        }

        resolve(tables) {}

      });

      _export("ResUpgrade", ResUpgrade = class ResUpgrade {
        constructor(_json_) {
          /**
           * 玩家等级
           */
          this.roleLevel = void 0;

          /**
           * 升级所需的经验
           */
          this.xp = void 0;

          /**
           * 升级奖励
           */
          this.rewardId = void 0;

          if (_json_.role_level === undefined) {
            throw new Error();
          }

          this.roleLevel = _json_.role_level;

          if (_json_.xp === undefined) {
            throw new Error();
          }

          this.xp = _json_.xp;

          if (_json_.reward_id === undefined) {
            throw new Error();
          }

          this.rewardId = _json_.reward_id;
        }

        resolve(tables) {}

      });

      _export("ResWave", ResWave = class ResWave {
        constructor(_json_) {
          /**
           * id
           */
          this.id = void 0;

          /**
           * 波次 ID
           */
          this.enemyGroupID = void 0;

          /**
           * 延迟时间
           */
          this.delay = void 0;

          /**
           * 0 表示普通敌机
           */
          this.planeType = void 0;

          /**
           * 敌机id
           */
          this.planeId = void 0;

          /**
           * 生成间隔时间
           */
          this.interval = void 0;

          /**
           * 根据敌机数量设置偏移位置
           */
          this.offsetPos = void 0;

          /**
           * 生成的敌机数量
           */
          this.num = void 0;

          /**
           * 初始位置
           */
          this.pos = void 0;

          /**
           * 轨迹路径(次数,轨迹索引,;id,速度,间隔；)
           */
          this.track = void 0;

          /**
           * 轨迹参数
           */
          this.trackParams = void 0;

          /**
           * 旋转速度
           */
          this.rotatioSpeed = void 0;

          /**
           * 首次射击延迟
           */
          this.FirstShootDelay = void 0;

          if (_json_.id === undefined) {
            throw new Error();
          }

          this.id = _json_.id;

          if (_json_.enemyGroupID === undefined) {
            throw new Error();
          }

          this.enemyGroupID = _json_.enemyGroupID;

          if (_json_.delay === undefined) {
            throw new Error();
          }

          this.delay = _json_.delay;

          if (_json_.planeType === undefined) {
            throw new Error();
          }

          this.planeType = _json_.planeType;

          if (_json_.planeId === undefined) {
            throw new Error();
          }

          this.planeId = _json_.planeId;

          if (_json_.interval === undefined) {
            throw new Error();
          }

          this.interval = _json_.interval;

          if (_json_.offsetPos === undefined) {
            throw new Error();
          }

          this.offsetPos = _json_.offsetPos;

          if (_json_.num === undefined) {
            throw new Error();
          }

          this.num = _json_.num;

          if (_json_.pos === undefined) {
            throw new Error();
          }

          this.pos = _json_.pos;

          if (_json_.track === undefined) {
            throw new Error();
          }

          this.track = _json_.track;

          if (_json_.trackParams === undefined) {
            throw new Error();
          }

          this.trackParams = _json_.trackParams;

          if (_json_.rotatioSpeed === undefined) {
            throw new Error();
          }

          this.rotatioSpeed = _json_.rotatioSpeed;

          if (_json_.FirstShootDelay === undefined) {
            throw new Error();
          }

          this.FirstShootDelay = _json_.FirstShootDelay;
        }

        resolve(tables) {}

      });

      _export("SkillCondition", SkillCondition = class SkillCondition {
        constructor(_json_) {
          this.type = void 0;
          this.param = void 0;

          if (_json_.type === undefined) {
            throw new Error();
          }

          this.type = _json_.type;

          if (_json_.param === undefined) {
            throw new Error();
          }

          {
            this.param = [];

            for (var _ele0 of _json_.param) {
              var _e0 = void 0;

              _e0 = _ele0;
              this.param.push(_e0);
            }
          }
        }

        resolve(tables) {}

      });

      _export("TbGlobalAttr", TbGlobalAttr = class TbGlobalAttr {
        constructor(_json_) {
          this._data = void 0;
          if (_json_.length != 1) throw new Error('table mode=one, but size != 1');
          this._data = new ResGlobalAttr(_json_[0]);
        }

        getData() {
          return this._data;
        }
        /**
         * 每回合发放的金币
         */


        get GoldProducion() {
          return this._data.GoldProducion;
        }
        /**
         * 体力上限值
         */


        get MaxEnergy() {
          return this._data.MaxEnergy;
        }
        /**
         * 体力恢复的间隔时间
         */


        get EnergyRecoverInterval() {
          return this._data.EnergyRecoverInterval;
        }
        /**
         * 体力恢复的值
         */


        get EnergyRecoverValue() {
          return this._data.EnergyRecoverValue;
        }
        /**
         * 局内道具拾取距离
         */


        get ItemPickUpRadius() {
          return this._data.ItemPickUpRadius;
        }
        /**
         * 受击保护
         */


        get PostHitProtection() {
          return this._data.PostHitProtection;
        }
        /**
         * 镜头平移最大跟随速度
         */


        get CameraTranslationMaxMoveSpeed() {
          return this._data.CameraTranslationMaxMoveSpeed;
        }
        /**
         * 镜头平移启动延迟
         */


        get CameraTranslationMoveDelay() {
          return this._data.CameraTranslationMoveDelay;
        }

        resolve(tables) {
          this._data.resolve(tables);
        }

      });

      _export("TbEquipUpgrade", TbEquipUpgrade = class TbEquipUpgrade {
        constructor(_json_) {
          this._dataList = void 0;
          this._dataList = [];

          for (var _json2_ of _json_) {
            var _v = void 0;

            _v = new ResEquipUpgrade(_json2_);

            this._dataList.push(_v);
          }
        }

        getDataList() {
          return this._dataList;
        }

        get(index) {
          return this._dataList[index];
        }

        resolve(tables) {
          for (var data of this._dataList) {
            data.resolve(tables);
          }
        }

      });

      _export("TbGM", TbGM = class TbGM {
        constructor(_json_) {
          this._dataList = void 0;
          this._dataList = [];

          for (var _json2_ of _json_) {
            var _v = void 0;

            _v = new GM(_json2_);

            this._dataList.push(_v);
          }
        }

        getDataList() {
          return this._dataList;
        }

        get(index) {
          return this._dataList[index];
        }

        resolve(tables) {
          for (var data of this._dataList) {
            data.resolve(tables);
          }
        }

      });

      _export("TbPlane", TbPlane = class TbPlane {
        constructor(_json_) {
          this._dataMap = void 0;
          this._dataList = void 0;
          this._dataMap = new Map();
          this._dataList = [];

          for (var _json2_ of _json_) {
            var _v = void 0;

            _v = new ResPlane(_json2_);

            this._dataList.push(_v);

            this._dataMap.set(_v.id, _v);
          }
        }

        getDataMap() {
          return this._dataMap;
        }

        getDataList() {
          return this._dataList;
        }

        get(key) {
          return this._dataMap.get(key);
        }

        resolve(tables) {
          for (var data of this._dataList) {
            data.resolve(tables);
          }
        }

      });

      _export("TbResAchievement", TbResAchievement = class TbResAchievement {
        constructor(_json_) {
          this._dataMap = void 0;
          this._dataList = void 0;
          this._dataMap = new Map();
          this._dataList = [];

          for (var _json2_ of _json_) {
            var _v = void 0;

            _v = new ResAchievement(_json2_);

            this._dataList.push(_v);

            this._dataMap.set(_v.taskId, _v);
          }
        }

        getDataMap() {
          return this._dataMap;
        }

        getDataList() {
          return this._dataList;
        }

        get(key) {
          return this._dataMap.get(key);
        }

        resolve(tables) {
          for (var data of this._dataList) {
            data.resolve(tables);
          }
        }

      });

      _export("TbResActivity", TbResActivity = class TbResActivity {
        constructor(_json_) {
          this._dataMap = void 0;
          this._dataList = void 0;
          this._dataMap = new Map();
          this._dataList = [];

          for (var _json2_ of _json_) {
            var _v = void 0;

            _v = new ResActivity(_json2_);

            this._dataList.push(_v);

            this._dataMap.set(_v.taskId, _v);
          }
        }

        getDataMap() {
          return this._dataMap;
        }

        getDataList() {
          return this._dataList;
        }

        get(key) {
          return this._dataMap.get(key);
        }

        resolve(tables) {
          for (var data of this._dataList) {
            data.resolve(tables);
          }
        }

      });

      _export("TbResBuffer", TbResBuffer = class TbResBuffer {
        constructor(_json_) {
          this._dataMap = void 0;
          this._dataList = void 0;
          this._dataMap = new Map();
          this._dataList = [];

          for (var _json2_ of _json_) {
            var _v = void 0;

            _v = new ResBuffer(_json2_);

            this._dataList.push(_v);

            this._dataMap.set(_v.id, _v);
          }
        }

        getDataMap() {
          return this._dataMap;
        }

        getDataList() {
          return this._dataList;
        }

        get(key) {
          return this._dataMap.get(key);
        }

        resolve(tables) {
          for (var data of this._dataList) {
            data.resolve(tables);
          }
        }

      });

      _export("TbResBullet", TbResBullet = class TbResBullet {
        constructor(_json_) {
          this._dataMap = void 0;
          this._dataList = void 0;
          this._dataMap = new Map();
          this._dataList = [];

          for (var _json2_ of _json_) {
            var _v = void 0;

            _v = new ResBullet(_json2_);

            this._dataList.push(_v);

            this._dataMap.set(_v.id, _v);
          }
        }

        getDataMap() {
          return this._dataMap;
        }

        getDataList() {
          return this._dataList;
        }

        get(key) {
          return this._dataMap.get(key);
        }

        resolve(tables) {
          for (var data of this._dataList) {
            data.resolve(tables);
          }
        }

      });

      _export("TbResChapter", TbResChapter = class TbResChapter {
        constructor(_json_) {
          this._dataMap = void 0;
          this._dataList = void 0;
          this._dataMap = new Map();
          this._dataList = [];

          for (var _json2_ of _json_) {
            var _v = void 0;

            _v = new ResChapter(_json2_);

            this._dataList.push(_v);

            this._dataMap.set(_v.id, _v);
          }
        }

        getDataMap() {
          return this._dataMap;
        }

        getDataList() {
          return this._dataList;
        }

        get(key) {
          return this._dataMap.get(key);
        }

        resolve(tables) {
          for (var data of this._dataList) {
            data.resolve(tables);
          }
        }

      });

      _export("TbResEffect", TbResEffect = class TbResEffect {
        constructor(_json_) {
          this._dataMap = void 0;
          this._dataList = void 0;
          this._dataMap = new Map();
          this._dataList = [];

          for (var _json2_ of _json_) {
            var _v = void 0;

            _v = new ResEffect(_json2_);

            this._dataList.push(_v);

            this._dataMap.set(_v.id, _v);
          }
        }

        getDataMap() {
          return this._dataMap;
        }

        getDataList() {
          return this._dataList;
        }

        get(key) {
          return this._dataMap.get(key);
        }

        resolve(tables) {
          for (var data of this._dataList) {
            data.resolve(tables);
          }
        }

      });

      _export("TbResEnemy", TbResEnemy = class TbResEnemy {
        constructor(_json_) {
          this._dataMap = void 0;
          this._dataList = void 0;
          this._dataMap = new Map();
          this._dataList = [];

          for (var _json2_ of _json_) {
            var _v = void 0;

            _v = new ResEnemy(_json2_);

            this._dataList.push(_v);

            this._dataMap.set(_v.id, _v);
          }
        }

        getDataMap() {
          return this._dataMap;
        }

        getDataList() {
          return this._dataList;
        }

        get(key) {
          return this._dataMap.get(key);
        }

        resolve(tables) {
          for (var data of this._dataList) {
            data.resolve(tables);
          }
        }

      });

      _export("TbResEquip", TbResEquip = class TbResEquip {
        constructor(_json_) {
          this._dataMap = void 0;
          this._dataList = void 0;
          this._dataMap = new Map();
          this._dataList = [];

          for (var _json2_ of _json_) {
            var _v = void 0;

            _v = new ResEquip(_json2_);

            this._dataList.push(_v);

            this._dataMap.set(_v.id, _v);
          }
        }

        getDataMap() {
          return this._dataMap;
        }

        getDataList() {
          return this._dataList;
        }

        get(key) {
          return this._dataMap.get(key);
        }

        resolve(tables) {
          for (var data of this._dataList) {
            data.resolve(tables);
          }
        }

      });

      _export("TbResGameMode", TbResGameMode = class TbResGameMode {
        constructor(_json_) {
          this._dataMap = void 0;
          this._dataList = void 0;
          this._dataMap = new Map();
          this._dataList = [];

          for (var _json2_ of _json_) {
            var _v = void 0;

            _v = new ResGameMode(_json2_);

            this._dataList.push(_v);

            this._dataMap.set(_v.ID, _v);
          }
        }

        getDataMap() {
          return this._dataMap;
        }

        getDataList() {
          return this._dataList;
        }

        get(key) {
          return this._dataMap.get(key);
        }

        resolve(tables) {
          for (var data of this._dataList) {
            data.resolve(tables);
          }
        }

      });

      _export("TbResItem", TbResItem = class TbResItem {
        constructor(_json_) {
          this._dataMap = void 0;
          this._dataList = void 0;
          this._dataMap = new Map();
          this._dataList = [];

          for (var _json2_ of _json_) {
            var _v = void 0;

            _v = new ResItem(_json2_);

            this._dataList.push(_v);

            this._dataMap.set(_v.id, _v);
          }
        }

        getDataMap() {
          return this._dataMap;
        }

        getDataList() {
          return this._dataList;
        }

        get(key) {
          return this._dataMap.get(key);
        }

        resolve(tables) {
          for (var data of this._dataList) {
            data.resolve(tables);
          }
        }

      });

      _export("TbResLevel", TbResLevel = class TbResLevel {
        constructor(_json_) {
          this._dataMap = void 0;
          this._dataList = void 0;
          this._dataMap = new Map();
          this._dataList = [];

          for (var _json2_ of _json_) {
            var _v = void 0;

            _v = new ResLevel(_json2_);

            this._dataList.push(_v);

            this._dataMap.set(_v.id, _v);
          }
        }

        getDataMap() {
          return this._dataMap;
        }

        getDataList() {
          return this._dataList;
        }

        get(key) {
          return this._dataMap.get(key);
        }

        resolve(tables) {
          for (var data of this._dataList) {
            data.resolve(tables);
          }
        }

      });

      _export("TbResLevelGroup", TbResLevelGroup = class TbResLevelGroup {
        constructor(_json_) {
          this._dataMap = void 0;
          this._dataList = void 0;
          this._dataMap = new Map();
          this._dataList = [];

          for (var _json2_ of _json_) {
            var _v = void 0;

            _v = new ResLevelGroup(_json2_);

            this._dataList.push(_v);

            this._dataMap.set(_v.id, _v);
          }
        }

        getDataMap() {
          return this._dataMap;
        }

        getDataList() {
          return this._dataList;
        }

        get(key) {
          return this._dataMap.get(key);
        }

        resolve(tables) {
          for (var data of this._dataList) {
            data.resolve(tables);
          }
        }

      });

      _export("TbResLoot", TbResLoot = class TbResLoot {
        constructor(_json_) {
          this._dataMap = void 0;
          this._dataList = void 0;
          this._dataMap = new Map();
          this._dataList = [];

          for (var _json2_ of _json_) {
            var _v = void 0;

            _v = new ResLoot(_json2_);

            this._dataList.push(_v);

            this._dataMap.set(_v.lootId, _v);
          }
        }

        getDataMap() {
          return this._dataMap;
        }

        getDataList() {
          return this._dataList;
        }

        get(key) {
          return this._dataMap.get(key);
        }

        resolve(tables) {
          for (var data of this._dataList) {
            data.resolve(tables);
          }
        }

      });

      _export("TbResSkill", TbResSkill = class TbResSkill {
        constructor(_json_) {
          this._dataMap = void 0;
          this._dataList = void 0;
          this._dataMap = new Map();
          this._dataList = [];

          for (var _json2_ of _json_) {
            var _v = void 0;

            _v = new ResSkill(_json2_);

            this._dataList.push(_v);

            this._dataMap.set(_v.id, _v);
          }
        }

        getDataMap() {
          return this._dataMap;
        }

        getDataList() {
          return this._dataList;
        }

        get(key) {
          return this._dataMap.get(key);
        }

        resolve(tables) {
          for (var data of this._dataList) {
            data.resolve(tables);
          }
        }

      });

      _export("TbResStage", TbResStage = class TbResStage {
        constructor(_json_) {
          this._dataMap = void 0;
          this._dataList = void 0;
          this._dataMap = new Map();
          this._dataList = [];

          for (var _json2_ of _json_) {
            var _v = void 0;

            _v = new ResStage(_json2_);

            this._dataList.push(_v);

            this._dataMap.set(_v.id, _v);
          }
        }

        getDataMap() {
          return this._dataMap;
        }

        getDataList() {
          return this._dataList;
        }

        get(key) {
          return this._dataMap.get(key);
        }

        resolve(tables) {
          for (var data of this._dataList) {
            data.resolve(tables);
          }
        }

      });

      _export("TbResTask", TbResTask = class TbResTask {
        constructor(_json_) {
          this._dataMap = void 0;
          this._dataList = void 0;
          this._dataMap = new Map();
          this._dataList = [];

          for (var _json2_ of _json_) {
            var _v = void 0;

            _v = new ResTask(_json2_);

            this._dataList.push(_v);

            this._dataMap.set(_v.taskId, _v);
          }
        }

        getDataMap() {
          return this._dataMap;
        }

        getDataList() {
          return this._dataList;
        }

        get(key) {
          return this._dataMap.get(key);
        }

        resolve(tables) {
          for (var data of this._dataList) {
            data.resolve(tables);
          }
        }

      });

      _export("TbResTaskOrbit", TbResTaskOrbit = class TbResTaskOrbit {
        constructor(_json_) {
          this._dataMap = void 0;
          this._dataList = void 0;
          this._dataMap = new Map();
          this._dataList = [];

          for (var _json2_ of _json_) {
            var _v = void 0;

            _v = new ResTaskOrbit(_json2_);

            this._dataList.push(_v);

            this._dataMap.set(_v.taskId, _v);
          }
        }

        getDataMap() {
          return this._dataMap;
        }

        getDataList() {
          return this._dataList;
        }

        get(key) {
          return this._dataMap.get(key);
        }

        resolve(tables) {
          for (var data of this._dataList) {
            data.resolve(tables);
          }
        }

      });

      _export("TbResTrack", TbResTrack = class TbResTrack {
        constructor(_json_) {
          this._dataMap = void 0;
          this._dataList = void 0;
          this._dataMap = new Map();
          this._dataList = [];

          for (var _json2_ of _json_) {
            var _v = void 0;

            _v = new ResTrack(_json2_);

            this._dataList.push(_v);

            this._dataMap.set(_v.id, _v);
          }
        }

        getDataMap() {
          return this._dataMap;
        }

        getDataList() {
          return this._dataList;
        }

        get(key) {
          return this._dataMap.get(key);
        }

        resolve(tables) {
          for (var data of this._dataList) {
            data.resolve(tables);
          }
        }

      });

      _export("TbResUpgrade", TbResUpgrade = class TbResUpgrade {
        constructor(_json_) {
          this._dataMap = void 0;
          this._dataList = void 0;
          this._dataMap = new Map();
          this._dataList = [];

          for (var _json2_ of _json_) {
            var _v = void 0;

            _v = new ResUpgrade(_json2_);

            this._dataList.push(_v);

            this._dataMap.set(_v.roleLevel, _v);
          }
        }

        getDataMap() {
          return this._dataMap;
        }

        getDataList() {
          return this._dataList;
        }

        get(key) {
          return this._dataMap.get(key);
        }

        resolve(tables) {
          for (var data of this._dataList) {
            data.resolve(tables);
          }
        }

      });

      _export("TbResWave", TbResWave = class TbResWave {
        constructor(_json_) {
          this._dataMap = void 0;
          this._dataList = void 0;
          this._dataMap = new Map();
          this._dataList = [];

          for (var _json2_ of _json_) {
            var _v = void 0;

            _v = new ResWave(_json2_);

            this._dataList.push(_v);

            this._dataMap.set(_v.id, _v);
          }
        }

        getDataMap() {
          return this._dataMap;
        }

        getDataList() {
          return this._dataList;
        }

        get(key) {
          return this._dataMap.get(key);
        }

        resolve(tables) {
          for (var data of this._dataList) {
            data.resolve(tables);
          }
        }

      });

      _export("Tables", Tables = class Tables {
        get TbGlobalAttr() {
          return this._TbGlobalAttr;
        }

        get TbEquipUpgrade() {
          return this._TbEquipUpgrade;
        }

        get TbGM() {
          return this._TbGM;
        }

        get TbPlane() {
          return this._TbPlane;
        }

        get TbResAchievement() {
          return this._TbResAchievement;
        }

        get TbResActivity() {
          return this._TbResActivity;
        }

        get TbResBuffer() {
          return this._TbResBuffer;
        }

        get TbResBullet() {
          return this._TbResBullet;
        }

        get TbResChapter() {
          return this._TbResChapter;
        }

        get TbResEffect() {
          return this._TbResEffect;
        }

        get TbResEnemy() {
          return this._TbResEnemy;
        }

        get TbResEquip() {
          return this._TbResEquip;
        }

        get TbResGameMode() {
          return this._TbResGameMode;
        }

        get TbResItem() {
          return this._TbResItem;
        }

        get TbResLevel() {
          return this._TbResLevel;
        }

        get TbResLevelGroup() {
          return this._TbResLevelGroup;
        }

        get TbResLoot() {
          return this._TbResLoot;
        }

        get TbResSkill() {
          return this._TbResSkill;
        }

        get TbResStage() {
          return this._TbResStage;
        }

        get TbResTask() {
          return this._TbResTask;
        }

        get TbResTaskOrbit() {
          return this._TbResTaskOrbit;
        }

        get TbResTrack() {
          return this._TbResTrack;
        }

        get TbResUpgrade() {
          return this._TbResUpgrade;
        }

        get TbResWave() {
          return this._TbResWave;
        }

        constructor(loader) {
          this._TbGlobalAttr = void 0;
          this._TbEquipUpgrade = void 0;
          this._TbGM = void 0;
          this._TbPlane = void 0;
          this._TbResAchievement = void 0;
          this._TbResActivity = void 0;
          this._TbResBuffer = void 0;
          this._TbResBullet = void 0;
          this._TbResChapter = void 0;
          this._TbResEffect = void 0;
          this._TbResEnemy = void 0;
          this._TbResEquip = void 0;
          this._TbResGameMode = void 0;
          this._TbResItem = void 0;
          this._TbResLevel = void 0;
          this._TbResLevelGroup = void 0;
          this._TbResLoot = void 0;
          this._TbResSkill = void 0;
          this._TbResStage = void 0;
          this._TbResTask = void 0;
          this._TbResTaskOrbit = void 0;
          this._TbResTrack = void 0;
          this._TbResUpgrade = void 0;
          this._TbResWave = void 0;
          this._TbGlobalAttr = new TbGlobalAttr(loader('tbglobalattr'));
          this._TbEquipUpgrade = new TbEquipUpgrade(loader('tbequipupgrade'));
          this._TbGM = new TbGM(loader('tbgm'));
          this._TbPlane = new TbPlane(loader('tbplane'));
          this._TbResAchievement = new TbResAchievement(loader('tbresachievement'));
          this._TbResActivity = new TbResActivity(loader('tbresactivity'));
          this._TbResBuffer = new TbResBuffer(loader('tbresbuffer'));
          this._TbResBullet = new TbResBullet(loader('tbresbullet'));
          this._TbResChapter = new TbResChapter(loader('tbreschapter'));
          this._TbResEffect = new TbResEffect(loader('tbreseffect'));
          this._TbResEnemy = new TbResEnemy(loader('tbresenemy'));
          this._TbResEquip = new TbResEquip(loader('tbresequip'));
          this._TbResGameMode = new TbResGameMode(loader('tbresgamemode'));
          this._TbResItem = new TbResItem(loader('tbresitem'));
          this._TbResLevel = new TbResLevel(loader('tbreslevel'));
          this._TbResLevelGroup = new TbResLevelGroup(loader('tbreslevelgroup'));
          this._TbResLoot = new TbResLoot(loader('tbresloot'));
          this._TbResSkill = new TbResSkill(loader('tbresskill'));
          this._TbResStage = new TbResStage(loader('tbresstage'));
          this._TbResTask = new TbResTask(loader('tbrestask'));
          this._TbResTaskOrbit = new TbResTaskOrbit(loader('tbrestaskorbit'));
          this._TbResTrack = new TbResTrack(loader('tbrestrack'));
          this._TbResUpgrade = new TbResUpgrade(loader('tbresupgrade'));
          this._TbResWave = new TbResWave(loader('tbreswave'));

          this._TbGlobalAttr.resolve(this);

          this._TbEquipUpgrade.resolve(this);

          this._TbGM.resolve(this);

          this._TbPlane.resolve(this);

          this._TbResAchievement.resolve(this);

          this._TbResActivity.resolve(this);

          this._TbResBuffer.resolve(this);

          this._TbResBullet.resolve(this);

          this._TbResChapter.resolve(this);

          this._TbResEffect.resolve(this);

          this._TbResEnemy.resolve(this);

          this._TbResEquip.resolve(this);

          this._TbResGameMode.resolve(this);

          this._TbResItem.resolve(this);

          this._TbResLevel.resolve(this);

          this._TbResLevelGroup.resolve(this);

          this._TbResLoot.resolve(this);

          this._TbResSkill.resolve(this);

          this._TbResStage.resolve(this);

          this._TbResTask.resolve(this);

          this._TbResTaskOrbit.resolve(this);

          this._TbResTrack.resolve(this);

          this._TbResUpgrade.resolve(this);

          this._TbResWave.resolve(this);
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=fc5386d40fe01a13e9c9f84a41a230ba2d8baa67.js.map