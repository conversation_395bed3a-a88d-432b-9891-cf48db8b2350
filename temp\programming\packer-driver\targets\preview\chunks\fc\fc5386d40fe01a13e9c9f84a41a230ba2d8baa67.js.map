{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/autogen/luban/schema.ts"], "names": ["A<PERSON><PERSON><PERSON><PERSON>", "ConParam", "ConsumeItem", "ConsumeMoney", "EffectParam", "EquipProp", "GM", "PlaneEffect", "PlaneMaterial", "PlaneProperty", "PlanePropertyElem", "PropInc", "randStrategy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ResAchievement", "ResActivity", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ResBullet", "ResChapter", "ResCondition", "ResEffect", "ResEnemy", "ResEquip", "ResEquipUpgrade", "ResGameMode", "ResGlobalAttr", "ResItem", "ResLevel", "ResLevelGroup", "ResLoot", "ResLootItem", "ResPlane", "ResSkill", "ResStage", "ResTask", "ResTask2", "ResTaskGoal", "ResTaskOrbit", "ResTrack", "ResUpgrade", "ResWave", "SkillCondition", "TbGlobalAttr", "TbEquipUpgrade", "TbGM", "TbPlane", "TbResAchievement", "TbResActivity", "TbRes<PERSON>uffer", "TbResBullet", "TbResChapter", "TbResEffect", "TbResEnemy", "TbResEquip", "TbResGameMode", "TbResItem", "TbResLevel", "TbResLevelGroup", "TbResLoot", "TbResSkill", "TbResStage", "TbResTask", "TbResTaskOrbit", "TbResTrack", "TbResUpgrade", "TbResWave", "Tables", "BindSocket", "BuffType", "BulletSourceType", "BulletType", "DamageType", "EffectType", "EquipClass", "GMTabID", "ItemEffectType", "ItemUseType", "ModeType", "MoneyType", "PlayCycle", "PropName", "QualityType", "ResCondType", "ResGoalType", "ResLootType", "ResPeriodType", "ResTaskClass", "SkillConditionType", "TargetType", "constructor", "_json_", "target", "buff<PERSON>", "undefined", "Error", "resolve", "tables", "vector2", "x", "y", "builtin", "vector3", "z", "vector4", "w", "con", "param", "id", "num", "type", "_ele0", "_e0", "push", "value", "tabID", "tabName", "name", "cmd", "desc", "effectId", "effect_id", "materialId", "materialCount", "material_id", "material_count", "MaxHP", "HPRecovery", "Attack", "Fortunate", "Miss", "BulletHurtResistance", "CollisionHurtResistance", "PickRadius", "FinalScore", "BombMax", "MaxEnergy", "EnergyRecovery", "propType", "prop<PERSON><PERSON><PERSON>", "prop_type", "prop_param", "inc", "ID", "Weight", "rating", "taskId", "groupId", "prevId", "periodType", "taskCond", "taskGoal", "accumulate", "rewardId", "linkTo", "task_id", "group_id", "prev_id", "period_type", "task_cond", "task_goal", "reward_id", "link_to", "_e", "uiRes", "precvId", "accumlate", "openDate", "openTime", "closeDate", "closeTime", "ui_res", "precv_id", "open_date", "open_time", "close_date", "close_time", "buffType", "conditionID", "duration", "durationBonus", "maxStack", "refreshType", "cycle", "cycleTimes", "EffectPath", "EffectSocket", "EffectPos", "effects", "source", "damageType", "prefab", "attackCoefficient", "penetrationCount", "penetrationCooldown", "hitBounceCount", "boundaryBounceCount", "damage_type", "attack_coefficient", "penetration_count", "penetration_cooldown", "hit_bounce_count", "boundary_bounce_count", "levelCount", "levelGroupCount", "strategy", "damageBonus", "life<PERSON><PERSON><PERSON>", "strategyList", "condType", "params", "cond_type", "description", "icon", "effectType", "effectValue", "effectParams", "effect_type", "effect_value", "effect_params", "comment", "rank", "purpose", "baseHp", "baseAtk", "moveSpeed", "killScore", "showHpBar", "useHitCount", "hitCountToKill", "hitCountInterval", "targetPriority", "immuneBulletDamage", "immuneCollideDamage", "ignoreBullet", "ignoreCollide", "immuneNuke", "immuneActiveSkill", "invincible", "collideLevel", "collideDamage", "dropRadius", "base_hp", "base_atk", "move_speed", "kill_score", "show_hp_bar", "use_hit_count", "hit_count_to_kill", "hit_count_interval", "target_priority", "immune_bullet_damage", "immune_collide_damage", "ignore_bullet", "ignore_collide", "immune_nuke", "immune_active_skill", "collide_level", "collide_damage", "drop_radius", "quality", "qualitySub", "equipClass", "props", "consumeItems", "quality_sub", "equip_class", "consume_items", "levelFrom", "levelTo", "propInc", "consumeMoney", "level_from", "level_to", "prop_inc", "consume_money", "modeType", "chapterID", "order", "resourceID", "conList", "times", "monType", "costParam1", "costParam2", "rebirthTimes", "rebirthCost", "power", "rogueID", "LevelLimit", "rogueFirst", "sweepLimit", "rewardID1", "rewardID2", "ratingList", "GoldProducion", "EnergyRecoverInterval", "EnergyRecoverValue", "ItemPickUpRadius", "PostHitProtection", "CameraTranslationMaxMoveSpeed", "CameraTranslationMoveDelay", "useType", "effectParam1", "effectParam2", "maxStack<PERSON>um", "use_type", "effect_param1", "effect_param2", "max_stack_num", "forbidFire", "forbidNBomb", "forbidActSkill", "planeCollisionScaling", "levelType", "normLevelCount", "normLevelST", "normSTList", "bossLevelCount", "bossLevelST", "bossSTList", "lootId", "lootGroup", "lootType", "itemList", "loot_id", "loot_group", "loot_type", "item_list", "itemId", "count", "rate", "protectTimes", "item_id", "protect_times", "starLevel", "portrait", "property", "materials", "star_level", "cd", "CostID", "CostNum", "ApplyBuffs", "mainStage", "subStage", "enemyGroupID", "delay", "enemyNorRate", "taskClass", "orbitGroupId", "orbitValue", "task_class", "orbit_group_id", "orbit_value", "taskOrbitId", "taskOrbitValue", "task_orbit_id", "task_orbit_value", "goalType", "goal_type", "index", "tpe", "roleLevel", "xp", "role_level", "planeType", "planeId", "interval", "offsetPos", "pos", "track", "trackParams", "rotatioSpeed", "FirstShootDelay", "_data", "length", "getData", "_dataList", "_json2_", "_v", "getDataList", "get", "data", "_dataMap", "Map", "set", "getDataMap", "key", "_TbGlobalAttr", "_TbEquipUpgrade", "_TbGM", "_TbPlane", "_TbResAchievement", "_TbResActivity", "_Tb<PERSON>es<PERSON>uffer", "_TbResBullet", "_TbResChapter", "_TbResEffect", "_TbResEnemy", "_TbResEquip", "_TbResGameMode", "_TbResItem", "_TbResLevel", "_TbResLevelGroup", "_TbResLoot", "_TbResSkill", "_TbResStage", "_TbResTask", "_TbResTaskOrbit", "_TbResTrack", "_TbResUpgrade", "_TbResWave", "loader"], "mappings": ";;;iBAoxBaA,S,EAoGAC,Q,EAyBAC,W,EAsBAC,Y,EA4BAC,W,EA6BAC,S,EAsBAC,E,EAoDAC,W,EAqBAC,a,EAyBAC,a,EAiEAC,iB,EAyBAC,O,EA4BAC,Y,EA4BAC,W,EAsBAC,c,EAuEAC,W,EAoHAC,S,EAsGAC,S,EA2FAC,U,EA4DAC,Y,EAsBAC,S,EA+DAC,Q,EA6LAC,Q,EAsEAC,e,EAwDAC,W,EAkKAC,a,EAsEAC,O,EAoFAC,Q,EA+DAC,a,EAyDAC,O,EAuCAC,W,EAiCAC,Q,EAiFAC,Q,EA0EAC,Q,EA+DAC,O,EA8HAC,Q,EA8GAC,W,EA0BAC,Y,EAgEAC,Q,EAmCAC,U,EAmCAC,O,EAyGAC,c,EAuBAC,Y,EAqDAC,c,EA4BAC,I,EA4BAC,O,EA+BAC,gB,EA+BAC,a,EA+BAC,W,EA+BAC,W,EA+BAC,Y,EA+BAC,W,EA+BAC,U,EA+BAC,U,EA+BAC,a,EA+BAC,S,EA+BAC,U,EA+BAC,e,EA+BAC,S,EA+BAC,U,EA+BAC,U,EA+BAC,S,EA+BAC,c,EA+BAC,U,EA+BAC,Y,EA+BAC,S,EAiCAC,M;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAniIb;AACA;AACA;AACA;AACA;AACA;AACA;;AAIA;AACA;AACA;4BACYC,U,0BAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;eAAAA,U;;AAaZ;AACA;AACA;;;0BACYC,Q,0BAAAA,Q;AAAAA,QAAAA,Q,CAAAA,Q;AAAAA,QAAAA,Q,CAAAA,Q;AAAAA,QAAAA,Q,CAAAA,Q;eAAAA,Q;;AAiBZ;AACA;AACA;;;kCACYC,gB,0BAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;AAAAA,QAAAA,gB,CAAAA,gB;eAAAA,gB;;AAiBZ;AACA;AACA;;;4BACYC,U,0BAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;eAAAA,U;;AAaZ;AACA;AACA;;;4BACYC,U,0BAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;eAAAA,U;;AAyBZ;AACA;AACA;;;4BACYC,U,0BAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;eAAAA,U;;AAqLZ;AACA;AACA;;;4BACYC,U,0BAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;eAAAA,U;;AAyBZ;AACA;AACA;;;yBACYC,O,0BAAAA,O;AAAAA,QAAAA,O,CAAAA,O;AAAAA,QAAAA,O,CAAAA,O;eAAAA,O;;AAaZ;AACA;AACA;;;gCACYC,c,0BAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;eAAAA,c;;AAiCZ;AACA;AACA;;;6BACYC,W,0BAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;eAAAA,W;;AAiBZ;AACA;AACA;;;0BACYC,Q,0BAAAA,Q;AAAAA,QAAAA,Q,CAAAA,Q;AAAAA,QAAAA,Q,CAAAA,Q;AAAAA,QAAAA,Q,CAAAA,Q;AAAAA,QAAAA,Q,CAAAA,Q;AAAAA,QAAAA,Q,CAAAA,Q;eAAAA,Q;;AAyBZ;AACA;AACA;;;2BACYC,S,0BAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;eAAAA,S;;AAyBZ;AACA;AACA;;;2BACYC,S,0BAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;eAAAA,S;;AAaZ;AACA;AACA;;;0BACYC,Q,0BAAAA,Q;AAAAA,QAAAA,Q,CAAAA,Q;AAAAA,QAAAA,Q,CAAAA,Q;AAAAA,QAAAA,Q,CAAAA,Q;eAAAA,Q;;AAiBZ;AACA;AACA;;;6BACYC,W,0BAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;eAAAA,W;;;6BAiCAC,W,0BAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;eAAAA,W;;;6BA6BAC,W,0BAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;eAAAA,W;;;6BAqHAC,W,0BAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;eAAAA,W;;;+BAaAC,a,0BAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;eAAAA,a;;;8BAqBAC,Y,0BAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;eAAAA,Y;;AAqCZ;AACA;AACA;;;oCACYC,kB,0BAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;eAAAA,kB;;AASZ;AACA;AACA;;;4BACYC,U,0BAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;eAAAA,U;;;2BAiCCxF,S,GAAN,MAAMA,SAAN,CAAgB;AAEnByF,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAOhBC,MAPgB;AAAA,eAQhBC,MARgB;;AACrB,cAAIF,MAAM,CAACC,MAAP,KAAkBE,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAKH,MAAL,GAAcD,MAAM,CAACC,MAArB;;AACA,cAAID,MAAM,CAACE,MAAP,KAAkBC,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAKF,MAAL,GAAcF,MAAM,CAACE,MAArB;AACH;;AAKDG,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAfkB,O;;;AAsBhB,cAAMC,OAAN,CAAc;AAEjBR,UAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,iBAOhBQ,CAPgB;AAAA,iBAQhBC,CARgB;;AACrB,gBAAIT,MAAM,CAACQ,CAAP,KAAaL,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKI,CAAL,GAASR,MAAM,CAACQ,CAAhB;;AACA,gBAAIR,MAAM,CAACS,CAAP,KAAaN,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKK,CAAL,GAAST,MAAM,CAACS,CAAhB;AACH;;AAKDJ,UAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAfgB;;;SADJI,O,uBAAAA,O;;;AAuBV,cAAMC,OAAN,CAAc;AAEjBZ,UAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,iBAShBQ,CATgB;AAAA,iBAUhBC,CAVgB;AAAA,iBAWhBG,CAXgB;;AACrB,gBAAIZ,MAAM,CAACQ,CAAP,KAAaL,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKI,CAAL,GAASR,MAAM,CAACQ,CAAhB;;AACA,gBAAIR,MAAM,CAACS,CAAP,KAAaN,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKK,CAAL,GAAST,MAAM,CAACS,CAAhB;;AACA,gBAAIT,MAAM,CAACY,CAAP,KAAaT,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKQ,CAAL,GAASZ,MAAM,CAACY,CAAhB;AACH;;AAMDP,UAAAA,OAAO,CAACC,MAAD,EAAgB,CAItB;;AAnBgB;;;SADJI,O,uBAAAA,O;;;AA2BV,cAAMG,OAAN,CAAc;AAEjBd,UAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,iBAWhBQ,CAXgB;AAAA,iBAYhBC,CAZgB;AAAA,iBAahBG,CAbgB;AAAA,iBAchBE,CAdgB;;AACrB,gBAAId,MAAM,CAACQ,CAAP,KAAaL,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKI,CAAL,GAASR,MAAM,CAACQ,CAAhB;;AACA,gBAAIR,MAAM,CAACS,CAAP,KAAaN,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKK,CAAL,GAAST,MAAM,CAACS,CAAhB;;AACA,gBAAIT,MAAM,CAACY,CAAP,KAAaT,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKQ,CAAL,GAASZ,MAAM,CAACY,CAAhB;;AACA,gBAAIZ,MAAM,CAACc,CAAP,KAAaX,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKU,CAAL,GAASd,MAAM,CAACc,CAAhB;AACH;;AAODT,UAAAA,OAAO,CAACC,MAAD,EAAgB,CAKtB;;AAvBgB;;;SADJI,O,uBAAAA,O;;0BA+BJnG,Q,GAAN,MAAMA,QAAN,CAAe;AAElBwF,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAOhBe,GAPgB;AAAA,eAQhBC,KARgB;;AACrB,cAAIhB,MAAM,CAACe,GAAP,KAAeZ,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKW,GAAL,GAAWf,MAAM,CAACe,GAAlB;;AACA,cAAIf,MAAM,CAACgB,KAAP,KAAiBb,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKY,KAAL,GAAahB,MAAM,CAACgB,KAApB;AACH;;AAKDX,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAfiB,O;AAsBtB;AACA;AACA;;;6BACa9F,W,GAAN,MAAMA,WAAN,CAAkB;AAErBuF,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAOhBiB,EAPgB;AAAA,eAQhBC,GARgB;;AACrB,cAAIlB,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAACkB,GAAP,KAAef,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKc,GAAL,GAAWlB,MAAM,CAACkB,GAAlB;AACH;;AAKDb,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAfoB,O;;8BAsBZ7F,Y,GAAN,MAAMA,YAAN,CAAmB;AAEtBsF,QAAAA,WAAW,CAACC,MAAD,EAAc;AAOzB;AACJ;AACA;AAT6B,eAUhBmB,IAVgB;;AAWzB;AACJ;AACA;AAb6B,eAchBD,GAdgB;;AACrB,cAAIlB,MAAM,CAACmB,IAAP,KAAgBhB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKe,IAAL,GAAYnB,MAAM,CAACmB,IAAnB;;AACA,cAAInB,MAAM,CAACkB,GAAP,KAAef,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKc,GAAL,GAAWlB,MAAM,CAACkB,GAAlB;AACH;;AAWDb,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AArBqB,O;;6BA4Bb5F,W,GAAN,MAAMA,WAAN,CAAkB;AAErBqF,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAShBmB,IATgB;AAAA,eAUhBlB,MAVgB;AAAA,eAWhBe,KAXgB;;AACrB,cAAIhB,MAAM,CAACmB,IAAP,KAAgBhB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKe,IAAL,GAAYnB,MAAM,CAACmB,IAAnB;;AACA,cAAInB,MAAM,CAACC,MAAP,KAAkBE,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAKH,MAAL,GAAcD,MAAM,CAACC,MAArB;;AACA,cAAID,MAAM,CAACgB,KAAP,KAAiBb,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD;AAAE,iBAAKY,KAAL,GAAa,EAAb;;AAAiB,iBAAI,IAAII,KAAR,IAAiBpB,MAAM,CAACgB,KAAxB,EAA+B;AAAE,kBAAIK,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKJ,KAAL,CAAWM,IAAX,CAAgBD,GAAhB;AAAsB;AAAC;AACpG;;AAMDhB,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAItB;;AAnBoB,O;AA0BzB;AACA;AACA;;;2BACa3F,S,GAAN,MAAMA,SAAN,CAAgB;AAEnBoF,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAOhBiB,EAPgB;AAAA,eAQhBM,KARgB;;AACrB,cAAIvB,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAACuB,KAAP,KAAiBpB,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKmB,KAAL,GAAavB,MAAM,CAACuB,KAApB;AACH;;AAKDlB,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAfkB,O;;oBAsBV1F,E,GAAN,MAAMA,EAAN,CAAS;AAEZmF,QAAAA,WAAW,CAACC,MAAD,EAAc;AAazB;AACJ;AACA;AAf6B,eAgBhBwB,KAhBgB;;AAiBzB;AACJ;AACA;AAnB6B,eAoBhBC,OApBgB;;AAqBzB;AACJ;AACA;AAvB6B,eAwBhBC,IAxBgB;;AAyBzB;AACJ;AACA;AA3B6B,eA4BhBC,GA5BgB;;AA6BzB;AACJ;AACA;AA/B6B,eAgChBC,IAhCgB;;AACrB,cAAI5B,MAAM,CAACwB,KAAP,KAAiBrB,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKoB,KAAL,GAAaxB,MAAM,CAACwB,KAApB;;AACA,cAAIxB,MAAM,CAACyB,OAAP,KAAmBtB,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKqB,OAAL,GAAezB,MAAM,CAACyB,OAAtB;;AACA,cAAIzB,MAAM,CAAC0B,IAAP,KAAgBvB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKsB,IAAL,GAAY1B,MAAM,CAAC0B,IAAnB;;AACA,cAAI1B,MAAM,CAAC2B,GAAP,KAAexB,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKuB,GAAL,GAAW3B,MAAM,CAAC2B,GAAlB;;AACA,cAAI3B,MAAM,CAAC4B,IAAP,KAAgBzB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKwB,IAAL,GAAY5B,MAAM,CAAC4B,IAAnB;AACH;;AAuBDvB,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAMtB;;AA1CW,O;AAiDhB;AACA;AACA;;;6BACazF,W,GAAN,MAAMA,WAAN,CAAkB;AAErBkF,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAKhB6B,QALgB;;AACrB,cAAI7B,MAAM,CAAC8B,SAAP,KAAqB3B,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKyB,QAAL,GAAgB7B,MAAM,CAAC8B,SAAvB;AACH;;AAIDzB,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAEtB;;AAXoB,O;AAkBzB;AACA;AACA;;;+BACaxF,a,GAAN,MAAMA,aAAN,CAAoB;AAEvBiF,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAOhB+B,UAPgB;AAAA,eAQhBC,aARgB;;AACrB,cAAIhC,MAAM,CAACiC,WAAP,KAAuB9B,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAK2B,UAAL,GAAkB/B,MAAM,CAACiC,WAAzB;;AACA,cAAIjC,MAAM,CAACkC,cAAP,KAA0B/B,SAA9B,EAAyC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC9D,eAAK4B,aAAL,GAAqBhC,MAAM,CAACkC,cAA5B;AACH;;AAKD7B,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAfsB,O;AAsB3B;AACA;AACA;;;+BACavF,a,GAAN,MAAMA,aAAN,CAAoB;AAEvBgF,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eA2BhBmC,KA3BgB;AAAA,eA4BhBC,UA5BgB;AAAA,eA6BhBC,MA7BgB;AAAA,eA8BhBC,SA9BgB;AAAA,eA+BhBC,IA/BgB;AAAA,eAgChBC,oBAhCgB;AAAA,eAiChBC,uBAjCgB;AAAA,eAkChBC,UAlCgB;AAAA,eAmChBC,UAnCgB;AAAA,eAoChBC,OApCgB;AAAA,eAqChBC,SArCgB;AAAA,eAsChBC,cAtCgB;;AACrB,cAAI9C,MAAM,CAACmC,KAAP,KAAiBhC,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAK+B,KAAL,GAAanC,MAAM,CAACmC,KAApB;;AACA,cAAInC,MAAM,CAACoC,UAAP,KAAsBjC,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKgC,UAAL,GAAkBpC,MAAM,CAACoC,UAAzB;;AACA,cAAIpC,MAAM,CAACqC,MAAP,KAAkBlC,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAKiC,MAAL,GAAcrC,MAAM,CAACqC,MAArB;;AACA,cAAIrC,MAAM,CAACsC,SAAP,KAAqBnC,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKkC,SAAL,GAAiBtC,MAAM,CAACsC,SAAxB;;AACA,cAAItC,MAAM,CAACuC,IAAP,KAAgBpC,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKmC,IAAL,GAAYvC,MAAM,CAACuC,IAAnB;;AACA,cAAIvC,MAAM,CAACwC,oBAAP,KAAgCrC,SAApC,EAA+C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpE,eAAKoC,oBAAL,GAA4BxC,MAAM,CAACwC,oBAAnC;;AACA,cAAIxC,MAAM,CAACyC,uBAAP,KAAmCtC,SAAvC,EAAkD;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvE,eAAKqC,uBAAL,GAA+BzC,MAAM,CAACyC,uBAAtC;;AACA,cAAIzC,MAAM,CAAC0C,UAAP,KAAsBvC,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKsC,UAAL,GAAkB1C,MAAM,CAAC0C,UAAzB;;AACA,cAAI1C,MAAM,CAAC2C,UAAP,KAAsBxC,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKuC,UAAL,GAAkB3C,MAAM,CAAC2C,UAAzB;;AACA,cAAI3C,MAAM,CAAC4C,OAAP,KAAmBzC,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKwC,OAAL,GAAe5C,MAAM,CAAC4C,OAAtB;;AACA,cAAI5C,MAAM,CAAC6C,SAAP,KAAqB1C,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKyC,SAAL,GAAiB7C,MAAM,CAAC6C,SAAxB;;AACA,cAAI7C,MAAM,CAAC8C,cAAP,KAA0B3C,SAA9B,EAAyC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC9D,eAAK0C,cAAL,GAAsB9C,MAAM,CAAC8C,cAA7B;AACH;;AAeDzC,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAatB;;AAvDsB,O;AA8D3B;AACA;AACA;;;mCACatF,iB,GAAN,MAAMA,iBAAN,CAAwB;AAE3B+E,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAOhB+C,QAPgB;AAAA,eAQhBC,SARgB;;AACrB,cAAIhD,MAAM,CAACiD,SAAP,KAAqB9C,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAK2C,QAAL,GAAgB/C,MAAM,CAACiD,SAAvB;;AACA,cAAIjD,MAAM,CAACkD,UAAP,KAAsB/C,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D;AAAE,iBAAK4C,SAAL,GAAiB,EAAjB;;AAAqB,iBAAI,IAAI5B,KAAR,IAAiBpB,MAAM,CAACkD,UAAxB,EAAoC;AAAE,kBAAI7B,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAK4B,SAAL,CAAe1B,IAAf,CAAoBD,GAApB;AAA0B;AAAC;AACjH;;AAKDhB,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAf0B,O;AAsB/B;AACA;AACA;;;yBACarF,O,GAAN,MAAMA,OAAN,CAAc;AAEjB8E,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAOhBiB,EAPgB;;AAQzB;AACJ;AACA;AAV6B,eAWhBkC,GAXgB;;AACrB,cAAInD,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAACmD,GAAP,KAAehD,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAK+C,GAAL,GAAWnD,MAAM,CAACmD,GAAlB;AACH;;AAQD9C,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAlBgB,O;AAyBrB;AACA;AACA;;;8BACapF,Y,GAAN,MAAMA,YAAN,CAAmB;AAEtB6E,QAAAA,WAAW,CAACC,MAAD,EAAc;AAOzB;AACJ;AACA;AAT6B,eAUhBoD,EAVgB;;AAWzB;AACJ;AACA;AAb6B,eAchBC,MAdgB;;AACrB,cAAIrD,MAAM,CAACoD,EAAP,KAAcjD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKgD,EAAL,GAAUpD,MAAM,CAACoD,EAAjB;;AACA,cAAIpD,MAAM,CAACqD,MAAP,KAAkBlD,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAKiD,MAAL,GAAcrD,MAAM,CAACqD,MAArB;AACH;;AAWDhD,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AArBqB,O;;6BA4BbnF,W,GAAN,MAAMA,WAAN,CAAkB;AAErB4E,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAOhBsD,MAPgB;AAAA,eAQhBtC,KARgB;;AACrB,cAAIhB,MAAM,CAACsD,MAAP,KAAkBnD,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAKkD,MAAL,GAActD,MAAM,CAACsD,MAArB;;AACA,cAAItD,MAAM,CAACgB,KAAP,KAAiBb,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKY,KAAL,GAAahB,MAAM,CAACgB,KAApB;AACH;;AAKDX,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAfoB,O;;gCAsBZlF,c,GAAN,MAAMA,cAAN,CAAqB;AAExB2E,QAAAA,WAAW,CAACC,MAAD,EAAc;AAqBzB;AACJ;AACA;AAvB6B,eAwBhBuD,MAxBgB;;AAyBzB;AACJ;AACA;AA3B6B,eA4BhBC,OA5BgB;;AA6BzB;AACJ;AACA;AA/B6B,eAgChBC,MAhCgB;;AAiCzB;AACJ;AACA;AAnC6B,eAoChBC,UApCgB;AAAA,eAqChBC,QArCgB;AAAA,eAsChBC,QAtCgB;;AAuCzB;AACJ;AACA;AAzC6B,eA0ChBC,UA1CgB;;AA2CzB;AACJ;AACA;AA7C6B,eA8ChBC,QA9CgB;;AA+CzB;AACJ;AACA;AAjD6B,eAkDhBC,MAlDgB;;AACrB,cAAI/D,MAAM,CAACgE,OAAP,KAAmB7D,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKmD,MAAL,GAAcvD,MAAM,CAACgE,OAArB;;AACA,cAAIhE,MAAM,CAACiE,QAAP,KAAoB9D,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKoD,OAAL,GAAexD,MAAM,CAACiE,QAAtB;;AACA,cAAIjE,MAAM,CAACkE,OAAP,KAAmB/D,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKqD,MAAL,GAAczD,MAAM,CAACkE,OAArB;;AACA,cAAIlE,MAAM,CAACmE,WAAP,KAAuBhE,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKsD,UAAL,GAAkB1D,MAAM,CAACmE,WAAzB;;AACA,cAAInE,MAAM,CAACoE,SAAP,KAAqBjE,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD;AAAE,iBAAKuD,QAAL,GAAgB,EAAhB;;AAAoB,iBAAI,IAAIvC,KAAR,IAAiBpB,MAAM,CAACoE,SAAxB,EAAmC;AAAE,kBAAI/C,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAI5F,YAAJ,CAAiB2F,KAAjB,CAAN;AAA+B,mBAAKuC,QAAL,CAAcrC,IAAd,CAAmBD,GAAnB;AAAyB;AAAC;;AAC7H,cAAIrB,MAAM,CAACqE,SAAP,KAAqBlE,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKwD,QAAL,GAAgB,IAAIlH,WAAJ,CAAgBsD,MAAM,CAACqE,SAAvB,CAAhB;;AACA,cAAIrE,MAAM,CAAC6D,UAAP,KAAsB1D,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKyD,UAAL,GAAkB7D,MAAM,CAAC6D,UAAzB;;AACA,cAAI7D,MAAM,CAACsE,SAAP,KAAqBnE,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAK0D,QAAL,GAAgB9D,MAAM,CAACsE,SAAvB;;AACA,cAAItE,MAAM,CAACuE,OAAP,KAAmBpE,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAK2D,MAAL,GAAc/D,MAAM,CAACuE,OAArB;AACH;;AAiCDlE,QAAAA,OAAO,CAACC,MAAD,EAAgB;AAAA;;AAKnB,eAAK,IAAIkE,EAAT,IAAe,KAAKb,QAApB,EAA8B;AAAEa,YAAAA,EAAE,QAAF,IAAAA,EAAE,CAAEnE,OAAJ,CAAYC,MAAZ;AAAsB;;AACtD,iCAAKsD,QAAL,4BAAevD,OAAf,CAAuBC,MAAvB;AAIH;;AAhEuB,O;;6BAuEfjF,W,GAAN,MAAMA,WAAN,CAAkB;AAErB0E,QAAAA,WAAW,CAACC,MAAD,EAAc;AAiCzB;AACJ;AACA;AAnC6B,eAoChBuD,MApCgB;;AAqCzB;AACJ;AACA;AAvC6B,eAwChBC,OAxCgB;;AAyCzB;AACJ;AACA;AA3C6B,eA4ChB5B,IA5CgB;;AA6CzB;AACJ;AACA;AA/C6B,eAgDhB6C,KAhDgB;;AAiDzB;AACJ;AACA;AAnD6B,eAoDhBC,OApDgB;;AAqDzB;AACJ;AACA;AAvD6B,eAwDhBhB,UAxDgB;;AAyDzB;AACJ;AACA;AA3D6B,eA4DhBC,QA5DgB;AAAA,eA6DhBC,QA7DgB;;AA8DzB;AACJ;AACA;AAhE6B,eAiEhBe,SAjEgB;;AAkEzB;AACJ;AACA;AApE6B,eAqEhBb,QArEgB;;AAsEzB;AACJ;AACA;AAxE6B,eAyEhBc,QAzEgB;;AA0EzB;AACJ;AACA;AA5E6B,eA6EhBC,QA7EgB;;AA8EzB;AACJ;AACA;AAhF6B,eAiFhBC,SAjFgB;;AAkFzB;AACJ;AACA;AApF6B,eAqFhBC,SArFgB;;AAsFzB;AACJ;AACA;AAxF6B,eAyFhBhB,MAzFgB;;AACrB,cAAI/D,MAAM,CAACgE,OAAP,KAAmB7D,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKmD,MAAL,GAAcvD,MAAM,CAACgE,OAArB;;AACA,cAAIhE,MAAM,CAACiE,QAAP,KAAoB9D,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKoD,OAAL,GAAexD,MAAM,CAACiE,QAAtB;;AACA,cAAIjE,MAAM,CAAC4B,IAAP,KAAgBzB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKwB,IAAL,GAAY5B,MAAM,CAAC4B,IAAnB;;AACA,cAAI5B,MAAM,CAACgF,MAAP,KAAkB7E,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAKqE,KAAL,GAAazE,MAAM,CAACgF,MAApB;;AACA,cAAIhF,MAAM,CAACiF,QAAP,KAAoB9E,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKsE,OAAL,GAAe1E,MAAM,CAACiF,QAAtB;;AACA,cAAIjF,MAAM,CAACmE,WAAP,KAAuBhE,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKsD,UAAL,GAAkB1D,MAAM,CAACmE,WAAzB;;AACA,cAAInE,MAAM,CAACoE,SAAP,KAAqBjE,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD;AAAE,iBAAKuD,QAAL,GAAgB,EAAhB;;AAAoB,iBAAI,IAAIvC,KAAR,IAAiBpB,MAAM,CAACoE,SAAxB,EAAmC;AAAE,kBAAI/C,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAI5F,YAAJ,CAAiB2F,KAAjB,CAAN;AAA+B,mBAAKuC,QAAL,CAAcrC,IAAd,CAAmBD,GAAnB;AAAyB;AAAC;;AAC7H,cAAIrB,MAAM,CAACqE,SAAP,KAAqBlE,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKwD,QAAL,GAAgB,IAAIlH,WAAJ,CAAgBsD,MAAM,CAACqE,SAAvB,CAAhB;;AACA,cAAIrE,MAAM,CAAC2E,SAAP,KAAqBxE,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKuE,SAAL,GAAiB3E,MAAM,CAAC2E,SAAxB;;AACA,cAAI3E,MAAM,CAACsE,SAAP,KAAqBnE,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAK0D,QAAL,GAAgB9D,MAAM,CAACsE,SAAvB;;AACA,cAAItE,MAAM,CAACkF,SAAP,KAAqB/E,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKwE,QAAL,GAAgB5E,MAAM,CAACkF,SAAvB;;AACA,cAAIlF,MAAM,CAACmF,SAAP,KAAqBhF,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKyE,QAAL,GAAgB7E,MAAM,CAACmF,SAAvB;;AACA,cAAInF,MAAM,CAACoF,UAAP,KAAsBjF,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK0E,SAAL,GAAiB9E,MAAM,CAACoF,UAAxB;;AACA,cAAIpF,MAAM,CAACqF,UAAP,KAAsBlF,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK2E,SAAL,GAAiB/E,MAAM,CAACqF,UAAxB;;AACA,cAAIrF,MAAM,CAACuE,OAAP,KAAmBpE,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAK2D,MAAL,GAAc/D,MAAM,CAACuE,OAArB;AACH;;AA4DDlE,QAAAA,OAAO,CAACC,MAAD,EAAgB;AAAA;;AAOnB,eAAK,IAAIkE,EAAT,IAAe,KAAKb,QAApB,EAA8B;AAAEa,YAAAA,EAAE,QAAF,IAAAA,EAAE,CAAEnE,OAAJ,CAAYC,MAAZ;AAAsB;;AACtD,kCAAKsD,QAAL,6BAAevD,OAAf,CAAuBC,MAAvB;AAQH;;AA7GoB,O;;2BAoHZhF,S,GAAN,MAAMA,SAAN,CAAgB;AAEnByE,QAAAA,WAAW,CAACC,MAAD,EAAc;AA6BzB;AACJ;AACA;AA/B6B,eAgChBiB,EAhCgB;;AAiCzB;AACJ;AACA;AAnC6B,eAoChBqE,QApCgB;;AAqCzB;AACJ;AACA;AAvC6B,eAwChBC,WAxCgB;;AAyCzB;AACJ;AACA;AA3C6B,eA4ChBC,QA5CgB;;AA6CzB;AACJ;AACA;AA/C6B,eAgDhBC,aAhDgB;;AAiDzB;AACJ;AACA;AAnD6B,eAoDhBC,QApDgB;;AAqDzB;AACJ;AACA;AAvD6B,eAwDhBC,WAxDgB;;AAyDzB;AACJ;AACA;AA3D6B,eA4DhBC,KA5DgB;;AA6DzB;AACJ;AACA;AA/D6B,eAgEhBC,UAhEgB;;AAiEzB;AACJ;AACA;AAnE6B,eAoEhBC,UApEgB;;AAqEzB;AACJ;AACA;AAvE6B,eAwEhBC,YAxEgB;;AAyEzB;AACJ;AACA;AA3E6B,eA4EhBC,SA5EgB;AAAA,eA6EhBC,OA7EgB;;AACrB,cAAIjG,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAACsF,QAAP,KAAoBnF,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKkF,QAAL,GAAgBtF,MAAM,CAACsF,QAAvB;;AACA,cAAItF,MAAM,CAACuF,WAAP,KAAuBpF,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKmF,WAAL,GAAmBvF,MAAM,CAACuF,WAA1B;;AACA,cAAIvF,MAAM,CAACwF,QAAP,KAAoBrF,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKoF,QAAL,GAAgBxF,MAAM,CAACwF,QAAvB;;AACA,cAAIxF,MAAM,CAACyF,aAAP,KAAyBtF,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAKqF,aAAL,GAAqBzF,MAAM,CAACyF,aAA5B;;AACA,cAAIzF,MAAM,CAAC0F,QAAP,KAAoBvF,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKsF,QAAL,GAAgB1F,MAAM,CAAC0F,QAAvB;;AACA,cAAI1F,MAAM,CAAC2F,WAAP,KAAuBxF,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKuF,WAAL,GAAmB3F,MAAM,CAAC2F,WAA1B;;AACA,cAAI3F,MAAM,CAAC4F,KAAP,KAAiBzF,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKwF,KAAL,GAAa5F,MAAM,CAAC4F,KAApB;;AACA,cAAI5F,MAAM,CAAC6F,UAAP,KAAsB1F,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKyF,UAAL,GAAkB7F,MAAM,CAAC6F,UAAzB;;AACA,cAAI7F,MAAM,CAAC8F,UAAP,KAAsB3F,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK0F,UAAL,GAAkB9F,MAAM,CAAC8F,UAAzB;;AACA,cAAI9F,MAAM,CAAC+F,YAAP,KAAwB5F,SAA5B,EAAuC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC5D,eAAK2F,YAAL,GAAoB/F,MAAM,CAAC+F,YAA3B;;AACA,cAAI/F,MAAM,CAACgG,SAAP,KAAqB7F,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAK4F,SAAL,GAAiB,IAAItF,OAAO,CAACH,OAAZ,CAAoBP,MAAM,CAACgG,SAA3B,CAAjB;;AACA,cAAIhG,MAAM,CAACiG,OAAP,KAAmB9F,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD;AAAE,iBAAK6F,OAAL,GAAe,EAAf;;AAAmB,iBAAI,IAAI7E,KAAR,IAAiBpB,MAAM,CAACiG,OAAxB,EAAiC;AAAE,kBAAI5E,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAI3G,WAAJ,CAAgB0G,KAAhB,CAAN;AAA8B,mBAAK6E,OAAL,CAAa3E,IAAb,CAAkBD,GAAlB;AAAwB;AAAC;AAC3H;;AAoDDhB,QAAAA,OAAO,CAACC,MAAD,EAAgB,CActB;;AA/FkB,O;;2BAsGV/E,S,GAAN,MAAMA,SAAN,CAAgB;AAEnBwE,QAAAA,WAAW,CAACC,MAAD,EAAc;AAyBzB;AACJ;AACA;AA3B6B,eA4BhBiB,EA5BgB;;AA6BzB;AACJ;AACA;AA/B6B,eAgChBS,IAhCgB;;AAiCzB;AACJ;AACA;AAnC6B,eAoChBwE,MApCgB;;AAqCzB;AACJ;AACA;AAvC6B,eAwChB/E,IAxCgB;;AAyCzB;AACJ;AACA;AA3C6B,eA4ChBgF,UA5CgB;;AA6CzB;AACJ;AACA;AA/C6B,eAgDhBC,MAhDgB;;AAiDzB;AACJ;AACA;AAnD6B,eAoDhBC,iBApDgB;;AAqDzB;AACJ;AACA;AAvD6B,eAwDhBC,gBAxDgB;;AAyDzB;AACJ;AACA;AA3D6B,eA4DhBC,mBA5DgB;;AA6DzB;AACJ;AACA;AA/D6B,eAgEhBC,cAhEgB;;AAiEzB;AACJ;AACA;AAnE6B,eAoEhBC,mBApEgB;;AACrB,cAAIzG,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAAC0B,IAAP,KAAgBvB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKsB,IAAL,GAAY1B,MAAM,CAAC0B,IAAnB;;AACA,cAAI1B,MAAM,CAACkG,MAAP,KAAkB/F,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAK8F,MAAL,GAAclG,MAAM,CAACkG,MAArB;;AACA,cAAIlG,MAAM,CAACmB,IAAP,KAAgBhB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKe,IAAL,GAAYnB,MAAM,CAACmB,IAAnB;;AACA,cAAInB,MAAM,CAAC0G,WAAP,KAAuBvG,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAK+F,UAAL,GAAkBnG,MAAM,CAAC0G,WAAzB;;AACA,cAAI1G,MAAM,CAACoG,MAAP,KAAkBjG,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAKgG,MAAL,GAAcpG,MAAM,CAACoG,MAArB;;AACA,cAAIpG,MAAM,CAAC2G,kBAAP,KAA8BxG,SAAlC,EAA6C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClE,eAAKiG,iBAAL,GAAyBrG,MAAM,CAAC2G,kBAAhC;;AACA,cAAI3G,MAAM,CAAC4G,iBAAP,KAA6BzG,SAAjC,EAA4C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjE,eAAKkG,gBAAL,GAAwBtG,MAAM,CAAC4G,iBAA/B;;AACA,cAAI5G,MAAM,CAAC6G,oBAAP,KAAgC1G,SAApC,EAA+C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpE,eAAKmG,mBAAL,GAA2BvG,MAAM,CAAC6G,oBAAlC;;AACA,cAAI7G,MAAM,CAAC8G,gBAAP,KAA4B3G,SAAhC,EAA2C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAChE,eAAKoG,cAAL,GAAsBxG,MAAM,CAAC8G,gBAA7B;;AACA,cAAI9G,MAAM,CAAC+G,qBAAP,KAAiC5G,SAArC,EAAgD;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrE,eAAKqG,mBAAL,GAA2BzG,MAAM,CAAC+G,qBAAlC;AACH;;AA+CD1G,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAYtB;;AApFkB,O;;4BA2FV9E,U,GAAN,MAAMA,UAAN,CAAiB;AAEpBuE,QAAAA,WAAW,CAACC,MAAD,EAAc;AAiBzB;AACJ;AACA;AAnB6B,eAoBhBiB,EApBgB;;AAqBzB;AACJ;AACA;AAvB6B,eAwBhB+F,UAxBgB;;AAyBzB;AACJ;AACA;AA3B6B,eA4BhBC,eA5BgB;;AA6BzB;AACJ;AACA;AA/B6B,eAgChBC,QAhCgB;;AAiCzB;AACJ;AACA;AAnC6B,eAoChBC,WApCgB;;AAqCzB;AACJ;AACA;AAvC6B,eAwChBC,UAxCgB;AAAA,eAyChBC,YAzCgB;;AACrB,cAAIrH,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAACgH,UAAP,KAAsB7G,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK4G,UAAL,GAAkBhH,MAAM,CAACgH,UAAzB;;AACA,cAAIhH,MAAM,CAACiH,eAAP,KAA2B9G,SAA/B,EAA0C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC/D,eAAK6G,eAAL,GAAuBjH,MAAM,CAACiH,eAA9B;;AACA,cAAIjH,MAAM,CAACkH,QAAP,KAAoB/G,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAK8G,QAAL,GAAgBlH,MAAM,CAACkH,QAAvB;;AACA,cAAIlH,MAAM,CAACmH,WAAP,KAAuBhH,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAK+G,WAAL,GAAmBnH,MAAM,CAACmH,WAA1B;;AACA,cAAInH,MAAM,CAACoH,UAAP,KAAsBjH,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKgH,UAAL,GAAkBpH,MAAM,CAACoH,UAAzB;;AACA,cAAIpH,MAAM,CAACqH,YAAP,KAAwBlH,SAA5B,EAAuC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC5D;AAAE,iBAAKiH,YAAL,GAAoB,EAApB;;AAAwB,iBAAI,IAAIjG,KAAR,IAAiBpB,MAAM,CAACqH,YAAxB,EAAsC;AAAE,kBAAIhG,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAInG,YAAJ,CAAiBkG,KAAjB,CAAN;AAA+B,mBAAKiG,YAAL,CAAkB/F,IAAlB,CAAuBD,GAAvB;AAA6B;AAAC;AAC3I;;AA4BDhB,QAAAA,OAAO,CAACC,MAAD,EAAgB;AAOnB,eAAK,IAAIkE,EAAT,IAAe,KAAK6C,YAApB,EAAkC;AAAE7C,YAAAA,EAAE,QAAF,IAAAA,EAAE,CAAEnE,OAAJ,CAAYC,MAAZ;AAAsB;AAC7D;;AArDmB,O;;8BA4DX7E,Y,GAAN,MAAMA,YAAN,CAAmB;AAEtBsE,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAOhBsH,QAPgB;AAAA,eAQhBC,MARgB;;AACrB,cAAIvH,MAAM,CAACwH,SAAP,KAAqBrH,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKkH,QAAL,GAAgBtH,MAAM,CAACwH,SAAvB;;AACA,cAAIxH,MAAM,CAACuH,MAAP,KAAkBpH,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD;AAAE,iBAAKmH,MAAL,GAAc,EAAd;;AAAkB,iBAAI,IAAInG,KAAR,IAAiBpB,MAAM,CAACuH,MAAxB,EAAgC;AAAE,kBAAIlG,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKmG,MAAL,CAAYjG,IAAZ,CAAiBD,GAAjB;AAAuB;AAAC;AACvG;;AAKDhB,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAfqB,O;;2BAsBb5E,S,GAAN,MAAMA,SAAN,CAAgB;AAEnBqE,QAAAA,WAAW,CAACC,MAAD,EAAc;AAiBzB;AACJ;AACA;AAnB6B,eAoBhBiB,EApBgB;;AAqBzB;AACJ;AACA;AAvB6B,eAwBhBS,IAxBgB;;AAyBzB;AACJ;AACA;AA3B6B,eA4BhB+F,WA5BgB;;AA6BzB;AACJ;AACA;AA/B6B,eAgChBC,IAhCgB;;AAiCzB;AACJ;AACA;AAnC6B,eAoChBC,UApCgB;;AAqCzB;AACJ;AACA;AAvC6B,eAwChBC,WAxCgB;;AAyCzB;AACJ;AACA;AA3C6B,eA4ChBC,YA5CgB;;AACrB,cAAI7H,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAAC0B,IAAP,KAAgBvB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKsB,IAAL,GAAY1B,MAAM,CAAC0B,IAAnB;;AACA,cAAI1B,MAAM,CAACyH,WAAP,KAAuBtH,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKqH,WAAL,GAAmBzH,MAAM,CAACyH,WAA1B;;AACA,cAAIzH,MAAM,CAAC0H,IAAP,KAAgBvH,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKsH,IAAL,GAAY1H,MAAM,CAAC0H,IAAnB;;AACA,cAAI1H,MAAM,CAAC8H,WAAP,KAAuB3H,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKuH,UAAL,GAAkB3H,MAAM,CAAC8H,WAAzB;;AACA,cAAI9H,MAAM,CAAC+H,YAAP,KAAwB5H,SAA5B,EAAuC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC5D,eAAKwH,WAAL,GAAmB5H,MAAM,CAAC+H,YAA1B;;AACA,cAAI/H,MAAM,CAACgI,aAAP,KAAyB7H,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAKyH,YAAL,GAAoB7H,MAAM,CAACgI,aAA3B;AACH;;AA+BD3H,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAQtB;;AAxDkB,O;;0BA+DV3E,Q,GAAN,MAAMA,QAAN,CAAe;AAElBoE,QAAAA,WAAW,CAACC,MAAD,EAAc;AAqDzB;AACJ;AACA;AAvD6B,eAwDhBiB,EAxDgB;;AAyDzB;AACJ;AACA;AA3D6B,eA4DhBS,IA5DgB;;AA6DzB;AACJ;AACA;AA/D6B,eAgEhBuG,OAhEgB;;AAiEzB;AACJ;AACA;AAnE6B,eAoEhB7B,MApEgB;;AAqEzB;AACJ;AACA;AAvE6B,eAwEhB8B,IAxEgB;;AAyEzB;AACJ;AACA;AA3E6B,eA4EhBC,OA5EgB;;AA6EzB;AACJ;AACA;AA/E6B,eAgFhBC,MAhFgB;;AAiFzB;AACJ;AACA;AAnF6B,eAoFhBC,OApFgB;;AAqFzB;AACJ;AACA;AAvF6B,eAwFhBC,SAxFgB;;AAyFzB;AACJ;AACA;AA3F6B,eA4FhBC,SA5FgB;;AA6FzB;AACJ;AACA;AA/F6B,eAgGhBC,SAhGgB;;AAiGzB;AACJ;AACA;AAnG6B,eAoGhBC,WApGgB;;AAqGzB;AACJ;AACA;AAvG6B,eAwGhBC,cAxGgB;;AAyGzB;AACJ;AACA;AA3G6B,eA4GhBC,gBA5GgB;;AA6GzB;AACJ;AACA;AA/G6B,eAgHhBC,cAhHgB;;AAiHzB;AACJ;AACA;AAnH6B,eAoHhBC,kBApHgB;;AAqHzB;AACJ;AACA;AAvH6B,eAwHhBC,mBAxHgB;;AAyHzB;AACJ;AACA;AA3H6B,eA4HhBC,YA5HgB;;AA6HzB;AACJ;AACA;AA/H6B,eAgIhBC,aAhIgB;;AAiIzB;AACJ;AACA;AAnI6B,eAoIhBC,UApIgB;;AAqIzB;AACJ;AACA;AAvI6B,eAwIhBC,iBAxIgB;;AAyIzB;AACJ;AACA;AA3I6B,eA4IhBC,UA5IgB;;AA6IzB;AACJ;AACA;AA/I6B,eAgJhBC,YAhJgB;;AAiJzB;AACJ;AACA;AAnJ6B,eAoJhBC,aApJgB;;AAqJzB;AACJ;AACA;AAvJ6B,eAwJhBC,UAxJgB;;AACrB,cAAItJ,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAAC0B,IAAP,KAAgBvB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKsB,IAAL,GAAY1B,MAAM,CAAC0B,IAAnB;;AACA,cAAI1B,MAAM,CAACiI,OAAP,KAAmB9H,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAK6H,OAAL,GAAejI,MAAM,CAACiI,OAAtB;;AACA,cAAIjI,MAAM,CAACoG,MAAP,KAAkBjG,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAKgG,MAAL,GAAcpG,MAAM,CAACoG,MAArB;;AACA,cAAIpG,MAAM,CAACkI,IAAP,KAAgB/H,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAK8H,IAAL,GAAYlI,MAAM,CAACkI,IAAnB;;AACA,cAAIlI,MAAM,CAACmI,OAAP,KAAmBhI,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAK+H,OAAL,GAAenI,MAAM,CAACmI,OAAtB;;AACA,cAAInI,MAAM,CAACuJ,OAAP,KAAmBpJ,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKgI,MAAL,GAAcpI,MAAM,CAACuJ,OAArB;;AACA,cAAIvJ,MAAM,CAACwJ,QAAP,KAAoBrJ,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKiI,OAAL,GAAerI,MAAM,CAACwJ,QAAtB;;AACA,cAAIxJ,MAAM,CAACyJ,UAAP,KAAsBtJ,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKkI,SAAL,GAAiBtI,MAAM,CAACyJ,UAAxB;;AACA,cAAIzJ,MAAM,CAAC0J,UAAP,KAAsBvJ,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKmI,SAAL,GAAiBvI,MAAM,CAAC0J,UAAxB;;AACA,cAAI1J,MAAM,CAAC2J,WAAP,KAAuBxJ,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKoI,SAAL,GAAiBxI,MAAM,CAAC2J,WAAxB;;AACA,cAAI3J,MAAM,CAAC4J,aAAP,KAAyBzJ,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAKqI,WAAL,GAAmBzI,MAAM,CAAC4J,aAA1B;;AACA,cAAI5J,MAAM,CAAC6J,iBAAP,KAA6B1J,SAAjC,EAA4C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjE,eAAKsI,cAAL,GAAsB1I,MAAM,CAAC6J,iBAA7B;;AACA,cAAI7J,MAAM,CAAC8J,kBAAP,KAA8B3J,SAAlC,EAA6C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClE,eAAKuI,gBAAL,GAAwB3I,MAAM,CAAC8J,kBAA/B;;AACA,cAAI9J,MAAM,CAAC+J,eAAP,KAA2B5J,SAA/B,EAA0C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC/D,eAAKwI,cAAL,GAAsB5I,MAAM,CAAC+J,eAA7B;;AACA,cAAI/J,MAAM,CAACgK,oBAAP,KAAgC7J,SAApC,EAA+C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpE,eAAKyI,kBAAL,GAA0B7I,MAAM,CAACgK,oBAAjC;;AACA,cAAIhK,MAAM,CAACiK,qBAAP,KAAiC9J,SAArC,EAAgD;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrE,eAAK0I,mBAAL,GAA2B9I,MAAM,CAACiK,qBAAlC;;AACA,cAAIjK,MAAM,CAACkK,aAAP,KAAyB/J,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAK2I,YAAL,GAAoB/I,MAAM,CAACkK,aAA3B;;AACA,cAAIlK,MAAM,CAACmK,cAAP,KAA0BhK,SAA9B,EAAyC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC9D,eAAK4I,aAAL,GAAqBhJ,MAAM,CAACmK,cAA5B;;AACA,cAAInK,MAAM,CAACoK,WAAP,KAAuBjK,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAK6I,UAAL,GAAkBjJ,MAAM,CAACoK,WAAzB;;AACA,cAAIpK,MAAM,CAACqK,mBAAP,KAA+BlK,SAAnC,EAA8C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnE,eAAK8I,iBAAL,GAAyBlJ,MAAM,CAACqK,mBAAhC;;AACA,cAAIrK,MAAM,CAACmJ,UAAP,KAAsBhJ,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK+I,UAAL,GAAkBnJ,MAAM,CAACmJ,UAAzB;;AACA,cAAInJ,MAAM,CAACsK,aAAP,KAAyBnK,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAKgJ,YAAL,GAAoBpJ,MAAM,CAACsK,aAA3B;;AACA,cAAItK,MAAM,CAACuK,cAAP,KAA0BpK,SAA9B,EAAyC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC9D,eAAKiJ,aAAL,GAAqBrJ,MAAM,CAACuK,cAA5B;;AACA,cAAIvK,MAAM,CAACwK,WAAP,KAAuBrK,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKkJ,UAAL,GAAkBtJ,MAAM,CAACwK,WAAzB;AACH;;AAuGDnK,QAAAA,OAAO,CAACC,MAAD,EAAgB,CA0BtB;;AAtLiB,O;;0BA6LT1E,Q,GAAN,MAAMA,QAAN,CAAe;AAElBmE,QAAAA,WAAW,CAACC,MAAD,EAAc;AAmBzB;AACJ;AACA;AArB6B,eAsBhBiB,EAtBgB;;AAuBzB;AACJ;AACA;AAzB6B,eA0BhBS,IA1BgB;;AA2BzB;AACJ;AACA;AA7B6B,eA8BhBgG,IA9BgB;;AA+BzB;AACJ;AACA;AAjC6B,eAkChB+C,OAlCgB;;AAmCzB;AACJ;AACA;AArC6B,eAsChBC,UAtCgB;;AAuCzB;AACJ;AACA;AAzC6B,eA0ChBC,UA1CgB;;AA2CzB;AACJ;AACA;AA7C6B,eA8ChBC,KA9CgB;;AA+CzB;AACJ;AACA;AAjD6B,eAkDhBC,YAlDgB;;AACrB,cAAI7K,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAAC0B,IAAP,KAAgBvB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKsB,IAAL,GAAY1B,MAAM,CAAC0B,IAAnB;;AACA,cAAI1B,MAAM,CAAC0H,IAAP,KAAgBvH,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKsH,IAAL,GAAY1H,MAAM,CAAC0H,IAAnB;;AACA,cAAI1H,MAAM,CAACyK,OAAP,KAAmBtK,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKqK,OAAL,GAAezK,MAAM,CAACyK,OAAtB;;AACA,cAAIzK,MAAM,CAAC8K,WAAP,KAAuB3K,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKsK,UAAL,GAAkB1K,MAAM,CAAC8K,WAAzB;;AACA,cAAI9K,MAAM,CAAC+K,WAAP,KAAuB5K,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKuK,UAAL,GAAkB3K,MAAM,CAAC+K,WAAzB;;AACA,cAAI/K,MAAM,CAAC4K,KAAP,KAAiBzK,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD;AAAE,iBAAKwK,KAAL,GAAa,EAAb;;AAAiB,iBAAI,IAAIxJ,KAAR,IAAiBpB,MAAM,CAAC4K,KAAxB,EAA+B;AAAE,kBAAIvJ,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAI1G,SAAJ,CAAcyG,KAAd,CAAN;AAA4B,mBAAKwJ,KAAL,CAAWtJ,IAAX,CAAgBD,GAAhB;AAAsB;AAAC;;AAChH,cAAIrB,MAAM,CAACgL,aAAP,KAAyB7K,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D;AAAE,iBAAKyK,YAAL,GAAoB,EAApB;;AAAwB,iBAAI,IAAIzJ,IAAR,IAAiBpB,MAAM,CAACgL,aAAxB,EAAuC;AAAE,kBAAI3J,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAI7G,WAAJ,CAAgB4G,IAAhB,CAAN;AAA8B,mBAAKyJ,YAAL,CAAkBvJ,IAAlB,CAAuBD,GAAvB;AAA6B;AAAC;AAC3I;;AAmCDhB,QAAAA,OAAO,CAACC,MAAD,EAAgB;AAOnB,eAAK,IAAIkE,EAAT,IAAe,KAAKoG,KAApB,EAA2B;AAAEpG,YAAAA,EAAE,QAAF,IAAAA,EAAE,CAAEnE,OAAJ,CAAYC,MAAZ;AAAsB;;AACnD,eAAK,IAAIkE,GAAT,IAAe,KAAKqG,YAApB,EAAkC;AAAErG,YAAAA,GAAE,QAAF,IAAAA,GAAE,CAAEnE,OAAJ,CAAYC,MAAZ;AAAsB;AAC7D;;AA/DiB,O;;iCAsETzE,e,GAAN,MAAMA,eAAN,CAAsB;AAEzBkE,QAAAA,WAAW,CAACC,MAAD,EAAc;AAezB;AACJ;AACA;AAjB6B,eAkBhB2K,UAlBgB;;AAmBzB;AACJ;AACA;AArB6B,eAsBhBM,SAtBgB;;AAuBzB;AACJ;AACA;AAzB6B,eA0BhBC,OA1BgB;;AA2BzB;AACJ;AACA;AA7B6B,eA8BhBC,OA9BgB;;AA+BzB;AACJ;AACA;AAjC6B,eAkChBC,YAlCgB;;AAmCzB;AACJ;AACA;AArC6B,eAsChBP,YAtCgB;;AACrB,cAAI7K,MAAM,CAAC+K,WAAP,KAAuB5K,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKuK,UAAL,GAAkB3K,MAAM,CAAC+K,WAAzB;;AACA,cAAI/K,MAAM,CAACqL,UAAP,KAAsBlL,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK6K,SAAL,GAAiBjL,MAAM,CAACqL,UAAxB;;AACA,cAAIrL,MAAM,CAACsL,QAAP,KAAoBnL,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAK8K,OAAL,GAAelL,MAAM,CAACsL,QAAtB;;AACA,cAAItL,MAAM,CAACuL,QAAP,KAAoBpL,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD;AAAE,iBAAK+K,OAAL,GAAe,EAAf;;AAAmB,iBAAI,IAAI/J,KAAR,IAAiBpB,MAAM,CAACuL,QAAxB,EAAkC;AAAE,kBAAIlK,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAIpG,OAAJ,CAAYmG,KAAZ,CAAN;AAA0B,mBAAK+J,OAAL,CAAa7J,IAAb,CAAkBD,GAAlB;AAAwB;AAAC;;AACrH,cAAIrB,MAAM,CAACwL,aAAP,KAAyBrL,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAKgL,YAAL,GAAoB,IAAI3Q,YAAJ,CAAiBuF,MAAM,CAACwL,aAAxB,CAApB;;AACA,cAAIxL,MAAM,CAACgL,aAAP,KAAyB7K,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D;AAAE,iBAAKyK,YAAL,GAAoB,EAApB;;AAAwB,iBAAI,IAAIzJ,KAAR,IAAiBpB,MAAM,CAACgL,aAAxB,EAAuC;AAAE,kBAAI3J,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAI7G,WAAJ,CAAgB4G,KAAhB,CAAN;AAA8B,mBAAKyJ,YAAL,CAAkBvJ,IAAlB,CAAuBD,GAAvB;AAA6B;AAAC;AAC3I;;AA2BDhB,QAAAA,OAAO,CAACC,MAAD,EAAgB;AAAA;;AAInB,eAAK,IAAIkE,EAAT,IAAe,KAAK2G,OAApB,EAA6B;AAAE3G,YAAAA,EAAE,QAAF,IAAAA,EAAE,CAAEnE,OAAJ,CAAYC,MAAZ;AAAsB;;AACrD,qCAAK8K,YAAL,gCAAmB/K,OAAnB,CAA2BC,MAA3B;;AACA,eAAK,IAAIkE,GAAT,IAAe,KAAKqG,YAApB,EAAkC;AAAErG,YAAAA,GAAE,QAAF,IAAAA,GAAE,CAAEnE,OAAJ,CAAYC,MAAZ;AAAsB;AAC7D;;AAjDwB,O;;6BAwDhBxE,W,GAAN,MAAMA,WAAN,CAAkB;AAErBiE,QAAAA,WAAW,CAACC,MAAD,EAAc;AA+CzB;AACJ;AACA;AAjD6B,eAkDhBoD,EAlDgB;;AAmDzB;AACJ;AACA;AArD6B,eAsDhBqI,QAtDgB;;AAuDzB;AACJ;AACA;AAzD6B,eA0DhBC,SA1DgB;;AA2DzB;AACJ;AACA;AA7D6B,eA8DhBC,KA9DgB;;AA+DzB;AACJ;AACA;AAjE6B,eAkEhBC,UAlEgB;;AAmEzB;AACJ;AACA;AArE6B,eAsEhBnE,WAtEgB;AAAA,eAuEhBoE,OAvEgB;;AAwEzB;AACJ;AACA;AA1E6B,eA2EhBjG,KA3EgB;;AA4EzB;AACJ;AACA;AA9E6B,eA+EhBkG,KA/EgB;;AAgFzB;AACJ;AACA;AAlF6B,eAmFhBC,OAnFgB;;AAoFzB;AACJ;AACA;AAtF6B,eAuFhBC,UAvFgB;;AAwFzB;AACJ;AACA;AA1F6B,eA2FhBC,UA3FgB;;AA4FzB;AACJ;AACA;AA9F6B,eA+FhBC,YA/FgB;;AAgGzB;AACJ;AACA;AAlG6B,eAmGhBC,WAnGgB;;AAoGzB;AACJ;AACA;AAtG6B,eAuGhBC,KAvGgB;;AAwGzB;AACJ;AACA;AA1G6B,eA2GhBC,OA3GgB;;AA4GzB;AACJ;AACA;AA9G6B,eA+GhBC,UA/GgB;;AAgHzB;AACJ;AACA;AAlH6B,eAmHhBC,UAnHgB;;AAoHzB;AACJ;AACA;AAtH6B,eAuHhBC,UAvHgB;;AAwHzB;AACJ;AACA;AA1H6B,eA2HhBC,SA3HgB;;AA4HzB;AACJ;AACA;AA9H6B,eA+HhBC,SA/HgB;AAAA,eAgIhBC,UAhIgB;;AACrB,cAAI3M,MAAM,CAACoD,EAAP,KAAcjD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKgD,EAAL,GAAUpD,MAAM,CAACoD,EAAjB;;AACA,cAAIpD,MAAM,CAACyL,QAAP,KAAoBtL,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKqL,QAAL,GAAgBzL,MAAM,CAACyL,QAAvB;;AACA,cAAIzL,MAAM,CAAC0L,SAAP,KAAqBvL,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKsL,SAAL,GAAiB1L,MAAM,CAAC0L,SAAxB;;AACA,cAAI1L,MAAM,CAAC2L,KAAP,KAAiBxL,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKuL,KAAL,GAAa3L,MAAM,CAAC2L,KAApB;;AACA,cAAI3L,MAAM,CAAC4L,UAAP,KAAsBzL,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKwL,UAAL,GAAkB5L,MAAM,CAAC4L,UAAzB;;AACA,cAAI5L,MAAM,CAACyH,WAAP,KAAuBtH,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKqH,WAAL,GAAmBzH,MAAM,CAACyH,WAA1B;;AACA,cAAIzH,MAAM,CAAC6L,OAAP,KAAmB1L,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD;AAAE,iBAAKyL,OAAL,GAAe,EAAf;;AAAmB,iBAAI,IAAIzK,KAAR,IAAiBpB,MAAM,CAAC6L,OAAxB,EAAiC;AAAE,kBAAIxK,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAI9G,QAAJ,CAAa6G,KAAb,CAAN;AAA2B,mBAAKyK,OAAL,CAAavK,IAAb,CAAkBD,GAAlB;AAAwB;AAAC;;AACrH,cAAIrB,MAAM,CAAC4F,KAAP,KAAiBzF,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKwF,KAAL,GAAa5F,MAAM,CAAC4F,KAApB;;AACA,cAAI5F,MAAM,CAAC8L,KAAP,KAAiB3L,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAK0L,KAAL,GAAa9L,MAAM,CAAC8L,KAApB;;AACA,cAAI9L,MAAM,CAAC+L,OAAP,KAAmB5L,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAK2L,OAAL,GAAe/L,MAAM,CAAC+L,OAAtB;;AACA,cAAI/L,MAAM,CAACgM,UAAP,KAAsB7L,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK4L,UAAL,GAAkBhM,MAAM,CAACgM,UAAzB;;AACA,cAAIhM,MAAM,CAACiM,UAAP,KAAsB9L,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK6L,UAAL,GAAkBjM,MAAM,CAACiM,UAAzB;;AACA,cAAIjM,MAAM,CAACkM,YAAP,KAAwB/L,SAA5B,EAAuC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC5D,eAAK8L,YAAL,GAAoBlM,MAAM,CAACkM,YAA3B;;AACA,cAAIlM,MAAM,CAACmM,WAAP,KAAuBhM,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAK+L,WAAL,GAAmBnM,MAAM,CAACmM,WAA1B;;AACA,cAAInM,MAAM,CAACoM,KAAP,KAAiBjM,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKgM,KAAL,GAAapM,MAAM,CAACoM,KAApB;;AACA,cAAIpM,MAAM,CAACqM,OAAP,KAAmBlM,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKiM,OAAL,GAAerM,MAAM,CAACqM,OAAtB;;AACA,cAAIrM,MAAM,CAACsM,UAAP,KAAsBnM,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKkM,UAAL,GAAkBtM,MAAM,CAACsM,UAAzB;;AACA,cAAItM,MAAM,CAACuM,UAAP,KAAsBpM,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKmM,UAAL,GAAkBvM,MAAM,CAACuM,UAAzB;;AACA,cAAIvM,MAAM,CAACwM,UAAP,KAAsBrM,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKoM,UAAL,GAAkBxM,MAAM,CAACwM,UAAzB;;AACA,cAAIxM,MAAM,CAACyM,SAAP,KAAqBtM,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKqM,SAAL,GAAiBzM,MAAM,CAACyM,SAAxB;;AACA,cAAIzM,MAAM,CAAC0M,SAAP,KAAqBvM,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKsM,SAAL,GAAiB1M,MAAM,CAAC0M,SAAxB;;AACA,cAAI1M,MAAM,CAAC2M,UAAP,KAAsBxM,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D;AAAE,iBAAKuM,UAAL,GAAkB,EAAlB;;AAAsB,iBAAI,IAAIvL,KAAR,IAAiBpB,MAAM,CAAC2M,UAAxB,EAAoC;AAAE,kBAAItL,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAIlG,WAAJ,CAAgBiG,KAAhB,CAAN;AAA8B,mBAAKuL,UAAL,CAAgBrL,IAAhB,CAAqBD,GAArB;AAA2B;AAAC;AACpI;;AAqFDhB,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAuBtB;;AA3JoB,O;;+BAkKZvE,a,GAAN,MAAMA,aAAN,CAAoB;AAEvBgE,QAAAA,WAAW,CAACC,MAAD,EAAc;AAmBzB;AACJ;AACA;AArB6B,eAsBhB4M,aAtBgB;;AAuBzB;AACJ;AACA;AAzB6B,eA0BhB/J,SA1BgB;;AA2BzB;AACJ;AACA;AA7B6B,eA8BhBgK,qBA9BgB;;AA+BzB;AACJ;AACA;AAjC6B,eAkChBC,kBAlCgB;;AAmCzB;AACJ;AACA;AArC6B,eAsChBC,gBAtCgB;;AAuCzB;AACJ;AACA;AAzC6B,eA0ChBC,iBA1CgB;;AA2CzB;AACJ;AACA;AA7C6B,eA8ChBC,6BA9CgB;;AA+CzB;AACJ;AACA;AAjD6B,eAkDhBC,0BAlDgB;;AACrB,cAAIlN,MAAM,CAAC4M,aAAP,KAAyBzM,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAKwM,aAAL,GAAqB5M,MAAM,CAAC4M,aAA5B;;AACA,cAAI5M,MAAM,CAAC6C,SAAP,KAAqB1C,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKyC,SAAL,GAAiB7C,MAAM,CAAC6C,SAAxB;;AACA,cAAI7C,MAAM,CAAC6M,qBAAP,KAAiC1M,SAArC,EAAgD;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrE,eAAKyM,qBAAL,GAA6B7M,MAAM,CAAC6M,qBAApC;;AACA,cAAI7M,MAAM,CAAC8M,kBAAP,KAA8B3M,SAAlC,EAA6C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClE,eAAK0M,kBAAL,GAA0B9M,MAAM,CAAC8M,kBAAjC;;AACA,cAAI9M,MAAM,CAAC+M,gBAAP,KAA4B5M,SAAhC,EAA2C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAChE,eAAK2M,gBAAL,GAAwB/M,MAAM,CAAC+M,gBAA/B;;AACA,cAAI/M,MAAM,CAACgN,iBAAP,KAA6B7M,SAAjC,EAA4C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjE,eAAK4M,iBAAL,GAAyBhN,MAAM,CAACgN,iBAAhC;;AACA,cAAIhN,MAAM,CAACiN,6BAAP,KAAyC9M,SAA7C,EAAwD;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7E,eAAK6M,6BAAL,GAAqCjN,MAAM,CAACiN,6BAA5C;;AACA,cAAIjN,MAAM,CAACkN,0BAAP,KAAsC/M,SAA1C,EAAqD;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1E,eAAK8M,0BAAL,GAAkClN,MAAM,CAACkN,0BAAzC;AACH;;AAmCD7M,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAStB;;AA/DsB,O;;yBAsEdtE,O,GAAN,MAAMA,OAAN,CAAc;AAEjB+D,QAAAA,WAAW,CAACC,MAAD,EAAc;AAuBzB;AACJ;AACA;AAzB6B,eA0BhBiB,EA1BgB;;AA2BzB;AACJ;AACA;AA7B6B,eA8BhBS,IA9BgB;;AA+BzB;AACJ;AACA;AAjC6B,eAkChBgG,IAlCgB;;AAmCzB;AACJ;AACA;AArC6B,eAsChB+C,OAtCgB;;AAuCzB;AACJ;AACA;AAzC6B,eA0ChBC,UA1CgB;;AA2CzB;AACJ;AACA;AA7C6B,eA8ChByC,OA9CgB;;AA+CzB;AACJ;AACA;AAjD6B,eAkDhBtL,QAlDgB;;AAmDzB;AACJ;AACA;AArD6B,eAsDhBuL,YAtDgB;;AAuDzB;AACJ;AACA;AAzD6B,eA0DhBC,YA1DgB;;AA2DzB;AACJ;AACA;AA7D6B,eA8DhBC,WA9DgB;;AACrB,cAAItN,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAAC0B,IAAP,KAAgBvB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKsB,IAAL,GAAY1B,MAAM,CAAC0B,IAAnB;;AACA,cAAI1B,MAAM,CAAC0H,IAAP,KAAgBvH,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKsH,IAAL,GAAY1H,MAAM,CAAC0H,IAAnB;;AACA,cAAI1H,MAAM,CAACyK,OAAP,KAAmBtK,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKqK,OAAL,GAAezK,MAAM,CAACyK,OAAtB;;AACA,cAAIzK,MAAM,CAAC8K,WAAP,KAAuB3K,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKsK,UAAL,GAAkB1K,MAAM,CAAC8K,WAAzB;;AACA,cAAI9K,MAAM,CAACuN,QAAP,KAAoBpN,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAK+M,OAAL,GAAenN,MAAM,CAACuN,QAAtB;;AACA,cAAIvN,MAAM,CAAC8B,SAAP,KAAqB3B,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKyB,QAAL,GAAgB7B,MAAM,CAAC8B,SAAvB;;AACA,cAAI9B,MAAM,CAACwN,aAAP,KAAyBrN,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAKgN,YAAL,GAAoBpN,MAAM,CAACwN,aAA3B;;AACA,cAAIxN,MAAM,CAACyN,aAAP,KAAyBtN,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAKiN,YAAL,GAAoBrN,MAAM,CAACyN,aAA3B;;AACA,cAAIzN,MAAM,CAAC0N,aAAP,KAAyBvN,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAKkN,WAAL,GAAmBtN,MAAM,CAAC0N,aAA1B;AACH;;AA2CDrN,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAWtB;;AA7EgB,O;;0BAoFRrE,Q,GAAN,MAAMA,QAAN,CAAe;AAElB8D,QAAAA,WAAW,CAACC,MAAD,EAAc;AAiBzB;AACJ;AACA;AAnB6B,eAoBhBiB,EApBgB;;AAqBzB;AACJ;AACA;AAvB6B,eAwBhBmF,MAxBgB;;AAyBzB;AACJ;AACA;AA3B6B,eA4BhBuH,UA5BgB;;AA6BzB;AACJ;AACA;AA/B6B,eAgChBC,WAhCgB;;AAiCzB;AACJ;AACA;AAnC6B,eAoChBC,cApCgB;;AAqCzB;AACJ;AACA;AAvC6B,eAwChBC,qBAxCgB;;AAyCzB;AACJ;AACA;AA3C6B,eA4ChBC,SA5CgB;;AACrB,cAAI/N,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAACoG,MAAP,KAAkBjG,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAKgG,MAAL,GAAcpG,MAAM,CAACoG,MAArB;;AACA,cAAIpG,MAAM,CAAC2N,UAAP,KAAsBxN,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKuN,UAAL,GAAkB3N,MAAM,CAAC2N,UAAzB;;AACA,cAAI3N,MAAM,CAAC4N,WAAP,KAAuBzN,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKwN,WAAL,GAAmB5N,MAAM,CAAC4N,WAA1B;;AACA,cAAI5N,MAAM,CAAC6N,cAAP,KAA0B1N,SAA9B,EAAyC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC9D,eAAKyN,cAAL,GAAsB7N,MAAM,CAAC6N,cAA7B;;AACA,cAAI7N,MAAM,CAAC8N,qBAAP,KAAiC3N,SAArC,EAAgD;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrE,eAAK0N,qBAAL,GAA6B9N,MAAM,CAAC8N,qBAApC;;AACA,cAAI9N,MAAM,CAAC+N,SAAP,KAAqB5N,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAK2N,SAAL,GAAiB/N,MAAM,CAAC+N,SAAxB;AACH;;AA+BD1N,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAQtB;;AAxDiB,O;;+BA+DTpE,a,GAAN,MAAMA,aAAN,CAAoB;AAEvB6D,QAAAA,WAAW,CAACC,MAAD,EAAc;AAiBzB;AACJ;AACA;AAnB6B,eAoBhBiB,EApBgB;;AAqBzB;AACJ;AACA;AAvB6B,eAwBhB+M,cAxBgB;;AAyBzB;AACJ;AACA;AA3B6B,eA4BhBC,WA5BgB;AAAA,eA6BhBC,UA7BgB;;AA8BzB;AACJ;AACA;AAhC6B,eAiChBC,cAjCgB;;AAkCzB;AACJ;AACA;AApC6B,eAqChBC,WArCgB;AAAA,eAsChBC,UAtCgB;;AACrB,cAAIrO,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAACgO,cAAP,KAA0B7N,SAA9B,EAAyC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC9D,eAAK4N,cAAL,GAAsBhO,MAAM,CAACgO,cAA7B;;AACA,cAAIhO,MAAM,CAACiO,WAAP,KAAuB9N,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAK6N,WAAL,GAAmBjO,MAAM,CAACiO,WAA1B;;AACA,cAAIjO,MAAM,CAACkO,UAAP,KAAsB/N,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D;AAAE,iBAAK8N,UAAL,GAAkB,EAAlB;;AAAsB,iBAAI,IAAI9M,KAAR,IAAiBpB,MAAM,CAACkO,UAAxB,EAAoC;AAAE,kBAAI7M,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAInG,YAAJ,CAAiBkG,KAAjB,CAAN;AAA+B,mBAAK8M,UAAL,CAAgB5M,IAAhB,CAAqBD,GAArB;AAA2B;AAAC;;AAClI,cAAIrB,MAAM,CAACmO,cAAP,KAA0BhO,SAA9B,EAAyC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC9D,eAAK+N,cAAL,GAAsBnO,MAAM,CAACmO,cAA7B;;AACA,cAAInO,MAAM,CAACoO,WAAP,KAAuBjO,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKgO,WAAL,GAAmBpO,MAAM,CAACoO,WAA1B;;AACA,cAAIpO,MAAM,CAACqO,UAAP,KAAsBlO,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D;AAAE,iBAAKiO,UAAL,GAAkB,EAAlB;;AAAsB,iBAAI,IAAIjN,KAAR,IAAiBpB,MAAM,CAACqO,UAAxB,EAAoC;AAAE,kBAAIhN,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAInG,YAAJ,CAAiBkG,KAAjB,CAAN;AAA+B,mBAAKiN,UAAL,CAAgB/M,IAAhB,CAAqBD,GAArB;AAA2B;AAAC;AACrI;;AAyBDhB,QAAAA,OAAO,CAACC,MAAD,EAAgB;AAInB,eAAK,IAAIkE,EAAT,IAAe,KAAK0J,UAApB,EAAgC;AAAE1J,YAAAA,EAAE,QAAF,IAAAA,EAAE,CAAEnE,OAAJ,CAAYC,MAAZ;AAAsB;;AAGxD,eAAK,IAAIkE,GAAT,IAAe,KAAK6J,UAApB,EAAgC;AAAE7J,YAAAA,GAAE,QAAF,IAAAA,GAAE,CAAEnE,OAAJ,CAAYC,MAAZ;AAAsB;AAC3D;;AAlDsB,O;;yBAyDdnE,O,GAAN,MAAMA,OAAN,CAAc;AAEjB4D,QAAAA,WAAW,CAACC,MAAD,EAAc;AAWzB;AACJ;AACA;AAb6B,eAchBsO,MAdgB;;AAezB;AACJ;AACA;AAjB6B,eAkBhBC,SAlBgB;;AAmBzB;AACJ;AACA;AArB6B,eAsBhBC,QAtBgB;AAAA,eAuBhBC,QAvBgB;;AACrB,cAAIzO,MAAM,CAAC0O,OAAP,KAAmBvO,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKkO,MAAL,GAActO,MAAM,CAAC0O,OAArB;;AACA,cAAI1O,MAAM,CAAC2O,UAAP,KAAsBxO,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKmO,SAAL,GAAiBvO,MAAM,CAAC2O,UAAxB;;AACA,cAAI3O,MAAM,CAAC4O,SAAP,KAAqBzO,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKoO,QAAL,GAAgBxO,MAAM,CAAC4O,SAAvB;;AACA,cAAI5O,MAAM,CAAC6O,SAAP,KAAqB1O,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD;AAAE,iBAAKqO,QAAL,GAAgB,EAAhB;;AAAoB,iBAAI,IAAIrN,KAAR,IAAiBpB,MAAM,CAAC6O,SAAxB,EAAmC;AAAE,kBAAIxN,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAIjF,WAAJ,CAAgBgF,KAAhB,CAAN;AAA8B,mBAAKqN,QAAL,CAAcnN,IAAd,CAAmBD,GAAnB;AAAyB;AAAC;AAC/H;;AAgBDhB,QAAAA,OAAO,CAACC,MAAD,EAAgB;AAInB,eAAK,IAAIkE,EAAT,IAAe,KAAKiK,QAApB,EAA8B;AAAEjK,YAAAA,EAAE,QAAF,IAAAA,EAAE,CAAEnE,OAAJ,CAAYC,MAAZ;AAAsB;AACzD;;AAhCgB,O;;6BAuCRlE,W,GAAN,MAAMA,WAAN,CAAkB;AAErB2D,QAAAA,WAAW,CAACC,MAAD,EAAc;AAWzB;AACJ;AACA;AAb6B,eAchB8O,MAdgB;AAAA,eAehBC,KAfgB;AAAA,eAgBhBC,IAhBgB;AAAA,eAiBhBC,YAjBgB;;AACrB,cAAIjP,MAAM,CAACkP,OAAP,KAAmB/O,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAK0O,MAAL,GAAc9O,MAAM,CAACkP,OAArB;;AACA,cAAIlP,MAAM,CAAC+O,KAAP,KAAiB5O,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAK2O,KAAL,GAAa/O,MAAM,CAAC+O,KAApB;;AACA,cAAI/O,MAAM,CAACgP,IAAP,KAAgB7O,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAK4O,IAAL,GAAYhP,MAAM,CAACgP,IAAnB;;AACA,cAAIhP,MAAM,CAACmP,aAAP,KAAyBhP,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAK6O,YAAL,GAAoBjP,MAAM,CAACmP,aAA3B;AACH;;AAUD9O,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAKtB;;AA1BoB,O;;0BAiCZjE,Q,GAAN,MAAMA,QAAN,CAAe;AAElB0D,QAAAA,WAAW,CAACC,MAAD,EAAc;AAuBzB;AACJ;AACA;AAzB6B,eA0BhBiB,EA1BgB;;AA2BzB;AACJ;AACA;AA7B6B,eA8BhBmO,SA9BgB;;AA+BzB;AACJ;AACA;AAjC6B,eAkChB1N,IAlCgB;;AAmCzB;AACJ;AACA;AArC6B,eAsChBgG,IAtCgB;;AAuCzB;AACJ;AACA;AAzC6B,eA0ChB2H,QA1CgB;;AA2CzB;AACJ;AACA;AA7C6B,eA8ChB5H,WA9CgB;;AA+CzB;AACJ;AACA;AAjD6B,eAkDhBgD,OAlDgB;AAAA,eAmDhB6E,QAnDgB;;AAoDzB;AACJ;AACA;AAtD6B,eAuDhBrJ,OAvDgB;;AAwDzB;AACJ;AACA;AA1D6B,eA2DhBsJ,SA3DgB;;AACrB,cAAIvP,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAACwP,UAAP,KAAsBrP,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKgP,SAAL,GAAiBpP,MAAM,CAACwP,UAAxB;;AACA,cAAIxP,MAAM,CAAC0B,IAAP,KAAgBvB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKsB,IAAL,GAAY1B,MAAM,CAAC0B,IAAnB;;AACA,cAAI1B,MAAM,CAAC0H,IAAP,KAAgBvH,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKsH,IAAL,GAAY1H,MAAM,CAAC0H,IAAnB;;AACA,cAAI1H,MAAM,CAACqP,QAAP,KAAoBlP,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKiP,QAAL,GAAgBrP,MAAM,CAACqP,QAAvB;;AACA,cAAIrP,MAAM,CAACyH,WAAP,KAAuBtH,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKqH,WAAL,GAAmBzH,MAAM,CAACyH,WAA1B;;AACA,cAAIzH,MAAM,CAACyK,OAAP,KAAmBtK,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKqK,OAAL,GAAezK,MAAM,CAACyK,OAAtB;;AACA,cAAIzK,MAAM,CAACsP,QAAP,KAAoBnP,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKkP,QAAL,GAAgB,IAAIvU,aAAJ,CAAkBiF,MAAM,CAACsP,QAAzB,CAAhB;;AACA,cAAItP,MAAM,CAACiG,OAAP,KAAmB9F,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD;AAAE,iBAAK6F,OAAL,GAAe,EAAf;;AAAmB,iBAAI,IAAI7E,KAAR,IAAiBpB,MAAM,CAACiG,OAAxB,EAAiC;AAAE,kBAAI5E,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAIxG,WAAJ,CAAgBuG,KAAhB,CAAN;AAA8B,mBAAK6E,OAAL,CAAa3E,IAAb,CAAkBD,GAAlB;AAAwB;AAAC;;AACxH,cAAIrB,MAAM,CAACuP,SAAP,KAAqBpP,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD;AAAE,iBAAKmP,SAAL,GAAiB,EAAjB;;AAAqB,iBAAI,IAAInO,KAAR,IAAiBpB,MAAM,CAACuP,SAAxB,EAAmC;AAAE,kBAAIlO,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAIvG,aAAJ,CAAkBsG,KAAlB,CAAN;AAAgC,mBAAKmO,SAAL,CAAejO,IAAf,CAAoBD,GAApB;AAA0B;AAAC;AACnI;;AAwCDhB,QAAAA,OAAO,CAACC,MAAD,EAAgB;AAAA;;AAQnB,iCAAKgP,QAAL,4BAAejP,OAAf,CAAuBC,MAAvB;;AACA,eAAK,IAAIkE,EAAT,IAAe,KAAKyB,OAApB,EAA6B;AAAEzB,YAAAA,EAAE,QAAF,IAAAA,EAAE,CAAEnE,OAAJ,CAAYC,MAAZ;AAAsB;;AACrD,eAAK,IAAIkE,IAAT,IAAe,KAAK+K,SAApB,EAA+B;AAAE/K,YAAAA,IAAE,QAAF,IAAAA,IAAE,CAAEnE,OAAJ,CAAYC,MAAZ;AAAsB;AAC1D;;AA1EiB,O;;0BAiFThE,Q,GAAN,MAAMA,QAAN,CAAe;AAElByD,QAAAA,WAAW,CAACC,MAAD,EAAc;AAqBzB;AACJ;AACA;AAvB6B,eAwBhBiB,EAxBgB;;AAyBzB;AACJ;AACA;AA3B6B,eA4BhBS,IA5BgB;;AA6BzB;AACJ;AACA;AA/B6B,eAgChBE,IAhCgB;;AAiCzB;AACJ;AACA;AAnC6B,eAoChB8F,IApCgB;;AAqCzB;AACJ;AACA;AAvC6B,eAwChB+H,EAxCgB;;AAyCzB;AACJ;AACA;AA3C6B,eA4ChBC,MA5CgB;;AA6CzB;AACJ;AACA;AA/C6B,eAgDhBC,OAhDgB;;AAiDzB;AACJ;AACA;AAnD6B,eAoDhBpK,WApDgB;AAAA,eAqDhBqK,UArDgB;;AACrB,cAAI5P,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAAC0B,IAAP,KAAgBvB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKsB,IAAL,GAAY1B,MAAM,CAAC0B,IAAnB;;AACA,cAAI1B,MAAM,CAAC4B,IAAP,KAAgBzB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKwB,IAAL,GAAY5B,MAAM,CAAC4B,IAAnB;;AACA,cAAI5B,MAAM,CAAC0H,IAAP,KAAgBvH,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKsH,IAAL,GAAY1H,MAAM,CAAC0H,IAAnB;;AACA,cAAI1H,MAAM,CAACyP,EAAP,KAActP,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKqP,EAAL,GAAUzP,MAAM,CAACyP,EAAjB;;AACA,cAAIzP,MAAM,CAAC0P,MAAP,KAAkBvP,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAKsP,MAAL,GAAc1P,MAAM,CAAC0P,MAArB;;AACA,cAAI1P,MAAM,CAAC2P,OAAP,KAAmBxP,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKuP,OAAL,GAAe3P,MAAM,CAAC2P,OAAtB;;AACA,cAAI3P,MAAM,CAACuF,WAAP,KAAuBpF,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKmF,WAAL,GAAmBvF,MAAM,CAACuF,WAA1B;;AACA,cAAIvF,MAAM,CAAC4P,UAAP,KAAsBzP,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D;AAAE,iBAAKwP,UAAL,GAAkB,EAAlB;;AAAsB,iBAAI,IAAIxO,KAAR,IAAiBpB,MAAM,CAAC4P,UAAxB,EAAoC;AAAE,kBAAIvO,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAI/G,SAAJ,CAAc8G,KAAd,CAAN;AAA4B,mBAAKwO,UAAL,CAAgBtO,IAAhB,CAAqBD,GAArB;AAA2B;AAAC;AAClI;;AAoCDhB,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAUtB;;AAnEiB,O;;0BA0ET/D,Q,GAAN,MAAMA,QAAN,CAAe;AAElBwD,QAAAA,WAAW,CAACC,MAAD,EAAc;AAiBzB;AACJ;AACA;AAnB6B,eAoBhBiB,EApBgB;;AAqBzB;AACJ;AACA;AAvB6B,eAwBhB4O,SAxBgB;;AAyBzB;AACJ;AACA;AA3B6B,eA4BhBC,QA5BgB;;AA6BzB;AACJ;AACA;AA/B6B,eAgChB3O,IAhCgB;;AAiCzB;AACJ;AACA;AAnC6B,eAoChB4O,YApCgB;;AAqCzB;AACJ;AACA;AAvC6B,eAwChBC,KAxCgB;;AAyCzB;AACJ;AACA;AA3C6B,eA4ChBC,YA5CgB;;AACrB,cAAIjQ,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAAC6P,SAAP,KAAqB1P,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKyP,SAAL,GAAiB7P,MAAM,CAAC6P,SAAxB;;AACA,cAAI7P,MAAM,CAAC8P,QAAP,KAAoB3P,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAK0P,QAAL,GAAgB9P,MAAM,CAAC8P,QAAvB;;AACA,cAAI9P,MAAM,CAACmB,IAAP,KAAgBhB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKe,IAAL,GAAYnB,MAAM,CAACmB,IAAnB;;AACA,cAAInB,MAAM,CAAC+P,YAAP,KAAwB5P,SAA5B,EAAuC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC5D,eAAK2P,YAAL,GAAoB/P,MAAM,CAAC+P,YAA3B;;AACA,cAAI/P,MAAM,CAACgQ,KAAP,KAAiB7P,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAK4P,KAAL,GAAahQ,MAAM,CAACgQ,KAApB;;AACA,cAAIhQ,MAAM,CAACiQ,YAAP,KAAwB9P,SAA5B,EAAuC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC5D,eAAK6P,YAAL,GAAoBjQ,MAAM,CAACiQ,YAA3B;AACH;;AA+BD5P,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAQtB;;AAxDiB,O;;yBA+DT9D,O,GAAN,MAAMA,OAAN,CAAc;AAEjBuD,QAAAA,WAAW,CAACC,MAAD,EAAc;AAmCzB;AACJ;AACA;AArC6B,eAsChBuD,MAtCgB;;AAuCzB;AACJ;AACA;AAzC6B,eA0ChBC,OA1CgB;;AA2CzB;AACJ;AACA;AA7C6B,eA8ChB0M,SA9CgB;;AA+CzB;AACJ;AACA;AAjD6B,eAkDhBzM,MAlDgB;;AAmDzB;AACJ;AACA;AArD6B,eAsDhBC,UAtDgB;;AAuDzB;AACJ;AACA;AAzD6B,eA0DhBC,QA1DgB;AAAA,eA2DhBC,QA3DgB;;AA4DzB;AACJ;AACA;AA9D6B,eA+DhBC,UA/DgB;;AAgEzB;AACJ;AACA;AAlE6B,eAmEhBC,QAnEgB;;AAoEzB;AACJ;AACA;AAtE6B,eAuEhBqM,YAvEgB;;AAwEzB;AACJ;AACA;AA1E6B,eA2EhBC,UA3EgB;;AA4EzB;AACJ;AACA;AA9E6B,eA+EhBxL,QA/EgB;;AAgFzB;AACJ;AACA;AAlF6B,eAmFhBC,QAnFgB;;AAoFzB;AACJ;AACA;AAtF6B,eAuFhBC,SAvFgB;;AAwFzB;AACJ;AACA;AA1F6B,eA2FhBC,SA3FgB;;AA4FzB;AACJ;AACA;AA9F6B,eA+FhBhB,MA/FgB;;AACrB,cAAI/D,MAAM,CAACgE,OAAP,KAAmB7D,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKmD,MAAL,GAAcvD,MAAM,CAACgE,OAArB;;AACA,cAAIhE,MAAM,CAACiE,QAAP,KAAoB9D,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKoD,OAAL,GAAexD,MAAM,CAACiE,QAAtB;;AACA,cAAIjE,MAAM,CAACqQ,UAAP,KAAsBlQ,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK8P,SAAL,GAAiBlQ,MAAM,CAACqQ,UAAxB;;AACA,cAAIrQ,MAAM,CAACkE,OAAP,KAAmB/D,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKqD,MAAL,GAAczD,MAAM,CAACkE,OAArB;;AACA,cAAIlE,MAAM,CAACmE,WAAP,KAAuBhE,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKsD,UAAL,GAAkB1D,MAAM,CAACmE,WAAzB;;AACA,cAAInE,MAAM,CAACoE,SAAP,KAAqBjE,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD;AAAE,iBAAKuD,QAAL,GAAgB,EAAhB;;AAAoB,iBAAI,IAAIvC,KAAR,IAAiBpB,MAAM,CAACoE,SAAxB,EAAmC;AAAE,kBAAI/C,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAI5F,YAAJ,CAAiB2F,KAAjB,CAAN;AAA+B,mBAAKuC,QAAL,CAAcrC,IAAd,CAAmBD,GAAnB;AAAyB;AAAC;;AAC7H,cAAIrB,MAAM,CAACqE,SAAP,KAAqBlE,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKwD,QAAL,GAAgB,IAAIlH,WAAJ,CAAgBsD,MAAM,CAACqE,SAAvB,CAAhB;;AACA,cAAIrE,MAAM,CAAC6D,UAAP,KAAsB1D,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKyD,UAAL,GAAkB7D,MAAM,CAAC6D,UAAzB;;AACA,cAAI7D,MAAM,CAACsE,SAAP,KAAqBnE,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAK0D,QAAL,GAAgB9D,MAAM,CAACsE,SAAvB;;AACA,cAAItE,MAAM,CAACsQ,cAAP,KAA0BnQ,SAA9B,EAAyC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC9D,eAAK+P,YAAL,GAAoBnQ,MAAM,CAACsQ,cAA3B;;AACA,cAAItQ,MAAM,CAACuQ,WAAP,KAAuBpQ,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKgQ,UAAL,GAAkBpQ,MAAM,CAACuQ,WAAzB;;AACA,cAAIvQ,MAAM,CAACkF,SAAP,KAAqB/E,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKwE,QAAL,GAAgB5E,MAAM,CAACkF,SAAvB;;AACA,cAAIlF,MAAM,CAACmF,SAAP,KAAqBhF,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKyE,QAAL,GAAgB7E,MAAM,CAACmF,SAAvB;;AACA,cAAInF,MAAM,CAACoF,UAAP,KAAsBjF,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK0E,SAAL,GAAiB9E,MAAM,CAACoF,UAAxB;;AACA,cAAIpF,MAAM,CAACqF,UAAP,KAAsBlF,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK2E,SAAL,GAAiB/E,MAAM,CAACqF,UAAxB;;AACA,cAAIrF,MAAM,CAACuE,OAAP,KAAmBpE,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAK2D,MAAL,GAAc/D,MAAM,CAACuE,OAArB;AACH;;AAgEDlE,QAAAA,OAAO,CAACC,MAAD,EAAgB;AAAA;;AAMnB,eAAK,IAAIkE,EAAT,IAAe,KAAKb,QAApB,EAA8B;AAAEa,YAAAA,EAAE,QAAF,IAAAA,EAAE,CAAEnE,OAAJ,CAAYC,MAAZ;AAAsB;;AACtD,kCAAKsD,QAAL,6BAAevD,OAAf,CAAuBC,MAAvB;AAUH;;AApHgB,O;AA2HrB;AACA;AACA;;;0BACa7D,Q,GAAN,MAAMA,QAAN,CAAe;AAElBsD,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAuChBuD,MAvCgB;AAAA,eAwChBC,OAxCgB;AAAA,eAyChB5B,IAzCgB;;AA0CzB;AACJ;AACA;AA5C6B,eA6ChBsO,SA7CgB;AAAA,eA8ChBzL,KA9CgB;AAAA,eA+ChBhB,MA/CgB;AAAA,eAgDhBC,UAhDgB;AAAA,eAiDhBC,QAjDgB;AAAA,eAkDhBC,QAlDgB;AAAA,eAmDhBe,SAnDgB;;AAoDzB;AACJ;AACA;AAtD6B,eAuDhBb,QAvDgB;AAAA,eAwDhB0M,WAxDgB;;AAyDzB;AACJ;AACA;AA3D6B,eA4DhBC,cA5DgB;;AA6DzB;AACJ;AACA;AA/D6B,eAgEhB7L,QAhEgB;;AAiEzB;AACJ;AACA;AAnE6B,eAoEhBC,QApEgB;;AAqEzB;AACJ;AACA;AAvE6B,eAwEhBC,SAxEgB;;AAyEzB;AACJ;AACA;AA3E6B,eA4EhBC,SA5EgB;;AA6EzB;AACJ;AACA;AA/E6B,eAgFhBhB,MAhFgB;;AACrB,cAAI/D,MAAM,CAACgE,OAAP,KAAmB7D,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKmD,MAAL,GAAcvD,MAAM,CAACgE,OAArB;;AACA,cAAIhE,MAAM,CAACiE,QAAP,KAAoB9D,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKoD,OAAL,GAAexD,MAAM,CAACiE,QAAtB;;AACA,cAAIjE,MAAM,CAAC4B,IAAP,KAAgBzB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKwB,IAAL,GAAY5B,MAAM,CAAC4B,IAAnB;;AACA,cAAI5B,MAAM,CAACqQ,UAAP,KAAsBlQ,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK8P,SAAL,GAAiBlQ,MAAM,CAACqQ,UAAxB;;AACA,cAAIrQ,MAAM,CAACgF,MAAP,KAAkB7E,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAKqE,KAAL,GAAazE,MAAM,CAACgF,MAApB;;AACA,cAAIhF,MAAM,CAACkE,OAAP,KAAmB/D,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKqD,MAAL,GAAczD,MAAM,CAACkE,OAArB;;AACA,cAAIlE,MAAM,CAACmE,WAAP,KAAuBhE,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKsD,UAAL,GAAkB1D,MAAM,CAACmE,WAAzB;;AACA,cAAInE,MAAM,CAACoE,SAAP,KAAqBjE,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD;AAAE,iBAAKuD,QAAL,GAAgB,EAAhB;;AAAoB,iBAAI,IAAIvC,KAAR,IAAiBpB,MAAM,CAACoE,SAAxB,EAAmC;AAAE,kBAAI/C,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAI5F,YAAJ,CAAiB2F,KAAjB,CAAN;AAA+B,mBAAKuC,QAAL,CAAcrC,IAAd,CAAmBD,GAAnB;AAAyB;AAAC;;AAC7H,cAAIrB,MAAM,CAACqE,SAAP,KAAqBlE,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKwD,QAAL,GAAgB,IAAIlH,WAAJ,CAAgBsD,MAAM,CAACqE,SAAvB,CAAhB;;AACA,cAAIrE,MAAM,CAAC2E,SAAP,KAAqBxE,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKuE,SAAL,GAAiB3E,MAAM,CAAC2E,SAAxB;;AACA,cAAI3E,MAAM,CAACsE,SAAP,KAAqBnE,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAK0D,QAAL,GAAgB9D,MAAM,CAACsE,SAAvB;;AACA,cAAItE,MAAM,CAAC0Q,aAAP,KAAyBvQ,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAKoQ,WAAL,GAAmBxQ,MAAM,CAAC0Q,aAA1B;;AACA,cAAI1Q,MAAM,CAAC2Q,gBAAP,KAA4BxQ,SAAhC,EAA2C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAChE,eAAKqQ,cAAL,GAAsBzQ,MAAM,CAAC2Q,gBAA7B;;AACA,cAAI3Q,MAAM,CAACkF,SAAP,KAAqB/E,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKwE,QAAL,GAAgB5E,MAAM,CAACkF,SAAvB;;AACA,cAAIlF,MAAM,CAACmF,SAAP,KAAqBhF,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKyE,QAAL,GAAgB7E,MAAM,CAACmF,SAAvB;;AACA,cAAInF,MAAM,CAACoF,UAAP,KAAsBjF,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK0E,SAAL,GAAiB9E,MAAM,CAACoF,UAAxB;;AACA,cAAIpF,MAAM,CAACqF,UAAP,KAAsBlF,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK2E,SAAL,GAAiB/E,MAAM,CAACqF,UAAxB;;AACA,cAAIrF,MAAM,CAACuE,OAAP,KAAmBpE,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAK2D,MAAL,GAAc/D,MAAM,CAACuE,OAArB;AACH;;AA6CDlE,QAAAA,OAAO,CAACC,MAAD,EAAgB;AAAA;;AAQnB,eAAK,IAAIkE,EAAT,IAAe,KAAKb,QAApB,EAA8B;AAAEa,YAAAA,EAAE,QAAF,IAAAA,EAAE,CAAEnE,OAAJ,CAAYC,MAAZ;AAAsB;;AACtD,kCAAKsD,QAAL,6BAAevD,OAAf,CAAuBC,MAAvB;AAUH;;AAvGiB,O;;6BA8GT5D,W,GAAN,MAAMA,WAAN,CAAkB;AAErBqD,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAShB4Q,QATgB;AAAA,eAUhBrJ,MAVgB;AAAA,eAWhB3F,IAXgB;;AACrB,cAAI5B,MAAM,CAAC6Q,SAAP,KAAqB1Q,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKwQ,QAAL,GAAgB5Q,MAAM,CAAC6Q,SAAvB;;AACA,cAAI7Q,MAAM,CAACuH,MAAP,KAAkBpH,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD;AAAE,iBAAKmH,MAAL,GAAc,EAAd;;AAAkB,iBAAI,IAAInG,KAAR,IAAiBpB,MAAM,CAACuH,MAAxB,EAAgC;AAAE,kBAAIlG,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKmG,MAAL,CAAYjG,IAAZ,CAAiBD,GAAjB;AAAuB;AAAC;;AACpG,cAAIrB,MAAM,CAAC4B,IAAP,KAAgBzB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKwB,IAAL,GAAY5B,MAAM,CAAC4B,IAAnB;AACH;;AAMDvB,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAItB;;AAnBoB,O;;8BA0BZ3D,Y,GAAN,MAAMA,YAAN,CAAmB;AAEtBoD,QAAAA,WAAW,CAACC,MAAD,EAAc;AAmBzB;AACJ;AACA;AArB6B,eAsBhBuD,MAtBgB;;AAuBzB;AACJ;AACA;AAzB6B,eA0BhBC,OA1BgB;;AA2BzB;AACJ;AACA;AA7B6B,eA8BhBsN,KA9BgB;;AA+BzB;AACJ;AACA;AAjC6B,eAkChBpN,UAlCgB;AAAA,eAmChBC,QAnCgB;AAAA,eAoChBC,QApCgB;;AAqCzB;AACJ;AACA;AAvC6B,eAwChBC,UAxCgB;;AAyCzB;AACJ;AACA;AA3C6B,eA4ChBC,QA5CgB;;AACrB,cAAI9D,MAAM,CAACgE,OAAP,KAAmB7D,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKmD,MAAL,GAAcvD,MAAM,CAACgE,OAArB;;AACA,cAAIhE,MAAM,CAACiE,QAAP,KAAoB9D,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKoD,OAAL,GAAexD,MAAM,CAACiE,QAAtB;;AACA,cAAIjE,MAAM,CAAC8Q,KAAP,KAAiB3Q,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAK0Q,KAAL,GAAa9Q,MAAM,CAAC8Q,KAApB;;AACA,cAAI9Q,MAAM,CAACmE,WAAP,KAAuBhE,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKsD,UAAL,GAAkB1D,MAAM,CAACmE,WAAzB;;AACA,cAAInE,MAAM,CAACoE,SAAP,KAAqBjE,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD;AAAE,iBAAKuD,QAAL,GAAgB,EAAhB;;AAAoB,iBAAI,IAAIvC,KAAR,IAAiBpB,MAAM,CAACoE,SAAxB,EAAmC;AAAE,kBAAI/C,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAG,IAAI5F,YAAJ,CAAiB2F,KAAjB,CAAN;AAA+B,mBAAKuC,QAAL,CAAcrC,IAAd,CAAmBD,GAAnB;AAAyB;AAAC;;AAC7H,cAAIrB,MAAM,CAACqE,SAAP,KAAqBlE,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKwD,QAAL,GAAgB,IAAIlH,WAAJ,CAAgBsD,MAAM,CAACqE,SAAvB,CAAhB;;AACA,cAAIrE,MAAM,CAAC6D,UAAP,KAAsB1D,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKyD,UAAL,GAAkB7D,MAAM,CAAC6D,UAAzB;;AACA,cAAI7D,MAAM,CAACsE,SAAP,KAAqBnE,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAK0D,QAAL,GAAgB9D,MAAM,CAACsE,SAAvB;AACH;;AA6BDjE,QAAAA,OAAO,CAACC,MAAD,EAAgB;AAAA;;AAKnB,eAAK,IAAIkE,EAAT,IAAe,KAAKb,QAApB,EAA8B;AAAEa,YAAAA,EAAE,QAAF,IAAAA,EAAE,CAAEnE,OAAJ,CAAYC,MAAZ;AAAsB;;AACtD,kCAAKsD,QAAL,6BAAevD,OAAf,CAAuBC,MAAvB;AAGH;;AAzDqB,O;;0BAgEb1D,Q,GAAN,MAAMA,QAAN,CAAe;AAElBmD,QAAAA,WAAW,CAACC,MAAD,EAAc;AASzB;AACJ;AACA;AAX6B,eAYhBiB,EAZgB;;AAazB;AACJ;AACA;AAf6B,eAgBhB8P,GAhBgB;;AAiBzB;AACJ;AACA;AAnB6B,eAoBhBxP,KApBgB;;AACrB,cAAIvB,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAAC+Q,GAAP,KAAe5Q,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAK2Q,GAAL,GAAW/Q,MAAM,CAAC+Q,GAAlB;;AACA,cAAI/Q,MAAM,CAACuB,KAAP,KAAiBpB,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKmB,KAAL,GAAavB,MAAM,CAACuB,KAApB;AACH;;AAeDlB,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAItB;;AA5BiB,O;;4BAmCTzD,U,GAAN,MAAMA,UAAN,CAAiB;AAEpBkD,QAAAA,WAAW,CAACC,MAAD,EAAc;AASzB;AACJ;AACA;AAX6B,eAYhBgR,SAZgB;;AAazB;AACJ;AACA;AAf6B,eAgBhBC,EAhBgB;;AAiBzB;AACJ;AACA;AAnB6B,eAoBhBnN,QApBgB;;AACrB,cAAI9D,MAAM,CAACkR,UAAP,KAAsB/Q,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK4Q,SAAL,GAAiBhR,MAAM,CAACkR,UAAxB;;AACA,cAAIlR,MAAM,CAACiR,EAAP,KAAc9Q,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAK6Q,EAAL,GAAUjR,MAAM,CAACiR,EAAjB;;AACA,cAAIjR,MAAM,CAACsE,SAAP,KAAqBnE,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAK0D,QAAL,GAAgB9D,MAAM,CAACsE,SAAvB;AACH;;AAeDjE,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAItB;;AA5BmB,O;;yBAmCXxD,O,GAAN,MAAMA,OAAN,CAAc;AAEjBiD,QAAAA,WAAW,CAACC,MAAD,EAAc;AA6BzB;AACJ;AACA;AA/B6B,eAgChBiB,EAhCgB;;AAiCzB;AACJ;AACA;AAnC6B,eAoChB8O,YApCgB;;AAqCzB;AACJ;AACA;AAvC6B,eAwChBC,KAxCgB;;AAyCzB;AACJ;AACA;AA3C6B,eA4ChBmB,SA5CgB;;AA6CzB;AACJ;AACA;AA/C6B,eAgDhBC,OAhDgB;;AAiDzB;AACJ;AACA;AAnD6B,eAoDhBC,QApDgB;;AAqDzB;AACJ;AACA;AAvD6B,eAwDhBC,SAxDgB;;AAyDzB;AACJ;AACA;AA3D6B,eA4DhBpQ,GA5DgB;;AA6DzB;AACJ;AACA;AA/D6B,eAgEhBqQ,GAhEgB;;AAiEzB;AACJ;AACA;AAnE6B,eAoEhBC,KApEgB;;AAqEzB;AACJ;AACA;AAvE6B,eAwEhBC,WAxEgB;;AAyEzB;AACJ;AACA;AA3E6B,eA4EhBC,YA5EgB;;AA6EzB;AACJ;AACA;AA/E6B,eAgFhBC,eAhFgB;;AACrB,cAAI3R,MAAM,CAACiB,EAAP,KAAcd,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKa,EAAL,GAAUjB,MAAM,CAACiB,EAAjB;;AACA,cAAIjB,MAAM,CAAC+P,YAAP,KAAwB5P,SAA5B,EAAuC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC5D,eAAK2P,YAAL,GAAoB/P,MAAM,CAAC+P,YAA3B;;AACA,cAAI/P,MAAM,CAACgQ,KAAP,KAAiB7P,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAK4P,KAAL,GAAahQ,MAAM,CAACgQ,KAApB;;AACA,cAAIhQ,MAAM,CAACmR,SAAP,KAAqBhR,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAK+Q,SAAL,GAAiBnR,MAAM,CAACmR,SAAxB;;AACA,cAAInR,MAAM,CAACoR,OAAP,KAAmBjR,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKgR,OAAL,GAAepR,MAAM,CAACoR,OAAtB;;AACA,cAAIpR,MAAM,CAACqR,QAAP,KAAoBlR,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKiR,QAAL,GAAgBrR,MAAM,CAACqR,QAAvB;;AACA,cAAIrR,MAAM,CAACsR,SAAP,KAAqBnR,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKkR,SAAL,GAAiBtR,MAAM,CAACsR,SAAxB;;AACA,cAAItR,MAAM,CAACkB,GAAP,KAAef,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKc,GAAL,GAAWlB,MAAM,CAACkB,GAAlB;;AACA,cAAIlB,MAAM,CAACuR,GAAP,KAAepR,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKmR,GAAL,GAAWvR,MAAM,CAACuR,GAAlB;;AACA,cAAIvR,MAAM,CAACwR,KAAP,KAAiBrR,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKoR,KAAL,GAAaxR,MAAM,CAACwR,KAApB;;AACA,cAAIxR,MAAM,CAACyR,WAAP,KAAuBtR,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKqR,WAAL,GAAmBzR,MAAM,CAACyR,WAA1B;;AACA,cAAIzR,MAAM,CAAC0R,YAAP,KAAwBvR,SAA5B,EAAuC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC5D,eAAKsR,YAAL,GAAoB1R,MAAM,CAAC0R,YAA3B;;AACA,cAAI1R,MAAM,CAAC2R,eAAP,KAA2BxR,SAA/B,EAA0C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC/D,eAAKuR,eAAL,GAAuB3R,MAAM,CAAC2R,eAA9B;AACH;;AAuDDtR,QAAAA,OAAO,CAACC,MAAD,EAAgB,CActB;;AAlGgB,O;;gCAyGRvD,c,GAAN,MAAMA,cAAN,CAAqB;AAExBgD,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAOhBmB,IAPgB;AAAA,eAQhBH,KARgB;;AACrB,cAAIhB,MAAM,CAACmB,IAAP,KAAgBhB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKe,IAAL,GAAYnB,MAAM,CAACmB,IAAnB;;AACA,cAAInB,MAAM,CAACgB,KAAP,KAAiBb,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD;AAAE,iBAAKY,KAAL,GAAa,EAAb;;AAAiB,iBAAI,IAAII,KAAR,IAAiBpB,MAAM,CAACgB,KAAxB,EAA+B;AAAE,kBAAIK,GAAG,SAAP;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKJ,KAAL,CAAWM,IAAX,CAAgBD,GAAhB;AAAsB;AAAC;AACpG;;AAKDhB,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAfuB,O;;8BAuBftD,Y,GAAN,MAAMA,YAAN,CAAmB;AAGtB+C,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eADjB4R,KACiB;AACrB,cAAI5R,MAAM,CAAC6R,MAAP,IAAiB,CAArB,EAAwB,MAAM,IAAIzR,KAAJ,CAAU,+BAAV,CAAN;AACxB,eAAKwR,KAAL,GAAa,IAAI7V,aAAJ,CAAkBiE,MAAM,CAAC,CAAD,CAAxB,CAAb;AACH;;AAED8R,QAAAA,OAAO,GAAkB;AAAE,iBAAO,KAAKF,KAAZ;AAAoB;AAE/C;AACJ;AACA;;;AACsB,YAAbhF,aAAa,GAAW;AAAE,iBAAO,KAAKgF,KAAL,CAAWhF,aAAlB;AAAkC;AACjE;AACJ;AACA;;;AACkB,YAAT/J,SAAS,GAAW;AAAE,iBAAO,KAAK+O,KAAL,CAAW/O,SAAlB;AAA8B;AACzD;AACJ;AACA;;;AAC8B,YAArBgK,qBAAqB,GAAW;AAAE,iBAAO,KAAK+E,KAAL,CAAW/E,qBAAlB;AAA0C;AACjF;AACJ;AACA;;;AAC2B,YAAlBC,kBAAkB,GAAW;AAAE,iBAAO,KAAK8E,KAAL,CAAW9E,kBAAlB;AAAuC;AAC3E;AACJ;AACA;;;AACyB,YAAhBC,gBAAgB,GAAW;AAAE,iBAAO,KAAK6E,KAAL,CAAW7E,gBAAlB;AAAqC;AACvE;AACJ;AACA;;;AAC0B,YAAjBC,iBAAiB,GAAW;AAAE,iBAAO,KAAK4E,KAAL,CAAW5E,iBAAlB;AAAsC;AACzE;AACJ;AACA;;;AACsC,YAA7BC,6BAA6B,GAAW;AAAE,iBAAO,KAAK2E,KAAL,CAAW3E,6BAAlB;AAAkD;AACjG;AACJ;AACA;;;AACmC,YAA1BC,0BAA0B,GAAW;AAAE,iBAAO,KAAK0E,KAAL,CAAW1E,0BAAlB;AAA+C;;AAE3F7M,QAAAA,OAAO,CAACC,MAAD,EACP;AACI,eAAKsR,KAAL,CAAWvR,OAAX,CAAmBC,MAAnB;AACH;;AA9CqB,O;;gCAqDbrD,c,GAAN,MAAMA,cAAN,CAAqB;AAGxB8C,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjB+R,SAEiB;AACrB,eAAKA,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBhS,MAAnB,EAA2B;AACvB,gBAAIiS,EAAmB,SAAvB;;AACAA,YAAAA,EAAE,GAAG,IAAIpW,eAAJ,CAAoBmW,OAApB,CAAL;;AACA,iBAAKD,SAAL,CAAezQ,IAAf,CAAoB2Q,EAApB;AACH;AACJ;;AAEDC,QAAAA,WAAW,GAAsB;AAAE,iBAAO,KAAKH,SAAZ;AAAuB;;AAE1DI,QAAAA,GAAG,CAACrB,KAAD,EAA6C;AAAE,iBAAO,KAAKiB,SAAL,CAAejB,KAAf,CAAP;AAA8B;;AAEhFzQ,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK8R,IAAT,IAAiB,KAAKL,SAAtB,EACA;AACIK,YAAAA,IAAI,CAAC/R,OAAL,CAAaC,MAAb;AACH;AACJ;;AArBuB,O;;sBA4BfpD,I,GAAN,MAAMA,IAAN,CAAW;AAGd6C,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjB+R,SAEiB;AACrB,eAAKA,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBhS,MAAnB,EAA2B;AACvB,gBAAIiS,EAAM,SAAV;;AACAA,YAAAA,EAAE,GAAG,IAAIrX,EAAJ,CAAOoX,OAAP,CAAL;;AACA,iBAAKD,SAAL,CAAezQ,IAAf,CAAoB2Q,EAApB;AACH;AACJ;;AAEDC,QAAAA,WAAW,GAAS;AAAE,iBAAO,KAAKH,SAAZ;AAAuB;;AAE7CI,QAAAA,GAAG,CAACrB,KAAD,EAAgC;AAAE,iBAAO,KAAKiB,SAAL,CAAejB,KAAf,CAAP;AAA8B;;AAEnEzQ,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK8R,IAAT,IAAiB,KAAKL,SAAtB,EACA;AACIK,YAAAA,IAAI,CAAC/R,OAAL,CAAaC,MAAb;AACH;AACJ;;AArBa,O;;yBA4BLnD,O,GAAN,MAAMA,OAAN,CAAc;AAGjB4C,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBqS,QAEiB;AAAA,eADjBN,SACiB;AACrB,eAAKM,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKP,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBhS,MAAnB,EAA2B;AACvB,gBAAIiS,EAAY,SAAhB;;AACAA,YAAAA,EAAE,GAAG,IAAI5V,QAAJ,CAAa2V,OAAb,CAAL;;AACA,iBAAKD,SAAL,CAAezQ,IAAf,CAAoB2Q,EAApB;;AACA,iBAAKI,QAAL,CAAcE,GAAd,CAAkBN,EAAE,CAAChR,EAArB,EAAyBgR,EAAzB;AACH;AACJ;;AAEDO,QAAAA,UAAU,GAA0B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC7DH,QAAAA,WAAW,GAAe;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEpDI,QAAAA,GAAG,CAACM,GAAD,EAAoC;AAAE,iBAAO,KAAKJ,QAAL,CAAcF,GAAd,CAAkBM,GAAlB,CAAP;AAAgC;;AAEzEpS,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK8R,IAAT,IAAiB,KAAKL,SAAtB,EACA;AACIK,YAAAA,IAAI,CAAC/R,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBgB,O;;kCA+BRlD,gB,GAAN,MAAMA,gBAAN,CAAuB;AAG1B2C,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBqS,QAEiB;AAAA,eADjBN,SACiB;AACrB,eAAKM,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKP,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBhS,MAAnB,EAA2B;AACvB,gBAAIiS,EAAkB,SAAtB;;AACAA,YAAAA,EAAE,GAAG,IAAI7W,cAAJ,CAAmB4W,OAAnB,CAAL;;AACA,iBAAKD,SAAL,CAAezQ,IAAf,CAAoB2Q,EAApB;;AACA,iBAAKI,QAAL,CAAcE,GAAd,CAAkBN,EAAE,CAAC1O,MAArB,EAA6B0O,EAA7B;AACH;AACJ;;AAEDO,QAAAA,UAAU,GAAgC;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AACnEH,QAAAA,WAAW,GAAqB;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAE1DI,QAAAA,GAAG,CAACM,GAAD,EAA0C;AAAE,iBAAO,KAAKJ,QAAL,CAAcF,GAAd,CAAkBM,GAAlB,CAAP;AAAgC;;AAE/EpS,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK8R,IAAT,IAAiB,KAAKL,SAAtB,EACA;AACIK,YAAAA,IAAI,CAAC/R,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxByB,O;;+BA+BjBjD,a,GAAN,MAAMA,aAAN,CAAoB;AAGvB0C,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBqS,QAEiB;AAAA,eADjBN,SACiB;AACrB,eAAKM,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKP,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBhS,MAAnB,EAA2B;AACvB,gBAAIiS,EAAe,SAAnB;;AACAA,YAAAA,EAAE,GAAG,IAAI5W,WAAJ,CAAgB2W,OAAhB,CAAL;;AACA,iBAAKD,SAAL,CAAezQ,IAAf,CAAoB2Q,EAApB;;AACA,iBAAKI,QAAL,CAAcE,GAAd,CAAkBN,EAAE,CAAC1O,MAArB,EAA6B0O,EAA7B;AACH;AACJ;;AAEDO,QAAAA,UAAU,GAA6B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAChEH,QAAAA,WAAW,GAAkB;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEvDI,QAAAA,GAAG,CAACM,GAAD,EAAuC;AAAE,iBAAO,KAAKJ,QAAL,CAAcF,GAAd,CAAkBM,GAAlB,CAAP;AAAgC;;AAE5EpS,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK8R,IAAT,IAAiB,KAAKL,SAAtB,EACA;AACIK,YAAAA,IAAI,CAAC/R,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBsB,O;;6BA+BdhD,W,GAAN,MAAMA,WAAN,CAAkB;AAGrByC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBqS,QAEiB;AAAA,eADjBN,SACiB;AACrB,eAAKM,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKP,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBhS,MAAnB,EAA2B;AACvB,gBAAIiS,EAAa,SAAjB;;AACAA,YAAAA,EAAE,GAAG,IAAI3W,SAAJ,CAAc0W,OAAd,CAAL;;AACA,iBAAKD,SAAL,CAAezQ,IAAf,CAAoB2Q,EAApB;;AACA,iBAAKI,QAAL,CAAcE,GAAd,CAAkBN,EAAE,CAAChR,EAArB,EAAyBgR,EAAzB;AACH;AACJ;;AAEDO,QAAAA,UAAU,GAA2B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC9DH,QAAAA,WAAW,GAAgB;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAErDI,QAAAA,GAAG,CAACM,GAAD,EAAqC;AAAE,iBAAO,KAAKJ,QAAL,CAAcF,GAAd,CAAkBM,GAAlB,CAAP;AAAgC;;AAE1EpS,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK8R,IAAT,IAAiB,KAAKL,SAAtB,EACA;AACIK,YAAAA,IAAI,CAAC/R,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBoB,O;;6BA+BZ/C,W,GAAN,MAAMA,WAAN,CAAkB;AAGrBwC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBqS,QAEiB;AAAA,eADjBN,SACiB;AACrB,eAAKM,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKP,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBhS,MAAnB,EAA2B;AACvB,gBAAIiS,EAAa,SAAjB;;AACAA,YAAAA,EAAE,GAAG,IAAI1W,SAAJ,CAAcyW,OAAd,CAAL;;AACA,iBAAKD,SAAL,CAAezQ,IAAf,CAAoB2Q,EAApB;;AACA,iBAAKI,QAAL,CAAcE,GAAd,CAAkBN,EAAE,CAAChR,EAArB,EAAyBgR,EAAzB;AACH;AACJ;;AAEDO,QAAAA,UAAU,GAA2B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC9DH,QAAAA,WAAW,GAAgB;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAErDI,QAAAA,GAAG,CAACM,GAAD,EAAqC;AAAE,iBAAO,KAAKJ,QAAL,CAAcF,GAAd,CAAkBM,GAAlB,CAAP;AAAgC;;AAE1EpS,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK8R,IAAT,IAAiB,KAAKL,SAAtB,EACA;AACIK,YAAAA,IAAI,CAAC/R,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBoB,O;;8BA+BZ9C,Y,GAAN,MAAMA,YAAN,CAAmB;AAGtBuC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBqS,QAEiB;AAAA,eADjBN,SACiB;AACrB,eAAKM,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKP,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBhS,MAAnB,EAA2B;AACvB,gBAAIiS,EAAc,SAAlB;;AACAA,YAAAA,EAAE,GAAG,IAAIzW,UAAJ,CAAewW,OAAf,CAAL;;AACA,iBAAKD,SAAL,CAAezQ,IAAf,CAAoB2Q,EAApB;;AACA,iBAAKI,QAAL,CAAcE,GAAd,CAAkBN,EAAE,CAAChR,EAArB,EAAyBgR,EAAzB;AACH;AACJ;;AAEDO,QAAAA,UAAU,GAA4B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC/DH,QAAAA,WAAW,GAAiB;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEtDI,QAAAA,GAAG,CAACM,GAAD,EAAsC;AAAE,iBAAO,KAAKJ,QAAL,CAAcF,GAAd,CAAkBM,GAAlB,CAAP;AAAgC;;AAE3EpS,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK8R,IAAT,IAAiB,KAAKL,SAAtB,EACA;AACIK,YAAAA,IAAI,CAAC/R,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBqB,O;;6BA+Bb7C,W,GAAN,MAAMA,WAAN,CAAkB;AAGrBsC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBqS,QAEiB;AAAA,eADjBN,SACiB;AACrB,eAAKM,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKP,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBhS,MAAnB,EAA2B;AACvB,gBAAIiS,EAAa,SAAjB;;AACAA,YAAAA,EAAE,GAAG,IAAIvW,SAAJ,CAAcsW,OAAd,CAAL;;AACA,iBAAKD,SAAL,CAAezQ,IAAf,CAAoB2Q,EAApB;;AACA,iBAAKI,QAAL,CAAcE,GAAd,CAAkBN,EAAE,CAAChR,EAArB,EAAyBgR,EAAzB;AACH;AACJ;;AAEDO,QAAAA,UAAU,GAA2B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC9DH,QAAAA,WAAW,GAAgB;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAErDI,QAAAA,GAAG,CAACM,GAAD,EAAqC;AAAE,iBAAO,KAAKJ,QAAL,CAAcF,GAAd,CAAkBM,GAAlB,CAAP;AAAgC;;AAE1EpS,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK8R,IAAT,IAAiB,KAAKL,SAAtB,EACA;AACIK,YAAAA,IAAI,CAAC/R,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBoB,O;;4BA+BZ5C,U,GAAN,MAAMA,UAAN,CAAiB;AAGpBqC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBqS,QAEiB;AAAA,eADjBN,SACiB;AACrB,eAAKM,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKP,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBhS,MAAnB,EAA2B;AACvB,gBAAIiS,EAAY,SAAhB;;AACAA,YAAAA,EAAE,GAAG,IAAItW,QAAJ,CAAaqW,OAAb,CAAL;;AACA,iBAAKD,SAAL,CAAezQ,IAAf,CAAoB2Q,EAApB;;AACA,iBAAKI,QAAL,CAAcE,GAAd,CAAkBN,EAAE,CAAChR,EAArB,EAAyBgR,EAAzB;AACH;AACJ;;AAEDO,QAAAA,UAAU,GAA0B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC7DH,QAAAA,WAAW,GAAe;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEpDI,QAAAA,GAAG,CAACM,GAAD,EAAoC;AAAE,iBAAO,KAAKJ,QAAL,CAAcF,GAAd,CAAkBM,GAAlB,CAAP;AAAgC;;AAEzEpS,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK8R,IAAT,IAAiB,KAAKL,SAAtB,EACA;AACIK,YAAAA,IAAI,CAAC/R,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBmB,O;;4BA+BX3C,U,GAAN,MAAMA,UAAN,CAAiB;AAGpBoC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBqS,QAEiB;AAAA,eADjBN,SACiB;AACrB,eAAKM,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKP,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBhS,MAAnB,EAA2B;AACvB,gBAAIiS,EAAY,SAAhB;;AACAA,YAAAA,EAAE,GAAG,IAAIrW,QAAJ,CAAaoW,OAAb,CAAL;;AACA,iBAAKD,SAAL,CAAezQ,IAAf,CAAoB2Q,EAApB;;AACA,iBAAKI,QAAL,CAAcE,GAAd,CAAkBN,EAAE,CAAChR,EAArB,EAAyBgR,EAAzB;AACH;AACJ;;AAEDO,QAAAA,UAAU,GAA0B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC7DH,QAAAA,WAAW,GAAe;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEpDI,QAAAA,GAAG,CAACM,GAAD,EAAoC;AAAE,iBAAO,KAAKJ,QAAL,CAAcF,GAAd,CAAkBM,GAAlB,CAAP;AAAgC;;AAEzEpS,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK8R,IAAT,IAAiB,KAAKL,SAAtB,EACA;AACIK,YAAAA,IAAI,CAAC/R,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBmB,O;;+BA+BX1C,a,GAAN,MAAMA,aAAN,CAAoB;AAGvBmC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBqS,QAEiB;AAAA,eADjBN,SACiB;AACrB,eAAKM,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKP,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBhS,MAAnB,EAA2B;AACvB,gBAAIiS,EAAe,SAAnB;;AACAA,YAAAA,EAAE,GAAG,IAAInW,WAAJ,CAAgBkW,OAAhB,CAAL;;AACA,iBAAKD,SAAL,CAAezQ,IAAf,CAAoB2Q,EAApB;;AACA,iBAAKI,QAAL,CAAcE,GAAd,CAAkBN,EAAE,CAAC7O,EAArB,EAAyB6O,EAAzB;AACH;AACJ;;AAEDO,QAAAA,UAAU,GAA6B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAChEH,QAAAA,WAAW,GAAkB;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEvDI,QAAAA,GAAG,CAACM,GAAD,EAAuC;AAAE,iBAAO,KAAKJ,QAAL,CAAcF,GAAd,CAAkBM,GAAlB,CAAP;AAAgC;;AAE5EpS,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK8R,IAAT,IAAiB,KAAKL,SAAtB,EACA;AACIK,YAAAA,IAAI,CAAC/R,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBsB,O;;2BA+BdzC,S,GAAN,MAAMA,SAAN,CAAgB;AAGnBkC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBqS,QAEiB;AAAA,eADjBN,SACiB;AACrB,eAAKM,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKP,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBhS,MAAnB,EAA2B;AACvB,gBAAIiS,EAAW,SAAf;;AACAA,YAAAA,EAAE,GAAG,IAAIjW,OAAJ,CAAYgW,OAAZ,CAAL;;AACA,iBAAKD,SAAL,CAAezQ,IAAf,CAAoB2Q,EAApB;;AACA,iBAAKI,QAAL,CAAcE,GAAd,CAAkBN,EAAE,CAAChR,EAArB,EAAyBgR,EAAzB;AACH;AACJ;;AAEDO,QAAAA,UAAU,GAAyB;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC5DH,QAAAA,WAAW,GAAc;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEnDI,QAAAA,GAAG,CAACM,GAAD,EAAmC;AAAE,iBAAO,KAAKJ,QAAL,CAAcF,GAAd,CAAkBM,GAAlB,CAAP;AAAgC;;AAExEpS,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK8R,IAAT,IAAiB,KAAKL,SAAtB,EACA;AACIK,YAAAA,IAAI,CAAC/R,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBkB,O;;4BA+BVxC,U,GAAN,MAAMA,UAAN,CAAiB;AAGpBiC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBqS,QAEiB;AAAA,eADjBN,SACiB;AACrB,eAAKM,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKP,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBhS,MAAnB,EAA2B;AACvB,gBAAIiS,EAAY,SAAhB;;AACAA,YAAAA,EAAE,GAAG,IAAIhW,QAAJ,CAAa+V,OAAb,CAAL;;AACA,iBAAKD,SAAL,CAAezQ,IAAf,CAAoB2Q,EAApB;;AACA,iBAAKI,QAAL,CAAcE,GAAd,CAAkBN,EAAE,CAAChR,EAArB,EAAyBgR,EAAzB;AACH;AACJ;;AAEDO,QAAAA,UAAU,GAA0B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC7DH,QAAAA,WAAW,GAAe;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEpDI,QAAAA,GAAG,CAACM,GAAD,EAAoC;AAAE,iBAAO,KAAKJ,QAAL,CAAcF,GAAd,CAAkBM,GAAlB,CAAP;AAAgC;;AAEzEpS,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK8R,IAAT,IAAiB,KAAKL,SAAtB,EACA;AACIK,YAAAA,IAAI,CAAC/R,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBmB,O;;iCA+BXvC,e,GAAN,MAAMA,eAAN,CAAsB;AAGzBgC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBqS,QAEiB;AAAA,eADjBN,SACiB;AACrB,eAAKM,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKP,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBhS,MAAnB,EAA2B;AACvB,gBAAIiS,EAAiB,SAArB;;AACAA,YAAAA,EAAE,GAAG,IAAI/V,aAAJ,CAAkB8V,OAAlB,CAAL;;AACA,iBAAKD,SAAL,CAAezQ,IAAf,CAAoB2Q,EAApB;;AACA,iBAAKI,QAAL,CAAcE,GAAd,CAAkBN,EAAE,CAAChR,EAArB,EAAyBgR,EAAzB;AACH;AACJ;;AAEDO,QAAAA,UAAU,GAA+B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAClEH,QAAAA,WAAW,GAAoB;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEzDI,QAAAA,GAAG,CAACM,GAAD,EAAyC;AAAE,iBAAO,KAAKJ,QAAL,CAAcF,GAAd,CAAkBM,GAAlB,CAAP;AAAgC;;AAE9EpS,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK8R,IAAT,IAAiB,KAAKL,SAAtB,EACA;AACIK,YAAAA,IAAI,CAAC/R,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBwB,O;;2BA+BhBtC,S,GAAN,MAAMA,SAAN,CAAgB;AAGnB+B,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBqS,QAEiB;AAAA,eADjBN,SACiB;AACrB,eAAKM,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKP,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBhS,MAAnB,EAA2B;AACvB,gBAAIiS,EAAW,SAAf;;AACAA,YAAAA,EAAE,GAAG,IAAI9V,OAAJ,CAAY6V,OAAZ,CAAL;;AACA,iBAAKD,SAAL,CAAezQ,IAAf,CAAoB2Q,EAApB;;AACA,iBAAKI,QAAL,CAAcE,GAAd,CAAkBN,EAAE,CAAC3D,MAArB,EAA6B2D,EAA7B;AACH;AACJ;;AAEDO,QAAAA,UAAU,GAAyB;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC5DH,QAAAA,WAAW,GAAc;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEnDI,QAAAA,GAAG,CAACM,GAAD,EAAmC;AAAE,iBAAO,KAAKJ,QAAL,CAAcF,GAAd,CAAkBM,GAAlB,CAAP;AAAgC;;AAExEpS,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK8R,IAAT,IAAiB,KAAKL,SAAtB,EACA;AACIK,YAAAA,IAAI,CAAC/R,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBkB,O;;4BA+BVrC,U,GAAN,MAAMA,UAAN,CAAiB;AAGpB8B,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBqS,QAEiB;AAAA,eADjBN,SACiB;AACrB,eAAKM,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKP,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBhS,MAAnB,EAA2B;AACvB,gBAAIiS,EAAY,SAAhB;;AACAA,YAAAA,EAAE,GAAG,IAAI3V,QAAJ,CAAa0V,OAAb,CAAL;;AACA,iBAAKD,SAAL,CAAezQ,IAAf,CAAoB2Q,EAApB;;AACA,iBAAKI,QAAL,CAAcE,GAAd,CAAkBN,EAAE,CAAChR,EAArB,EAAyBgR,EAAzB;AACH;AACJ;;AAEDO,QAAAA,UAAU,GAA0B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC7DH,QAAAA,WAAW,GAAe;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEpDI,QAAAA,GAAG,CAACM,GAAD,EAAoC;AAAE,iBAAO,KAAKJ,QAAL,CAAcF,GAAd,CAAkBM,GAAlB,CAAP;AAAgC;;AAEzEpS,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK8R,IAAT,IAAiB,KAAKL,SAAtB,EACA;AACIK,YAAAA,IAAI,CAAC/R,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBmB,O;;4BA+BXpC,U,GAAN,MAAMA,UAAN,CAAiB;AAGpB6B,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBqS,QAEiB;AAAA,eADjBN,SACiB;AACrB,eAAKM,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKP,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBhS,MAAnB,EAA2B;AACvB,gBAAIiS,EAAY,SAAhB;;AACAA,YAAAA,EAAE,GAAG,IAAI1V,QAAJ,CAAayV,OAAb,CAAL;;AACA,iBAAKD,SAAL,CAAezQ,IAAf,CAAoB2Q,EAApB;;AACA,iBAAKI,QAAL,CAAcE,GAAd,CAAkBN,EAAE,CAAChR,EAArB,EAAyBgR,EAAzB;AACH;AACJ;;AAEDO,QAAAA,UAAU,GAA0B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC7DH,QAAAA,WAAW,GAAe;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEpDI,QAAAA,GAAG,CAACM,GAAD,EAAoC;AAAE,iBAAO,KAAKJ,QAAL,CAAcF,GAAd,CAAkBM,GAAlB,CAAP;AAAgC;;AAEzEpS,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK8R,IAAT,IAAiB,KAAKL,SAAtB,EACA;AACIK,YAAAA,IAAI,CAAC/R,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBmB,O;;2BA+BXnC,S,GAAN,MAAMA,SAAN,CAAgB;AAGnB4B,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBqS,QAEiB;AAAA,eADjBN,SACiB;AACrB,eAAKM,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKP,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBhS,MAAnB,EAA2B;AACvB,gBAAIiS,EAAW,SAAf;;AACAA,YAAAA,EAAE,GAAG,IAAIzV,OAAJ,CAAYwV,OAAZ,CAAL;;AACA,iBAAKD,SAAL,CAAezQ,IAAf,CAAoB2Q,EAApB;;AACA,iBAAKI,QAAL,CAAcE,GAAd,CAAkBN,EAAE,CAAC1O,MAArB,EAA6B0O,EAA7B;AACH;AACJ;;AAEDO,QAAAA,UAAU,GAAyB;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC5DH,QAAAA,WAAW,GAAc;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEnDI,QAAAA,GAAG,CAACM,GAAD,EAAmC;AAAE,iBAAO,KAAKJ,QAAL,CAAcF,GAAd,CAAkBM,GAAlB,CAAP;AAAgC;;AAExEpS,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK8R,IAAT,IAAiB,KAAKL,SAAtB,EACA;AACIK,YAAAA,IAAI,CAAC/R,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBkB,O;;gCA+BVlC,c,GAAN,MAAMA,cAAN,CAAqB;AAGxB2B,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBqS,QAEiB;AAAA,eADjBN,SACiB;AACrB,eAAKM,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKP,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBhS,MAAnB,EAA2B;AACvB,gBAAIiS,EAAgB,SAApB;;AACAA,YAAAA,EAAE,GAAG,IAAItV,YAAJ,CAAiBqV,OAAjB,CAAL;;AACA,iBAAKD,SAAL,CAAezQ,IAAf,CAAoB2Q,EAApB;;AACA,iBAAKI,QAAL,CAAcE,GAAd,CAAkBN,EAAE,CAAC1O,MAArB,EAA6B0O,EAA7B;AACH;AACJ;;AAEDO,QAAAA,UAAU,GAA8B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AACjEH,QAAAA,WAAW,GAAmB;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAExDI,QAAAA,GAAG,CAACM,GAAD,EAAwC;AAAE,iBAAO,KAAKJ,QAAL,CAAcF,GAAd,CAAkBM,GAAlB,CAAP;AAAgC;;AAE7EpS,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK8R,IAAT,IAAiB,KAAKL,SAAtB,EACA;AACIK,YAAAA,IAAI,CAAC/R,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBuB,O;;4BA+BfjC,U,GAAN,MAAMA,UAAN,CAAiB;AAGpB0B,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBqS,QAEiB;AAAA,eADjBN,SACiB;AACrB,eAAKM,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKP,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBhS,MAAnB,EAA2B;AACvB,gBAAIiS,EAAY,SAAhB;;AACAA,YAAAA,EAAE,GAAG,IAAIrV,QAAJ,CAAaoV,OAAb,CAAL;;AACA,iBAAKD,SAAL,CAAezQ,IAAf,CAAoB2Q,EAApB;;AACA,iBAAKI,QAAL,CAAcE,GAAd,CAAkBN,EAAE,CAAChR,EAArB,EAAyBgR,EAAzB;AACH;AACJ;;AAEDO,QAAAA,UAAU,GAA0B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC7DH,QAAAA,WAAW,GAAe;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEpDI,QAAAA,GAAG,CAACM,GAAD,EAAoC;AAAE,iBAAO,KAAKJ,QAAL,CAAcF,GAAd,CAAkBM,GAAlB,CAAP;AAAgC;;AAEzEpS,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK8R,IAAT,IAAiB,KAAKL,SAAtB,EACA;AACIK,YAAAA,IAAI,CAAC/R,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBmB,O;;8BA+BXhC,Y,GAAN,MAAMA,YAAN,CAAmB;AAGtByB,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBqS,QAEiB;AAAA,eADjBN,SACiB;AACrB,eAAKM,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKP,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBhS,MAAnB,EAA2B;AACvB,gBAAIiS,EAAc,SAAlB;;AACAA,YAAAA,EAAE,GAAG,IAAIpV,UAAJ,CAAemV,OAAf,CAAL;;AACA,iBAAKD,SAAL,CAAezQ,IAAf,CAAoB2Q,EAApB;;AACA,iBAAKI,QAAL,CAAcE,GAAd,CAAkBN,EAAE,CAACjB,SAArB,EAAgCiB,EAAhC;AACH;AACJ;;AAEDO,QAAAA,UAAU,GAA4B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC/DH,QAAAA,WAAW,GAAiB;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEtDI,QAAAA,GAAG,CAACM,GAAD,EAAsC;AAAE,iBAAO,KAAKJ,QAAL,CAAcF,GAAd,CAAkBM,GAAlB,CAAP;AAAgC;;AAE3EpS,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK8R,IAAT,IAAiB,KAAKL,SAAtB,EACA;AACIK,YAAAA,IAAI,CAAC/R,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBqB,O;;2BA+Bb/B,S,GAAN,MAAMA,SAAN,CAAgB;AAGnBwB,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBqS,QAEiB;AAAA,eADjBN,SACiB;AACrB,eAAKM,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKP,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmBhS,MAAnB,EAA2B;AACvB,gBAAIiS,EAAW,SAAf;;AACAA,YAAAA,EAAE,GAAG,IAAInV,OAAJ,CAAYkV,OAAZ,CAAL;;AACA,iBAAKD,SAAL,CAAezQ,IAAf,CAAoB2Q,EAApB;;AACA,iBAAKI,QAAL,CAAcE,GAAd,CAAkBN,EAAE,CAAChR,EAArB,EAAyBgR,EAAzB;AACH;AACJ;;AAEDO,QAAAA,UAAU,GAAyB;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC5DH,QAAAA,WAAW,GAAc;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEnDI,QAAAA,GAAG,CAACM,GAAD,EAAmC;AAAE,iBAAO,KAAKJ,QAAL,CAAcF,GAAd,CAAkBM,GAAlB,CAAP;AAAgC;;AAExEpS,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK8R,IAAT,IAAiB,KAAKL,SAAtB,EACA;AACIK,YAAAA,IAAI,CAAC/R,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBkB,O;;wBAiCV9B,M,GAAN,MAAMA,MAAN,CAAa;AAEA,YAAZxB,YAAY,GAAkB;AAAE,iBAAO,KAAK0V,aAAZ;AAA2B;;AAE7C,YAAdzV,cAAc,GAAoB;AAAE,iBAAO,KAAK0V,eAAZ;AAA6B;;AAE7D,YAAJzV,IAAI,GAAU;AAAE,iBAAO,KAAK0V,KAAZ;AAAmB;;AAE5B,YAAPzV,OAAO,GAAa;AAAE,iBAAO,KAAK0V,QAAZ;AAAsB;;AAE5B,YAAhBzV,gBAAgB,GAAsB;AAAE,iBAAO,KAAK0V,iBAAZ;AAA+B;;AAE1D,YAAbzV,aAAa,GAAmB;AAAE,iBAAO,KAAK0V,cAAZ;AAA4B;;AAEnD,YAAXzV,WAAW,GAAiB;AAAE,iBAAO,KAAK0V,YAAZ;AAA0B;;AAE7C,YAAXzV,WAAW,GAAiB;AAAE,iBAAO,KAAK0V,YAAZ;AAA0B;;AAE5C,YAAZzV,YAAY,GAAkB;AAAE,iBAAO,KAAK0V,aAAZ;AAA2B;;AAEhD,YAAXzV,WAAW,GAAiB;AAAE,iBAAO,KAAK0V,YAAZ;AAA0B;;AAE9C,YAAVzV,UAAU,GAAgB;AAAE,iBAAO,KAAK0V,WAAZ;AAAyB;;AAE3C,YAAVzV,UAAU,GAAgB;AAAE,iBAAO,KAAK0V,WAAZ;AAAyB;;AAExC,YAAbzV,aAAa,GAAmB;AAAE,iBAAO,KAAK0V,cAAZ;AAA4B;;AAErD,YAATzV,SAAS,GAAe;AAAE,iBAAO,KAAK0V,UAAZ;AAAwB;;AAExC,YAAVzV,UAAU,GAAgB;AAAE,iBAAO,KAAK0V,WAAZ;AAAyB;;AAEtC,YAAfzV,eAAe,GAAqB;AAAE,iBAAO,KAAK0V,gBAAZ;AAA8B;;AAE3D,YAATzV,SAAS,GAAe;AAAE,iBAAO,KAAK0V,UAAZ;AAAwB;;AAExC,YAAVzV,UAAU,GAAgB;AAAE,iBAAO,KAAK0V,WAAZ;AAAyB;;AAE3C,YAAVzV,UAAU,GAAgB;AAAE,iBAAO,KAAK0V,WAAZ;AAAyB;;AAE5C,YAATzV,SAAS,GAAe;AAAE,iBAAO,KAAK0V,UAAZ;AAAwB;;AAEpC,YAAdzV,cAAc,GAAoB;AAAE,iBAAO,KAAK0V,eAAZ;AAA6B;;AAEvD,YAAVzV,UAAU,GAAgB;AAAE,iBAAO,KAAK0V,WAAZ;AAAyB;;AAEzC,YAAZzV,YAAY,GAAkB;AAAE,iBAAO,KAAK0V,aAAZ;AAA2B;;AAElD,YAATzV,SAAS,GAAe;AAAE,iBAAO,KAAK0V,UAAZ;AAAwB;;AAEtDlU,QAAAA,WAAW,CAACmU,MAAD,EAAqB;AAAA,eAjDxBxB,aAiDwB;AAAA,eA/CxBC,eA+CwB;AAAA,eA7CxBC,KA6CwB;AAAA,eA3CxBC,QA2CwB;AAAA,eAzCxBC,iBAyCwB;AAAA,eAvCxBC,cAuCwB;AAAA,eArCxBC,YAqCwB;AAAA,eAnCxBC,YAmCwB;AAAA,eAjCxBC,aAiCwB;AAAA,eA/BxBC,YA+BwB;AAAA,eA7BxBC,WA6BwB;AAAA,eA3BxBC,WA2BwB;AAAA,eAzBxBC,cAyBwB;AAAA,eAvBxBC,UAuBwB;AAAA,eArBxBC,WAqBwB;AAAA,eAnBxBC,gBAmBwB;AAAA,eAjBxBC,UAiBwB;AAAA,eAfxBC,WAewB;AAAA,eAbxBC,WAawB;AAAA,eAXxBC,UAWwB;AAAA,eATxBC,eASwB;AAAA,eAPxBC,WAOwB;AAAA,eALxBC,aAKwB;AAAA,eAHxBC,UAGwB;AAC5B,eAAKvB,aAAL,GAAqB,IAAI1V,YAAJ,CAAiBkX,MAAM,CAAC,cAAD,CAAvB,CAArB;AACA,eAAKvB,eAAL,GAAuB,IAAI1V,cAAJ,CAAmBiX,MAAM,CAAC,gBAAD,CAAzB,CAAvB;AACA,eAAKtB,KAAL,GAAa,IAAI1V,IAAJ,CAASgX,MAAM,CAAC,MAAD,CAAf,CAAb;AACA,eAAKrB,QAAL,GAAgB,IAAI1V,OAAJ,CAAY+W,MAAM,CAAC,SAAD,CAAlB,CAAhB;AACA,eAAKpB,iBAAL,GAAyB,IAAI1V,gBAAJ,CAAqB8W,MAAM,CAAC,kBAAD,CAA3B,CAAzB;AACA,eAAKnB,cAAL,GAAsB,IAAI1V,aAAJ,CAAkB6W,MAAM,CAAC,eAAD,CAAxB,CAAtB;AACA,eAAKlB,YAAL,GAAoB,IAAI1V,WAAJ,CAAgB4W,MAAM,CAAC,aAAD,CAAtB,CAApB;AACA,eAAKjB,YAAL,GAAoB,IAAI1V,WAAJ,CAAgB2W,MAAM,CAAC,aAAD,CAAtB,CAApB;AACA,eAAKhB,aAAL,GAAqB,IAAI1V,YAAJ,CAAiB0W,MAAM,CAAC,cAAD,CAAvB,CAArB;AACA,eAAKf,YAAL,GAAoB,IAAI1V,WAAJ,CAAgByW,MAAM,CAAC,aAAD,CAAtB,CAApB;AACA,eAAKd,WAAL,GAAmB,IAAI1V,UAAJ,CAAewW,MAAM,CAAC,YAAD,CAArB,CAAnB;AACA,eAAKb,WAAL,GAAmB,IAAI1V,UAAJ,CAAeuW,MAAM,CAAC,YAAD,CAArB,CAAnB;AACA,eAAKZ,cAAL,GAAsB,IAAI1V,aAAJ,CAAkBsW,MAAM,CAAC,eAAD,CAAxB,CAAtB;AACA,eAAKX,UAAL,GAAkB,IAAI1V,SAAJ,CAAcqW,MAAM,CAAC,WAAD,CAApB,CAAlB;AACA,eAAKV,WAAL,GAAmB,IAAI1V,UAAJ,CAAeoW,MAAM,CAAC,YAAD,CAArB,CAAnB;AACA,eAAKT,gBAAL,GAAwB,IAAI1V,eAAJ,CAAoBmW,MAAM,CAAC,iBAAD,CAA1B,CAAxB;AACA,eAAKR,UAAL,GAAkB,IAAI1V,SAAJ,CAAckW,MAAM,CAAC,WAAD,CAApB,CAAlB;AACA,eAAKP,WAAL,GAAmB,IAAI1V,UAAJ,CAAeiW,MAAM,CAAC,YAAD,CAArB,CAAnB;AACA,eAAKN,WAAL,GAAmB,IAAI1V,UAAJ,CAAegW,MAAM,CAAC,YAAD,CAArB,CAAnB;AACA,eAAKL,UAAL,GAAkB,IAAI1V,SAAJ,CAAc+V,MAAM,CAAC,WAAD,CAApB,CAAlB;AACA,eAAKJ,eAAL,GAAuB,IAAI1V,cAAJ,CAAmB8V,MAAM,CAAC,gBAAD,CAAzB,CAAvB;AACA,eAAKH,WAAL,GAAmB,IAAI1V,UAAJ,CAAe6V,MAAM,CAAC,YAAD,CAArB,CAAnB;AACA,eAAKF,aAAL,GAAqB,IAAI1V,YAAJ,CAAiB4V,MAAM,CAAC,cAAD,CAAvB,CAArB;AACA,eAAKD,UAAL,GAAkB,IAAI1V,SAAJ,CAAc2V,MAAM,CAAC,WAAD,CAApB,CAAlB;;AAEA,eAAKxB,aAAL,CAAmBrS,OAAnB,CAA2B,IAA3B;;AACA,eAAKsS,eAAL,CAAqBtS,OAArB,CAA6B,IAA7B;;AACA,eAAKuS,KAAL,CAAWvS,OAAX,CAAmB,IAAnB;;AACA,eAAKwS,QAAL,CAAcxS,OAAd,CAAsB,IAAtB;;AACA,eAAKyS,iBAAL,CAAuBzS,OAAvB,CAA+B,IAA/B;;AACA,eAAK0S,cAAL,CAAoB1S,OAApB,CAA4B,IAA5B;;AACA,eAAK2S,YAAL,CAAkB3S,OAAlB,CAA0B,IAA1B;;AACA,eAAK4S,YAAL,CAAkB5S,OAAlB,CAA0B,IAA1B;;AACA,eAAK6S,aAAL,CAAmB7S,OAAnB,CAA2B,IAA3B;;AACA,eAAK8S,YAAL,CAAkB9S,OAAlB,CAA0B,IAA1B;;AACA,eAAK+S,WAAL,CAAiB/S,OAAjB,CAAyB,IAAzB;;AACA,eAAKgT,WAAL,CAAiBhT,OAAjB,CAAyB,IAAzB;;AACA,eAAKiT,cAAL,CAAoBjT,OAApB,CAA4B,IAA5B;;AACA,eAAKkT,UAAL,CAAgBlT,OAAhB,CAAwB,IAAxB;;AACA,eAAKmT,WAAL,CAAiBnT,OAAjB,CAAyB,IAAzB;;AACA,eAAKoT,gBAAL,CAAsBpT,OAAtB,CAA8B,IAA9B;;AACA,eAAKqT,UAAL,CAAgBrT,OAAhB,CAAwB,IAAxB;;AACA,eAAKsT,WAAL,CAAiBtT,OAAjB,CAAyB,IAAzB;;AACA,eAAKuT,WAAL,CAAiBvT,OAAjB,CAAyB,IAAzB;;AACA,eAAKwT,UAAL,CAAgBxT,OAAhB,CAAwB,IAAxB;;AACA,eAAKyT,eAAL,CAAqBzT,OAArB,CAA6B,IAA7B;;AACA,eAAK0T,WAAL,CAAiB1T,OAAjB,CAAyB,IAAzB;;AACA,eAAK2T,aAAL,CAAmB3T,OAAnB,CAA2B,IAA3B;;AACA,eAAK4T,UAAL,CAAgB5T,OAAhB,CAAwB,IAAxB;AACH;;AApGe,O", "sourcesContent": ["\n//------------------------------------------------------------------------------\n// <auto-generated>\n//     This code was generated by a tool.\n//     Changes to this file may cause incorrect behavior and will be lost if\n//     the code is regenerated.\n// </auto-generated>\n//------------------------------------------------------------------------------\n\n\n \n/**\n * 特效绑定点\n */\nexport enum BindSocket {\n    /**\n     * 自己\n     */\n    Self = 0,\n    /**\n     * 屏幕\n     */\n    Scene = 1,\n}\n\n \n \n/**\n * buff类型\n */\nexport enum BuffType {\n    /**\n     * 正面\n     */\n    Positive = 0,\n    /**\n     * 中性\n     */\n    Neutral = 1,\n    /**\n     * 负面\n     */\n    Negative = 2,\n}\n\n \n \n/**\n * 子弹来源\n */\nexport enum BulletSourceType {\n    /**\n     * 自机\n     */\n    MAINPLANE = 0,\n    /**\n     * 僚机\n     */\n    WINGPLANE = 1,\n    /**\n     * 敌机\n     */\n    ENEMYPLANE = 2,\n}\n\n \n \n/**\n * 子弹类型\n */\nexport enum BulletType {\n    /**\n     * 常规\n     */\n    NORMAL = 0,\n    /**\n     * 核弹\n     */\n    NUCLEAR = 1,\n}\n\n \n \n/**\n * 伤害类型\n */\nexport enum DamageType {\n    /**\n     * 所有\n     */\n    ALL = 0,\n    /**\n     * 爆炸\n     */\n    EXPLOSIVE = 1,\n    /**\n     * 普通\n     */\n    NORMAL = 2,\n    /**\n     * 能量\n     */\n    ENERGETIC = 3,\n    /**\n     * 物理\n     */\n    PHYSICAL = 4,\n}\n\n \n \n/**\n * 效果类型\n */\nexport enum EffectType {\n    /**\n     * 最大生命值%\n     */\n    AttrMaxHPPer = 1,\n    /**\n     * 最大生命值+\n     */\n    AttrMaxHPAdd = 2,\n    /**\n     * 生命恢复速度%\n     */\n    AttrHPRecoveryPer = 3,\n    /**\n     * 生命恢复速度+\n     */\n    AttrHPRecoveryAdd = 4,\n    /**\n     * 基于最大生命值的恢复速度%\n     */\n    AttrHPRecoveryMaxHPPerAdd = 5,\n    /**\n     * 回复最大生命值%\n     */\n    HealMaxHPPer = 6,\n    /**\n     * 回复已损失生命%\n     */\n    HealLoseHPPer = 7,\n    /**\n     * 回复指定生命值+\n     */\n    HealHP = 8,\n    /**\n     * 攻击力%\n     */\n    AttrAttackPer = 9,\n    /**\n     * 攻击力+\n     */\n    AttrAttackAdd = 10,\n    /**\n     * 对Boss伤害%\n     */\n    AttrAttackBossPer = 11,\n    /**\n     * 对普通怪物伤害%\n     */\n    AttrAttackNormalPer = 12,\n    /**\n     * 幸运值%\n     */\n    AttrFortunatePer = 13,\n    /**\n     * 幸运值+\n     */\n    AttrFortunateAdd = 14,\n    /**\n     * 闪避率+\n     */\n    AttrMissAdd = 15,\n    /**\n     * 子弹伤害抗性%\n     */\n    AttrBulletHurtResistancePer = 16,\n    /**\n     * 子弹伤害抗性+\n     */\n    AttrBulletHurtResistanceAdd = 17,\n    /**\n     * 撞击伤害减免%\n     */\n    AttrCollisionHurtDerateAdd = 18,\n    /**\n     * 撞击伤害抗性%\n     */\n    AttrCollisionHurtResistancePer = 19,\n    /**\n     * 撞击伤害抗性+\n     */\n    AttrCollisionHurtResistanceAdd = 20,\n    /**\n     * 子弹伤害减免%\n     */\n    AttrBulletHurtDerateAdd = 21,\n    /**\n     * 结算得分%\n     */\n    AttrFinalScoreAdd = 22,\n    /**\n     * 击杀得分%\n     */\n    AttrKillScoreAdd = 23,\n    /**\n     * 能量回复%\n     */\n    AttrEnergyRecoveryPerAdd = 24,\n    /**\n     * 能量回复+\n     */\n    AttrEnergyRecoveryAdd = 25,\n    /**\n     * 拾取范围%\n     */\n    AttrPickRadiusPer = 26,\n    /**\n     * 拾取范围+\n     */\n    AttrPickRadiusAdd = 27,\n    /**\n     * 添加Buff\n     */\n    ApplyBuff = 28,\n    /**\n     * 免疫子弹伤害\n     */\n    ImmuneBulletHurt = 29,\n    /**\n     * 免疫撞击伤害\n     */\n    ImmuneCollisionHurt = 30,\n    /**\n     * 无视子弹\n     */\n    IgnoreBullet = 31,\n    /**\n     * 无视撞击\n     */\n    IgnoreCollision = 32,\n    /**\n     * 免疫核弹伤害\n     */\n    ImmuneBombHurt = 33,\n    /**\n     * 免疫主动技能\n     */\n    ImmuneActiveSkillHurt = 34,\n    /**\n     * 无敌\n     */\n    Invincible = 35,\n    /**\n     * 开局核弹携带数+\n     */\n    AttrBombMax = 36,\n    /**\n     * 子弹攻击+\n     */\n    BulletAttackAdd = 37,\n    /**\n     * 子弹攻击%\n     */\n    BulletAttackPer = 38,\n    /**\n     * 基于最大生命值系数的伤害%\n     */\n    HurtMaxHPPer = 39,\n    /**\n     * 基于当前生命值系数的伤害%\n     */\n    HurtCurHPPer = 40,\n    /**\n     * 核弹伤害+\n     */\n    AttrBombHurtAdd = 41,\n    /**\n     * 核弹伤害%\n     */\n    AttrBombHurtPer = 42,\n    /**\n     * 击杀\n     */\n    Kill = 43,\n    /**\n     * 伤害\n     */\n    Hurt = 44,\n}\n\n \n \n/**\n * 装备部位\n */\nexport enum EquipClass {\n    /**\n     * 无效值\n     */\n    NONE = 0,\n    /**\n     * 火力核心\n     */\n    WEAPON = 1,\n    /**\n     * 副武器\n     */\n    SUB_WEAPON = 2,\n    /**\n     * 装甲核心\n     */\n    ARMOR = 3,\n    /**\n     * 科技核心\n     */\n    TECHNIC = 4,\n}\n\n \n \n/**\n * GM命令页签\n */\nexport enum GMTabID {\n    /**\n     * 通用\n     */\n    COMMON = 0,\n    /**\n     * 战斗\n     */\n    BATTLE = 1,\n}\n\n \n \n/**\n * 道具的使用效果\n */\nexport enum ItemEffectType {\n    /**\n     * 无效果\n     */\n    NONE = 0,\n    /**\n     * 使用后消耗道具，并掉落相应的ID的随机道具，参数1为掉落ID\n     */\n    DROP = 1,\n    /**\n     * 使用后消耗道具，并掉落相应数量的金币，参数1为每个1道具对应多少个金币\n     */\n    GEN_GOLD = 2,\n    /**\n     * 使用后消耗道具，并掉落相应数量的钻石，参数1为每个1道具对应多少个钻石\n     */\n    GEN_DIAMOND = 3,\n    /**\n     * 使用后消耗道具，并掉落相应数量的经验，参数1为每个1道具对应多少经验值\n     */\n    GEN_XP = 4,\n    /**\n     * 使用后消耗道具，并掉落相应数量的体力，参数1为每个1道具对应多少体力值\n     */\n    GEN_ENERGY = 5,\n    /**\n     * 使用后消耗道具，并掉落相应数量的另一个道具，参数1为新道具ID，参数2为每个1道具对应多少个新道具\n     */\n    GEN_ITEM = 6,\n}\n\n \n \n/**\n * 道具的使用类型\n */\nexport enum ItemUseType {\n    /**\n     * 不可直接从背包来使用的道具\n     */\n    NONE = 0,\n    /**\n     * 先放入背包内，然后由玩家从背包手动选择后使用\n     */\n    MANUAL = 1,\n    /**\n     * 当进入背包时立刻触发使用，并产生效果，一般不直接占用背包格子\n     */\n    AUTO = 2,\n}\n\n \n \n/**\n * 模式类型\n */\nexport enum ModeType {\n    /**\n     * 无尽\n     */\n    ENDLESS = 0,\n    /**\n     * 剧情\n     */\n    STORY = 1,\n    /**\n     * 远征\n     */\n    EXPEDITION = 2,\n    /**\n     * 无尽PK\n     */\n    ENDLESSPK = 3,\n    /**\n     * 好友PK\n     */\n    FRIENDPK = 4,\n}\n\n \n \n/**\n * 货币类型\n */\nexport enum MoneyType {\n    /**\n     * 无效值\n     */\n    NONE = 0,\n    /**\n     * 金币\n     */\n    GOLD = 1,\n    /**\n     * 钻石\n     */\n    DIAMOND = 2,\n    /**\n     * 体力\n     */\n    POWER = 3,\n    /**\n     * 道具\n     */\n    ITEM = 4,\n}\n\n \n \n/**\n * 模式类型\n */\nexport enum PlayCycle {\n    /**\n     * 每日\n     */\n    DAY = 0,\n    /**\n     * 每周\n     */\n    WEEK = 1,\n}\n\n \n \n/**\n * 装备属性名称\n */\nexport enum PropName {\n    /**\n     * 无效值\n     */\n    NONE = 0,\n    /**\n     * 攻击力\n     */\n    HURT = 1,\n    /**\n     * 生命值\n     */\n    HP = 2,\n}\n\n \n \n/**\n * 普通(Common)、精良(Uncommon)、稀有(Rare)、史诗(Epic)、传说(Legendary)、神话(Mythic)\n */\nexport enum QualityType {\n    /**\n     * 无效值\n     */\n    NONE = 0,\n    /**\n     * 普通\n     */\n    COMMON = 1,\n    /**\n     * 精良\n     */\n    UNCOMMON = 2,\n    /**\n     * 稀有\n     */\n    RACE = 3,\n    /**\n     * 史诗\n     */\n    EPIC = 4,\n    /**\n     * 传说\n     */\n    LEGENDARY = 5,\n    /**\n     * 神话\n     */\n    MYTHIC = 6,\n}\n\n \n \nexport enum ResCondType {\n    /**\n     * 可以不填，默认无条件限制\n     */\n    NONE = 0,\n    /**\n     * 等级达到指定值\n     */\n    LEVEL = 1,\n    /**\n     * 关卡\n     */\n    STAGE = 2,\n    /**\n     * 星级\n     */\n    STAR = 3,\n    /**\n     * 战力\n     */\n    FORCE = 4,\n    /**\n     * 指定 ID 的活动在开放时间段内\n     */\n    ACTIVITY = 5,\n}\n\n \n \nexport enum ResGoalType {\n    /**\n     * 通过某模型大类达到指定次数/模式ID/次数\n     */\n    MODE_PASS_TIMES = 1,\n    /**\n     * 通过某指定关卡达到指定次数/关卡ID/次数\n     */\n    STAGE_PASS_TIMES = 2,\n    /**\n     * 消灭指定类型的怪物数量达到目标/怪物类型/数量\n     */\n    KILL_MONSTER_CLASS_COUNT = 3,\n    /**\n     * 商城指定抽奖操作达到指定次数/抽奖ID/次数\n     */\n    LOTTERY_TIMES = 4,\n    /**\n     * 领取挂机奖励达到指定次数/次数\n     */\n    AFK_TIMES = 5,\n    /**\n     * 充值达到指定金额/金额\n     */\n    CHARGE_AMOUNT = 6,\n    /**\n     * 购买指定商品（包括体力等）达到指定数量/商品ID/数量\n     */\n    BUY_ITEM_COUNT = 7,\n    /**\n     * 看广告达到指定次数/次数\n     */\n    WATCH_AD_TIMES = 8,\n    /**\n     * 累计登录达到指定天数/天数\n     */\n    LOGIN_DAYS = 9,\n    /**\n     * 玩家达到指定等级\n     */\n    ROLE_LEVEL = 10,\n    /**\n     * 消耗金币达到指定数额\n     */\n    CONSUME_GOLD = 11,\n    /**\n     * 消耗钻石达到指定数额\n     */\n    CONSUME_DIAMOND = 12,\n    /**\n     * 消耗体力达到指定数额\n     */\n    CONSUME_ENERGY = 13,\n    /**\n     * 消耗道具达到指定数额\n     */\n    CONSUME_ITEM = 14,\n    /**\n     * 拥有指定品质的装备达到指定数量\n     */\n    EQUIP_QUALITY = 15,\n    /**\n     * 拥有指定等级的装备达到指定数量\n     */\n    EQUIP_LEVEL = 16,\n    /**\n     * 执行装备合成达到指定次数\n     */\n    EQUIP_COMB_TIMES = 17,\n    /**\n     * 执行装备升级达到指定次数\n     */\n    EQUIP_UPGRADE_TIMES = 18,\n    /**\n     * 公会捐献达到指定次数\n     */\n    GUILD_DONATE_TIMES = 19,\n    /**\n     * 拥有战机（已解锁）达到指定数量\n     */\n    FIGHTER_UNLOCK_COUNT = 20,\n    /**\n     * 所有已解锁战机星级累计到指定数量\n     */\n    FIGHTER_STAR_TOTAL = 21,\n    /**\n     * 角色战力达到指定数值\n     */\n    ROLE_FORCE = 22,\n    /**\n     * 消耗指定金额的货币获得奖励\n     */\n    SPECIFY_BUG_CONSUME = 23,\n    /**\n     * 使用特定道具达到指定次数\n     */\n    USE_ITEM_TIMES = 24,\n    /**\n     * 执行签到操作，配置时参数1配置为签到的第几天(1-7，以接受任务的时间算起，当天第1天)\n     */\n    DAILY_SIGN_IN = 25,\n    /**\n     * 参与某模型大类达到指定次数/模式ID/次数\n     */\n    MODE_ENTER_TIMES = 26,\n    /**\n     * 参与某指定关卡达到指定次数/关卡ID/次数\n     */\n    STAGE_ENTER_TIMES = 27,\n    /**\n     * 任务达成后的累计轨道值达到指定数值/轨道ID/数值\n     */\n    ORBIT_VALUE = 28,\n}\n\n \n \nexport enum ResLootType {\n    /**\n     * 单个\n     */\n    SINGLE = 1,\n    /**\n     * 多个\n     */\n    MULTI = 2,\n}\n\n \n \nexport enum ResPeriodType {\n    /**\n     * 单次完成，不重置， 默认，可不填\n     */\n    SINGLE = 0,\n    /**\n     * 每日 0时重置\n     */\n    DAILY = 1,\n    /**\n     * 每周 周一0时重置\n     */\n    WEEKLY = 2,\n    /**\n     * 每月 1日0时重置\n     */\n    MONTHLY = 3,\n}\n\n \n \nexport enum ResTaskClass {\n    /**\n     * 默认，通过 ID 来识别\n     */\n    NONE = 0,\n    /**\n     * 日常任务\n     */\n    DAILY_TASK = 1,\n    /**\n     * 周常任务\n     */\n    WEEKLY_TASK = 2,\n    /**\n     * 月常任务\n     */\n    MONTHLY_TASK = 3,\n    /**\n     * 挑战任务\n     */\n    CHALLENGE = 4,\n    /**\n     * 活动\n     */\n    ACTIVITY = 5,\n    /**\n     * 任务轨道\n     */\n    TASK_ORBIT = 8,\n    /**\n     * 成就\n     */\n    ACHIEVEMENT = 9,\n}\n\n \n \n/**\n * 技能/buff条件\n */\nexport enum SkillConditionType {\n    /**\n     * 无条件\n     */\n    NONE = 0,\n}\n\n \n \n/**\n * 目标类型\n */\nexport enum TargetType {\n    /**\n     * 自己\n     */\n    Self = 0,\n    /**\n     * 自机\n     */\n    Main = 1,\n    /**\n     * 玩家方\n     */\n    MainFriendly = 2,\n    /**\n     * 全部敌方\n     */\n    Enemy = 3,\n    /**\n     * Boss敌方\n     */\n    BossEnemy = 4,\n    /**\n     * 普通敌方\n     */\n    NormalEnemy = 5,\n}\n\n \n\n\n\n\n\nexport class ApplyBuff {\n\n    constructor(_json_: any) {\n        if (_json_.target === undefined) { throw new Error() }\n        this.target = _json_.target\n        if (_json_.buffID === undefined) { throw new Error() }\n        this.buffID = _json_.buffID\n    }\n\n    readonly target: TargetType\n    readonly buffID: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n\n\n\nexport namespace builtin {\nexport class vector2 {\n\n    constructor(_json_: any) {\n        if (_json_.x === undefined) { throw new Error() }\n        this.x = _json_.x\n        if (_json_.y === undefined) { throw new Error() }\n        this.y = _json_.y\n    }\n\n    readonly x: number\n    readonly y: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n}\n\n\nexport namespace builtin {\nexport class vector3 {\n\n    constructor(_json_: any) {\n        if (_json_.x === undefined) { throw new Error() }\n        this.x = _json_.x\n        if (_json_.y === undefined) { throw new Error() }\n        this.y = _json_.y\n        if (_json_.z === undefined) { throw new Error() }\n        this.z = _json_.z\n    }\n\n    readonly x: number\n    readonly y: number\n    readonly z: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n    }\n}\n\n}\n\n\nexport namespace builtin {\nexport class vector4 {\n\n    constructor(_json_: any) {\n        if (_json_.x === undefined) { throw new Error() }\n        this.x = _json_.x\n        if (_json_.y === undefined) { throw new Error() }\n        this.y = _json_.y\n        if (_json_.z === undefined) { throw new Error() }\n        this.z = _json_.z\n        if (_json_.w === undefined) { throw new Error() }\n        this.w = _json_.w\n    }\n\n    readonly x: number\n    readonly y: number\n    readonly z: number\n    readonly w: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n    }\n}\n\n}\n\n\n\nexport class ConParam {\n\n    constructor(_json_: any) {\n        if (_json_.con === undefined) { throw new Error() }\n        this.con = _json_.con\n        if (_json_.param === undefined) { throw new Error() }\n        this.param = _json_.param\n    }\n\n    readonly con: number\n    readonly param: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n\n\n\n\n/**\n * 消耗的材料\n */\nexport class ConsumeItem {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.num === undefined) { throw new Error() }\n        this.num = _json_.num\n    }\n\n    readonly id: number\n    readonly num: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n\n\n\n\nexport class ConsumeMoney {\n\n    constructor(_json_: any) {\n        if (_json_.type === undefined) { throw new Error() }\n        this.type = _json_.type\n        if (_json_.num === undefined) { throw new Error() }\n        this.num = _json_.num\n    }\n\n    /**\n     * 货币类型\n     */\n    readonly type: MoneyType\n    /**\n     * 货币数量\n     */\n    readonly num: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n\n\n\n\nexport class EffectParam {\n\n    constructor(_json_: any) {\n        if (_json_.type === undefined) { throw new Error() }\n        this.type = _json_.type\n        if (_json_.target === undefined) { throw new Error() }\n        this.target = _json_.target\n        if (_json_.param === undefined) { throw new Error() }\n        { this.param = []; for(let _ele0 of _json_.param) { let _e0; _e0 = _ele0; this.param.push(_e0);}}\n    }\n\n    readonly type: EffectType\n    readonly target: TargetType\n    readonly param: number[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n    }\n}\n\n\n\n\n\n/**\n * 装备属性\n */\nexport class EquipProp {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.value === undefined) { throw new Error() }\n        this.value = _json_.value\n    }\n\n    readonly id: PropName\n    readonly value: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n\n\n\n\nexport class GM {\n\n    constructor(_json_: any) {\n        if (_json_.tabID === undefined) { throw new Error() }\n        this.tabID = _json_.tabID\n        if (_json_.tabName === undefined) { throw new Error() }\n        this.tabName = _json_.tabName\n        if (_json_.name === undefined) { throw new Error() }\n        this.name = _json_.name\n        if (_json_.cmd === undefined) { throw new Error() }\n        this.cmd = _json_.cmd\n        if (_json_.desc === undefined) { throw new Error() }\n        this.desc = _json_.desc\n    }\n\n    /**\n     * 页签ID\n     */\n    readonly tabID: GMTabID\n    /**\n     * 页签名称\n     */\n    readonly tabName: string\n    /**\n     * 按钮名称\n     */\n    readonly name: string\n    /**\n     * 命令\n     */\n    readonly cmd: string\n    /**\n     * 描述\n     */\n    readonly desc: string\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\n/**\n * 战机效果\n */\nexport class PlaneEffect {\n\n    constructor(_json_: any) {\n        if (_json_.effect_id === undefined) { throw new Error() }\n        this.effectId = _json_.effect_id\n    }\n\n    readonly effectId: number\n\n    resolve(tables:Tables) {\n        \n    }\n}\n\n\n\n\n\n/**\n * 升星材料\n */\nexport class PlaneMaterial {\n\n    constructor(_json_: any) {\n        if (_json_.material_id === undefined) { throw new Error() }\n        this.materialId = _json_.material_id\n        if (_json_.material_count === undefined) { throw new Error() }\n        this.materialCount = _json_.material_count\n    }\n\n    readonly materialId: number\n    readonly materialCount: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n\n\n\n\n/**\n * 战机属性\n */\nexport class PlaneProperty {\n\n    constructor(_json_: any) {\n        if (_json_.MaxHP === undefined) { throw new Error() }\n        this.MaxHP = _json_.MaxHP\n        if (_json_.HPRecovery === undefined) { throw new Error() }\n        this.HPRecovery = _json_.HPRecovery\n        if (_json_.Attack === undefined) { throw new Error() }\n        this.Attack = _json_.Attack\n        if (_json_.Fortunate === undefined) { throw new Error() }\n        this.Fortunate = _json_.Fortunate\n        if (_json_.Miss === undefined) { throw new Error() }\n        this.Miss = _json_.Miss\n        if (_json_.BulletHurtResistance === undefined) { throw new Error() }\n        this.BulletHurtResistance = _json_.BulletHurtResistance\n        if (_json_.CollisionHurtResistance === undefined) { throw new Error() }\n        this.CollisionHurtResistance = _json_.CollisionHurtResistance\n        if (_json_.PickRadius === undefined) { throw new Error() }\n        this.PickRadius = _json_.PickRadius\n        if (_json_.FinalScore === undefined) { throw new Error() }\n        this.FinalScore = _json_.FinalScore\n        if (_json_.BombMax === undefined) { throw new Error() }\n        this.BombMax = _json_.BombMax\n        if (_json_.MaxEnergy === undefined) { throw new Error() }\n        this.MaxEnergy = _json_.MaxEnergy\n        if (_json_.EnergyRecovery === undefined) { throw new Error() }\n        this.EnergyRecovery = _json_.EnergyRecovery\n    }\n\n    readonly MaxHP: number\n    readonly HPRecovery: number\n    readonly Attack: number\n    readonly Fortunate: number\n    readonly Miss: number\n    readonly BulletHurtResistance: number\n    readonly CollisionHurtResistance: number\n    readonly PickRadius: number\n    readonly FinalScore: number\n    readonly BombMax: number\n    readonly MaxEnergy: number\n    readonly EnergyRecovery: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\n/**\n * 战机属性\n */\nexport class PlanePropertyElem {\n\n    constructor(_json_: any) {\n        if (_json_.prop_type === undefined) { throw new Error() }\n        this.propType = _json_.prop_type\n        if (_json_.prop_param === undefined) { throw new Error() }\n        { this.propParam = []; for(let _ele0 of _json_.prop_param) { let _e0; _e0 = _ele0; this.propParam.push(_e0);}}\n    }\n\n    readonly propType: EffectType\n    readonly propParam: number[]\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n\n\n\n\n/**\n * 属性增幅\n */\nexport class PropInc {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.inc === undefined) { throw new Error() }\n        this.inc = _json_.inc\n    }\n\n    readonly id: PropName\n    /**\n     * 万分比\n     */\n    readonly inc: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n\n\n\n\n/**\n * 随机策略\n */\nexport class randStrategy {\n\n    constructor(_json_: any) {\n        if (_json_.ID === undefined) { throw new Error() }\n        this.ID = _json_.ID\n        if (_json_.Weight === undefined) { throw new Error() }\n        this.Weight = _json_.Weight\n    }\n\n    /**\n     * 随机策略ID\n     */\n    readonly ID: number\n    /**\n     * ID的权重\n     */\n    readonly Weight: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n\n\n\n\nexport class RatingParam {\n\n    constructor(_json_: any) {\n        if (_json_.rating === undefined) { throw new Error() }\n        this.rating = _json_.rating\n        if (_json_.param === undefined) { throw new Error() }\n        this.param = _json_.param\n    }\n\n    readonly rating: number\n    readonly param: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n\n\n\n\nexport class ResAchievement {\n\n    constructor(_json_: any) {\n        if (_json_.task_id === undefined) { throw new Error() }\n        this.taskId = _json_.task_id\n        if (_json_.group_id === undefined) { throw new Error() }\n        this.groupId = _json_.group_id\n        if (_json_.prev_id === undefined) { throw new Error() }\n        this.prevId = _json_.prev_id\n        if (_json_.period_type === undefined) { throw new Error() }\n        this.periodType = _json_.period_type\n        if (_json_.task_cond === undefined) { throw new Error() }\n        { this.taskCond = []; for(let _ele0 of _json_.task_cond) { let _e0; _e0 = new ResCondition(_ele0); this.taskCond.push(_e0);}}\n        if (_json_.task_goal === undefined) { throw new Error() }\n        this.taskGoal = new ResTaskGoal(_json_.task_goal)\n        if (_json_.accumulate === undefined) { throw new Error() }\n        this.accumulate = _json_.accumulate\n        if (_json_.reward_id === undefined) { throw new Error() }\n        this.rewardId = _json_.reward_id\n        if (_json_.link_to === undefined) { throw new Error() }\n        this.linkTo = _json_.link_to\n    }\n\n    /**\n     * 成就 ID\n     */\n    readonly taskId: number\n    /**\n     * 成就集 ID\n     */\n    readonly groupId: number\n    /**\n     * 前置ID\n     */\n    readonly prevId: number\n    /**\n     * 重置周期\n     */\n    readonly periodType: ResPeriodType\n    readonly taskCond: ResCondition[]\n    readonly taskGoal: ResTaskGoal\n    /**\n     * 是否累积\n     */\n    readonly accumulate: boolean\n    /**\n     * 奖励ID\n     */\n    readonly rewardId: number\n    /**\n     * 追踪\n     */\n    readonly linkTo: string\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        for (let _e of this.taskCond) { _e?.resolve(tables); }\n        this.taskGoal?.resolve(tables);\n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResActivity {\n\n    constructor(_json_: any) {\n        if (_json_.task_id === undefined) { throw new Error() }\n        this.taskId = _json_.task_id\n        if (_json_.group_id === undefined) { throw new Error() }\n        this.groupId = _json_.group_id\n        if (_json_.desc === undefined) { throw new Error() }\n        this.desc = _json_.desc\n        if (_json_.ui_res === undefined) { throw new Error() }\n        this.uiRes = _json_.ui_res\n        if (_json_.precv_id === undefined) { throw new Error() }\n        this.precvId = _json_.precv_id\n        if (_json_.period_type === undefined) { throw new Error() }\n        this.periodType = _json_.period_type\n        if (_json_.task_cond === undefined) { throw new Error() }\n        { this.taskCond = []; for(let _ele0 of _json_.task_cond) { let _e0; _e0 = new ResCondition(_ele0); this.taskCond.push(_e0);}}\n        if (_json_.task_goal === undefined) { throw new Error() }\n        this.taskGoal = new ResTaskGoal(_json_.task_goal)\n        if (_json_.accumlate === undefined) { throw new Error() }\n        this.accumlate = _json_.accumlate\n        if (_json_.reward_id === undefined) { throw new Error() }\n        this.rewardId = _json_.reward_id\n        if (_json_.open_date === undefined) { throw new Error() }\n        this.openDate = _json_.open_date\n        if (_json_.open_time === undefined) { throw new Error() }\n        this.openTime = _json_.open_time\n        if (_json_.close_date === undefined) { throw new Error() }\n        this.closeDate = _json_.close_date\n        if (_json_.close_time === undefined) { throw new Error() }\n        this.closeTime = _json_.close_time\n        if (_json_.link_to === undefined) { throw new Error() }\n        this.linkTo = _json_.link_to\n    }\n\n    /**\n     * 活动ID\n     */\n    readonly taskId: number\n    /**\n     * 活动集ID\n     */\n    readonly groupId: number\n    /**\n     * 说明\n     */\n    readonly desc: string\n    /**\n     * UI资源配置\n     */\n    readonly uiRes: string\n    /**\n     * 前置ID\n     */\n    readonly precvId: number\n    /**\n     * 重置周期\n     */\n    readonly periodType: ResPeriodType\n    /**\n     * 条件类型\n     */\n    readonly taskCond: ResCondition[]\n    readonly taskGoal: ResTaskGoal\n    /**\n     * 目标是否累积\n     */\n    readonly accumlate: boolean\n    /**\n     * 奖励ID\n     */\n    readonly rewardId: number\n    /**\n     * 开放日期\n     */\n    readonly openDate: string\n    /**\n     * 开放时间\n     */\n    readonly openTime: string\n    /**\n     * 结束日期\n     */\n    readonly closeDate: string\n    /**\n     * 结束时间\n     */\n    readonly closeTime: string\n    /**\n     * 活动追踪\n     */\n    readonly linkTo: string\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        for (let _e of this.taskCond) { _e?.resolve(tables); }\n        this.taskGoal?.resolve(tables);\n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResBuffer {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.buffType === undefined) { throw new Error() }\n        this.buffType = _json_.buffType\n        if (_json_.conditionID === undefined) { throw new Error() }\n        this.conditionID = _json_.conditionID\n        if (_json_.duration === undefined) { throw new Error() }\n        this.duration = _json_.duration\n        if (_json_.durationBonus === undefined) { throw new Error() }\n        this.durationBonus = _json_.durationBonus\n        if (_json_.maxStack === undefined) { throw new Error() }\n        this.maxStack = _json_.maxStack\n        if (_json_.refreshType === undefined) { throw new Error() }\n        this.refreshType = _json_.refreshType\n        if (_json_.cycle === undefined) { throw new Error() }\n        this.cycle = _json_.cycle\n        if (_json_.cycleTimes === undefined) { throw new Error() }\n        this.cycleTimes = _json_.cycleTimes\n        if (_json_.EffectPath === undefined) { throw new Error() }\n        this.EffectPath = _json_.EffectPath\n        if (_json_.EffectSocket === undefined) { throw new Error() }\n        this.EffectSocket = _json_.EffectSocket\n        if (_json_.EffectPos === undefined) { throw new Error() }\n        this.EffectPos = new builtin.vector2(_json_.EffectPos)\n        if (_json_.effects === undefined) { throw new Error() }\n        { this.effects = []; for(let _ele0 of _json_.effects) { let _e0; _e0 = new EffectParam(_ele0); this.effects.push(_e0);}}\n    }\n\n    /**\n     * ID\n     */\n    readonly id: number\n    /**\n     * 类别\n     */\n    readonly buffType: BuffType\n    /**\n     * Buff触发条件\n     */\n    readonly conditionID: number\n    /**\n     * 持续时间\n     */\n    readonly duration: number\n    /**\n     * 持续时间加成\n     */\n    readonly durationBonus: number\n    /**\n     * 最大叠加次数\n     */\n    readonly maxStack: number\n    /**\n     * 叠加刷新策略\n     */\n    readonly refreshType: boolean\n    /**\n     * 周期\n     */\n    readonly cycle: number\n    /**\n     * 周期计数\n     */\n    readonly cycleTimes: number\n    /**\n     * 特效Prefab\n     */\n    readonly EffectPath: string\n    /**\n     * 特效挂点\n     */\n    readonly EffectSocket: BindSocket\n    /**\n     * 特效偏移\n     */\n    readonly EffectPos: builtin.vector2\n    readonly effects: EffectParam[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResBullet {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.name === undefined) { throw new Error() }\n        this.name = _json_.name\n        if (_json_.source === undefined) { throw new Error() }\n        this.source = _json_.source\n        if (_json_.type === undefined) { throw new Error() }\n        this.type = _json_.type\n        if (_json_.damage_type === undefined) { throw new Error() }\n        this.damageType = _json_.damage_type\n        if (_json_.prefab === undefined) { throw new Error() }\n        this.prefab = _json_.prefab\n        if (_json_.attack_coefficient === undefined) { throw new Error() }\n        this.attackCoefficient = _json_.attack_coefficient\n        if (_json_.penetration_count === undefined) { throw new Error() }\n        this.penetrationCount = _json_.penetration_count\n        if (_json_.penetration_cooldown === undefined) { throw new Error() }\n        this.penetrationCooldown = _json_.penetration_cooldown\n        if (_json_.hit_bounce_count === undefined) { throw new Error() }\n        this.hitBounceCount = _json_.hit_bounce_count\n        if (_json_.boundary_bounce_count === undefined) { throw new Error() }\n        this.boundaryBounceCount = _json_.boundary_bounce_count\n    }\n\n    /**\n     * ID\n     */\n    readonly id: number\n    /**\n     * 名称\n     */\n    readonly name: string\n    /**\n     * 子弹来源\n     */\n    readonly source: BulletSourceType\n    /**\n     * 子弹类型\n     */\n    readonly type: BulletType\n    /**\n     * 子弹伤害类型\n     */\n    readonly damageType: DamageType\n    /**\n     * 子弹Prefab\n     */\n    readonly prefab: string\n    /**\n     * 攻击转换系数%\n     */\n    readonly attackCoefficient: number\n    /**\n     * 穿透次数\n     */\n    readonly penetrationCount: number\n    /**\n     * 穿透伤害冷却\n     */\n    readonly penetrationCooldown: number\n    /**\n     * 命中反弹次数\n     */\n    readonly hitBounceCount: number\n    /**\n     * 屏幕反弹次数\n     */\n    readonly boundaryBounceCount: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResChapter {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.levelCount === undefined) { throw new Error() }\n        this.levelCount = _json_.levelCount\n        if (_json_.levelGroupCount === undefined) { throw new Error() }\n        this.levelGroupCount = _json_.levelGroupCount\n        if (_json_.strategy === undefined) { throw new Error() }\n        this.strategy = _json_.strategy\n        if (_json_.damageBonus === undefined) { throw new Error() }\n        this.damageBonus = _json_.damageBonus\n        if (_json_.lifeBounus === undefined) { throw new Error() }\n        this.lifeBounus = _json_.lifeBounus\n        if (_json_.strategyList === undefined) { throw new Error() }\n        { this.strategyList = []; for(let _ele0 of _json_.strategyList) { let _e0; _e0 = new randStrategy(_ele0); this.strategyList.push(_e0);}}\n    }\n\n    /**\n     * 章节ID\n     */\n    readonly id: number\n    /**\n     * 章节关卡数量\n     */\n    readonly levelCount: number\n    /**\n     * 章节关卡组数量\n     */\n    readonly levelGroupCount: number\n    /**\n     * 1=随机<br/>2=随机不重复<br/>3=顺序重复\n     */\n    readonly strategy: number\n    /**\n     * 章节伤害加成\n     */\n    readonly damageBonus: number\n    /**\n     * 章节生命加成\n     */\n    readonly lifeBounus: number\n    readonly strategyList: randStrategy[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        for (let _e of this.strategyList) { _e?.resolve(tables); }\n    }\n}\n\n\n\n\n\nexport class ResCondition {\n\n    constructor(_json_: any) {\n        if (_json_.cond_type === undefined) { throw new Error() }\n        this.condType = _json_.cond_type\n        if (_json_.params === undefined) { throw new Error() }\n        { this.params = []; for(let _ele0 of _json_.params) { let _e0; _e0 = _ele0; this.params.push(_e0);}}\n    }\n\n    readonly condType: ResCondType\n    readonly params: number[]\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n\n\n\n\nexport class ResEffect {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.name === undefined) { throw new Error() }\n        this.name = _json_.name\n        if (_json_.description === undefined) { throw new Error() }\n        this.description = _json_.description\n        if (_json_.icon === undefined) { throw new Error() }\n        this.icon = _json_.icon\n        if (_json_.effect_type === undefined) { throw new Error() }\n        this.effectType = _json_.effect_type\n        if (_json_.effect_value === undefined) { throw new Error() }\n        this.effectValue = _json_.effect_value\n        if (_json_.effect_params === undefined) { throw new Error() }\n        this.effectParams = _json_.effect_params\n    }\n\n    /**\n     * 效果ID\n     */\n    readonly id: number\n    /**\n     * 效果名称\n     */\n    readonly name: string\n    /**\n     * 效果描述\n     */\n    readonly description: string\n    /**\n     * 效果图标\n     */\n    readonly icon: string\n    /**\n     * 效果类型\n     */\n    readonly effectType: number\n    /**\n     * 效果数值\n     */\n    readonly effectValue: number\n    /**\n     * 效果参数\n     */\n    readonly effectParams: string\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResEnemy {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.name === undefined) { throw new Error() }\n        this.name = _json_.name\n        if (_json_.comment === undefined) { throw new Error() }\n        this.comment = _json_.comment\n        if (_json_.prefab === undefined) { throw new Error() }\n        this.prefab = _json_.prefab\n        if (_json_.rank === undefined) { throw new Error() }\n        this.rank = _json_.rank\n        if (_json_.purpose === undefined) { throw new Error() }\n        this.purpose = _json_.purpose\n        if (_json_.base_hp === undefined) { throw new Error() }\n        this.baseHp = _json_.base_hp\n        if (_json_.base_atk === undefined) { throw new Error() }\n        this.baseAtk = _json_.base_atk\n        if (_json_.move_speed === undefined) { throw new Error() }\n        this.moveSpeed = _json_.move_speed\n        if (_json_.kill_score === undefined) { throw new Error() }\n        this.killScore = _json_.kill_score\n        if (_json_.show_hp_bar === undefined) { throw new Error() }\n        this.showHpBar = _json_.show_hp_bar\n        if (_json_.use_hit_count === undefined) { throw new Error() }\n        this.useHitCount = _json_.use_hit_count\n        if (_json_.hit_count_to_kill === undefined) { throw new Error() }\n        this.hitCountToKill = _json_.hit_count_to_kill\n        if (_json_.hit_count_interval === undefined) { throw new Error() }\n        this.hitCountInterval = _json_.hit_count_interval\n        if (_json_.target_priority === undefined) { throw new Error() }\n        this.targetPriority = _json_.target_priority\n        if (_json_.immune_bullet_damage === undefined) { throw new Error() }\n        this.immuneBulletDamage = _json_.immune_bullet_damage\n        if (_json_.immune_collide_damage === undefined) { throw new Error() }\n        this.immuneCollideDamage = _json_.immune_collide_damage\n        if (_json_.ignore_bullet === undefined) { throw new Error() }\n        this.ignoreBullet = _json_.ignore_bullet\n        if (_json_.ignore_collide === undefined) { throw new Error() }\n        this.ignoreCollide = _json_.ignore_collide\n        if (_json_.immune_nuke === undefined) { throw new Error() }\n        this.immuneNuke = _json_.immune_nuke\n        if (_json_.immune_active_skill === undefined) { throw new Error() }\n        this.immuneActiveSkill = _json_.immune_active_skill\n        if (_json_.invincible === undefined) { throw new Error() }\n        this.invincible = _json_.invincible\n        if (_json_.collide_level === undefined) { throw new Error() }\n        this.collideLevel = _json_.collide_level\n        if (_json_.collide_damage === undefined) { throw new Error() }\n        this.collideDamage = _json_.collide_damage\n        if (_json_.drop_radius === undefined) { throw new Error() }\n        this.dropRadius = _json_.drop_radius\n    }\n\n    /**\n     * 单位ID\n     */\n    readonly id: number\n    /**\n     * 单位名称\n     */\n    readonly name: string\n    /**\n     * 备注\n     */\n    readonly comment: string\n    /**\n     * 单位Prefab\n     */\n    readonly prefab: string\n    /**\n     * 单位级别\n     */\n    readonly rank: number\n    /**\n     * 单位用途\n     */\n    readonly purpose: string\n    /**\n     * 基础生命值\n     */\n    readonly baseHp: number\n    /**\n     * 基础攻击力\n     */\n    readonly baseAtk: number\n    /**\n     * 移动速度\n     */\n    readonly moveSpeed: number\n    /**\n     * 击杀得分\n     */\n    readonly killScore: number\n    /**\n     * 是否显示血条\n     */\n    readonly showHpBar: boolean\n    /**\n     * 是否开启受击次数\n     */\n    readonly useHitCount: boolean\n    /**\n     * 受击次数\n     */\n    readonly hitCountToKill: number\n    /**\n     * 受击间隔\n     */\n    readonly hitCountInterval: number\n    /**\n     * 选中级别\n     */\n    readonly targetPriority: number\n    /**\n     * 免疫子弹伤害\n     */\n    readonly immuneBulletDamage: boolean\n    /**\n     * 免疫撞击伤害\n     */\n    readonly immuneCollideDamage: boolean\n    /**\n     * 无视子弹\n     */\n    readonly ignoreBullet: boolean\n    /**\n     * 无视撞击\n     */\n    readonly ignoreCollide: boolean\n    /**\n     * 是否免疫核弹\n     */\n    readonly immuneNuke: boolean\n    /**\n     * 是否免疫主动技能\n     */\n    readonly immuneActiveSkill: boolean\n    /**\n     * 是否无敌\n     */\n    readonly invincible: boolean\n    /**\n     * 相撞等级\n     */\n    readonly collideLevel: number\n    /**\n     * 相撞伤害\n     */\n    readonly collideDamage: number\n    /**\n     * 掉落半径\n     */\n    readonly dropRadius: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResEquip {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.name === undefined) { throw new Error() }\n        this.name = _json_.name\n        if (_json_.icon === undefined) { throw new Error() }\n        this.icon = _json_.icon\n        if (_json_.quality === undefined) { throw new Error() }\n        this.quality = _json_.quality\n        if (_json_.quality_sub === undefined) { throw new Error() }\n        this.qualitySub = _json_.quality_sub\n        if (_json_.equip_class === undefined) { throw new Error() }\n        this.equipClass = _json_.equip_class\n        if (_json_.props === undefined) { throw new Error() }\n        { this.props = []; for(let _ele0 of _json_.props) { let _e0; _e0 = new EquipProp(_ele0); this.props.push(_e0);}}\n        if (_json_.consume_items === undefined) { throw new Error() }\n        { this.consumeItems = []; for(let _ele0 of _json_.consume_items) { let _e0; _e0 = new ConsumeItem(_ele0); this.consumeItems.push(_e0);}}\n    }\n\n    /**\n     * ID\n     */\n    readonly id: number\n    /**\n     * 名称\n     */\n    readonly name: string\n    /**\n     * 图标\n     */\n    readonly icon: string\n    /**\n     * 品质\n     */\n    readonly quality: QualityType\n    /**\n     * 品质子等级\n     */\n    readonly qualitySub: number\n    /**\n     * 装备部位\n     */\n    readonly equipClass: EquipClass\n    /**\n     * 属性\n     */\n    readonly props: EquipProp[]\n    /**\n     * 消耗材料\n     */\n    readonly consumeItems: ConsumeItem[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        for (let _e of this.props) { _e?.resolve(tables); }\n        for (let _e of this.consumeItems) { _e?.resolve(tables); }\n    }\n}\n\n\n\n\n\nexport class ResEquipUpgrade {\n\n    constructor(_json_: any) {\n        if (_json_.equip_class === undefined) { throw new Error() }\n        this.equipClass = _json_.equip_class\n        if (_json_.level_from === undefined) { throw new Error() }\n        this.levelFrom = _json_.level_from\n        if (_json_.level_to === undefined) { throw new Error() }\n        this.levelTo = _json_.level_to\n        if (_json_.prop_inc === undefined) { throw new Error() }\n        { this.propInc = []; for(let _ele0 of _json_.prop_inc) { let _e0; _e0 = new PropInc(_ele0); this.propInc.push(_e0);}}\n        if (_json_.consume_money === undefined) { throw new Error() }\n        this.consumeMoney = new ConsumeMoney(_json_.consume_money)\n        if (_json_.consume_items === undefined) { throw new Error() }\n        { this.consumeItems = []; for(let _ele0 of _json_.consume_items) { let _e0; _e0 = new ConsumeItem(_ele0); this.consumeItems.push(_e0);}}\n    }\n\n    /**\n     * 部位\n     */\n    readonly equipClass: EquipClass\n    /**\n     * 等级下限\n     */\n    readonly levelFrom: number\n    /**\n     * 等级上限\n     */\n    readonly levelTo: number\n    /**\n     * 属性增幅\n     */\n    readonly propInc: PropInc[]\n    /**\n     * 消耗货币\n     */\n    readonly consumeMoney: ConsumeMoney\n    /**\n     * 消耗材料\n     */\n    readonly consumeItems: ConsumeItem[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n        for (let _e of this.propInc) { _e?.resolve(tables); }\n        this.consumeMoney?.resolve(tables);\n        for (let _e of this.consumeItems) { _e?.resolve(tables); }\n    }\n}\n\n\n\n\n\nexport class ResGameMode {\n\n    constructor(_json_: any) {\n        if (_json_.ID === undefined) { throw new Error() }\n        this.ID = _json_.ID\n        if (_json_.modeType === undefined) { throw new Error() }\n        this.modeType = _json_.modeType\n        if (_json_.chapterID === undefined) { throw new Error() }\n        this.chapterID = _json_.chapterID\n        if (_json_.order === undefined) { throw new Error() }\n        this.order = _json_.order\n        if (_json_.resourceID === undefined) { throw new Error() }\n        this.resourceID = _json_.resourceID\n        if (_json_.description === undefined) { throw new Error() }\n        this.description = _json_.description\n        if (_json_.conList === undefined) { throw new Error() }\n        { this.conList = []; for(let _ele0 of _json_.conList) { let _e0; _e0 = new ConParam(_ele0); this.conList.push(_e0);}}\n        if (_json_.cycle === undefined) { throw new Error() }\n        this.cycle = _json_.cycle\n        if (_json_.times === undefined) { throw new Error() }\n        this.times = _json_.times\n        if (_json_.monType === undefined) { throw new Error() }\n        this.monType = _json_.monType\n        if (_json_.costParam1 === undefined) { throw new Error() }\n        this.costParam1 = _json_.costParam1\n        if (_json_.costParam2 === undefined) { throw new Error() }\n        this.costParam2 = _json_.costParam2\n        if (_json_.rebirthTimes === undefined) { throw new Error() }\n        this.rebirthTimes = _json_.rebirthTimes\n        if (_json_.rebirthCost === undefined) { throw new Error() }\n        this.rebirthCost = _json_.rebirthCost\n        if (_json_.power === undefined) { throw new Error() }\n        this.power = _json_.power\n        if (_json_.rogueID === undefined) { throw new Error() }\n        this.rogueID = _json_.rogueID\n        if (_json_.LevelLimit === undefined) { throw new Error() }\n        this.LevelLimit = _json_.LevelLimit\n        if (_json_.rogueFirst === undefined) { throw new Error() }\n        this.rogueFirst = _json_.rogueFirst\n        if (_json_.sweepLimit === undefined) { throw new Error() }\n        this.sweepLimit = _json_.sweepLimit\n        if (_json_.rewardID1 === undefined) { throw new Error() }\n        this.rewardID1 = _json_.rewardID1\n        if (_json_.rewardID2 === undefined) { throw new Error() }\n        this.rewardID2 = _json_.rewardID2\n        if (_json_.ratingList === undefined) { throw new Error() }\n        { this.ratingList = []; for(let _ele0 of _json_.ratingList) { let _e0; _e0 = new RatingParam(_ele0); this.ratingList.push(_e0);}}\n    }\n\n    /**\n     * ID\n     */\n    readonly ID: number\n    /**\n     * 模式类型\n     */\n    readonly modeType: ModeType\n    /**\n     * 章节ID\n     */\n    readonly chapterID: number\n    /**\n     * 排序\n     */\n    readonly order: number\n    /**\n     * 入口资源\n     */\n    readonly resourceID: number\n    /**\n     * 文本介绍\n     */\n    readonly description: string\n    readonly conList: ConParam[]\n    /**\n     * 进入周期\n     */\n    readonly cycle: PlayCycle\n    /**\n     * 进入次数\n     */\n    readonly times: number\n    /**\n     * 消耗类型\n     */\n    readonly monType: MoneyType\n    /**\n     * 消耗参数1\n     */\n    readonly costParam1: number\n    /**\n     * 消耗参数2\n     */\n    readonly costParam2: number\n    /**\n     * 复活次数\n     */\n    readonly rebirthTimes: number\n    /**\n     * 复活消耗\n     */\n    readonly rebirthCost: number\n    /**\n     * 战力评估\n     */\n    readonly power: number\n    /**\n     * 肉鸽组\n     */\n    readonly rogueID: number\n    /**\n     * 局内等级上限\n     */\n    readonly LevelLimit: number\n    /**\n     * 初始肉鸽选择\n     */\n    readonly rogueFirst: number\n    /**\n     * 扫荡次数\n     */\n    readonly sweepLimit: number\n    /**\n     * 奖励ID1\n     */\n    readonly rewardID1: number\n    /**\n     * 奖励ID2\n     */\n    readonly rewardID2: number\n    readonly ratingList: RatingParam[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResGlobalAttr {\n\n    constructor(_json_: any) {\n        if (_json_.GoldProducion === undefined) { throw new Error() }\n        this.GoldProducion = _json_.GoldProducion\n        if (_json_.MaxEnergy === undefined) { throw new Error() }\n        this.MaxEnergy = _json_.MaxEnergy\n        if (_json_.EnergyRecoverInterval === undefined) { throw new Error() }\n        this.EnergyRecoverInterval = _json_.EnergyRecoverInterval\n        if (_json_.EnergyRecoverValue === undefined) { throw new Error() }\n        this.EnergyRecoverValue = _json_.EnergyRecoverValue\n        if (_json_.ItemPickUpRadius === undefined) { throw new Error() }\n        this.ItemPickUpRadius = _json_.ItemPickUpRadius\n        if (_json_.PostHitProtection === undefined) { throw new Error() }\n        this.PostHitProtection = _json_.PostHitProtection\n        if (_json_.CameraTranslationMaxMoveSpeed === undefined) { throw new Error() }\n        this.CameraTranslationMaxMoveSpeed = _json_.CameraTranslationMaxMoveSpeed\n        if (_json_.CameraTranslationMoveDelay === undefined) { throw new Error() }\n        this.CameraTranslationMoveDelay = _json_.CameraTranslationMoveDelay\n    }\n\n    /**\n     * 每回合发放的金币\n     */\n    readonly GoldProducion: number\n    /**\n     * 体力上限值\n     */\n    readonly MaxEnergy: number\n    /**\n     * 体力恢复的间隔时间\n     */\n    readonly EnergyRecoverInterval: number\n    /**\n     * 体力恢复的值\n     */\n    readonly EnergyRecoverValue: number\n    /**\n     * 局内道具拾取距离\n     */\n    readonly ItemPickUpRadius: number\n    /**\n     * 受击保护\n     */\n    readonly PostHitProtection: number\n    /**\n     * 镜头平移最大跟随速度\n     */\n    readonly CameraTranslationMaxMoveSpeed: number\n    /**\n     * 镜头平移启动延迟\n     */\n    readonly CameraTranslationMoveDelay: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResItem {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.name === undefined) { throw new Error() }\n        this.name = _json_.name\n        if (_json_.icon === undefined) { throw new Error() }\n        this.icon = _json_.icon\n        if (_json_.quality === undefined) { throw new Error() }\n        this.quality = _json_.quality\n        if (_json_.quality_sub === undefined) { throw new Error() }\n        this.qualitySub = _json_.quality_sub\n        if (_json_.use_type === undefined) { throw new Error() }\n        this.useType = _json_.use_type\n        if (_json_.effect_id === undefined) { throw new Error() }\n        this.effectId = _json_.effect_id\n        if (_json_.effect_param1 === undefined) { throw new Error() }\n        this.effectParam1 = _json_.effect_param1\n        if (_json_.effect_param2 === undefined) { throw new Error() }\n        this.effectParam2 = _json_.effect_param2\n        if (_json_.max_stack_num === undefined) { throw new Error() }\n        this.maxStackNum = _json_.max_stack_num\n    }\n\n    /**\n     * ID\n     */\n    readonly id: number\n    /**\n     * 名称\n     */\n    readonly name: string\n    /**\n     * 图标\n     */\n    readonly icon: string\n    /**\n     * 品质\n     */\n    readonly quality: QualityType\n    /**\n     * 品质子等级\n     */\n    readonly qualitySub: number\n    /**\n     * 使用类型\n     */\n    readonly useType: ItemUseType\n    /**\n     * 效果类型\n     */\n    readonly effectId: ItemEffectType\n    /**\n     * 效果参数1\n     */\n    readonly effectParam1: number\n    /**\n     * 效果参数2\n     */\n    readonly effectParam2: number\n    /**\n     * 最大叠放数量\n     */\n    readonly maxStackNum: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResLevel {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.prefab === undefined) { throw new Error() }\n        this.prefab = _json_.prefab\n        if (_json_.forbidFire === undefined) { throw new Error() }\n        this.forbidFire = _json_.forbidFire\n        if (_json_.forbidNBomb === undefined) { throw new Error() }\n        this.forbidNBomb = _json_.forbidNBomb\n        if (_json_.forbidActSkill === undefined) { throw new Error() }\n        this.forbidActSkill = _json_.forbidActSkill\n        if (_json_.planeCollisionScaling === undefined) { throw new Error() }\n        this.planeCollisionScaling = _json_.planeCollisionScaling\n        if (_json_.levelType === undefined) { throw new Error() }\n        this.levelType = _json_.levelType\n    }\n\n    /**\n     * 关卡id\n     */\n    readonly id: number\n    /**\n     * 关卡prefab\n     */\n    readonly prefab: string\n    /**\n     * 是、否（默认值）\n     */\n    readonly forbidFire: boolean\n    /**\n     * 是、否（默认值）\n     */\n    readonly forbidNBomb: boolean\n    /**\n     * 是、否（默认值）\n     */\n    readonly forbidActSkill: boolean\n    /**\n     * 0到1（1表示正常碰撞）\n     */\n    readonly planeCollisionScaling: number\n    /**\n     * 1=常规关卡<br/>2=机关关卡<br/>3=金币关卡<br/>4=火箭关卡<br/>99=Boss关\n     */\n    readonly levelType: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResLevelGroup {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.normLevelCount === undefined) { throw new Error() }\n        this.normLevelCount = _json_.normLevelCount\n        if (_json_.normLevelST === undefined) { throw new Error() }\n        this.normLevelST = _json_.normLevelST\n        if (_json_.normSTList === undefined) { throw new Error() }\n        { this.normSTList = []; for(let _ele0 of _json_.normSTList) { let _e0; _e0 = new randStrategy(_ele0); this.normSTList.push(_e0);}}\n        if (_json_.bossLevelCount === undefined) { throw new Error() }\n        this.bossLevelCount = _json_.bossLevelCount\n        if (_json_.bossLevelST === undefined) { throw new Error() }\n        this.bossLevelST = _json_.bossLevelST\n        if (_json_.bossSTList === undefined) { throw new Error() }\n        { this.bossSTList = []; for(let _ele0 of _json_.bossSTList) { let _e0; _e0 = new randStrategy(_ele0); this.bossSTList.push(_e0);}}\n    }\n\n    /**\n     * 关卡组ID\n     */\n    readonly id: number\n    /**\n     * 常规关卡数量\n     */\n    readonly normLevelCount: number\n    /**\n     * 1=随机<br/>2=随机不重复<br/>3=顺序重复\n     */\n    readonly normLevelST: number\n    readonly normSTList: randStrategy[]\n    /**\n     * BOSS关卡数量\n     */\n    readonly bossLevelCount: number\n    /**\n     * 1=随机<br/>2=随机不重复<br/>3=顺序重复\n     */\n    readonly bossLevelST: number\n    readonly bossSTList: randStrategy[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n        for (let _e of this.normSTList) { _e?.resolve(tables); }\n        \n        \n        for (let _e of this.bossSTList) { _e?.resolve(tables); }\n    }\n}\n\n\n\n\n\nexport class ResLoot {\n\n    constructor(_json_: any) {\n        if (_json_.loot_id === undefined) { throw new Error() }\n        this.lootId = _json_.loot_id\n        if (_json_.loot_group === undefined) { throw new Error() }\n        this.lootGroup = _json_.loot_group\n        if (_json_.loot_type === undefined) { throw new Error() }\n        this.lootType = _json_.loot_type\n        if (_json_.item_list === undefined) { throw new Error() }\n        { this.itemList = []; for(let _ele0 of _json_.item_list) { let _e0; _e0 = new ResLootItem(_ele0); this.itemList.push(_e0);}}\n    }\n\n    /**\n     * 掉落ID\n     */\n    readonly lootId: number\n    /**\n     * 掉落组\n     */\n    readonly lootGroup: number\n    /**\n     * 掉落方式\n     */\n    readonly lootType: ResLootType\n    readonly itemList: ResLootItem[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n        for (let _e of this.itemList) { _e?.resolve(tables); }\n    }\n}\n\n\n\n\n\nexport class ResLootItem {\n\n    constructor(_json_: any) {\n        if (_json_.item_id === undefined) { throw new Error() }\n        this.itemId = _json_.item_id\n        if (_json_.count === undefined) { throw new Error() }\n        this.count = _json_.count\n        if (_json_.rate === undefined) { throw new Error() }\n        this.rate = _json_.rate\n        if (_json_.protect_times === undefined) { throw new Error() }\n        this.protectTimes = _json_.protect_times\n    }\n\n    /**\n     * item_id 可以是道具，装备，飞机，或者另一个掉落ID\n     */\n    readonly itemId: number\n    readonly count: number\n    readonly rate: number\n    readonly protectTimes: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResPlane {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.star_level === undefined) { throw new Error() }\n        this.starLevel = _json_.star_level\n        if (_json_.name === undefined) { throw new Error() }\n        this.name = _json_.name\n        if (_json_.icon === undefined) { throw new Error() }\n        this.icon = _json_.icon\n        if (_json_.portrait === undefined) { throw new Error() }\n        this.portrait = _json_.portrait\n        if (_json_.description === undefined) { throw new Error() }\n        this.description = _json_.description\n        if (_json_.quality === undefined) { throw new Error() }\n        this.quality = _json_.quality\n        if (_json_.property === undefined) { throw new Error() }\n        this.property = new PlaneProperty(_json_.property)\n        if (_json_.effects === undefined) { throw new Error() }\n        { this.effects = []; for(let _ele0 of _json_.effects) { let _e0; _e0 = new PlaneEffect(_ele0); this.effects.push(_e0);}}\n        if (_json_.materials === undefined) { throw new Error() }\n        { this.materials = []; for(let _ele0 of _json_.materials) { let _e0; _e0 = new PlaneMaterial(_ele0); this.materials.push(_e0);}}\n    }\n\n    /**\n     * ID\n     */\n    readonly id: number\n    /**\n     * 战机星级\n     */\n    readonly starLevel: number\n    /**\n     * 名称\n     */\n    readonly name: string\n    /**\n     * 图标\n     */\n    readonly icon: string\n    /**\n     * 立绘\n     */\n    readonly portrait: string\n    /**\n     * 描述\n     */\n    readonly description: string\n    /**\n     * 品质\n     */\n    readonly quality: QualityType\n    readonly property: PlaneProperty\n    /**\n     * 效果列表\n     */\n    readonly effects: PlaneEffect[]\n    /**\n     * 升星材料\n     */\n    readonly materials: PlaneMaterial[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        this.property?.resolve(tables);\n        for (let _e of this.effects) { _e?.resolve(tables); }\n        for (let _e of this.materials) { _e?.resolve(tables); }\n    }\n}\n\n\n\n\n\nexport class ResSkill {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.name === undefined) { throw new Error() }\n        this.name = _json_.name\n        if (_json_.desc === undefined) { throw new Error() }\n        this.desc = _json_.desc\n        if (_json_.icon === undefined) { throw new Error() }\n        this.icon = _json_.icon\n        if (_json_.cd === undefined) { throw new Error() }\n        this.cd = _json_.cd\n        if (_json_.CostID === undefined) { throw new Error() }\n        this.CostID = _json_.CostID\n        if (_json_.CostNum === undefined) { throw new Error() }\n        this.CostNum = _json_.CostNum\n        if (_json_.conditionID === undefined) { throw new Error() }\n        this.conditionID = _json_.conditionID\n        if (_json_.ApplyBuffs === undefined) { throw new Error() }\n        { this.ApplyBuffs = []; for(let _ele0 of _json_.ApplyBuffs) { let _e0; _e0 = new ApplyBuff(_ele0); this.ApplyBuffs.push(_e0);}}\n    }\n\n    /**\n     * ID\n     */\n    readonly id: number\n    /**\n     * 技能名称\n     */\n    readonly name: string\n    /**\n     * 描述\n     */\n    readonly desc: string\n    /**\n     * 技能图标prefab\n     */\n    readonly icon: string\n    /**\n     * 冷却时间\n     */\n    readonly cd: number\n    /**\n     * 费用ID\n     */\n    readonly CostID: number\n    /**\n     * 费用消耗值\n     */\n    readonly CostNum: number\n    /**\n     * 条件\n     */\n    readonly conditionID: number\n    readonly ApplyBuffs: ApplyBuff[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResStage {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.mainStage === undefined) { throw new Error() }\n        this.mainStage = _json_.mainStage\n        if (_json_.subStage === undefined) { throw new Error() }\n        this.subStage = _json_.subStage\n        if (_json_.type === undefined) { throw new Error() }\n        this.type = _json_.type\n        if (_json_.enemyGroupID === undefined) { throw new Error() }\n        this.enemyGroupID = _json_.enemyGroupID\n        if (_json_.delay === undefined) { throw new Error() }\n        this.delay = _json_.delay\n        if (_json_.enemyNorRate === undefined) { throw new Error() }\n        this.enemyNorRate = _json_.enemyNorRate\n    }\n\n    /**\n     * id\n     */\n    readonly id: number\n    /**\n     * 关卡\n     */\n    readonly mainStage: number\n    /**\n     * 阶段\n     */\n    readonly subStage: number\n    /**\n     * 类型0:普通敌机 100：boss\n     */\n    readonly type: number\n    /**\n     * 波次id\n     */\n    readonly enemyGroupID: string\n    /**\n     * 延迟时间\n     */\n    readonly delay: number\n    /**\n     * 属性倍率（血量，攻击，碰撞攻击）\n     */\n    readonly enemyNorRate: string\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResTask {\n\n    constructor(_json_: any) {\n        if (_json_.task_id === undefined) { throw new Error() }\n        this.taskId = _json_.task_id\n        if (_json_.group_id === undefined) { throw new Error() }\n        this.groupId = _json_.group_id\n        if (_json_.task_class === undefined) { throw new Error() }\n        this.taskClass = _json_.task_class\n        if (_json_.prev_id === undefined) { throw new Error() }\n        this.prevId = _json_.prev_id\n        if (_json_.period_type === undefined) { throw new Error() }\n        this.periodType = _json_.period_type\n        if (_json_.task_cond === undefined) { throw new Error() }\n        { this.taskCond = []; for(let _ele0 of _json_.task_cond) { let _e0; _e0 = new ResCondition(_ele0); this.taskCond.push(_e0);}}\n        if (_json_.task_goal === undefined) { throw new Error() }\n        this.taskGoal = new ResTaskGoal(_json_.task_goal)\n        if (_json_.accumulate === undefined) { throw new Error() }\n        this.accumulate = _json_.accumulate\n        if (_json_.reward_id === undefined) { throw new Error() }\n        this.rewardId = _json_.reward_id\n        if (_json_.orbit_group_id === undefined) { throw new Error() }\n        this.orbitGroupId = _json_.orbit_group_id\n        if (_json_.orbit_value === undefined) { throw new Error() }\n        this.orbitValue = _json_.orbit_value\n        if (_json_.open_date === undefined) { throw new Error() }\n        this.openDate = _json_.open_date\n        if (_json_.open_time === undefined) { throw new Error() }\n        this.openTime = _json_.open_time\n        if (_json_.close_date === undefined) { throw new Error() }\n        this.closeDate = _json_.close_date\n        if (_json_.close_time === undefined) { throw new Error() }\n        this.closeTime = _json_.close_time\n        if (_json_.link_to === undefined) { throw new Error() }\n        this.linkTo = _json_.link_to\n    }\n\n    /**\n     * 任务 ID\n     */\n    readonly taskId: number\n    /**\n     * 任务集 ID\n     */\n    readonly groupId: number\n    /**\n     * 任务类型\n     */\n    readonly taskClass: ResTaskClass\n    /**\n     * 前置任务 ID\n     */\n    readonly prevId: number\n    /**\n     * 重置周期\n     */\n    readonly periodType: ResPeriodType\n    /**\n     * 任务接取条件\n     */\n    readonly taskCond: ResCondition[]\n    readonly taskGoal: ResTaskGoal\n    /**\n     * 目标值累积\n     */\n    readonly accumulate: boolean\n    /**\n     * 奖励ID\n     */\n    readonly rewardId: number\n    /**\n     * 奖励轨道集ID\n     */\n    readonly orbitGroupId: number\n    /**\n     * 完成奖励值\n     */\n    readonly orbitValue: number\n    /**\n     * 开放日期(yyyymmdd)\n     */\n    readonly openDate: string\n    /**\n     * 开放时间(HHMMSS)\n     */\n    readonly openTime: string\n    /**\n     * 结束日期(yyyymmdd)\n     */\n    readonly closeDate: string\n    /**\n     * 结束时间(HHMMSS)\n     */\n    readonly closeTime: string\n    /**\n     * 任务追踪\n     */\n    readonly linkTo: string\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        for (let _e of this.taskCond) { _e?.resolve(tables); }\n        this.taskGoal?.resolve(tables);\n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\n/**\n * 任务配置表项定义\n */\nexport class ResTask2 {\n\n    constructor(_json_: any) {\n        if (_json_.task_id === undefined) { throw new Error() }\n        this.taskId = _json_.task_id\n        if (_json_.group_id === undefined) { throw new Error() }\n        this.groupId = _json_.group_id\n        if (_json_.desc === undefined) { throw new Error() }\n        this.desc = _json_.desc\n        if (_json_.task_class === undefined) { throw new Error() }\n        this.taskClass = _json_.task_class\n        if (_json_.ui_res === undefined) { throw new Error() }\n        this.uiRes = _json_.ui_res\n        if (_json_.prev_id === undefined) { throw new Error() }\n        this.prevId = _json_.prev_id\n        if (_json_.period_type === undefined) { throw new Error() }\n        this.periodType = _json_.period_type\n        if (_json_.task_cond === undefined) { throw new Error() }\n        { this.taskCond = []; for(let _ele0 of _json_.task_cond) { let _e0; _e0 = new ResCondition(_ele0); this.taskCond.push(_e0);}}\n        if (_json_.task_goal === undefined) { throw new Error() }\n        this.taskGoal = new ResTaskGoal(_json_.task_goal)\n        if (_json_.accumlate === undefined) { throw new Error() }\n        this.accumlate = _json_.accumlate\n        if (_json_.reward_id === undefined) { throw new Error() }\n        this.rewardId = _json_.reward_id\n        if (_json_.task_orbit_id === undefined) { throw new Error() }\n        this.taskOrbitId = _json_.task_orbit_id\n        if (_json_.task_orbit_value === undefined) { throw new Error() }\n        this.taskOrbitValue = _json_.task_orbit_value\n        if (_json_.open_date === undefined) { throw new Error() }\n        this.openDate = _json_.open_date\n        if (_json_.open_time === undefined) { throw new Error() }\n        this.openTime = _json_.open_time\n        if (_json_.close_date === undefined) { throw new Error() }\n        this.closeDate = _json_.close_date\n        if (_json_.close_time === undefined) { throw new Error() }\n        this.closeTime = _json_.close_time\n        if (_json_.link_to === undefined) { throw new Error() }\n        this.linkTo = _json_.link_to\n    }\n\n    readonly taskId: number\n    readonly groupId: number\n    readonly desc: string\n    /**\n     * 可不配置\n     */\n    readonly taskClass: ResTaskClass\n    readonly uiRes: string\n    readonly prevId: number\n    readonly periodType: ResPeriodType\n    readonly taskCond: ResCondition[]\n    readonly taskGoal: ResTaskGoal\n    readonly accumlate: boolean\n    /**\n     * 需要客户端领取\n     */\n    readonly rewardId: number\n    readonly taskOrbitId: number\n    /**\n     * 自动累积\n     */\n    readonly taskOrbitValue: number\n    /**\n     * yyyymmdd\n     */\n    readonly openDate: string\n    /**\n     * HHMMSS\n     */\n    readonly openTime: string\n    /**\n     * yyyymmdd\n     */\n    readonly closeDate: string\n    /**\n     * HHMMSS\n     */\n    readonly closeTime: string\n    /**\n     * 连接到完成任务相关的 UI\n     */\n    readonly linkTo: string\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        for (let _e of this.taskCond) { _e?.resolve(tables); }\n        this.taskGoal?.resolve(tables);\n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResTaskGoal {\n\n    constructor(_json_: any) {\n        if (_json_.goal_type === undefined) { throw new Error() }\n        this.goalType = _json_.goal_type\n        if (_json_.params === undefined) { throw new Error() }\n        { this.params = []; for(let _ele0 of _json_.params) { let _e0; _e0 = _ele0; this.params.push(_e0);}}\n        if (_json_.desc === undefined) { throw new Error() }\n        this.desc = _json_.desc\n    }\n\n    readonly goalType: ResGoalType\n    readonly params: number[]\n    readonly desc: string\n\n    resolve(tables:Tables) {\n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResTaskOrbit {\n\n    constructor(_json_: any) {\n        if (_json_.task_id === undefined) { throw new Error() }\n        this.taskId = _json_.task_id\n        if (_json_.group_id === undefined) { throw new Error() }\n        this.groupId = _json_.group_id\n        if (_json_.index === undefined) { throw new Error() }\n        this.index = _json_.index\n        if (_json_.period_type === undefined) { throw new Error() }\n        this.periodType = _json_.period_type\n        if (_json_.task_cond === undefined) { throw new Error() }\n        { this.taskCond = []; for(let _ele0 of _json_.task_cond) { let _e0; _e0 = new ResCondition(_ele0); this.taskCond.push(_e0);}}\n        if (_json_.task_goal === undefined) { throw new Error() }\n        this.taskGoal = new ResTaskGoal(_json_.task_goal)\n        if (_json_.accumulate === undefined) { throw new Error() }\n        this.accumulate = _json_.accumulate\n        if (_json_.reward_id === undefined) { throw new Error() }\n        this.rewardId = _json_.reward_id\n    }\n\n    /**\n     * 轨道ID\n     */\n    readonly taskId: number\n    /**\n     * 轨道集\n     */\n    readonly groupId: number\n    /**\n     * 序号\n     */\n    readonly index: number\n    /**\n     * 循环类型\n     */\n    readonly periodType: ResPeriodType\n    readonly taskCond: ResCondition[]\n    readonly taskGoal: ResTaskGoal\n    /**\n     * 是否累积\n     */\n    readonly accumulate: boolean\n    /**\n     * 奖励ID\n     */\n    readonly rewardId: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        for (let _e of this.taskCond) { _e?.resolve(tables); }\n        this.taskGoal?.resolve(tables);\n        \n        \n    }\n}\n\n\n\n\n\nexport class ResTrack {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.tpe === undefined) { throw new Error() }\n        this.tpe = _json_.tpe\n        if (_json_.value === undefined) { throw new Error() }\n        this.value = _json_.value\n    }\n\n    /**\n     * id\n     */\n    readonly id: number\n    /**\n     * 类型\n     */\n    readonly tpe: number\n    /**\n     * 值(不同的轨迹类型，数据代表的信息不一样)\n     */\n    readonly value: string\n\n    resolve(tables:Tables) {\n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResUpgrade {\n\n    constructor(_json_: any) {\n        if (_json_.role_level === undefined) { throw new Error() }\n        this.roleLevel = _json_.role_level\n        if (_json_.xp === undefined) { throw new Error() }\n        this.xp = _json_.xp\n        if (_json_.reward_id === undefined) { throw new Error() }\n        this.rewardId = _json_.reward_id\n    }\n\n    /**\n     * 玩家等级\n     */\n    readonly roleLevel: number\n    /**\n     * 升级所需的经验\n     */\n    readonly xp: number\n    /**\n     * 升级奖励\n     */\n    readonly rewardId: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class ResWave {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.enemyGroupID === undefined) { throw new Error() }\n        this.enemyGroupID = _json_.enemyGroupID\n        if (_json_.delay === undefined) { throw new Error() }\n        this.delay = _json_.delay\n        if (_json_.planeType === undefined) { throw new Error() }\n        this.planeType = _json_.planeType\n        if (_json_.planeId === undefined) { throw new Error() }\n        this.planeId = _json_.planeId\n        if (_json_.interval === undefined) { throw new Error() }\n        this.interval = _json_.interval\n        if (_json_.offsetPos === undefined) { throw new Error() }\n        this.offsetPos = _json_.offsetPos\n        if (_json_.num === undefined) { throw new Error() }\n        this.num = _json_.num\n        if (_json_.pos === undefined) { throw new Error() }\n        this.pos = _json_.pos\n        if (_json_.track === undefined) { throw new Error() }\n        this.track = _json_.track\n        if (_json_.trackParams === undefined) { throw new Error() }\n        this.trackParams = _json_.trackParams\n        if (_json_.rotatioSpeed === undefined) { throw new Error() }\n        this.rotatioSpeed = _json_.rotatioSpeed\n        if (_json_.FirstShootDelay === undefined) { throw new Error() }\n        this.FirstShootDelay = _json_.FirstShootDelay\n    }\n\n    /**\n     * id\n     */\n    readonly id: number\n    /**\n     * 波次 ID\n     */\n    readonly enemyGroupID: number\n    /**\n     * 延迟时间\n     */\n    readonly delay: number\n    /**\n     * 0 表示普通敌机\n     */\n    readonly planeType: number\n    /**\n     * 敌机id\n     */\n    readonly planeId: number\n    /**\n     * 生成间隔时间\n     */\n    readonly interval: number\n    /**\n     * 根据敌机数量设置偏移位置\n     */\n    readonly offsetPos: string\n    /**\n     * 生成的敌机数量\n     */\n    readonly num: number\n    /**\n     * 初始位置\n     */\n    readonly pos: string\n    /**\n     * 轨迹路径(次数,轨迹索引,;id,速度,间隔；)\n     */\n    readonly track: string\n    /**\n     * 轨迹参数\n     */\n    readonly trackParams: string\n    /**\n     * 旋转速度\n     */\n    readonly rotatioSpeed: number\n    /**\n     * 首次射击延迟\n     */\n    readonly FirstShootDelay: string\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class SkillCondition {\n\n    constructor(_json_: any) {\n        if (_json_.type === undefined) { throw new Error() }\n        this.type = _json_.type\n        if (_json_.param === undefined) { throw new Error() }\n        { this.param = []; for(let _ele0 of _json_.param) { let _e0; _e0 = _ele0; this.param.push(_e0);}}\n    }\n\n    readonly type: SkillConditionType\n    readonly param: number[]\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n\n\n\n\n\nexport class TbGlobalAttr {\n\n    private _data: ResGlobalAttr\n    constructor(_json_: any) {\n        if (_json_.length != 1) throw new Error('table mode=one, but size != 1')\n        this._data = new ResGlobalAttr(_json_[0])\n    }\n\n    getData(): ResGlobalAttr { return this._data; }\n\n    /**\n     * 每回合发放的金币\n     */\n    get  GoldProducion(): number { return this._data.GoldProducion; }\n    /**\n     * 体力上限值\n     */\n    get  MaxEnergy(): number { return this._data.MaxEnergy; }\n    /**\n     * 体力恢复的间隔时间\n     */\n    get  EnergyRecoverInterval(): number { return this._data.EnergyRecoverInterval; }\n    /**\n     * 体力恢复的值\n     */\n    get  EnergyRecoverValue(): number { return this._data.EnergyRecoverValue; }\n    /**\n     * 局内道具拾取距离\n     */\n    get  ItemPickUpRadius(): number { return this._data.ItemPickUpRadius; }\n    /**\n     * 受击保护\n     */\n    get  PostHitProtection(): number { return this._data.PostHitProtection; }\n    /**\n     * 镜头平移最大跟随速度\n     */\n    get  CameraTranslationMaxMoveSpeed(): number { return this._data.CameraTranslationMaxMoveSpeed; }\n    /**\n     * 镜头平移启动延迟\n     */\n    get  CameraTranslationMoveDelay(): number { return this._data.CameraTranslationMoveDelay; }\n\n    resolve(tables:Tables)\n    {\n        this._data.resolve(tables)\n    }\n    \n}\n\n\n\n\nexport class TbEquipUpgrade {\n    private _dataList: ResEquipUpgrade[]\n    \n    constructor(_json_: any) {\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResEquipUpgrade\n            _v = new ResEquipUpgrade(_json2_)\n            this._dataList.push(_v)\n        }\n    }\n\n    getDataList(): ResEquipUpgrade[] { return this._dataList }\n\n    get(index: number): ResEquipUpgrade | undefined { return this._dataList[index] }\n    \n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbGM {\n    private _dataList: GM[]\n    \n    constructor(_json_: any) {\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: GM\n            _v = new GM(_json2_)\n            this._dataList.push(_v)\n        }\n    }\n\n    getDataList(): GM[] { return this._dataList }\n\n    get(index: number): GM | undefined { return this._dataList[index] }\n    \n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbPlane {\n    private _dataMap: Map<number, ResPlane>\n    private _dataList: ResPlane[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResPlane>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResPlane\n            _v = new ResPlane(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResPlane> { return this._dataMap; }\n    getDataList(): ResPlane[] { return this._dataList; }\n\n    get(key: number): ResPlane | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResAchievement {\n    private _dataMap: Map<number, ResAchievement>\n    private _dataList: ResAchievement[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResAchievement>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResAchievement\n            _v = new ResAchievement(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.taskId, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResAchievement> { return this._dataMap; }\n    getDataList(): ResAchievement[] { return this._dataList; }\n\n    get(key: number): ResAchievement | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResActivity {\n    private _dataMap: Map<number, ResActivity>\n    private _dataList: ResActivity[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResActivity>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResActivity\n            _v = new ResActivity(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.taskId, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResActivity> { return this._dataMap; }\n    getDataList(): ResActivity[] { return this._dataList; }\n\n    get(key: number): ResActivity | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResBuffer {\n    private _dataMap: Map<number, ResBuffer>\n    private _dataList: ResBuffer[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResBuffer>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResBuffer\n            _v = new ResBuffer(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResBuffer> { return this._dataMap; }\n    getDataList(): ResBuffer[] { return this._dataList; }\n\n    get(key: number): ResBuffer | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResBullet {\n    private _dataMap: Map<number, ResBullet>\n    private _dataList: ResBullet[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResBullet>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResBullet\n            _v = new ResBullet(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResBullet> { return this._dataMap; }\n    getDataList(): ResBullet[] { return this._dataList; }\n\n    get(key: number): ResBullet | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResChapter {\n    private _dataMap: Map<number, ResChapter>\n    private _dataList: ResChapter[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResChapter>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResChapter\n            _v = new ResChapter(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResChapter> { return this._dataMap; }\n    getDataList(): ResChapter[] { return this._dataList; }\n\n    get(key: number): ResChapter | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResEffect {\n    private _dataMap: Map<number, ResEffect>\n    private _dataList: ResEffect[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResEffect>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResEffect\n            _v = new ResEffect(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResEffect> { return this._dataMap; }\n    getDataList(): ResEffect[] { return this._dataList; }\n\n    get(key: number): ResEffect | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResEnemy {\n    private _dataMap: Map<number, ResEnemy>\n    private _dataList: ResEnemy[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResEnemy>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResEnemy\n            _v = new ResEnemy(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResEnemy> { return this._dataMap; }\n    getDataList(): ResEnemy[] { return this._dataList; }\n\n    get(key: number): ResEnemy | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResEquip {\n    private _dataMap: Map<number, ResEquip>\n    private _dataList: ResEquip[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResEquip>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResEquip\n            _v = new ResEquip(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResEquip> { return this._dataMap; }\n    getDataList(): ResEquip[] { return this._dataList; }\n\n    get(key: number): ResEquip | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResGameMode {\n    private _dataMap: Map<number, ResGameMode>\n    private _dataList: ResGameMode[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResGameMode>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResGameMode\n            _v = new ResGameMode(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.ID, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResGameMode> { return this._dataMap; }\n    getDataList(): ResGameMode[] { return this._dataList; }\n\n    get(key: number): ResGameMode | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResItem {\n    private _dataMap: Map<number, ResItem>\n    private _dataList: ResItem[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResItem>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResItem\n            _v = new ResItem(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResItem> { return this._dataMap; }\n    getDataList(): ResItem[] { return this._dataList; }\n\n    get(key: number): ResItem | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResLevel {\n    private _dataMap: Map<number, ResLevel>\n    private _dataList: ResLevel[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResLevel>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResLevel\n            _v = new ResLevel(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResLevel> { return this._dataMap; }\n    getDataList(): ResLevel[] { return this._dataList; }\n\n    get(key: number): ResLevel | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResLevelGroup {\n    private _dataMap: Map<number, ResLevelGroup>\n    private _dataList: ResLevelGroup[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResLevelGroup>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResLevelGroup\n            _v = new ResLevelGroup(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResLevelGroup> { return this._dataMap; }\n    getDataList(): ResLevelGroup[] { return this._dataList; }\n\n    get(key: number): ResLevelGroup | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResLoot {\n    private _dataMap: Map<number, ResLoot>\n    private _dataList: ResLoot[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResLoot>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResLoot\n            _v = new ResLoot(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.lootId, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResLoot> { return this._dataMap; }\n    getDataList(): ResLoot[] { return this._dataList; }\n\n    get(key: number): ResLoot | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResSkill {\n    private _dataMap: Map<number, ResSkill>\n    private _dataList: ResSkill[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResSkill>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResSkill\n            _v = new ResSkill(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResSkill> { return this._dataMap; }\n    getDataList(): ResSkill[] { return this._dataList; }\n\n    get(key: number): ResSkill | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResStage {\n    private _dataMap: Map<number, ResStage>\n    private _dataList: ResStage[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResStage>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResStage\n            _v = new ResStage(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResStage> { return this._dataMap; }\n    getDataList(): ResStage[] { return this._dataList; }\n\n    get(key: number): ResStage | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResTask {\n    private _dataMap: Map<number, ResTask>\n    private _dataList: ResTask[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResTask>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResTask\n            _v = new ResTask(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.taskId, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResTask> { return this._dataMap; }\n    getDataList(): ResTask[] { return this._dataList; }\n\n    get(key: number): ResTask | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResTaskOrbit {\n    private _dataMap: Map<number, ResTaskOrbit>\n    private _dataList: ResTaskOrbit[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResTaskOrbit>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResTaskOrbit\n            _v = new ResTaskOrbit(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.taskId, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResTaskOrbit> { return this._dataMap; }\n    getDataList(): ResTaskOrbit[] { return this._dataList; }\n\n    get(key: number): ResTaskOrbit | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResTrack {\n    private _dataMap: Map<number, ResTrack>\n    private _dataList: ResTrack[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResTrack>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResTrack\n            _v = new ResTrack(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResTrack> { return this._dataMap; }\n    getDataList(): ResTrack[] { return this._dataList; }\n\n    get(key: number): ResTrack | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResUpgrade {\n    private _dataMap: Map<number, ResUpgrade>\n    private _dataList: ResUpgrade[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResUpgrade>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResUpgrade\n            _v = new ResUpgrade(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.roleLevel, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResUpgrade> { return this._dataMap; }\n    getDataList(): ResUpgrade[] { return this._dataList; }\n\n    get(key: number): ResUpgrade | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbResWave {\n    private _dataMap: Map<number, ResWave>\n    private _dataList: ResWave[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResWave>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResWave\n            _v = new ResWave(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResWave> { return this._dataMap; }\n    getDataList(): ResWave[] { return this._dataList; }\n\n    get(key: number): ResWave | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\ntype JsonLoader = (file: string) => any\n\nexport class Tables {\n    private _TbGlobalAttr: TbGlobalAttr\n    get TbGlobalAttr(): TbGlobalAttr  { return this._TbGlobalAttr;}\n    private _TbEquipUpgrade: TbEquipUpgrade\n    get TbEquipUpgrade(): TbEquipUpgrade  { return this._TbEquipUpgrade;}\n    private _TbGM: TbGM\n    get TbGM(): TbGM  { return this._TbGM;}\n    private _TbPlane: TbPlane\n    get TbPlane(): TbPlane  { return this._TbPlane;}\n    private _TbResAchievement: TbResAchievement\n    get TbResAchievement(): TbResAchievement  { return this._TbResAchievement;}\n    private _TbResActivity: TbResActivity\n    get TbResActivity(): TbResActivity  { return this._TbResActivity;}\n    private _TbResBuffer: TbResBuffer\n    get TbResBuffer(): TbResBuffer  { return this._TbResBuffer;}\n    private _TbResBullet: TbResBullet\n    get TbResBullet(): TbResBullet  { return this._TbResBullet;}\n    private _TbResChapter: TbResChapter\n    get TbResChapter(): TbResChapter  { return this._TbResChapter;}\n    private _TbResEffect: TbResEffect\n    get TbResEffect(): TbResEffect  { return this._TbResEffect;}\n    private _TbResEnemy: TbResEnemy\n    get TbResEnemy(): TbResEnemy  { return this._TbResEnemy;}\n    private _TbResEquip: TbResEquip\n    get TbResEquip(): TbResEquip  { return this._TbResEquip;}\n    private _TbResGameMode: TbResGameMode\n    get TbResGameMode(): TbResGameMode  { return this._TbResGameMode;}\n    private _TbResItem: TbResItem\n    get TbResItem(): TbResItem  { return this._TbResItem;}\n    private _TbResLevel: TbResLevel\n    get TbResLevel(): TbResLevel  { return this._TbResLevel;}\n    private _TbResLevelGroup: TbResLevelGroup\n    get TbResLevelGroup(): TbResLevelGroup  { return this._TbResLevelGroup;}\n    private _TbResLoot: TbResLoot\n    get TbResLoot(): TbResLoot  { return this._TbResLoot;}\n    private _TbResSkill: TbResSkill\n    get TbResSkill(): TbResSkill  { return this._TbResSkill;}\n    private _TbResStage: TbResStage\n    get TbResStage(): TbResStage  { return this._TbResStage;}\n    private _TbResTask: TbResTask\n    get TbResTask(): TbResTask  { return this._TbResTask;}\n    private _TbResTaskOrbit: TbResTaskOrbit\n    get TbResTaskOrbit(): TbResTaskOrbit  { return this._TbResTaskOrbit;}\n    private _TbResTrack: TbResTrack\n    get TbResTrack(): TbResTrack  { return this._TbResTrack;}\n    private _TbResUpgrade: TbResUpgrade\n    get TbResUpgrade(): TbResUpgrade  { return this._TbResUpgrade;}\n    private _TbResWave: TbResWave\n    get TbResWave(): TbResWave  { return this._TbResWave;}\n\n    constructor(loader: JsonLoader) {\n        this._TbGlobalAttr = new TbGlobalAttr(loader('tbglobalattr'))\n        this._TbEquipUpgrade = new TbEquipUpgrade(loader('tbequipupgrade'))\n        this._TbGM = new TbGM(loader('tbgm'))\n        this._TbPlane = new TbPlane(loader('tbplane'))\n        this._TbResAchievement = new TbResAchievement(loader('tbresachievement'))\n        this._TbResActivity = new TbResActivity(loader('tbresactivity'))\n        this._TbResBuffer = new TbResBuffer(loader('tbresbuffer'))\n        this._TbResBullet = new TbResBullet(loader('tbresbullet'))\n        this._TbResChapter = new TbResChapter(loader('tbreschapter'))\n        this._TbResEffect = new TbResEffect(loader('tbreseffect'))\n        this._TbResEnemy = new TbResEnemy(loader('tbresenemy'))\n        this._TbResEquip = new TbResEquip(loader('tbresequip'))\n        this._TbResGameMode = new TbResGameMode(loader('tbresgamemode'))\n        this._TbResItem = new TbResItem(loader('tbresitem'))\n        this._TbResLevel = new TbResLevel(loader('tbreslevel'))\n        this._TbResLevelGroup = new TbResLevelGroup(loader('tbreslevelgroup'))\n        this._TbResLoot = new TbResLoot(loader('tbresloot'))\n        this._TbResSkill = new TbResSkill(loader('tbresskill'))\n        this._TbResStage = new TbResStage(loader('tbresstage'))\n        this._TbResTask = new TbResTask(loader('tbrestask'))\n        this._TbResTaskOrbit = new TbResTaskOrbit(loader('tbrestaskorbit'))\n        this._TbResTrack = new TbResTrack(loader('tbrestrack'))\n        this._TbResUpgrade = new TbResUpgrade(loader('tbresupgrade'))\n        this._TbResWave = new TbResWave(loader('tbreswave'))\n\n        this._TbGlobalAttr.resolve(this)\n        this._TbEquipUpgrade.resolve(this)\n        this._TbGM.resolve(this)\n        this._TbPlane.resolve(this)\n        this._TbResAchievement.resolve(this)\n        this._TbResActivity.resolve(this)\n        this._TbResBuffer.resolve(this)\n        this._TbResBullet.resolve(this)\n        this._TbResChapter.resolve(this)\n        this._TbResEffect.resolve(this)\n        this._TbResEnemy.resolve(this)\n        this._TbResEquip.resolve(this)\n        this._TbResGameMode.resolve(this)\n        this._TbResItem.resolve(this)\n        this._TbResLevel.resolve(this)\n        this._TbResLevelGroup.resolve(this)\n        this._TbResLoot.resolve(this)\n        this._TbResSkill.resolve(this)\n        this._TbResStage.resolve(this)\n        this._TbResTask.resolve(this)\n        this._TbResTaskOrbit.resolve(this)\n        this._TbResTrack.resolve(this)\n        this._TbResUpgrade.resolve(this)\n        this._TbResWave.resolve(this)\n    }\n}\n\n"]}