{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/wave/Wave.ts"], "names": ["_decorator", "CCFloat", "CCInteger", "Component", "WaveData", "eSpawnOrder", "eWaveCompletion", "GameIns", "ccclass", "property", "executeInEditMode", "menu", "WaveTrack", "WaveTrackGroup", "Wave", "type", "_isCompleted", "_waveElapsedTime", "_nextSpawnTime", "_totalWeight", "_waveCompleteParam", "_nextSpawnIndex", "_spawnQueue", "isCompleted", "_reset", "length", "onLoad", "waveData", "spawnOrder", "Random", "spawnGroup", "for<PERSON>ach", "group", "weight", "selfWeight", "trigger", "waveCompletionParam", "eval", "waveCompletion", "SpawnCount", "i", "randomWeight", "Math", "random", "push", "planeID", "tick", "dtInMiliseconds", "spawnFromQueue", "spawnFromGroup", "spawnSingleFromQueue", "spawnInterval", "index", "spawnPos", "spawnAngle", "createPlane", "planeId", "pos", "angle", "enemy", "enemyManager", "addPlane", "nodePosition", "node", "worldPosition", "setPos", "x", "y"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAuBC,MAAAA,O,OAAAA,O;AAASC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,S,OAAAA,S;;AAC3CC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,W,iBAAAA,W;AAAaC,MAAAA,e,iBAAAA,e;;AACvBC,MAAAA,O,iBAAAA,O;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA,iBAArB;AAAwCC,QAAAA;AAAxC,O,GAAiDX,U;;2BAG1CY,S,WADZJ,OAAO,CAAC,WAAD,C,UAEHC,QAAQ,CAACP,SAAD,C,UAERO,QAAQ,CAACR,OAAD,C,UAERQ,QAAQ,CAACR,OAAD,C,UAERQ,QAAQ,CAACR,OAAD,C,2BARb,MACaW,SADb,CACuB;AAAA;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAAA,O;;;;;iBAEP,C;;;;;;;iBAEG,C;;;;;;;iBAEK,C;;;;;;;iBAEF,C;;;;gCAITC,c,YADZL,OAAO,CAAC,gBAAD,C,UAEHC,QAAQ,CAACP,SAAD,C,UAERO,QAAQ,CAACP,SAAD,C,UAERO,QAAQ,CAACP,SAAD,C,WAERO,QAAQ,CAAC,CAACG,SAAD,CAAD,C,6BARb,MACaC,cADb,CAC4B;AAAA;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAAA,O;;;;;iBAEV,C;;;;;;;iBAEG,C;;;;;;;iBAEE,C;;;;;;;iBAEU,E;;;;sBAMpBC,I,aAHZN,OAAO,CAAC,MAAD,C,WACPG,IAAI,CAAC,OAAD,C,WACJD,iBAAiB,E,WAEbD,QAAQ,CAAC;AAACM,QAAAA,IAAI;AAAA;AAAA;AAAL,OAAD,C,gEAJb,MAGaD,IAHb,SAG0BX,SAH1B,CAGoC;AAAA;AAAA;;AAAA;;AAIhC;AACJ;AACA;AANoC,eAOxBa,YAPwB,GAOA,KAPA;AAAA,eAUxBC,gBAVwB,GAUG,CAVH;AAAA,eAWxBC,cAXwB,GAWC,CAXD;AAAA,eAYxBC,YAZwB,GAYD,CAZC;AAahC;AAbgC,eAcxBC,kBAdwB,GAcK,CAdL;AAehC;AAfgC,eAgBxBC,eAhBwB,GAgBE,CAhBF;AAAA,eAiBxBC,WAjBwB,GAiBA,EAjBA;AAAA;;AAQhC;AACsB,YAAXC,WAAW,GAAG;AAAE,iBAAO,KAAKP,YAAZ;AAA2B;;AAU9CQ,QAAAA,MAAM,GAAG;AACb,eAAKR,YAAL,GAAoB,KAApB;AACA,eAAKC,gBAAL,GAAwB,CAAxB;AACA,eAAKC,cAAL,GAAsB,CAAtB;AACA,eAAKG,eAAL,GAAuB,CAAvB;AACA,eAAKC,WAAL,CAAiBG,MAAjB,GAA0B,CAA1B,CALa,CAMb;AACH;;AAEDC,QAAAA,MAAM,GAAG;AACL,cAAI,KAAKC,QAAL,IAAiB,KAAKA,QAAL,CAAcC,UAAd,KAA6B;AAAA;AAAA,0CAAYC,MAA9D,EAAsE;AAClE,iBAAKV,YAAL,GAAoB,CAApB,CADkE,CAElE;;AACA,iBAAKQ,QAAL,CAAcG,UAAd,CAAyBC,OAAzB,CAAkCC,KAAD,IAAW;AACxC,mBAAKb,YAAL,IAAqBa,KAAK,CAACC,MAA3B;AACAD,cAAAA,KAAK,CAACE,UAAN,GAAmB,KAAKf,YAAxB;AACH,aAHD;AAIH;AACJ;;AAEDgB,QAAAA,OAAO,GAAG;AACN,eAAKX,MAAL,GADM,CAGN;;;AACA,cAAI,KAAKG,QAAT,EAAmB;AACf,iBAAKP,kBAAL,GAA0B,KAAKO,QAAL,CAAcS,mBAAd,CAAkCC,IAAlC,EAA1B;;AACA,gBAAI,KAAKV,QAAL,CAAcW,cAAd,KAAiC;AAAA;AAAA,oDAAgBC,UAArD,EAAiE;AAC7D,kBAAI,KAAKZ,QAAL,CAAcC,UAAd,KAA6B;AAAA;AAAA,8CAAYC,MAA7C,EAAqD;AACjD,qBAAK,IAAIW,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKpB,kBAAzB,EAA6CoB,CAAC,EAA9C,EAAkD;AAC9C,sBAAMC,YAAY,GAAGC,IAAI,CAACC,MAAL,KAAgB,KAAKxB,YAA1C;;AACA,uBAAK,IAAMa,KAAX,IAAoB,KAAKL,QAAL,CAAcG,UAAlC,EAA8C;AAC1C,wBAAIW,YAAY,IAAIT,KAAK,CAACE,UAA1B,EAAsC;AAClC,2BAAKZ,WAAL,CAAiBsB,IAAjB,CAAsBZ,KAAK,CAACa,OAA5B;;AACA;AACH;AACJ;AACJ;AACJ,eAVD,MAUO;AACH,qBAAK,IAAIL,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAG,KAAKpB,kBAAzB,EAA6CoB,EAAC,EAA9C,EAAkD;AAC9C;AACA,uBAAKlB,WAAL,CAAiBsB,IAAjB,CAAsB,KAAKjB,QAAL,CAAcG,UAAd,CAAyBU,EAAC,GAAG,KAAKb,QAAL,CAAcG,UAAd,CAAyBL,MAAtD,EAA8DoB,OAApF;AACH;AACJ;AACJ;AACJ;AACJ,SAhE+B,CAkEhC;;;AACAC,QAAAA,IAAI,CAACC,eAAD,EAA0B;AAC1B,cAAI,KAAK/B,YAAT,EAAuB;AAEvB,eAAKC,gBAAL,IAAyB8B,eAAzB;;AACA,cAAI,KAAKpB,QAAL,CAAcW,cAAd,KAAiC;AAAA;AAAA,kDAAgBC,UAArD,EAAiE;AAC7D;AACA,gBAAI,KAAKtB,gBAAL,IAAyB,KAAKC,cAAlC,EAAkD;AAC9C,kBAAI,CAAC,KAAK8B,cAAL,EAAL,EAA4B;AACxB,qBAAKhC,YAAL,GAAoB,IAApB;AACH;AACJ;AACJ,WAPD,MAQK;AACD;AACA,gBAAI,KAAKC,gBAAL,IAAyB,KAAKG,kBAAlC,EAAsD;AAClD,mBAAKJ,YAAL,GAAoB,IAApB;AACH,aAFD,MAEO;AACH,kBAAI,KAAKC,gBAAL,IAAyB,KAAKC,cAAlC,EAAkD;AAC9C,qBAAK+B,cAAL;AACH;AACJ;AACJ;AACJ;;AAEOD,QAAAA,cAAc,GAAY;AAC9B,cAAI,KAAK3B,eAAL,IAAwB,KAAKC,WAAL,CAAiBG,MAA7C,EAAqD;AACjD,mBAAO,KAAP;AACH;;AAED,eAAKyB,oBAAL,CAA0B,KAAK7B,eAAL,EAA1B;AACA,eAAKH,cAAL,GAAsB,KAAKD,gBAAL,GAAwB,KAAKU,QAAL,CAAcwB,aAAd,CAA4Bd,IAA5B,EAA9C;AACA,iBAAO,IAAP;AACH;;AAEOa,QAAAA,oBAAoB,CAACE,KAAD,EAAsB;AAC9C,cAAIA,KAAK,IAAI,KAAK9B,WAAL,CAAiBG,MAA9B,EAAsC;AAClC;AACH;;AAED,cAAI4B,QAAQ,GAAG,KAAK1B,QAAL,CAAc0B,QAA7B;AACA,cAAIC,UAAU,GAAG,KAAK3B,QAAL,CAAc2B,UAAd,CAAyBjB,IAAzB,EAAjB,CAN8C,CAO9C;;AAEA,eAAKkB,WAAL,CAAiB,KAAKjC,WAAL,CAAiB8B,KAAjB,CAAjB,EAA0CC,QAA1C,EAAoDC,UAApD;AACH;;AAEOL,QAAAA,cAAc,GAAS;AAC3B,cAAII,QAAQ,GAAG,KAAK1B,QAAL,CAAc0B,QAA7B;AACA,cAAIC,UAAU,GAAG,KAAK3B,QAAL,CAAc2B,UAAd,CAAyBjB,IAAzB,EAAjB,CAF2B,CAG3B;;AAEA,cAAI,KAAKV,QAAL,CAAcC,UAAd,KAA6B;AAAA;AAAA,0CAAYC,MAA7C,EAAqD;AACjD,gBAAMY,YAAY,GAAGC,IAAI,CAACC,MAAL,KAAgB,KAAKxB,YAA1C;;AACA,iBAAK,IAAMa,KAAX,IAAoB,KAAKL,QAAL,CAAcG,UAAlC,EAA8C;AAC1C,kBAAIW,YAAY,IAAIT,KAAK,CAACE,UAA1B,EAAsC;AAClC,qBAAKqB,WAAL,CAAiBvB,KAAK,CAACa,OAAvB,EAAgCQ,QAAhC,EAA0CC,UAA1C;AACA;AACH;AACJ;AACJ,WARD,MAQO;AACH,iBAAKC,WAAL,CAAiB,KAAK5B,QAAL,CAAcG,UAAd,CAAyB,KAAKT,eAAL,KAAyB,KAAKM,QAAL,CAAcG,UAAd,CAAyBL,MAA3E,EAAmFoB,OAApG,EAA6GQ,QAA7G,EAAuHC,UAAvH;AACH;AACJ;;AAEaC,QAAAA,WAAW,CAACC,OAAD,EAAkBC,GAAlB,EAA6BC,KAA7B,EAA4C;AAAA;;AAAA;AACjE,gBAAIC,KAAK,GAAG;AAAA;AAAA,oCAAQC,YAAR,CAAqBC,QAArB,CAA8BL,OAA9B,EAAuC,IAAvC,CAAZ;;AACA,gBAAIG,KAAJ,EAAW;AACP;AACA;AACA;AACA,kBAAMG,YAAY,GAAG,KAAI,CAACC,IAAL,CAAUC,aAA/B;AACAL,cAAAA,KAAK,CAACM,MAAN,CAAaH,YAAY,CAACI,CAAb,GAAiBT,GAAG,CAACS,CAAlC,EAAqCJ,YAAY,CAACK,CAAb,GAAiBV,GAAG,CAACU,CAA1D,EALO,CAMP;AACA;AACH;AAVgE;AAWpE;;AA9I+B,O;;;;;iBAEF;AAAA;AAAA,qC", "sourcesContent": ["import { _decorator, CCBoolean, CCFloat, CCInteger, Component, Vec2 } from 'cc';\r\nimport { WaveData, eSpawnOrder, eWaveCompletion } from '../data/WaveData';\r\nimport { GameIns } from 'db://assets/bundles/common/script/game/GameIns';\r\nconst { ccclass, property, executeInEditMode, menu } = _decorator;\r\n\r\n@ccclass('WaveTrack')\r\nexport class WaveTrack {\r\n    @property(CCInteger)\r\n    public id = 0;\r\n    @property(CCFloat)\r\n    public speed = 0;\r\n    @property(CCFloat)\r\n    public accelerate = 0;\r\n    @property(CCFloat)\r\n    public Interval = 0;\r\n}\r\n\r\n@ccclass('WaveTrackGroup')\r\nexport class WaveTrackGroup {\r\n    @property(CCInteger)\r\n    public type = 0;\r\n    @property(CCInteger)\r\n    public loopNum = 0;\r\n    @property(CCInteger)\r\n    public formIndex = 0;\r\n    @property([WaveTrack])\r\n    public tracks: WaveTrack[] = [];\r\n}\r\n\r\n@ccclass('Wave')\r\n@menu(\"怪物/波次\")\r\n@executeInEditMode()\r\nexport class Wave extends Component {\r\n    @property({type:WaveData})\r\n    readonly waveData: WaveData = new WaveData();\r\n\r\n    /*\r\n     * 以下是新的wave逻辑, 旧的逻辑主要在WaveManager与EnemyWave\r\n     */\r\n    private _isCompleted: boolean = false;\r\n    // 当前波次是否已完成\r\n    public get isCompleted() { return this._isCompleted; }\r\n    private _waveElapsedTime: number = 0;\r\n    private _nextSpawnTime: number = 0;\r\n    private _totalWeight: number = 0;\r\n    // 这个参数可能是时间，也可能是数量，取决于waveData.waveCompletion\r\n    private _waveCompleteParam: number = 0;\r\n    // 以下两个是用在waveCompletion == SpawnCount时的队列\r\n    private _nextSpawnIndex: number = 0;\r\n    private _spawnQueue: number[] = [];\r\n\r\n    private _reset() {\r\n        this._isCompleted = false;\r\n        this._waveElapsedTime = 0;\r\n        this._nextSpawnTime = 0;\r\n        this._nextSpawnIndex = 0;\r\n        this._spawnQueue.length = 0;\r\n        // this._spawnQueue = this.waveData.planeList;\r\n    }\r\n\r\n    onLoad() {\r\n        if (this.waveData && this.waveData.spawnOrder === eSpawnOrder.Random) {\r\n            this._totalWeight = 0;\r\n            // add up _totalWeight if is random\r\n            this.waveData.spawnGroup.forEach((group) => {\r\n                this._totalWeight += group.weight;\r\n                group.selfWeight = this._totalWeight;\r\n            });\r\n        }\r\n    }\r\n\r\n    trigger() {\r\n        this._reset();\r\n\r\n        // 对于固定数量的波次，可以预先生成队列，每次从里面取即可\r\n        if (this.waveData) {\r\n            this._waveCompleteParam = this.waveData.waveCompletionParam.eval();\r\n            if (this.waveData.waveCompletion === eWaveCompletion.SpawnCount) {\r\n                if (this.waveData.spawnOrder === eSpawnOrder.Random) {\r\n                    for (let i = 0; i < this._waveCompleteParam; i++) {\r\n                        const randomWeight = Math.random() * this._totalWeight;\r\n                        for (const group of this.waveData.spawnGroup) {\r\n                            if (randomWeight <= group.selfWeight) {\r\n                                this._spawnQueue.push(group.planeID);\r\n                                break;\r\n                            }\r\n                        }\r\n                    }\r\n                } else {\r\n                    for (let i = 0; i < this._waveCompleteParam; i++) {\r\n                        // 通过取余实现循环\r\n                        this._spawnQueue.push(this.waveData.spawnGroup[i % this.waveData.spawnGroup.length].planeID);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    // tick wave\r\n    tick(dtInMiliseconds: number) {\r\n        if (this._isCompleted) return;\r\n\r\n        this._waveElapsedTime += dtInMiliseconds;\r\n        if (this.waveData.waveCompletion === eWaveCompletion.SpawnCount) {\r\n            // 产出固定数量的波次\r\n            if (this._waveElapsedTime >= this._nextSpawnTime) {\r\n                if (!this.spawnFromQueue()) {\r\n                    this._isCompleted = true;\r\n                }\r\n            }\r\n        }\r\n        else {\r\n            // 完全根据时间的波次\r\n            if (this._waveElapsedTime >= this._waveCompleteParam) {\r\n                this._isCompleted = true;\r\n            } else {\r\n                if (this._waveElapsedTime >= this._nextSpawnTime) {\r\n                    this.spawnFromGroup();\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    private spawnFromQueue(): boolean {        \r\n        if (this._nextSpawnIndex >= this._spawnQueue.length) {\r\n            return false;\r\n        }\r\n\r\n        this.spawnSingleFromQueue(this._nextSpawnIndex++);\r\n        this._nextSpawnTime = this._waveElapsedTime + this.waveData.spawnInterval.eval();\r\n        return true;\r\n    }\r\n\r\n    private spawnSingleFromQueue(index: number): void {\r\n        if (index >= this._spawnQueue.length) {\r\n            return;\r\n        }\r\n\r\n        let spawnPos = this.waveData.spawnPos;\r\n        let spawnAngle = this.waveData.spawnAngle.eval();\r\n        // let spawnSpeed = this.waveData.spawnSpeed.eval();\r\n\r\n        this.createPlane(this._spawnQueue[index], spawnPos, spawnAngle);\r\n    }\r\n\r\n    private spawnFromGroup(): void {\r\n        let spawnPos = this.waveData.spawnPos;\r\n        let spawnAngle = this.waveData.spawnAngle.eval();\r\n        // let spawnSpeed = this.waveData.spawnSpeed.eval();\r\n\r\n        if (this.waveData.spawnOrder === eSpawnOrder.Random) {\r\n            const randomWeight = Math.random() * this._totalWeight;\r\n            for (const group of this.waveData.spawnGroup) {\r\n                if (randomWeight <= group.selfWeight) {\r\n                    this.createPlane(group.planeID, spawnPos, spawnAngle);\r\n                    break;\r\n                }\r\n            }\r\n        } else {\r\n            this.createPlane(this.waveData.spawnGroup[this._nextSpawnIndex++ % this.waveData.spawnGroup.length].planeID, spawnPos, spawnAngle);\r\n        }\r\n    }\r\n\r\n    private async createPlane(planeId: number, pos: Vec2, angle: number) {\r\n        let enemy = GameIns.enemyManager.addPlane(planeId, null);\r\n        if (enemy) {\r\n            // enemy.initTrack(this.waveData.trackGroups, this.waveData.liveParam, spawnPos.x, spawnPos.y);\r\n            // enemy.setStandByTime(0);\r\n            // console.log(\"createPlane\", planeId, pos, angle, speed);\r\n            const nodePosition = this.node.worldPosition;\r\n            enemy.setPos(nodePosition.x + pos.x, nodePosition.y + pos.y);\r\n            // enemy.setPos(pos.x, pos.y);\r\n            // enemy.initMove(speed, angle, this.waveData.delayDestroy);\r\n        }\r\n    }\r\n\r\n}"]}