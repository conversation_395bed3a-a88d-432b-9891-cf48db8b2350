System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Color, Label, math, Node, Sprite, BundleName, BaseUI, UILayer, UIMgr, ResTaskClass, DataMgr, EventMgr, HomeUIEvent, ButtonPlus, List, ProgressPanel, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _crd, ccclass, property, TaskUI;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfBundleName(extras) {
    _reporterNs.report("BundleName", "db://assets/bundles/common/script/const/BundleConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBaseUI(extras) {
    _reporterNs.report("BaseUI", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUILayer(extras) {
    _reporterNs.report("UILayer", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIOpt(extras) {
    _reporterNs.report("UIOpt", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfResTaskClass(extras) {
    _reporterNs.report("ResTaskClass", "../../autogen/luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDataMgr(extras) {
    _reporterNs.report("DataMgr", "../../data/DataManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventMgr(extras) {
    _reporterNs.report("EventMgr", "../../event/EventManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfHomeUIEvent(extras) {
    _reporterNs.report("HomeUIEvent", "../../event/HomeUIEvent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfButtonPlus(extras) {
    _reporterNs.report("ButtonPlus", "../common/components/button/ButtonPlus", _context.meta, extras);
  }

  function _reportPossibleCrUseOfList(extras) {
    _reporterNs.report("List", "../common/components/list/List", _context.meta, extras);
  }

  function _reportPossibleCrUseOfProgressPanel(extras) {
    _reporterNs.report("ProgressPanel", "./components/ProgressPanel", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTaskItem(extras) {
    _reporterNs.report("TaskItem", "./components/TaskItem", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Color = _cc.Color;
      Label = _cc.Label;
      math = _cc.math;
      Node = _cc.Node;
      Sprite = _cc.Sprite;
    }, function (_unresolved_2) {
      BundleName = _unresolved_2.BundleName;
    }, function (_unresolved_3) {
      BaseUI = _unresolved_3.BaseUI;
      UILayer = _unresolved_3.UILayer;
      UIMgr = _unresolved_3.UIMgr;
    }, function (_unresolved_4) {
      ResTaskClass = _unresolved_4.ResTaskClass;
    }, function (_unresolved_5) {
      DataMgr = _unresolved_5.DataMgr;
    }, function (_unresolved_6) {
      EventMgr = _unresolved_6.EventMgr;
    }, function (_unresolved_7) {
      HomeUIEvent = _unresolved_7.HomeUIEvent;
    }, function (_unresolved_8) {
      ButtonPlus = _unresolved_8.ButtonPlus;
    }, function (_unresolved_9) {
      List = _unresolved_9.default;
    }, function (_unresolved_10) {
      ProgressPanel = _unresolved_10.ProgressPanel;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "409a5OcnLZJJbyaYQAvWw08", "TaskUI", undefined);

      __checkObsolete__(['_decorator', 'Color', 'Label', 'math', 'Node', 'Sprite']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("TaskUI", TaskUI = (_dec = ccclass('TaskUI'), _dec2 = property(Label), _dec3 = property(_crd && List === void 0 ? (_reportPossibleCrUseOfList({
        error: Error()
      }), List) : List), _dec4 = property(_crd && List === void 0 ? (_reportPossibleCrUseOfList({
        error: Error()
      }), List) : List), _dec5 = property(Node), _dec6 = property(_crd && ProgressPanel === void 0 ? (_reportPossibleCrUseOfProgressPanel({
        error: Error()
      }), ProgressPanel) : ProgressPanel), _dec(_class = (_class2 = class TaskUI extends (_crd && BaseUI === void 0 ? (_reportPossibleCrUseOfBaseUI({
        error: Error()
      }), BaseUI) : BaseUI) {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "title", _descriptor, this);

          _initializerDefineProperty(this, "dailyOrWeekList", _descriptor2, this);

          _initializerDefineProperty(this, "achievementList", _descriptor3, this);

          _initializerDefineProperty(this, "tabBtnContainer", _descriptor4, this);

          _initializerDefineProperty(this, "progressPanel", _descriptor5, this);

          this._currentTaskClass = (_crd && ResTaskClass === void 0 ? (_reportPossibleCrUseOfResTaskClass({
            error: Error()
          }), ResTaskClass) : ResTaskClass).DAILY_TASK;
          this._tabBtns = [];
          this._btnTaskClassList = [(_crd && ResTaskClass === void 0 ? (_reportPossibleCrUseOfResTaskClass({
            error: Error()
          }), ResTaskClass) : ResTaskClass).DAILY_TASK, (_crd && ResTaskClass === void 0 ? (_reportPossibleCrUseOfResTaskClass({
            error: Error()
          }), ResTaskClass) : ResTaskClass).WEEKLY_TASK, (_crd && ResTaskClass === void 0 ? (_reportPossibleCrUseOfResTaskClass({
            error: Error()
          }), ResTaskClass) : ResTaskClass).ACHIEVEMENT];
        }

        static getUrl() {
          return "prefab/ui/TaskUI";
        }

        static getLayer() {
          return (_crd && UILayer === void 0 ? (_reportPossibleCrUseOfUILayer({
            error: Error()
          }), UILayer) : UILayer).Background;
        }

        static getBundleName() {
          return (_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
            error: Error()
          }), BundleName) : BundleName).HomeTask;
        }

        static getUIOption() {
          return {
            isClickBgHideUI: true
          };
        }

        onLoad() {
          this.tabBtnContainer.children.forEach((c, index) => {
            var btn = c.getComponent(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
              error: Error()
            }), ButtonPlus) : ButtonPlus);

            this._tabBtns.push(btn);

            btn.addClick(() => {
              this.switchTaskClass(this._btnTaskClassList[index]);
            }, this);
          });
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).once((_crd && HomeUIEvent === void 0 ? (_reportPossibleCrUseOfHomeUIEvent({
            error: Error()
          }), HomeUIEvent) : HomeUIEvent).Leave, this.onLeave, this);
        }

        onLeave() {
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).off((_crd && HomeUIEvent === void 0 ? (_reportPossibleCrUseOfHomeUIEvent({
            error: Error()
          }), HomeUIEvent) : HomeUIEvent).Leave, this.onLeave, this);
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).closeUI(TaskUI);
        }

        onShow() {
          var _this = this;

          return _asyncToGenerator(function* () {
            _this.switchTaskClass((_crd && ResTaskClass === void 0 ? (_reportPossibleCrUseOfResTaskClass({
              error: Error()
            }), ResTaskClass) : ResTaskClass).DAILY_TASK);
          })();
        }

        switchTaskClass(taskClass) {
          this._currentTaskClass = taskClass;

          this._tabBtns.forEach((btn, index) => {
            if (taskClass == this._btnTaskClassList[index]) {
              btn.interactable = false;
              btn.getComponent(Sprite).color = Color.GRAY;
            } else {
              btn.interactable = true;
              btn.getComponent(Sprite).color = math.color("#8DB0E1");
            }
          });

          var taskList = (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).task.getTaskListByClass(taskClass);
          this.dailyOrWeekList.numItems = taskList.length;

          switch (taskClass) {
            case (_crd && ResTaskClass === void 0 ? (_reportPossibleCrUseOfResTaskClass({
              error: Error()
            }), ResTaskClass) : ResTaskClass).DAILY_TASK:
            case (_crd && ResTaskClass === void 0 ? (_reportPossibleCrUseOfResTaskClass({
              error: Error()
            }), ResTaskClass) : ResTaskClass).WEEKLY_TASK:
              this.dailyOrWeekList.node.active = true;
              this.achievementList.node.active = false;
              this.progressPanel.node.active = true;
              this.progressPanel.init(taskList);
              this.title.string = "任务";
              break;

            case (_crd && ResTaskClass === void 0 ? (_reportPossibleCrUseOfResTaskClass({
              error: Error()
            }), ResTaskClass) : ResTaskClass).ACHIEVEMENT:
              this.dailyOrWeekList.node.active = false;
              this.achievementList.node.active = true;
              this.title.string = "成就";
              break;

            default:
              break;
          }
        }

        OnListRender(item, index) {
          var taskList = (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).task.getTaskListByClass(this._currentTaskClass);
          item.onRender(taskList[index]);
        }

        onHide() {
          return _asyncToGenerator(function* () {})();
        }

        onClose() {
          return _asyncToGenerator(function* () {})();
        }

        update(dt) {}

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "title", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "dailyOrWeekList", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "achievementList", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "tabBtnContainer", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "progressPanel", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=ff790ef906fea958f16bb343d2760e0a7df9e3f3.js.map