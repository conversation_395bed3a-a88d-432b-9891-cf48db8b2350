{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/task/TaskUI.ts"], "names": ["_decorator", "Color", "Label", "math", "Node", "Sprite", "BundleName", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "ResTaskClass", "DataMgr", "EventMgr", "HomeUIEvent", "ButtonPlus", "List", "ProgressPanel", "ccclass", "property", "TaskUI", "_currentTaskClass", "DAILY_TASK", "_tabBtns", "_btnTaskClassList", "WEEKLY_TASK", "ACHIEVEMENT", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "Background", "getBundleName", "HomeTask", "getUIOption", "isClickBgHideUI", "onLoad", "tabBtnContainer", "children", "for<PERSON>ach", "c", "index", "btn", "getComponent", "push", "addClick", "switchTaskClass", "once", "Leave", "onLeave", "off", "closeUI", "onShow", "taskClass", "interactable", "color", "GRAY", "taskList", "task", "getTaskListByClass", "dailyOrWeekList", "numItems", "length", "node", "active", "achievementList", "progressPanel", "init", "title", "string", "OnList<PERSON>ender", "item", "onRender", "onHide", "onClose", "update", "dt"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;;AACtCC,MAAAA,U,iBAAAA,U;;AAEAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,U,iBAAAA,U;;AACFC,MAAAA,I;;AACEC,MAAAA,a,kBAAAA,a;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBlB,U;;wBAIjBmB,M,WADZF,OAAO,CAAC,QAAD,C,UAQHC,QAAQ,CAAChB,KAAD,C,UAERgB,QAAQ;AAAA;AAAA,uB,UAERA,QAAQ;AAAA;AAAA,uB,UAERA,QAAQ,CAACd,IAAD,C,UAERc,QAAQ;AAAA;AAAA,yC,2BAhBb,MACaC,MADb;AAAA;AAAA,4BACmC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAkBvBC,iBAlBuB,GAkBW;AAAA;AAAA,4CAAaC,UAlBxB;AAAA,eAmBvBC,QAnBuB,GAmBE,EAnBF;AAAA,eAoBvBC,iBApBuB,GAoBa,CAAC;AAAA;AAAA,4CAAaF,UAAd,EAA0B;AAAA;AAAA,4CAAaG,WAAvC,EAAoD;AAAA;AAAA,4CAAaC,WAAjE,CApBb;AAAA;;AACX,eAANC,MAAM,GAAW;AAAE,iBAAO,kBAAP;AAA4B;;AACvC,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,UAAf;AAA2B;;AACpC,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,QAAlB;AAA4B;;AAC3C,eAAXC,WAAW,GAAU;AAC/B,iBAAO;AAAEC,YAAAA,eAAe,EAAE;AAAnB,WAAP;AACH;;AAgBSC,QAAAA,MAAM,GAAS;AACrB,eAAKC,eAAL,CAAsBC,QAAtB,CAA+BC,OAA/B,CAAuC,CAACC,CAAD,EAAIC,KAAJ,KAAc;AACjD,gBAAMC,GAAG,GAAGF,CAAC,CAACG,YAAF;AAAA;AAAA,yCAAZ;;AACA,iBAAKlB,QAAL,CAAcmB,IAAd,CAAmBF,GAAnB;;AACAA,YAAAA,GAAG,CAACG,QAAJ,CAAa,MAAM;AACf,mBAAKC,eAAL,CAAqB,KAAKpB,iBAAL,CAAuBe,KAAvB,CAArB;AACH,aAFD,EAEG,IAFH;AAGH,WAND;AAOA;AAAA;AAAA,oCAASM,IAAT,CAAc;AAAA;AAAA,0CAAYC,KAA1B,EAAiC,KAAKC,OAAtC,EAA+C,IAA/C;AACH;;AAEOA,QAAAA,OAAO,GAAG;AACd;AAAA;AAAA,oCAASC,GAAT,CAAa;AAAA;AAAA,0CAAYF,KAAzB,EAAgC,KAAKC,OAArC,EAA8C,IAA9C;AACA;AAAA;AAAA,8BAAME,OAAN,CAAc7B,MAAd;AACH;;AAEK8B,QAAAA,MAAM,GAAgC;AAAA;;AAAA;AACxC,YAAA,KAAI,CAACN,eAAL,CAAqB;AAAA;AAAA,8CAAatB,UAAlC;AADwC;AAE3C;;AAEOsB,QAAAA,eAAe,CAACO,SAAD,EAA0B;AAC7C,eAAK9B,iBAAL,GAAyB8B,SAAzB;;AACA,eAAK5B,QAAL,CAAcc,OAAd,CAAsB,CAACG,GAAD,EAAMD,KAAN,KAAgB;AAClC,gBAAIY,SAAS,IAAI,KAAK3B,iBAAL,CAAuBe,KAAvB,CAAjB,EAAgD;AAC5CC,cAAAA,GAAG,CAACY,YAAJ,GAAmB,KAAnB;AACAZ,cAAAA,GAAG,CAACC,YAAJ,CAAiBnC,MAAjB,EAA0B+C,KAA1B,GAAkCnD,KAAK,CAACoD,IAAxC;AACH,aAHD,MAGO;AACHd,cAAAA,GAAG,CAACY,YAAJ,GAAmB,IAAnB;AACAZ,cAAAA,GAAG,CAACC,YAAJ,CAAiBnC,MAAjB,EAA0B+C,KAA1B,GAAkCjD,IAAI,CAACiD,KAAL,CAAW,SAAX,CAAlC;AACH;AACJ,WARD;;AASA,cAAME,QAAQ,GAAG;AAAA;AAAA,kCAAQC,IAAR,CAAaC,kBAAb,CAAgCN,SAAhC,CAAjB;AACA,eAAKO,eAAL,CAAsBC,QAAtB,GAAiCJ,QAAQ,CAACK,MAA1C;;AACA,kBAAQT,SAAR;AACI,iBAAK;AAAA;AAAA,8CAAa7B,UAAlB;AACA,iBAAK;AAAA;AAAA,8CAAaG,WAAlB;AACI,mBAAKiC,eAAL,CAAsBG,IAAtB,CAA2BC,MAA3B,GAAoC,IAApC;AACA,mBAAKC,eAAL,CAAsBF,IAAtB,CAA2BC,MAA3B,GAAoC,KAApC;AACA,mBAAKE,aAAL,CAAoBH,IAApB,CAAyBC,MAAzB,GAAkC,IAAlC;AACA,mBAAKE,aAAL,CAAoBC,IAApB,CAAyBV,QAAzB;AACA,mBAAKW,KAAL,CAAYC,MAAZ,GAAqB,IAArB;AACA;;AACJ,iBAAK;AAAA;AAAA,8CAAazC,WAAlB;AACI,mBAAKgC,eAAL,CAAsBG,IAAtB,CAA2BC,MAA3B,GAAoC,KAApC;AACA,mBAAKC,eAAL,CAAsBF,IAAtB,CAA2BC,MAA3B,GAAoC,IAApC;AACA,mBAAKI,KAAL,CAAYC,MAAZ,GAAqB,IAArB;AACA;;AACJ;AACI;AAfR;AAiBH;;AAEDC,QAAAA,YAAY,CAACC,IAAD,EAAiB9B,KAAjB,EAAgC;AACxC,cAAMgB,QAAQ,GAAG;AAAA;AAAA,kCAAQC,IAAR,CAAaC,kBAAb,CAAgC,KAAKpC,iBAArC,CAAjB;AACAgD,UAAAA,IAAI,CAACC,QAAL,CAAcf,QAAQ,CAAChB,KAAD,CAAtB;AACH;;AAEKgC,QAAAA,MAAM,GAAgC;AAAA;AAC3C;;AACKC,QAAAA,OAAO,GAAgC;AAAA;AAC5C;;AACSC,QAAAA,MAAM,CAACC,EAAD,EAAmB,CAClC;;AApF8B,O;;;;;iBAQT,I;;;;;;;iBAES,I;;;;;;;iBAEA,I;;;;;;;iBAEA,I;;;;;;;iBAEO,I", "sourcesContent": ["import { _decorator, Color, Label, math, Node, Sprite } from 'cc';\nimport { BundleName } from 'db://assets/bundles/common/script/const/BundleConst';\n\nimport { BaseUI, UILayer, UIMgr, UIOpt } from 'db://assets/scripts/core/base/UIMgr';\nimport { ResTaskClass } from '../../autogen/luban/schema';\nimport { DataMgr } from '../../data/DataManager';\nimport { EventMgr } from '../../event/EventManager';\nimport { HomeUIEvent } from '../../event/HomeUIEvent';\nimport { ButtonPlus } from '../common/components/button/ButtonPlus';\nimport List from '../common/components/list/List';\nimport { ProgressPanel } from './components/ProgressPanel';\nimport { TaskItem } from './components/TaskItem';\nconst { ccclass, property } = _decorator;\n\n\n@ccclass('TaskUI')\nexport class TaskUI extends BaseUI {\n    public static getUrl(): string { return \"prefab/ui/TaskUI\"; }\n    public static getLayer(): UILayer { return UILayer.Background }\n    public static getBundleName(): string { return BundleName.HomeTask }\n    public static getUIOption(): UIOpt {\n        return { isClickBgHideUI: true }\n    }\n    @property(Label)\n    title: Label | null = null;\n    @property(List)\n    dailyOrWeekList: List | null = null;\n    @property(List)\n    achievementList: List | null = null;\n    @property(Node)\n    tabBtnContainer: Node | null = null;\n    @property(ProgressPanel)\n    progressPanel: ProgressPanel | null = null;\n\n    private _currentTaskClass: ResTaskClass = ResTaskClass.DAILY_TASK;\n    private _tabBtns: ButtonPlus[] = [];\n    private _btnTaskClassList: ResTaskClass[] = [ResTaskClass.DAILY_TASK, ResTaskClass.WEEKLY_TASK, ResTaskClass.ACHIEVEMENT];\n\n    protected onLoad(): void {\n        this.tabBtnContainer!.children.forEach((c, index) => {\n            const btn = c.getComponent(ButtonPlus)!\n            this._tabBtns.push(btn);\n            btn.addClick(() => {\n                this.switchTaskClass(this._btnTaskClassList[index]);\n            }, this)\n        })\n        EventMgr.once(HomeUIEvent.Leave, this.onLeave, this)\n    }\n\n    private onLeave() {\n        EventMgr.off(HomeUIEvent.Leave, this.onLeave, this)\n        UIMgr.closeUI(TaskUI)\n    }\n\n    async onShow(...args: any[]): Promise<void> {\n        this.switchTaskClass(ResTaskClass.DAILY_TASK);\n    }\n\n    private switchTaskClass(taskClass: ResTaskClass) {\n        this._currentTaskClass = taskClass\n        this._tabBtns.forEach((btn, index) => {\n            if (taskClass == this._btnTaskClassList[index]) {\n                btn.interactable = false;\n                btn.getComponent(Sprite)!.color = Color.GRAY;\n            } else {\n                btn.interactable = true;\n                btn.getComponent(Sprite)!.color = math.color(\"#8DB0E1\");\n            }\n        })\n        const taskList = DataMgr.task.getTaskListByClass(taskClass)\n        this.dailyOrWeekList!.numItems = taskList.length;\n        switch (taskClass) {\n            case ResTaskClass.DAILY_TASK:\n            case ResTaskClass.WEEKLY_TASK:\n                this.dailyOrWeekList!.node.active = true;\n                this.achievementList!.node.active = false;\n                this.progressPanel!.node.active = true;\n                this.progressPanel!.init(taskList);\n                this.title!.string = \"任务\";\n                break;\n            case ResTaskClass.ACHIEVEMENT:\n                this.dailyOrWeekList!.node.active = false;\n                this.achievementList!.node.active = true;\n                this.title!.string = \"成就\";\n                break;\n            default:\n                break;\n        }\n    }\n\n    OnListRender(item: TaskItem, index: number) {\n        const taskList = DataMgr.task.getTaskListByClass(this._currentTaskClass);\n        item.onRender(taskList[index]);\n    }\n\n    async onHide(...args: any[]): Promise<void> {\n    }\n    async onClose(...args: any[]): Promise<void> {\n    }\n    protected update(dt: number): void {\n    }\n\n}\n\n"]}