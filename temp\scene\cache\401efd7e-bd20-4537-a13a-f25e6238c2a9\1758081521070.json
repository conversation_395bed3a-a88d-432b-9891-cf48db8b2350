"[\n  {\n    \"__type__\": \"cc.SceneAsset\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_native\": \"\",\n    \"scene\": {\n      \"__id__\": 1\n    }\n  },\n  {\n    \"__type__\": \"cc.Scene\",\n    \"_name\": \"LevelEditor\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": null,\n    \"_children\": [\n      {\n        \"__id__\": 2\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": {\n      \"__id__\": 405\n    },\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"autoReleaseAssets\": false,\n    \"_globals\": {\n      \"__id__\": 406\n    },\n    \"_id\": \"401efd7e-bd20-4537-a13a-f25e6238c2a9\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Canvas\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 1\n    },\n    \"_children\": [\n      {\n        \"__id__\": 3\n      },\n      {\n        \"__id__\": 5\n      },\n      {\n        \"__id__\": 100\n      },\n      {\n        \"__id__\": 332\n      },\n      {\n        \"__id__\": 346\n      },\n      {\n        \"__id__\": 357\n      },\n      {\n        \"__id__\": 366\n      },\n      {\n        \"__id__\": 373\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 382\n      },\n      {\n        \"__id__\": 383\n      },\n      {\n        \"__id__\": 384\n      },\n      {\n        \"__id__\": 385\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 375,\n      \"y\": 667,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"beI88Z2HpFELqR4T5EMHpg\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Camera\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 2\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 4\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 1000\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"ebFwiq8gBFaYpqYbdoDODe\"\n  },\n  {\n    \"__type__\": \"cc.Camera\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 3\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_projection\": 0,\n    \"_priority\": 0,\n    \"_fov\": 45,\n    \"_fovAxis\": 0,\n    \"_orthoHeight\": 667,\n    \"_near\": 0,\n    \"_far\": 2000,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_depth\": 1,\n    \"_stencil\": 0,\n    \"_clearFlags\": 7,\n    \"_rect\": {\n      \"__type__\": \"cc.Rect\",\n      \"x\": 0,\n      \"y\": 0,\n      \"width\": 1,\n      \"height\": 1\n    },\n    \"_aperture\": 19,\n    \"_shutter\": 7,\n    \"_iso\": 0,\n    \"_screenScale\": 1,\n    \"_visibility\": 1108344832,\n    \"_targetTexture\": null,\n    \"_postProcess\": null,\n    \"_usePostProcess\": false,\n    \"_cameraType\": -1,\n    \"_trackingType\": 0,\n    \"_id\": \"63WIch3o5BEYRlXzTT0oWc\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"BackgroundLayer\",\n    \"_objFlags\": 512,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 2\n    },\n    \"_children\": [\n      {\n        \"__id__\": 6\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 99\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"46ihxbAfVPT4CTuCqzBBFA\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"layer\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 5\n    },\n    \"_children\": [\n      {\n        \"__id__\": 7\n      },\n      {\n        \"__id__\": 93\n      },\n      {\n        \"__id__\": 94\n      },\n      {\n        \"__id__\": 95\n      },\n      {\n        \"__id__\": 96\n      },\n      {\n        \"__id__\": 97\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 98\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": -1158.9999999999975,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"fdDqnZ5FJIxa90qgrtP4Qm\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"backgrounds\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 6\n    },\n    \"_children\": [\n      {\n        \"__id__\": 8\n      },\n      {\n        \"__id__\": 13\n      },\n      {\n        \"__id__\": 18\n      },\n      {\n        \"__id__\": 23\n      },\n      {\n        \"__id__\": 28\n      },\n      {\n        \"__id__\": 33\n      },\n      {\n        \"__id__\": 38\n      },\n      {\n        \"__id__\": 43\n      },\n      {\n        \"__id__\": 48\n      },\n      {\n        \"__id__\": 53\n      },\n      {\n        \"__id__\": 58\n      },\n      {\n        \"__id__\": 63\n      },\n      {\n        \"__id__\": 68\n      },\n      {\n        \"__id__\": 73\n      },\n      {\n        \"__id__\": 78\n      },\n      {\n        \"__id__\": 83\n      },\n      {\n        \"__id__\": 88\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"2donQbELJIhIQ4FJqDLboZ\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 7\n    },\n    \"_prefab\": {\n      \"__id__\": 9\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 8\n    },\n    \"asset\": {\n      \"__uuid__\": \"4c4ae1d7-4d0f-420e-bffc-195807ab0c8d\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 10\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"dfo1mIuc9O8Z5HSGydquIA\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [\n      {\n        \"__id__\": 11\n      }\n    ],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 12\n    },\n    \"propertyPath\": [\n      \"_lpos\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": -67,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"c46/YsCPVOJYA4mWEpNYRx\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 7\n    },\n    \"_prefab\": {\n      \"__id__\": 14\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 13\n    },\n    \"asset\": {\n      \"__uuid__\": \"4c4ae1d7-4d0f-420e-bffc-195807ab0c8d\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 15\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"e6MBmafNdGQrN0zF4K1udl\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [\n      {\n        \"__id__\": 16\n      }\n    ],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 17\n    },\n    \"propertyPath\": [\n      \"_lpos\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 1133,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"c46/YsCPVOJYA4mWEpNYRx\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 7\n    },\n    \"_prefab\": {\n      \"__id__\": 19\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 18\n    },\n    \"asset\": {\n      \"__uuid__\": \"4c4ae1d7-4d0f-420e-bffc-195807ab0c8d\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 20\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"93DcuOir5BfZV0cCZj6Yz3\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [\n      {\n        \"__id__\": 21\n      }\n    ],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 22\n    },\n    \"propertyPath\": [\n      \"_lpos\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 2333,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"c46/YsCPVOJYA4mWEpNYRx\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 7\n    },\n    \"_prefab\": {\n      \"__id__\": 24\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 23\n    },\n    \"asset\": {\n      \"__uuid__\": \"4c4ae1d7-4d0f-420e-bffc-195807ab0c8d\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 25\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"02zCKSxQpJX5Ufer+3oSn8\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [\n      {\n        \"__id__\": 26\n      }\n    ],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 27\n    },\n    \"propertyPath\": [\n      \"_lpos\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 3533,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"c46/YsCPVOJYA4mWEpNYRx\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 7\n    },\n    \"_prefab\": {\n      \"__id__\": 29\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 28\n    },\n    \"asset\": {\n      \"__uuid__\": \"4c4ae1d7-4d0f-420e-bffc-195807ab0c8d\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 30\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"ff3BwVFHJKy5MtmWkfF+Gd\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [\n      {\n        \"__id__\": 31\n      }\n    ],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 32\n    },\n    \"propertyPath\": [\n      \"_lpos\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 4733,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"c46/YsCPVOJYA4mWEpNYRx\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 7\n    },\n    \"_prefab\": {\n      \"__id__\": 34\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 33\n    },\n    \"asset\": {\n      \"__uuid__\": \"4c4ae1d7-4d0f-420e-bffc-195807ab0c8d\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 35\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"e1UUFeP7NEPrUyTnBRfP3B\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [\n      {\n        \"__id__\": 36\n      }\n    ],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 37\n    },\n    \"propertyPath\": [\n      \"_lpos\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 5933,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"c46/YsCPVOJYA4mWEpNYRx\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 7\n    },\n    \"_prefab\": {\n      \"__id__\": 39\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 38\n    },\n    \"asset\": {\n      \"__uuid__\": \"4c4ae1d7-4d0f-420e-bffc-195807ab0c8d\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 40\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"8fgVgUFXZI05wLTYi+ssxj\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [\n      {\n        \"__id__\": 41\n      }\n    ],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 42\n    },\n    \"propertyPath\": [\n      \"_lpos\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 7133,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"c46/YsCPVOJYA4mWEpNYRx\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 7\n    },\n    \"_prefab\": {\n      \"__id__\": 44\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 43\n    },\n    \"asset\": {\n      \"__uuid__\": \"4c4ae1d7-4d0f-420e-bffc-195807ab0c8d\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 45\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"2dnsD2FRdPZbpiOIbWM6Cu\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [\n      {\n        \"__id__\": 46\n      }\n    ],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 47\n    },\n    \"propertyPath\": [\n      \"_lpos\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 8333,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"c46/YsCPVOJYA4mWEpNYRx\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 7\n    },\n    \"_prefab\": {\n      \"__id__\": 49\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 48\n    },\n    \"asset\": {\n      \"__uuid__\": \"4c4ae1d7-4d0f-420e-bffc-195807ab0c8d\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 50\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"2eKMX9jopCxqT7kQNRpjVE\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [\n      {\n        \"__id__\": 51\n      }\n    ],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 52\n    },\n    \"propertyPath\": [\n      \"_lpos\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 9533,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"c46/YsCPVOJYA4mWEpNYRx\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 7\n    },\n    \"_prefab\": {\n      \"__id__\": 54\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 53\n    },\n    \"asset\": {\n      \"__uuid__\": \"4c4ae1d7-4d0f-420e-bffc-195807ab0c8d\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 55\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"f0cruchAJCwbPPJLQw7RE7\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [\n      {\n        \"__id__\": 56\n      }\n    ],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 57\n    },\n    \"propertyPath\": [\n      \"_lpos\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 10733,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"c46/YsCPVOJYA4mWEpNYRx\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 7\n    },\n    \"_prefab\": {\n      \"__id__\": 59\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 58\n    },\n    \"asset\": {\n      \"__uuid__\": \"4c4ae1d7-4d0f-420e-bffc-195807ab0c8d\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 60\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"c9MZ498/VNlIMeEJMXW/Mv\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [\n      {\n        \"__id__\": 61\n      }\n    ],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 62\n    },\n    \"propertyPath\": [\n      \"_lpos\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 11933,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"c46/YsCPVOJYA4mWEpNYRx\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 7\n    },\n    \"_prefab\": {\n      \"__id__\": 64\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 63\n    },\n    \"asset\": {\n      \"__uuid__\": \"4c4ae1d7-4d0f-420e-bffc-195807ab0c8d\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 65\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"88otl1CFNP+o6A1ZDmnOVL\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [\n      {\n        \"__id__\": 66\n      }\n    ],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 67\n    },\n    \"propertyPath\": [\n      \"_lpos\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 13133,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"c46/YsCPVOJYA4mWEpNYRx\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 7\n    },\n    \"_prefab\": {\n      \"__id__\": 69\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 68\n    },\n    \"asset\": {\n      \"__uuid__\": \"4c4ae1d7-4d0f-420e-bffc-195807ab0c8d\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 70\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"31GvDRKhZD5rn+5CKHeJm2\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [\n      {\n        \"__id__\": 71\n      }\n    ],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 72\n    },\n    \"propertyPath\": [\n      \"_lpos\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 14333,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"c46/YsCPVOJYA4mWEpNYRx\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 7\n    },\n    \"_prefab\": {\n      \"__id__\": 74\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 73\n    },\n    \"asset\": {\n      \"__uuid__\": \"4c4ae1d7-4d0f-420e-bffc-195807ab0c8d\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 75\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"d93Q+oIoFGZqGjOV6fLXmr\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [\n      {\n        \"__id__\": 76\n      }\n    ],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 77\n    },\n    \"propertyPath\": [\n      \"_lpos\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 15533,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"c46/YsCPVOJYA4mWEpNYRx\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 7\n    },\n    \"_prefab\": {\n      \"__id__\": 79\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 78\n    },\n    \"asset\": {\n      \"__uuid__\": \"4c4ae1d7-4d0f-420e-bffc-195807ab0c8d\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 80\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"cfI2g49KhG7Zj6Fryt8vZV\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [\n      {\n        \"__id__\": 81\n      }\n    ],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 82\n    },\n    \"propertyPath\": [\n      \"_lpos\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 16733,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"c46/YsCPVOJYA4mWEpNYRx\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 7\n    },\n    \"_prefab\": {\n      \"__id__\": 84\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 83\n    },\n    \"asset\": {\n      \"__uuid__\": \"4c4ae1d7-4d0f-420e-bffc-195807ab0c8d\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 85\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"73F/k9qPlOHIki9m8ESCJO\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [\n      {\n        \"__id__\": 86\n      }\n    ],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 87\n    },\n    \"propertyPath\": [\n      \"_lpos\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 17933,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"c46/YsCPVOJYA4mWEpNYRx\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 7\n    },\n    \"_prefab\": {\n      \"__id__\": 89\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 88\n    },\n    \"asset\": {\n      \"__uuid__\": \"4c4ae1d7-4d0f-420e-bffc-195807ab0c8d\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 90\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"fbdniVNWlO3Z9zdFKtefL7\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [\n      {\n        \"__id__\": 91\n      }\n    ],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 92\n    },\n    \"propertyPath\": [\n      \"_lpos\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 19133,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"c46/YsCPVOJYA4mWEpNYRx\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"terrains\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 6\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"admejcQXxDDII8XFIlSfXm\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"scrolls\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 6\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"c1WlwoFVFFB4WDZcB3oViK\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"dynamic\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 6\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"5b1g4rrxBB/6jACYmOuWAv\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"waves\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 6\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"55l7yOctxB0rYr1E+n6LWW\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"events\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 6\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"8fJShyBNVJZ6TUnCKPuKRJ\"\n  },\n  {\n    \"__type__\": \"92d5epe+XRMm6NUwiCSJBKR\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 6\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_id\": \"a7+URy9AxJAaUA5eK9j7Wj\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 5\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 100,\n      \"height\": 100\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"c7cr3UHz9ID5OL86RVgF3A\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"FloorLayers\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 2\n    },\n    \"_children\": [\n      {\n        \"__id__\": 101\n      },\n      {\n        \"__id__\": 144\n      },\n      {\n        \"__id__\": 152\n      },\n      {\n        \"__id__\": 159\n      },\n      {\n        \"__id__\": 167\n      },\n      {\n        \"__id__\": 315\n      },\n      {\n        \"__id__\": 322\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"62oMpiqNVJeLfnCzN4mnAI\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"layer_6\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 100\n    },\n    \"_children\": [\n      {\n        \"__id__\": 102\n      },\n      {\n        \"__id__\": 103\n      },\n      {\n        \"__id__\": 104\n      },\n      {\n        \"__id__\": 134\n      },\n      {\n        \"__id__\": 135\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 143\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": -1158.9999999999975,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"0cfslUroBIY4S9ZmhUwGCY\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"terrains\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 101\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"35QFu47iZKwZLFGCqS6G5U\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"scrolls\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 101\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"42KVSVxyNKLINXZAfoHmG3\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"dynamic\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 101\n    },\n    \"_children\": [\n      {\n        \"__id__\": 105\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"1eQ7rn0x9EDpXImPvwVjT9\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"dyna_0\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 104\n    },\n    \"_children\": [\n      {\n        \"__id__\": 106\n      },\n      {\n        \"__id__\": 120\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 941.26,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"f6v2oBZWxGxII3fleGVhaQ\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 105\n    },\n    \"_prefab\": {\n      \"__id__\": 107\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 106\n    },\n    \"asset\": {\n      \"__uuid__\": \"8ab54a00-9127-44b9-8bc1-51d318148fab\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 108\n    },\n    \"targetOverrides\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"d9cmI1TBNCh41kGVFo0PCp\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [\n      {\n        \"__id__\": 109\n      }\n    ],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.MountedChildrenInfo\",\n    \"targetInfo\": {\n      \"__id__\": 110\n    },\n    \"nodes\": [\n      {\n        \"__id__\": 111\n      },\n      {\n        \"__id__\": 114\n      },\n      {\n        \"__id__\": 117\n      }\n    ]\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"c46/YsCPVOJYA4mWEpNYRx\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 106\n    },\n    \"_prefab\": {\n      \"__id__\": 112\n    },\n    \"__editorExtras__\": {\n      \"mountedRoot\": {\n        \"__id__\": 106\n      }\n    }\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 111\n    },\n    \"asset\": {\n      \"__uuid__\": \"e549918d-a699-468c-9d07-2b1c9dbc1a7a\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 113\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"b6Klyyb5VFj4LLIa/2O3w0\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 106\n    },\n    \"_prefab\": {\n      \"__id__\": 115\n    },\n    \"__editorExtras__\": {\n      \"mountedRoot\": {\n        \"__id__\": 106\n      }\n    }\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 114\n    },\n    \"asset\": {\n      \"__uuid__\": \"db54204b-56fb-485e-bc0c-66149c7951c3\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 116\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"52vmdt3rBMl700dt6ftjaQ\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 106\n    },\n    \"_prefab\": {\n      \"__id__\": 118\n    },\n    \"__editorExtras__\": {\n      \"mountedRoot\": {\n        \"__id__\": 106\n      }\n    }\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 117\n    },\n    \"asset\": {\n      \"__uuid__\": \"e3ae5be9-b8ca-450f-a8f7-edd149148782\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 119\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"a17H2v7YJJoY/SMn/HRiEM\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 105\n    },\n    \"_prefab\": {\n      \"__id__\": 121\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 120\n    },\n    \"asset\": {\n      \"__uuid__\": \"23436676-d303-4acf-82f3-4a44d352b69f\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 122\n    },\n    \"targetOverrides\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"17byj9x5BCFY8RAyWCddXo\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [\n      {\n        \"__id__\": 123\n      }\n    ],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.MountedChildrenInfo\",\n    \"targetInfo\": {\n      \"__id__\": 124\n    },\n    \"nodes\": [\n      {\n        \"__id__\": 125\n      },\n      {\n        \"__id__\": 128\n      },\n      {\n        \"__id__\": 131\n      }\n    ]\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"c46/YsCPVOJYA4mWEpNYRx\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 120\n    },\n    \"_prefab\": {\n      \"__id__\": 126\n    },\n    \"__editorExtras__\": {\n      \"mountedRoot\": {\n        \"__id__\": 120\n      }\n    }\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 125\n    },\n    \"asset\": {\n      \"__uuid__\": \"b37ff484-6217-472b-bf28-09319f8a31bf\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 127\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"951KDrL2VD6bf1+Ky0cOpg\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 120\n    },\n    \"_prefab\": {\n      \"__id__\": 129\n    },\n    \"__editorExtras__\": {\n      \"mountedRoot\": {\n        \"__id__\": 120\n      }\n    }\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 128\n    },\n    \"asset\": {\n      \"__uuid__\": \"5b101290-4eae-4e19-b77b-6674f856e767\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 130\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"c3YZ6yyXxLgr0zuDBiy8d5\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 120\n    },\n    \"_prefab\": {\n      \"__id__\": 132\n    },\n    \"__editorExtras__\": {\n      \"mountedRoot\": {\n        \"__id__\": 120\n      }\n    }\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 131\n    },\n    \"asset\": {\n      \"__uuid__\": \"475c7890-0252-4203-8578-e5928cc7c2e8\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 133\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"7e4a/OQT9LYb0h+nMbbK8P\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"waves\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 101\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"14avaCB7tH3rpIaUiTcbB/\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"events\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 101\n    },\n    \"_children\": [\n      {\n        \"__id__\": 136\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"5ftv4gFzBKObIZ51xiIjen\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"event\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 135\n    },\n    \"_children\": [\n      {\n        \"__id__\": 137\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 140\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 23.641,\n      \"y\": 1970.411,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"90DPEfTBZBvam3b/5LU/8d\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 136\n    },\n    \"_prefab\": {\n      \"__id__\": 138\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 137\n    },\n    \"asset\": {\n      \"__uuid__\": \"d5f5e836-9495-4c08-890f-d92cc6696838\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 139\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"34ftD+rpdOyayHyPNku6yP\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"9fa8bQG+gZMmpqlZsjy9rl1\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 136\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"conditions\": [\n      {\n        \"__id__\": 141\n      }\n    ],\n    \"triggers\": [\n      {\n        \"__id__\": 142\n      }\n    ],\n    \"_id\": \"ccxWwiF8JKX7096Ea/WG9Y\"\n  },\n  {\n    \"__type__\": \"LevelEditorCondition\"\n  },\n  {\n    \"__type__\": \"LevelEditorEventTrigger\"\n  },\n  {\n    \"__type__\": \"92d5epe+XRMm6NUwiCSJBKR\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 101\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_id\": \"39IX5ddYxOYqQKhatzTzMu\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"layer_5\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 100\n    },\n    \"_children\": [\n      {\n        \"__id__\": 145\n      },\n      {\n        \"__id__\": 146\n      },\n      {\n        \"__id__\": 148\n      },\n      {\n        \"__id__\": 149\n      },\n      {\n        \"__id__\": 150\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 151\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": -1158.9999999999975,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"64lR1jAxZH1oBO8RgBfnEX\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"terrains\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 144\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"4fb+71Do1KtIKwpaDEz8YH\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"scrolls\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 144\n    },\n    \"_children\": [\n      {\n        \"__id__\": 147\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"33U8WP8Z5Eypo0lbOBsj84\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"scroll_0\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 146\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"15Cg4iMMFOjZxL9SYItTrO\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"dynamic\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 144\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"51+bd1geNGyqG8/2YIRYiR\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"waves\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 144\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"0fkua9zaRAjpL+BIrMrSn5\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"events\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 144\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"26kK+fTupDcKo1GKtGZWVX\"\n  },\n  {\n    \"__type__\": \"92d5epe+XRMm6NUwiCSJBKR\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 144\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_id\": \"f5YDh/g4VDyJEtLvOD9NvB\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"layer_4\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 100\n    },\n    \"_children\": [\n      {\n        \"__id__\": 153\n      },\n      {\n        \"__id__\": 154\n      },\n      {\n        \"__id__\": 155\n      },\n      {\n        \"__id__\": 156\n      },\n      {\n        \"__id__\": 157\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 158\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": -1448.7499999999968,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"a2uS95TGpCoLeQCdQra61A\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"terrains\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 152\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"b1hEj7b/RAz6ViagTRspi3\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"scrolls\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 152\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"f3P5wp9/tKDJKPKIDRfd1G\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"dynamic\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 152\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"a1FcK1q2RMYpUuvh4x19E4\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"waves\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 152\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"cfVb/q0QVF2LNmQHAVWrMv\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"events\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 152\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"ae2Cgy5yRLcZmWMv6cmLEX\"\n  },\n  {\n    \"__type__\": \"92d5epe+XRMm6NUwiCSJBKR\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 152\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_id\": \"49zndRGc5NMqbDGqN76lMb\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"layer_3\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 100\n    },\n    \"_children\": [\n      {\n        \"__id__\": 160\n      },\n      {\n        \"__id__\": 161\n      },\n      {\n        \"__id__\": 163\n      },\n      {\n        \"__id__\": 164\n      },\n      {\n        \"__id__\": 165\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 166\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": -1332.8499999999972,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"fepgNc8wtDs68w8ivqvhWp\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"terrains\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 159\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"88bIBuRg1NlqTm48AaA3T6\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"scrolls\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 159\n    },\n    \"_children\": [\n      {\n        \"__id__\": 162\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"behfi/JDpBlKTibDjHXBz+\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"scroll_0\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 161\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"ae4FbNotBArLuv54m09Xcl\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"dynamic\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 159\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"afqLBvmJFPVYaFIYL414mC\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"waves\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 159\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"8eGHcOkKlFc47HIT31/OUT\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"events\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 159\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"f7e4AVgHZArJ3m3lgXNhhj\"\n  },\n  {\n    \"__type__\": \"92d5epe+XRMm6NUwiCSJBKR\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 159\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_id\": \"e9HU37I7pCBqiVF8Za0Wdo\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"layer_2\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 100\n    },\n    \"_children\": [\n      {\n        \"__id__\": 168\n      },\n      {\n        \"__id__\": 310\n      },\n      {\n        \"__id__\": 311\n      },\n      {\n        \"__id__\": 312\n      },\n      {\n        \"__id__\": 313\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 314\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": -1158.9999999999975,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"e26jvR/SxKBKmBIiv0z3JS\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"terrains\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 167\n    },\n    \"_children\": [\n      {\n        \"__id__\": 169\n      },\n      {\n        \"__id__\": 172\n      },\n      {\n        \"__id__\": 175\n      },\n      {\n        \"__id__\": 178\n      },\n      {\n        \"__id__\": 181\n      },\n      {\n        \"__id__\": 184\n      },\n      {\n        \"__id__\": 187\n      },\n      {\n        \"__id__\": 190\n      },\n      {\n        \"__id__\": 193\n      },\n      {\n        \"__id__\": 196\n      },\n      {\n        \"__id__\": 199\n      },\n      {\n        \"__id__\": 202\n      },\n      {\n        \"__id__\": 205\n      },\n      {\n        \"__id__\": 208\n      },\n      {\n        \"__id__\": 211\n      },\n      {\n        \"__id__\": 214\n      },\n      {\n        \"__id__\": 217\n      },\n      {\n        \"__id__\": 220\n      },\n      {\n        \"__id__\": 223\n      },\n      {\n        \"__id__\": 226\n      },\n      {\n        \"__id__\": 229\n      },\n      {\n        \"__id__\": 232\n      },\n      {\n        \"__id__\": 235\n      },\n      {\n        \"__id__\": 238\n      },\n      {\n        \"__id__\": 241\n      },\n      {\n        \"__id__\": 244\n      },\n      {\n        \"__id__\": 247\n      },\n      {\n        \"__id__\": 250\n      },\n      {\n        \"__id__\": 253\n      },\n      {\n        \"__id__\": 256\n      },\n      {\n        \"__id__\": 259\n      },\n      {\n        \"__id__\": 262\n      },\n      {\n        \"__id__\": 265\n      },\n      {\n        \"__id__\": 268\n      },\n      {\n        \"__id__\": 271\n      },\n      {\n        \"__id__\": 274\n      },\n      {\n        \"__id__\": 277\n      },\n      {\n        \"__id__\": 280\n      },\n      {\n        \"__id__\": 283\n      },\n      {\n        \"__id__\": 286\n      },\n      {\n        \"__id__\": 289\n      },\n      {\n        \"__id__\": 292\n      },\n      {\n        \"__id__\": 295\n      },\n      {\n        \"__id__\": 298\n      },\n      {\n        \"__id__\": 301\n      },\n      {\n        \"__id__\": 304\n      },\n      {\n        \"__id__\": 307\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"b3pCB9FcRPbLy3BODAulUq\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 168\n    },\n    \"_prefab\": {\n      \"__id__\": 170\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 169\n    },\n    \"asset\": {\n      \"__uuid__\": \"fa2a93d0-62ca-4bb2-a9e7-72aeb5c74d91\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 171\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"b7Hq5ksoxMc6xeK7zwoFvY\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 168\n    },\n    \"_prefab\": {\n      \"__id__\": 173\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 172\n    },\n    \"asset\": {\n      \"__uuid__\": \"fa2a93d0-62ca-4bb2-a9e7-72aeb5c74d91\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 174\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"4a8j2L/sJCeq0C9v7r9ShB\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 168\n    },\n    \"_prefab\": {\n      \"__id__\": 176\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 175\n    },\n    \"asset\": {\n      \"__uuid__\": \"fa2a93d0-62ca-4bb2-a9e7-72aeb5c74d91\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 177\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"34TLwYAQRNU4ZqsCVXNv/U\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 168\n    },\n    \"_prefab\": {\n      \"__id__\": 179\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 178\n    },\n    \"asset\": {\n      \"__uuid__\": \"fa2a93d0-62ca-4bb2-a9e7-72aeb5c74d91\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 180\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"9ctW5Iw4ZGp4jJ7fCZ1nfZ\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 168\n    },\n    \"_prefab\": {\n      \"__id__\": 182\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 181\n    },\n    \"asset\": {\n      \"__uuid__\": \"34375de8-2fcc-4139-82d9-ce002f0703df\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 183\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"c2f4WVI2tPJoKpM/XPwg1k\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 168\n    },\n    \"_prefab\": {\n      \"__id__\": 185\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 184\n    },\n    \"asset\": {\n      \"__uuid__\": \"34375de8-2fcc-4139-82d9-ce002f0703df\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 186\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"81yXUpLH9I1rnmzrLu7pbw\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 168\n    },\n    \"_prefab\": {\n      \"__id__\": 188\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 187\n    },\n    \"asset\": {\n      \"__uuid__\": \"f7dcaf81-b030-4ceb-8246-902a9fb758f0\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 189\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"0aoEb34ElDdpGtRUrYmIZf\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 168\n    },\n    \"_prefab\": {\n      \"__id__\": 191\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 190\n    },\n    \"asset\": {\n      \"__uuid__\": \"d07ea99a-7a22-4181-9b53-5df074b47e12\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 192\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"cdPGKJlUNP5IhSlpLkdHqX\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 168\n    },\n    \"_prefab\": {\n      \"__id__\": 194\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 193\n    },\n    \"asset\": {\n      \"__uuid__\": \"6b9b463c-9068-400e-9320-a6855f0e5820\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 195\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"ccU9xLGbZDiINcsm7MwFE+\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 168\n    },\n    \"_prefab\": {\n      \"__id__\": 197\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 196\n    },\n    \"asset\": {\n      \"__uuid__\": \"d3efcc2f-42cd-4820-b60f-79a115d1cefe\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 198\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"4av9nubR9Pq6HPDGVs/PzC\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 168\n    },\n    \"_prefab\": {\n      \"__id__\": 200\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 199\n    },\n    \"asset\": {\n      \"__uuid__\": \"a87bdde7-3c51-4024-801e-9d53f6d4c559\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 201\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"fdaZvh4ZdLy5vpGKW8icQ9\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 168\n    },\n    \"_prefab\": {\n      \"__id__\": 203\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 202\n    },\n    \"asset\": {\n      \"__uuid__\": \"3f0eabf3-2b26-4403-8582-1818c448726d\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 204\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"20Q3x4FzZEnJaQKpXLMPVs\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 168\n    },\n    \"_prefab\": {\n      \"__id__\": 206\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 205\n    },\n    \"asset\": {\n      \"__uuid__\": \"f1fc9976-9904-427c-859e-13b13eb2b32e\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 207\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"c6szJ22aRNPazleI14uE9+\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 168\n    },\n    \"_prefab\": {\n      \"__id__\": 209\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 208\n    },\n    \"asset\": {\n      \"__uuid__\": \"f1fc9976-9904-427c-859e-13b13eb2b32e\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 210\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"2ewcqkR+pGrY5Ay9ZjIEGc\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 168\n    },\n    \"_prefab\": {\n      \"__id__\": 212\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 211\n    },\n    \"asset\": {\n      \"__uuid__\": \"1dda7088-102b-4d67-a176-5f5ddd4611a6\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 213\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"0blfm+M9ZAOJyj5vyHr/Bx\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 168\n    },\n    \"_prefab\": {\n      \"__id__\": 215\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 214\n    },\n    \"asset\": {\n      \"__uuid__\": \"1dda7088-102b-4d67-a176-5f5ddd4611a6\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 216\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"banj/gJZtGsopAqVChF2ZE\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 168\n    },\n    \"_prefab\": {\n      \"__id__\": 218\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 217\n    },\n    \"asset\": {\n      \"__uuid__\": \"a4a203cb-0beb-4a25-8db7-91384aa74193\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 219\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"96qx/2kWhCe6WbCJf5k+Qc\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 168\n    },\n    \"_prefab\": {\n      \"__id__\": 221\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 220\n    },\n    \"asset\": {\n      \"__uuid__\": \"1ee4d5da-29ea-472f-bdf1-67ca1e2d2ee1\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 222\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"f8aRHvthlKLKWIVaavJTX6\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 168\n    },\n    \"_prefab\": {\n      \"__id__\": 224\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 223\n    },\n    \"asset\": {\n      \"__uuid__\": \"6fd60140-b3ad-48bb-a5c0-1b6e1cff9930\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 225\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"cbQ5O4a/xCu6bpRiYqnfOM\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 168\n    },\n    \"_prefab\": {\n      \"__id__\": 227\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 226\n    },\n    \"asset\": {\n      \"__uuid__\": \"6fd60140-b3ad-48bb-a5c0-1b6e1cff9930\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 228\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"5f2P2jARdAFYYrD6mKPm02\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 168\n    },\n    \"_prefab\": {\n      \"__id__\": 230\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 229\n    },\n    \"asset\": {\n      \"__uuid__\": \"6fd60140-b3ad-48bb-a5c0-1b6e1cff9930\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 231\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"9c5D2yvI9ImoDb3/48zv4X\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 168\n    },\n    \"_prefab\": {\n      \"__id__\": 233\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 232\n    },\n    \"asset\": {\n      \"__uuid__\": \"31b083c6-01d9-41fd-929f-482ae01644b0\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 234\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"e8mFUDiHRCJrAa4BHgA9NN\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 168\n    },\n    \"_prefab\": {\n      \"__id__\": 236\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 235\n    },\n    \"asset\": {\n      \"__uuid__\": \"c13f050f-d21e-4a87-b3b2-68c05f496aa9\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 237\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"5a46JzXBVJ4IXtZ47sUURT\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 168\n    },\n    \"_prefab\": {\n      \"__id__\": 239\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 238\n    },\n    \"asset\": {\n      \"__uuid__\": \"c13f050f-d21e-4a87-b3b2-68c05f496aa9\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 240\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"93/WwvUipPrZztasswW9Fy\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 168\n    },\n    \"_prefab\": {\n      \"__id__\": 242\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 241\n    },\n    \"asset\": {\n      \"__uuid__\": \"c13f050f-d21e-4a87-b3b2-68c05f496aa9\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 243\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"32TR6Ea25L8Iy0jIPutPxs\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 168\n    },\n    \"_prefab\": {\n      \"__id__\": 245\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 244\n    },\n    \"asset\": {\n      \"__uuid__\": \"c13f050f-d21e-4a87-b3b2-68c05f496aa9\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 246\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"28J74A7ztDIokBWfVhBmWd\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 168\n    },\n    \"_prefab\": {\n      \"__id__\": 248\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 247\n    },\n    \"asset\": {\n      \"__uuid__\": \"b3511bf2-bb28-459c-aaba-9a14b83c0d46\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 249\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"103a6cnp5Cvaoeqh4re16f\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 168\n    },\n    \"_prefab\": {\n      \"__id__\": 251\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 250\n    },\n    \"asset\": {\n      \"__uuid__\": \"0a47a305-5aa2-4ad6-b8d7-3db3f8ae759d\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 252\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"eenzwsQeBFmZbOg2jUVK2b\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 168\n    },\n    \"_prefab\": {\n      \"__id__\": 254\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 253\n    },\n    \"asset\": {\n      \"__uuid__\": \"9093ff23-4245-43e6-9095-f558379b7375\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 255\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"9bO3DwCC9HRYQAS8JWwlE3\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 168\n    },\n    \"_prefab\": {\n      \"__id__\": 257\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 256\n    },\n    \"asset\": {\n      \"__uuid__\": \"9093ff23-4245-43e6-9095-f558379b7375\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 258\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"8aDSj8bLJPIbDt95O3wUus\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 168\n    },\n    \"_prefab\": {\n      \"__id__\": 260\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 259\n    },\n    \"asset\": {\n      \"__uuid__\": \"2c6064ed-ef51-482d-a932-c1f0e894a5bb\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 261\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"bb92NOAxpB57w8xTSbRLfJ\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 168\n    },\n    \"_prefab\": {\n      \"__id__\": 263\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 262\n    },\n    \"asset\": {\n      \"__uuid__\": \"1070c072-21b8-4c1f-b62b-16b98d72049d\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 264\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"e7UZr8AUBKZK/6nMmEzyVW\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 168\n    },\n    \"_prefab\": {\n      \"__id__\": 266\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 265\n    },\n    \"asset\": {\n      \"__uuid__\": \"143b8954-1efd-4e6f-b884-72287ff5a79f\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 267\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"18O/OmkSdKnqXwEqSOyVra\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 168\n    },\n    \"_prefab\": {\n      \"__id__\": 269\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 268\n    },\n    \"asset\": {\n      \"__uuid__\": \"eabf2306-2d05-4845-9a01-6febc804a1b7\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 270\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"4fHtTfB2JCPKS02eJNNvn2\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 168\n    },\n    \"_prefab\": {\n      \"__id__\": 272\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 271\n    },\n    \"asset\": {\n      \"__uuid__\": \"3d2365f8-50af-4528-a96a-961db2fa169b\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 273\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"380Tms/rlIhoPJPjB+Zcuo\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 168\n    },\n    \"_prefab\": {\n      \"__id__\": 275\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 274\n    },\n    \"asset\": {\n      \"__uuid__\": \"53e327fc-c905-46ab-a5ac-1d6b6c19a014\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 276\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"31TxmbPE5E9INWcCPaVAW0\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 168\n    },\n    \"_prefab\": {\n      \"__id__\": 278\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 277\n    },\n    \"asset\": {\n      \"__uuid__\": \"49fd6607-5227-4a63-a61a-6231cb527b7c\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 279\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"2ckiKgaR5GvIUtJlI0CrzB\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 168\n    },\n    \"_prefab\": {\n      \"__id__\": 281\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 280\n    },\n    \"asset\": {\n      \"__uuid__\": \"49fd6607-5227-4a63-a61a-6231cb527b7c\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 282\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"4cGRdMeo9EqpDUjUD7I7K/\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 168\n    },\n    \"_prefab\": {\n      \"__id__\": 284\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 283\n    },\n    \"asset\": {\n      \"__uuid__\": \"d06534c6-6dd5-40ed-99b2-07b5fd76e8bf\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 285\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"65vAAdeSNGIaYQQhmgiYk7\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 168\n    },\n    \"_prefab\": {\n      \"__id__\": 287\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 286\n    },\n    \"asset\": {\n      \"__uuid__\": \"d06534c6-6dd5-40ed-99b2-07b5fd76e8bf\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 288\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"e2V3SsvvFBUqm0rAUAhG/7\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 168\n    },\n    \"_prefab\": {\n      \"__id__\": 290\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 289\n    },\n    \"asset\": {\n      \"__uuid__\": \"d06534c6-6dd5-40ed-99b2-07b5fd76e8bf\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 291\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"cd+atXmEJG0I1LGDy81eCp\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 168\n    },\n    \"_prefab\": {\n      \"__id__\": 293\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 292\n    },\n    \"asset\": {\n      \"__uuid__\": \"0c493656-1d04-41e8-a3ff-62d8dc3db8a3\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 294\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"3fb9CxSIpAGK1eXglpAKAU\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 168\n    },\n    \"_prefab\": {\n      \"__id__\": 296\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 295\n    },\n    \"asset\": {\n      \"__uuid__\": \"0c493656-1d04-41e8-a3ff-62d8dc3db8a3\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 297\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"f7NV0DUnVBv79LxRchImhB\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 168\n    },\n    \"_prefab\": {\n      \"__id__\": 299\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 298\n    },\n    \"asset\": {\n      \"__uuid__\": \"0c493656-1d04-41e8-a3ff-62d8dc3db8a3\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 300\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"66CEkccHdIar92FNFmA+sP\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 168\n    },\n    \"_prefab\": {\n      \"__id__\": 302\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 301\n    },\n    \"asset\": {\n      \"__uuid__\": \"0c493656-1d04-41e8-a3ff-62d8dc3db8a3\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 303\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"a2okQkLuBJ8odAVftCubeC\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 168\n    },\n    \"_prefab\": {\n      \"__id__\": 305\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 304\n    },\n    \"asset\": {\n      \"__uuid__\": \"0c493656-1d04-41e8-a3ff-62d8dc3db8a3\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 306\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"abue8JZehDqb+s057eVatC\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 168\n    },\n    \"_prefab\": {\n      \"__id__\": 308\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 307\n    },\n    \"asset\": {\n      \"__uuid__\": \"0c493656-1d04-41e8-a3ff-62d8dc3db8a3\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 309\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"19mJcflvVECaw/f4KYlx3S\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"scrolls\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 167\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"72yKSJkKdFWodtQTvwf4iV\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"dynamic\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 167\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"890bi9itFD6rn3sCANroWA\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"waves\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 167\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"24lCVue7RPF7v73AFciyi+\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"events\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 167\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"822P67+E9L06YYuk5fi4a1\"\n  },\n  {\n    \"__type__\": \"92d5epe+XRMm6NUwiCSJBKR\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 167\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_id\": \"09C9bK1VtH5p8BST0spfCI\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"layer_1\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 100\n    },\n    \"_children\": [\n      {\n        \"__id__\": 316\n      },\n      {\n        \"__id__\": 317\n      },\n      {\n        \"__id__\": 318\n      },\n      {\n        \"__id__\": 319\n      },\n      {\n        \"__id__\": 320\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 321\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": -1158.9999999999975,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"bbXrjXfuBKjJYoagzohd0B\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"terrains\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 315\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"e4AYssj5dEh5YH9d6HQeeK\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"scrolls\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 315\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"81Rr4mSaZKg675OGlYTnCa\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"dynamic\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 315\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"80HiieSPFLzrigOWRRJAOG\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"waves\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 315\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"7a/5qWHZxAxLC43rfN3Gcw\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"events\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 315\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"34OBU9DqtI4bXk41I+B/MS\"\n  },\n  {\n    \"__type__\": \"92d5epe+XRMm6NUwiCSJBKR\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 315\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_id\": \"40L//GwlxFjKi+/7ccNvQZ\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"layer_0\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 100\n    },\n    \"_children\": [\n      {\n        \"__id__\": 323\n      },\n      {\n        \"__id__\": 327\n      },\n      {\n        \"__id__\": 328\n      },\n      {\n        \"__id__\": 329\n      },\n      {\n        \"__id__\": 330\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 331\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": -1158.9999999999975,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"54UIv/OUpABZ8Es8QmkxSZ\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"terrains\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 322\n    },\n    \"_children\": [\n      {\n        \"__id__\": 324\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"9aWp2VzHlEAJZ/TegRrU1/\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 323\n    },\n    \"_prefab\": {\n      \"__id__\": 325\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 324\n    },\n    \"asset\": {\n      \"__uuid__\": \"34375de8-2fcc-4139-82d9-ce002f0703df\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 326\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"8epNoeaGdJMaVFMbArpZ7u\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"scrolls\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 322\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"62OSTFZ4tAB4f/6x29j1yV\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"dynamic\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 322\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"82W6tcHeNKq4lqGoMuqOeM\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"waves\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 322\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"6dLjdkbudBi5XkpO1hmeeH\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"events\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 322\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"e6xjo/MChDhK+lKPOg0WG8\"\n  },\n  {\n    \"__type__\": \"92d5epe+XRMm6NUwiCSJBKR\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 322\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_id\": \"c3mLMce9BMk7a2PBHm1QRH\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"MainPlane\",\n    \"_objFlags\": 512,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 2\n    },\n    \"_children\": [\n      {\n        \"__id__\": 333\n      },\n      {\n        \"__id__\": 335\n      },\n      {\n        \"__id__\": 340\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 345\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"c5Llji/3pG8ZUsk+/AnCts\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"enemy\",\n    \"_objFlags\": 512,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 332\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 334\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"22ge7J4Q5BcrDOrtqtJWcy\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 333\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 100,\n      \"height\": 100\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"0e4ol/pP9AdL9y81NxK1IW\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Plane 128\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 332\n    },\n    \"_children\": [\n      {\n        \"__id__\": 336\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 339\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"dbYdiDAGtJNILT2mAHeQHg\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"128\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 335\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 337\n      },\n      {\n        \"__id__\": 338\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -146.727,\n      \"y\": -381.491,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"b1innlyJpPfbCEAHtzu7nR\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 336\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 126,\n      \"height\": 106\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"fcvUM8lEVG7r+Duly9xTUi\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 336\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"bcbad599-6805-464f-b852-c6330a6cc136@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 1,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"b9z9YmlO1ORJddzAvkDa26\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 335\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 100,\n      \"height\": 100\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"69CWNsBiBF8qLjUsjYBXVD\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Plane 150\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 332\n    },\n    \"_children\": [\n      {\n        \"__id__\": 341\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 344\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"8fSyYswbxJMYFuKxLm+oIa\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"150\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 340\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 342\n      },\n      {\n        \"__id__\": 343\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 49.887,\n      \"y\": -384.426,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"97bvio7axI36wm/98rDd6y\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 341\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 148,\n      \"height\": 124\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"4c8v2GIyBL7p+TWWFkd8E0\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 341\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"13a76ed5-7bc0-444c-b47f-8beab0558280@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 1,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"c3kMW+vo1DD4E5mvwzvbme\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 340\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 100,\n      \"height\": 100\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"7dfuqEV4hA5JIX88WPdF7X\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 332\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 100,\n      \"height\": 100\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"2dVZOPnxBB55oxOGHxThGs\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"SkyLayers\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 2\n    },\n    \"_children\": [\n      {\n        \"__id__\": 347\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"a3kr0ELyxNI6WeWVx+sGNb\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"layer_0\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 346\n    },\n    \"_children\": [\n      {\n        \"__id__\": 348\n      },\n      {\n        \"__id__\": 352\n      },\n      {\n        \"__id__\": 353\n      },\n      {\n        \"__id__\": 354\n      },\n      {\n        \"__id__\": 355\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 356\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": -1912.3499999999958,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"96Q22hhu9HzbUCVqculagv\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"terrains\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 347\n    },\n    \"_children\": [\n      {\n        \"__id__\": 349\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"06WA3H0kxGaLfPEQI2z/zx\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 348\n    },\n    \"_prefab\": {\n      \"__id__\": 350\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 349\n    },\n    \"asset\": {\n      \"__uuid__\": \"03913dea-9c35-45cb-a101-aa1db7eede2c\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 351\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"4c5oRDLQpNmYKBuFxQOouD\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"scrolls\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 347\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"eeR+Ky3aNBRr+2GSeqXgI7\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"dynamic\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 347\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"48rkR1qj5PMI4egW9xCUdW\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"waves\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 347\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"04k4wYZF9E/JuQvIrKCmwu\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"events\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 347\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"dbpIvtzrhLLZ4OOZGUDmae\"\n  },\n  {\n    \"__type__\": \"92d5epe+XRMm6NUwiCSJBKR\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 347\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_id\": \"55EojDSuRNY64xAAmMGsFg\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"DrawNode\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 2\n    },\n    \"_children\": [\n      {\n        \"__id__\": 358\n      },\n      {\n        \"__id__\": 361\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 364\n      },\n      {\n        \"__id__\": 365\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"33t5ymWlpLkbT5idbqGQ7X\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"drawView\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 357\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 359\n      },\n      {\n        \"__id__\": 360\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"65LbppaIRGz7EVQ8Ktul+X\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 358\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 100,\n      \"height\": 100\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"b1Cf74eydN0Yy5qbbxInaR\"\n  },\n  {\n    \"__type__\": \"cc.Graphics\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 358\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_lineWidth\": 10,\n    \"_strokeColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_lineJoin\": 2,\n    \"_lineCap\": 0,\n    \"_fillColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_miterLimit\": 10,\n    \"_id\": \"75zN+z8lVGqLYHx2jxa0qd\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"drawMask\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 357\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 362\n      },\n      {\n        \"__id__\": 363\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"9btiDQ+VZN95aFsLmdMyGC\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 361\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 100,\n      \"height\": 100\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"9eg7PH/u9Il7l5kqhTVfqD\"\n  },\n  {\n    \"__type__\": \"cc.Graphics\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 361\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_lineWidth\": 1,\n    \"_strokeColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_lineJoin\": 2,\n    \"_lineCap\": 0,\n    \"_fillColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_miterLimit\": 10,\n    \"_id\": \"f7nWk9GTBEfo/KF0FlisGD\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 357\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 850,\n      \"height\": 1334\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"9cewjtt/5IRbxIivL8bKq5\"\n  },\n  {\n    \"__type__\": \"cc.Graphics\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 357\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_lineWidth\": 10,\n    \"_strokeColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_lineJoin\": 2,\n    \"_lineCap\": 0,\n    \"_fillColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_miterLimit\": 10,\n    \"_id\": \"23xs7GFb1G6bRSeyAo5w9K\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"GizmoManager\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 2\n    },\n    \"_children\": [\n      {\n        \"__id__\": 367\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 370\n      },\n      {\n        \"__id__\": 371\n      },\n      {\n        \"__id__\": 372\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"d3v5uXomlOT7+eWq/qB6tX\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Icon_trigger.png\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 366\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 368\n      },\n      {\n        \"__id__\": 369\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 23.64100000000002,\n      \"y\": 811.4110000000026,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"2dTN3Iu1FHvLaoxs7pxrx+\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 367\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 200,\n      \"height\": 200\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"730pC7CXlPsa5u89e5/b6m\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 367\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": null,\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 1,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"b6QV4Uo2xBN7awXICK0iHD\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 366\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 100,\n      \"height\": 100\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"9e2rwEjI5C+YjwXO+qHC/c\"\n  },\n  {\n    \"__type__\": \"35b7e0iBnFHtqqvAd1SurM7\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 366\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"gizmosEnabled\": true,\n    \"drawInPlayMode\": false,\n    \"refreshRate\": 60,\n    \"maxDrawDistance\": 2000,\n    \"_id\": \"7cdnbQJwBJa4W5MKv0v5K3\"\n  },\n  {\n    \"__type__\": \"cc.Graphics\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 366\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_lineWidth\": 2,\n    \"_strokeColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_lineJoin\": 2,\n    \"_lineCap\": 0,\n    \"_fillColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_miterLimit\": 10,\n    \"_id\": \"ccCxkB499DzKU1PLjeh6V0\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"WavePreview\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 2\n    },\n    \"_children\": [\n      {\n        \"__id__\": 374\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 381\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -375,\n      \"y\": -667,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"b7MmUNi9VGIZ/SfYAsJZEf\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 373\n    },\n    \"_prefab\": {\n      \"__id__\": 375\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 374\n    },\n    \"asset\": {\n      \"__uuid__\": \"f22ea656-6b22-4569-95bf-8be5766bba40\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"c46/YsCPVOJYA4mWEpNYRx\",\n    \"instance\": {\n      \"__id__\": 376\n    },\n    \"targetOverrides\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"abqjrprQdHU7v+7SyIUm0y\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [\n      {\n        \"__id__\": 377\n      },\n      {\n        \"__id__\": 379\n      },\n      {\n        \"__id__\": 380\n      }\n    ],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 378\n    },\n    \"propertyPath\": [\n      \"_lpos\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 398.641,\n      \"y\": 1978.4110000000026,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"c46/YsCPVOJYA4mWEpNYRx\"\n    ]\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 378\n    },\n    \"propertyPath\": [\n      \"_lrot\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0.7071067811865476,\n      \"w\": -0.7071067811865475\n    }\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 378\n    },\n    \"propertyPath\": [\n      \"_euler\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 270\n    }\n  },\n  {\n    \"__type__\": \"e6727UuHORMv49xH6m7Wz2U\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 373\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_id\": \"f9BxcunIhB1a1g7v1ywwDe\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 2\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 750,\n      \"height\": 1334\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"d6rUX5yfhMlKoWX2bSbawx\"\n  },\n  {\n    \"__type__\": \"cc.Canvas\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 2\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_cameraComponent\": {\n      \"__id__\": 4\n    },\n    \"_alignCanvasWithScreen\": true,\n    \"_id\": \"12O/ljcVlEqLmVm3U2gEOQ\"\n  },\n  {\n    \"__type__\": \"68a25Vb5mhGApMaV59XFjQ0\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 2\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"progress\": 0.05794999999999988,\n    \"_id\": \"61Fg6c5BVE0I4TKZPyCZ+2\"\n  },\n  {\n    \"__type__\": \"a4bf2J2KGJJV7RbX1jwoQWJ\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 2\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"levelname\": \"\",\n    \"totalTime\": 100,\n    \"backgroundLayer\": {\n      \"__id__\": 386\n    },\n    \"floorLayers\": [\n      {\n        \"__id__\": 387\n      },\n      {\n        \"__id__\": 388\n      },\n      {\n        \"__id__\": 389\n      },\n      {\n        \"__id__\": 390\n      },\n      {\n        \"__id__\": 394\n      },\n      {\n        \"__id__\": 395\n      },\n      {\n        \"__id__\": 399\n      }\n    ],\n    \"skyLayers\": [\n      {\n        \"__id__\": 404\n      }\n    ],\n    \"_id\": \"f2rN/MimJBS6HE7SikeuYU\"\n  },\n  {\n    \"__type__\": \"LevelEditorBackgroundLayer\",\n    \"remark\": \"\",\n    \"zIndex\": 0,\n    \"node\": {\n      \"__id__\": 6\n    },\n    \"speed\": 200,\n    \"scrollLayers\": [],\n    \"randomLayers\": [],\n    \"backgrounds\": [\n      {\n        \"__uuid__\": \"4c4ae1d7-4d0f-420e-bffc-195807ab0c8d\",\n        \"__expectedType__\": \"cc.Prefab\"\n      }\n    ]\n  },\n  {\n    \"__type__\": \"LevelEditorLayer\",\n    \"remark\": \"开场衔接层\",\n    \"zIndex\": 0,\n    \"node\": {\n      \"__id__\": 322\n    },\n    \"speed\": 200,\n    \"scrollLayers\": [],\n    \"randomLayers\": []\n  },\n  {\n    \"__type__\": \"LevelEditorLayer\",\n    \"remark\": \"云层衔接\",\n    \"zIndex\": 0,\n    \"node\": {\n      \"__id__\": 315\n    },\n    \"speed\": 200,\n    \"scrollLayers\": [],\n    \"randomLayers\": []\n  },\n  {\n    \"__type__\": \"LevelEditorLayer\",\n    \"remark\": \"Element\",\n    \"zIndex\": 0,\n    \"node\": {\n      \"__id__\": 167\n    },\n    \"speed\": 200,\n    \"scrollLayers\": [],\n    \"randomLayers\": []\n  },\n  {\n    \"__type__\": \"LevelEditorLayer\",\n    \"remark\": \"Fog\",\n    \"zIndex\": 0,\n    \"node\": {\n      \"__id__\": 159\n    },\n    \"speed\": 230,\n    \"scrollLayers\": [\n      {\n        \"__id__\": 391\n      }\n    ],\n    \"randomLayers\": []\n  },\n  {\n    \"__type__\": \"LevelEditorScrollLayerUI\",\n    \"scrollPrefabs\": [],\n    \"weight\": 100,\n    \"splicingMode\": 1,\n    \"splicingOffsetX\": {\n      \"__id__\": 392\n    },\n    \"splicingOffsetY\": {\n      \"__id__\": 393\n    }\n  },\n  {\n    \"__type__\": \"LayerRandomRange\",\n    \"min\": 0,\n    \"max\": 0\n  },\n  {\n    \"__type__\": \"LayerRandomRange\",\n    \"min\": 0,\n    \"max\": 0\n  },\n  {\n    \"__type__\": \"LevelEditorLayer\",\n    \"remark\": \"Clouds\",\n    \"zIndex\": 0,\n    \"node\": {\n      \"__id__\": 152\n    },\n    \"speed\": 250,\n    \"scrollLayers\": [],\n    \"randomLayers\": []\n  },\n  {\n    \"__type__\": \"LevelEditorLayer\",\n    \"remark\": \"Mask\",\n    \"zIndex\": 0,\n    \"node\": {\n      \"__id__\": 144\n    },\n    \"speed\": 200,\n    \"scrollLayers\": [\n      {\n        \"__id__\": 396\n      }\n    ],\n    \"randomLayers\": []\n  },\n  {\n    \"__type__\": \"LevelEditorScrollLayerUI\",\n    \"scrollPrefabs\": [],\n    \"weight\": 100,\n    \"splicingMode\": 1,\n    \"splicingOffsetX\": {\n      \"__id__\": 397\n    },\n    \"splicingOffsetY\": {\n      \"__id__\": 398\n    }\n  },\n  {\n    \"__type__\": \"LayerRandomRange\",\n    \"min\": 0,\n    \"max\": 0\n  },\n  {\n    \"__type__\": \"LayerRandomRange\",\n    \"min\": 0,\n    \"max\": 0\n  },\n  {\n    \"__type__\": \"LevelEditorLayer\",\n    \"remark\": \"random\",\n    \"zIndex\": 0,\n    \"node\": {\n      \"__id__\": 101\n    },\n    \"speed\": 200,\n    \"scrollLayers\": [],\n    \"randomLayers\": [\n      {\n        \"__id__\": 400\n      }\n    ]\n  },\n  {\n    \"__type__\": \"LevelEditorRandTerrainsLayersUI\",\n    \"dynamicTerrains\": [\n      {\n        \"__id__\": 401\n      }\n    ]\n  },\n  {\n    \"__type__\": \"LevelEditorRandTerrainsLayerUI\",\n    \"weight\": 100,\n    \"dynamicTerrain\": [\n      {\n        \"__id__\": 402\n      },\n      {\n        \"__id__\": 403\n      }\n    ]\n  },\n  {\n    \"__type__\": \"LevelEditorRandTerrainUI\",\n    \"weight\": 100,\n    \"terrainElement\": {\n      \"__uuid__\": \"8ab54a00-9127-44b9-8bc1-51d318148fab\",\n      \"__expectedType__\": \"cc.Prefab\"\n    }\n  },\n  {\n    \"__type__\": \"LevelEditorRandTerrainUI\",\n    \"weight\": 100,\n    \"terrainElement\": {\n      \"__uuid__\": \"23436676-d303-4acf-82f3-4a44d352b69f\",\n      \"__expectedType__\": \"cc.Prefab\"\n    }\n  },\n  {\n    \"__type__\": \"LevelEditorLayer\",\n    \"remark\": \"\",\n    \"zIndex\": 0,\n    \"node\": {\n      \"__id__\": 347\n    },\n    \"speed\": 330,\n    \"scrollLayers\": [],\n    \"randomLayers\": []\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": null,\n    \"asset\": null,\n    \"fileId\": \"401efd7e-bd20-4537-a13a-f25e6238c2a9\",\n    \"instance\": null,\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": [\n      {\n        \"__id__\": 8\n      },\n      {\n        \"__id__\": 13\n      },\n      {\n        \"__id__\": 18\n      },\n      {\n        \"__id__\": 23\n      },\n      {\n        \"__id__\": 28\n      },\n      {\n        \"__id__\": 33\n      },\n      {\n        \"__id__\": 38\n      },\n      {\n        \"__id__\": 43\n      },\n      {\n        \"__id__\": 48\n      },\n      {\n        \"__id__\": 53\n      },\n      {\n        \"__id__\": 58\n      },\n      {\n        \"__id__\": 63\n      },\n      {\n        \"__id__\": 68\n      },\n      {\n        \"__id__\": 73\n      },\n      {\n        \"__id__\": 78\n      },\n      {\n        \"__id__\": 83\n      },\n      {\n        \"__id__\": 88\n      },\n      {\n        \"__id__\": 106\n      },\n      {\n        \"__id__\": 111\n      },\n      {\n        \"__id__\": 114\n      },\n      {\n        \"__id__\": 117\n      },\n      {\n        \"__id__\": 120\n      },\n      {\n        \"__id__\": 125\n      },\n      {\n        \"__id__\": 128\n      },\n      {\n        \"__id__\": 131\n      },\n      {\n        \"__id__\": 137\n      },\n      {\n        \"__id__\": 169\n      },\n      {\n        \"__id__\": 172\n      },\n      {\n        \"__id__\": 175\n      },\n      {\n        \"__id__\": 178\n      },\n      {\n        \"__id__\": 181\n      },\n      {\n        \"__id__\": 184\n      },\n      {\n        \"__id__\": 187\n      },\n      {\n        \"__id__\": 190\n      },\n      {\n        \"__id__\": 193\n      },\n      {\n        \"__id__\": 196\n      },\n      {\n        \"__id__\": 199\n      },\n      {\n        \"__id__\": 202\n      },\n      {\n        \"__id__\": 205\n      },\n      {\n        \"__id__\": 208\n      },\n      {\n        \"__id__\": 211\n      },\n      {\n        \"__id__\": 214\n      },\n      {\n        \"__id__\": 217\n      },\n      {\n        \"__id__\": 220\n      },\n      {\n        \"__id__\": 223\n      },\n      {\n        \"__id__\": 226\n      },\n      {\n        \"__id__\": 229\n      },\n      {\n        \"__id__\": 232\n      },\n      {\n        \"__id__\": 235\n      },\n      {\n        \"__id__\": 238\n      },\n      {\n        \"__id__\": 241\n      },\n      {\n        \"__id__\": 244\n      },\n      {\n        \"__id__\": 247\n      },\n      {\n        \"__id__\": 250\n      },\n      {\n        \"__id__\": 253\n      },\n      {\n        \"__id__\": 256\n      },\n      {\n        \"__id__\": 259\n      },\n      {\n        \"__id__\": 262\n      },\n      {\n        \"__id__\": 265\n      },\n      {\n        \"__id__\": 268\n      },\n      {\n        \"__id__\": 271\n      },\n      {\n        \"__id__\": 274\n      },\n      {\n        \"__id__\": 277\n      },\n      {\n        \"__id__\": 280\n      },\n      {\n        \"__id__\": 283\n      },\n      {\n        \"__id__\": 286\n      },\n      {\n        \"__id__\": 289\n      },\n      {\n        \"__id__\": 292\n      },\n      {\n        \"__id__\": 295\n      },\n      {\n        \"__id__\": 298\n      },\n      {\n        \"__id__\": 301\n      },\n      {\n        \"__id__\": 304\n      },\n      {\n        \"__id__\": 307\n      },\n      {\n        \"__id__\": 324\n      },\n      {\n        \"__id__\": 349\n      },\n      {\n        \"__id__\": 374\n      }\n    ]\n  },\n  {\n    \"__type__\": \"cc.SceneGlobals\",\n    \"ambient\": {\n      \"__id__\": 407\n    },\n    \"shadows\": {\n      \"__id__\": 408\n    },\n    \"_skybox\": {\n      \"__id__\": 409\n    },\n    \"fog\": {\n      \"__id__\": 410\n    },\n    \"octree\": {\n      \"__id__\": 411\n    },\n    \"skin\": {\n      \"__id__\": 412\n    },\n    \"lightProbeInfo\": {\n      \"__id__\": 413\n    },\n    \"postSettings\": {\n      \"__id__\": 414\n    },\n    \"bakedWithStationaryMainLight\": false,\n    \"bakedWithHighpLightmap\": false\n  },\n  {\n    \"__type__\": \"cc.AmbientInfo\",\n    \"_skyColorHDR\": {\n      \"__type__\": \"cc.Vec4\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 0.520833125\n    },\n    \"_skyColor\": {\n      \"__type__\": \"cc.Vec4\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 0.520833125\n    },\n    \"_skyIllumHDR\": 20000,\n    \"_skyIllum\": 20000,\n    \"_groundAlbedoHDR\": {\n      \"__type__\": \"cc.Vec4\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 0\n    },\n    \"_groundAlbedo\": {\n      \"__type__\": \"cc.Vec4\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 0\n    },\n    \"_skyColorLDR\": {\n      \"__type__\": \"cc.Vec4\",\n      \"x\": 0.2,\n      \"y\": 0.5,\n      \"z\": 0.8,\n      \"w\": 1\n    },\n    \"_skyIllumLDR\": 20000,\n    \"_groundAlbedoLDR\": {\n      \"__type__\": \"cc.Vec4\",\n      \"x\": 0.2,\n      \"y\": 0.2,\n      \"z\": 0.2,\n      \"w\": 1\n    }\n  },\n  {\n    \"__type__\": \"cc.ShadowsInfo\",\n    \"_enabled\": false,\n    \"_type\": 0,\n    \"_normal\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 1,\n      \"z\": 0\n    },\n    \"_distance\": 0,\n    \"_planeBias\": 1,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 76,\n      \"g\": 76,\n      \"b\": 76,\n      \"a\": 255\n    },\n    \"_maxReceived\": 4,\n    \"_size\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 512,\n      \"y\": 512\n    }\n  },\n  {\n    \"__type__\": \"cc.SkyboxInfo\",\n    \"_envLightingType\": 0,\n    \"_envmapHDR\": null,\n    \"_envmap\": null,\n    \"_envmapLDR\": null,\n    \"_diffuseMapHDR\": null,\n    \"_diffuseMapLDR\": null,\n    \"_enabled\": false,\n    \"_useHDR\": true,\n    \"_editableMaterial\": null,\n    \"_reflectionHDR\": null,\n    \"_reflectionLDR\": null,\n    \"_rotationAngle\": 0\n  },\n  {\n    \"__type__\": \"cc.FogInfo\",\n    \"_type\": 0,\n    \"_fogColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 200,\n      \"g\": 200,\n      \"b\": 200,\n      \"a\": 255\n    },\n    \"_enabled\": false,\n    \"_fogDensity\": 0.3,\n    \"_fogStart\": 0.5,\n    \"_fogEnd\": 300,\n    \"_fogAtten\": 5,\n    \"_fogTop\": 1.5,\n    \"_fogRange\": 1.2,\n    \"_accurate\": false\n  },\n  {\n    \"__type__\": \"cc.OctreeInfo\",\n    \"_enabled\": false,\n    \"_minPos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -1024,\n      \"y\": -1024,\n      \"z\": -1024\n    },\n    \"_maxPos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1024,\n      \"y\": 1024,\n      \"z\": 1024\n    },\n    \"_depth\": 8\n  },\n  {\n    \"__type__\": \"cc.SkinInfo\",\n    \"_enabled\": false,\n    \"_blurRadius\": 0.01,\n    \"_sssIntensity\": 3\n  },\n  {\n    \"__type__\": \"cc.LightProbeInfo\",\n    \"_giScale\": 1,\n    \"_giSamples\": 1024,\n    \"_bounces\": 2,\n    \"_reduceRinging\": 0,\n    \"_showProbe\": true,\n    \"_showWireframe\": true,\n    \"_showConvex\": false,\n    \"_data\": null,\n    \"_lightProbeSphereVolume\": 1\n  },\n  {\n    \"__type__\": \"cc.PostSettingsInfo\",\n    \"_toneMappingType\": 0\n  }\n]"